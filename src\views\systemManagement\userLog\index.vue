<template>
  <div class="open-cantainer">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px;margin-right: 20px;"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        :placeholder="$t('filters.topIDNick')"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        :placeholder="$t('filters.agent')"
        style="width: 150px; margin-right: 20px; margin-top: 5px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.ip"
        size="mini"
        :placeholder="$t('tableHeader.IP')"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.stype"
        :placeholder="$t('tableHeader.operationType')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 5px"
        class="filter-item"
      >
        <el-option
          v-for="item in typeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <span style="font-size: 12px">{{$t('others.operationTime')}}</span>
      <el-date-picker
        style="width: 220px; margin-top: 5px; margin-right: 20px;"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change='filterTimeTransform'
      >
      </el-date-picker>
      <el-input
        v-model="listQuery.device_id"
        size="mini"
        :placeholder="$t('tableHeader.equipmentID')"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-right: 20px; margin-top: 5px"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
      <el-button
        class="filter-item"
        style="margin-right: 20px; margin-top: 5px"
        :loading="exportLoading"
        v-if="$store.getters.roles.indexOf('getuserlogexport')>-1"
        @click="handleExport"
        size="mini"
        type="success"
      >
        {{$t('buttons.export')}}
      </el-button>
    </div>


    <el-table
      v-loading="listLoading"
      :data="openList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="user_id" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center" min-width="95"> </el-table-column>
      <el-table-column :label="$t('filters.topID')" prop="top_agent_id" align="center" min-width="78">
          <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('filters.topNick')" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorID')" prop="pareid" align="center" min-width="78">
           <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.operationType')" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{typeObj[row.op_type]}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.IP_addr')" prop="ip_address" align="center" min-width="130"> </el-table-column>
      <el-table-column :label="$t('tableHeader.equipment')" prop="device" align="center" min-width="120"> </el-table-column>
      <el-table-column :label="$t('tableHeader.equipmentID')" prop="device_id" align="center" min-width="120"> </el-table-column>
      <el-table-column :label="$t('tableHeader.operationSystem')" prop="price" align="center" min-width="90px">
        <template slot-scope="{row}">
          <span>{{os_typeObj[row.os_type]}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('dialog.language')" prop="price" align="center" min-width="90px">
        <template slot-scope="{row}">
          <span>{{langObj[row.lang_type]}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.APP_version')" prop="version" align="center" min-width="90px">
        <template slot-scope="{row}">
          <span>{{row.version || '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('others.operationTime')" prop="created_time" align="center" min-width="75"> </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[300,100,50,10]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
//封装的api
import { getuserlog, getuserlogexport } from "@/api/systemManagement";
export default {
  name: "userlog",
  data() {
    return {
      listLoading: false,
      total: 0,
      openList: null,
      filterTime: [],
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        ip:"",
        stype: undefined, //账户模式 1：全仓 2：逐仓
        device_id: "", // 设备ID
        pageNo: 1,
        pagesize: 300,
        star: '', //开始
        end: '', //结束
      },
      typeOptions: [
        { key: 0, name: this.$t('login.title') },
        { key: 1, name: this.$t('filters.Registered') },
        { key: 2, name: this.$t('filters.Retrieve_login_password') },
        { key: 3, name: this.$t('filters.Setting_a_Login_Password') },
        { key: 4, name: this.$t('filters.Changing_the_Login_Password') },
        { key: 5, name: this.$t('filters.Change_fund_Password') },
        { key: 6, name: this.$t('filters.Changing_Mobile_phone_Number') },
        { key: 7, name: this.$t('filters.Modify_email') },
        { key: 8, name: this.$t('filters.Withdrawal_application') },
      ],
      typeObj: {
        0:this.$t('login.title'),
        1:this.$t('filters.Registered'),
        2:this.$t('filters.Retrieve_login_password'),
        3:this.$t('filters.Setting_a_Login_Password'),
        4:this.$t('filters.Changing_the_Login_Password'),
        5:this.$t('filters.Change_fund_Password'),
        6:this.$t('filters.Changing_Mobile_phone_Number'),
        7:this.$t('filters.Modify_email'),
        8:this.$t('filters.Withdrawal_application'),
        9: this.$t('filters.Set_fund_password'),
        10: this.$t('filters.Set_up_the_Google_validator'),
        11: this.$t('filters.Modify_the_Google_validator'),
        12: this.$t('filters.Set_mobile_phone_number'),
        13: this.$t('filters.Set_up_your_email'),
        14: this.$t('filters.KYC1_application'),
        15: this.$t('filters.KYC2_application'),
        16: this.$t('filters.Manual_KYC_application'),
      },
      langObj:{
        0: this.$t('filters.Chinese_simplified'),
        1: this.$t('filters.English'),
        2: this.$t('filters.Chinese_traditional'),
        3: this.$t('filters.Korean'),
        4: this.$t('filters.Japanese'),
      },
      os_typeObj:{
        1: "android",
        2: "iOS",
        3: "WEB",
        4: "H5",
        5: "open_api",
        6: this.$t('others.system_automatically'),
      },
      exportLoading: false, //导出加载中效果
    };
  },

  components: {},

  computed: {
    // 默认时间
    timeDefault () {
      let date = new Date()
      // 通过时间戳计算
      let defalutStartTime = ((date.getTime() - 1 * 24 * 3600 * 1000)/1000).toDate('yyyy-MM-dd') // 转化为时间戳
      let defalutEndTime = ((date.getTime()/1000)).toDate('yyyy-MM-dd')
      return [defalutStartTime, defalutEndTime]
    }
  },

  mounted() {
    this.filterTime = this.timeDefault;
    this.listQuery.star = (this.filterTime && this.filterTime[0]) || "";
    this.listQuery.end = this.filterTime ? this.filterTime[1] + " 23:59:59" : "";
    this.getList();
  },

  methods: {
    getList() {
      this.listLoading = true;
      let data = {};
      Object.assign(data, this.listQuery, {
        stype: this.listQuery.stype === 0 ? 0 : this.listQuery.stype || undefined,
      });
      getuserlog(data).then((res) => {
        this.openList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    //表格导出功能
    handleExport() {
      this.exportLoading = true;
      let data = {}
      Object.assign(data,this.listQuery)
      let newDate = parseInt(new Date().getTime()/1000);
      data.end = this.listQuery.end ? this.listQuery.end : (newDate.toDate('yyyy-MM-dd')+ ' 23:59:59')
      newDate = parseInt(newDate-518400)
      data.star = this.listQuery.star ? (this.listQuery.star + ' 00:00:00' ): (newDate.toDate('yyyy-MM-dd')+ ' 00:00:00')
      getuserlogexport(data).then((res) => {
        if(res.ret == 0){
            window.location.href=res.data.download_url;
            this.exportLoading = false
        }
      }).catch(err=>{
        this.exportLoading = false
      })
    },
    filterTimeTransform(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val? val[1]+' 23:59:59':'';
    },
  },
};
</script>
<style lang="scss" scoped>
</style>