<template>
	<div class="app-container">
		<!-- 查询条件 -->
		<el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="80px">
			<el-form-item :label="$t('sysdict.dictName')" prop="dictType">
				<el-select v-model="queryParams.data.dictType" :placeholder="$t('sysdict.pleaseSelectDictName')"
					clearable style="width: 240px" @change="handleDictTypeChange">
					<el-option v-for="item in dictTypeOptions" :key="item.dictType" :label="item.dictName"
						:value="item.dictType" />
				</el-select>
			</el-form-item>
			<el-form-item :label="$t('sysdict.dictLabel')" prop="dictLabel">
				<el-input v-model="queryParams.data.dictLabel" :placeholder="$t('sysdict.pleaseEnterDictLabel')"
					clearable style="width: 240px" @keyup.enter.native="handleQuery" />
			</el-form-item>
			<el-form-item :label="$t('sysdict.status')" prop="status">
				<el-select v-model="queryParams.data.status" :placeholder="$t('sysdict.pleaseSelectStatus')" clearable
					style="width: 240px">
					<el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label"
						:value="dict.value" />
				</el-select>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" icon="el-icon-search" @click="handleQuery"> {{ $t('common.search') }}
				</el-button>
				<el-button icon="el-icon-refresh" @click="resetQuery"> {{ $t('common.reset') }} </el-button>
			</el-form-item>
		</el-form>

		<!-- 操作按钮 -->
		<el-row :gutter="10" class="mb8">
			<el-col :span="1.5">
				<el-button type="primary" plain icon="el-icon-plus" :disabled="!queryParams.data.dictType"
					@click="handleAdd"> {{ $t('common.add') }} </el-button>
			</el-col>
			<right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
		</el-row>

		<!-- 数据表格 -->
		<el-table v-loading="loading" :data="dataList" border style="width: 100%">
			<el-table-column type="index" :label="$t('common.index')" width="50" align="center" />
			<el-table-column prop="id" :label="$t('sysdict.dictCode')" width="100" align="center" />
			<el-table-column prop="dictLabel" :label="$t('sysdict.dictLabel')" width="160" align="center"
				:show-overflow-tooltip="true" />
			<el-table-column prop="dictValue" :label="$t('sysdict.dictValue')" width="160" align="center"
				:show-overflow-tooltip="true" />
			<el-table-column prop="dictSort" :label="$t('sysdict.dictSort')" width="100" align="center" />
			<el-table-column prop="status" :label="$t('sysdict.status')" width="100" align="center">
				<template slot-scope="scope">
					<el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">{{ scope.row.status === '0' ?
						$t('sysdict.normal') : $t('sysdict.disabled') }}</el-tag>
				</template>
			</el-table-column>
			<el-table-column prop="remark" :label="$t('sysdict.remark')" align="center" :show-overflow-tooltip="true" />
			<el-table-column prop="createTime" :label="$t('sysdict.createTime')" width="180" align="center" />
			<el-table-column :label="$t('common.operation')" width="180" align="center" fixed="right">
				<template slot-scope="scope">
					<el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">{{
						$t('common.edit')
					}}</el-button>
					<el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">{{
						$t('common.delete') }}</el-button>
				</template>
			</el-table-column>
		</el-table>

		<!-- 分页 -->
		<pagina-tion v-show="total > 0" :total="total" :page.sync="queryParams.data.pageNo"
			:limit.sync="queryParams.data.pagesize" @pagination="getList" />

		<!-- 添加或修改字典数据对话框 -->
		<el-dialog :title="title" :visible.sync="open" width="50%" append-to-body>
			<el-form ref="form" :model="form" :rules="rules" label-width="150px">
				<el-form-item :label="$t('sysdict.dictType')" prop="dictType">
					<el-input v-model="form.dictType" :disabled="true" />
				</el-form-item>
				<el-form-item :label="$t('sysdict.dictLabel')" prop="dictLabel">
					<el-input v-model="form.dictLabel" :placeholder="$t('sysdict.pleaseEnterDictLabel')" />
				</el-form-item>
				<el-form-item :label="$t('sysdict.dictValue')" prop="dictValue">
					<el-input v-model="form.dictValue" :placeholder="$t('sysdict.pleaseEnterDictValue')" />
				</el-form-item>
				<el-form-item :label="$t('sysdict.dictSort')" prop="dictSort">
					<el-input-number v-model="form.dictSort" controls-position="right" :min="0" />
				</el-form-item>
				<el-form-item :label="$t('sysdict.cssClass')" prop="cssClass">
					<el-input v-model="form.cssClass" :placeholder="$t('sysdict.pleaseEnterCssClass')" />
				</el-form-item>
				<el-form-item :label="$t('sysdict.listClass')" prop="listClass">
					<el-select v-model="form.listClass" :placeholder="$t('sysdict.pleaseSelectListClass')"
						style="width: 100%">
						<el-option v-for="item in listClassOptions" :key="item.value" :label="item.label"
							:value="item.value" />
					</el-select>
				</el-form-item>
				<el-form-item :label="$t('sysdict.isDefault')" prop="isDefault">
					<el-radio-group v-model="form.isDefault">
						<el-radio label="Y">{{ $t('common.yes') }}</el-radio>
						<el-radio label="N">{{ $t('common.no') }}</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item :label="$t('sysdict.status')" prop="status">
					<el-radio-group v-model="form.status">
						<el-radio v-for="dict in statusOptions" :key="dict.value" :label="dict.value">{{ dict.label
						}}</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item :label="$t('sysdict.remark')" prop="remark">
					<el-input v-model="form.remark" type="textarea" :placeholder="$t('sysdict.pleaseEnterRemark')" />
				</el-form-item>
				<el-form-item :label="$t('forms.Select_language')" prop="langType">
					<el-select v-model="form.langType" :placeholder="$t('forms.Select_language')" style="width: 100%;">
						<el-option v-for="item in langOptions" :key="item.value" :label="$t(`filters.${item.name}`)"
							:value="item.value">
						</el-option>
					</el-select>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>
				<el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import { getDictTypeList, getDictDataList, getDictDataInfo, addDictDataInfo, upDictDataInfo, delDictDataInfo } from '@/api/sysdict'

export default {
	name: 'SysDictData',
	data() {
		return {
			// 遮罩层
			loading: false,
			// 显示搜索条件
			showSearch: true,
			// 总条数
			total: 0,
			// 字典数据表格数据
			dataList: [],
			// 字典类型选项
			dictTypeOptions: [],
			// 弹出层标题
			title: '',
			// 是否显示弹出层
			open: false,
			// 状态数据字典
			statusOptions: [
				{
					value: '0',
					label: this.$t('sysdict.normal')
				},
				{
					value: '1',
					label: this.$t('sysdict.disabled')
				}
			],
			// 样式类选项
			listClassOptions: [
				{
					value: "default",
					label: "默认"
				},
				{
					value: "primary",
					label: "主要"
				},
				{
					value: "success",
					label: "成功"
				},
				{
					value: "info",
					label: "信息"
				},
				{
					value: "warning",
					label: "警告"
				},
				{
					value: "danger",
					label: "危险"
				}
			],
			// 查询参数
			queryParams: {
				data: {
					pageNo: 1,
					pagesize: 10,
					dictType: undefined,
					dictLabel: undefined,
					status: undefined,
				}
			},
			// 表单参数
			form: {
				dictType: '',
				dictLabel: '',
				dictValue: '',
				dictSort: 0,
				cssClass: '',
				listClass: '',
				isDefault: 'N',
				status: '0',
				remark: '',
				langType: 0,
			},
			langOptions: [
				{ name: 'Simplified', value: 0, },
				{ name: 'English', value: 1, },
				{ name: 'Traditional', value: 2, },
				{ name: 'Korean', value: 3, },
				{ name: 'Vietnamese', value: 4, },
				{ name: 'Indonesian', value: 5, },
				{ name: 'Russian', value: 6, },
				{ name: 'German', value: 7, },
				{ name: 'Japanese', value: 8, },
			],
			// 表单校验
			rules: {
				dictLabel: [
					{ required: true, message: this.$t('sysdict.dictLabelRequired'), trigger: 'blur' }
				],
				dictValue: [
					{ required: true, message: this.$t('sysdict.dictValueRequired'), trigger: 'blur' }
				],
				dictSort: [
					{ required: true, message: this.$t('sysdict.dictSortRequired'), trigger: 'blur' }
				]
			}
		}
	},
	created() {
		this.getDictTypeOptions()
		// 获取路由参数中的字典类型和名称
		const { dictType, dictName } = this.$route.query
		if (dictType) {
			this.queryParams.data.dictType = dictType
			// 如果是从字典类型页面跳转过来，需要添加该选项到下拉框中
			if (dictName && !this.dictTypeOptions.some(item => item.dictType === dictType)) {
				this.dictTypeOptions.push({
					dictType: dictType,
					dictName: dictName
				})
			}
			this.getList()
		}
	},
	methods: {
		/** 查询字典类型下拉选项 */
		getDictTypeOptions() {
			getDictTypeList({ data: { pageSize: 100 } }).then(response => {
				this.dictTypeOptions = response.data.list
			})
		},
		/** 查询字典数据列表 */
		getList() {
			this.loading = true
			getDictDataList(this.queryParams).then(response => {
				this.dataList = response.data.list
				this.total = response.data.total
				this.loading = false
			})
		},
		/** 字典类型选择框变化 */
		handleDictTypeChange(value) {
			this.queryParams.data.dictType = value
			this.getList()
		},
		/** 取消按钮 */
		cancel() {
			this.open = false
			this.reset()
		},
		/** 重置表单 */
		resetForm(refName) {
			if (this.$refs[refName]) {
				this.$refs[refName].resetFields()
			}
		},
		/** 表单重置 */
		reset() {
			this.form = {
				id: undefined,
				dictType: this.queryParams.data.dictType,
				dictLabel: '',
				dictValue: '',
				dictSort: 0,
				cssClass: '',
				listClass: '',
				isDefault: 'N',
				status: '0',
				remark: '',
				langType: 0,
			}
			if (this.$refs.form) {
				this.$refs.form.resetFields()
			}
		},
		/** 搜索按钮操作 */
		handleQuery() {
			this.queryParams.data.page = 1
			this.getList()
		},
		/** 重置按钮操作 */
		resetQuery() {
			this.resetForm('queryForm')
			const dictType = this.queryParams.data.dictType
			this.queryParams.data = {
				page: 1,
				pageSize: 10,
				dictType: dictType,
			}
			this.handleQuery()
		},
		/** 新增按钮操作 */
		handleAdd() {
			this.reset()
			this.open = true
			this.title = this.$t('sysdict.addDictData')
		},
		/** 修改按钮操作 */
		handleUpdate(row) {
			this.reset()
			const id = row.id || this.ids
			getDictDataInfo({ id: id }).then(response => {
				this.form = response.data
				this.open = true
				this.title = this.$t('sysdict.editDictData')
			})
		},
		/** 提交按钮 */
		submitForm() {
			this.$refs['form'].validate(valid => {
				if (valid) {
					if (this.form.id !== undefined) {
						upDictDataInfo(this.form).then(response => {
							this.$message.success(this.$t('common.updateSuccess'))
							this.open = false
							this.getList()
						})
					} else {
						addDictDataInfo(this.form).then(response => {
							this.$message.success(this.$t('common.addSuccess'))
							this.open = false
							this.getList()
						})
					}
				}
			})
		},
		/** 删除按钮操作 */
		handleDelete(row) {
			const id = row.id
			this.$confirm(this.$t('common.confirmDelete'), this.$t('common.tip'), {
				confirmButtonText: this.$t('common.confirm'),
				cancelButtonText: this.$t('common.cancel'),
				type: 'warning'
			}).then(() => {
				return delDictDataInfo({ id: id })
			}).then(() => {
				this.getList()
				this.$message.success(this.$t('common.deleteSuccess'))
			}).catch(() => { })
		}
	}
}
</script>
