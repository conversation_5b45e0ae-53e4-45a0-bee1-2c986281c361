<template>
  <div class="ipstatisticslist-container">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.ip_address"
        :placeholder="$t('tableHeader.IP')"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-left: 20px; font-size: 12px">{{$t('menus.num_login_accounts')}}≥</span>
       <el-input
        v-model.number="listQuery.account_amount"
        size="mini"
        :placeholder="$t('filters.pleaseInput')"
        style="width: 150px; margin-left: 5px"
        class="filter-item"
        clearable
        onkeyup="value=value.replace(/[^\d]/g,0)"
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-left: 20px; font-size: 12px">{{$t('tableHeader.transaction_number')}}≥</span>
       <el-input
        v-model.number="listQuery.trade_times"
        size="mini"
        :placeholder="$t('filters.pleaseInput')"
        style="width: 150px; margin-left: 5px"
        class="filter-item"
        clearable
        onkeyup="value=value.replace(/[^\d]/g,0)"
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-left: 20px; font-size: 12px">{{$t('tableHeader.net_PNl')}}≥</span>
       <el-input
        v-model="listQuery.pure_pnl"
        size="mini"
        :placeholder="$t('filters.pleaseInput')"
        style="width: 150px; margin-left: 5px"
        class="filter-item"
        clearable
        oninput="value=value.replace(/^\D*(\d*(?:\.\d{0,6})?).*$/g, '$1')"
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-left: 20px; font-size: 12px">{{$t('tableHeader.PNL')}}≥</span>
       <el-input
        v-model="listQuery.pnl"
        size="mini"
        :placeholder="$t('filters.pleaseInput')"
        style="width: 150px; margin-left: 5px"
        class="filter-item"
        clearable
        oninput="value=value.replace(/^\D*(\d*(?:\.\d{0,6})?).*$/g, '$1')"
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-left: 20px; font-size: 12px">{{$t('menus.num_logged_devices')}}≥</span>
       <el-input
        v-model.number="listQuery.device_amount"
        size="mini"
        :placeholder="$t('filters.pleaseInput')"
        style="width: 150px; margin-left: 5px"
        class="filter-item"
        clearable
        onkeyup="value=value.replace(/[^\d]/g,0)"
        @keyup.enter.native="handleFilter"
      />
      <el-button
        style="margin-left: 20px; margin-top: 5px"
        class="filter-item"
        size="mini"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
      
    </div>
    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.IP')" prop="ip_address" align="center">
        <template slot-scope="{row}">
          <span>{{row.ip_address || '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.num_login_accounts')" prop="account_amount" align="center"> </el-table-column>
      <el-table-column :label="$t('tableHeader.transaction_number')" prop="trade_times" align="center"> </el-table-column>
      <el-table-column :label="$t('tableHeader.net_PNl')" prop="pure_pnl" align="center"> </el-table-column>
      <el-table-column :label="$t('tableHeader.PNL')" prop="pnl" align="center"> </el-table-column>
      <el-table-column :label="$t('tableHeader.poundage')" prop="commission" align="center"> </el-table-column>
      <el-table-column :label="$t('tableHeader.num_logged_devices')" prop="device_amount" align="center"> </el-table-column>
      <el-table-column :label="$t('tableHeader.operation')" align="center" width="280" v-if="$store.getters.roles.indexOf('ipuserlist')>-1 || $store.getters.roles.indexOf('iptradelist')>-1">
        <template slot-scope="{ row }">
          <el-button size="mini" type="primary" v-if="$store.getters.roles.indexOf('ipuserlist')>-1"  @click="detailClick(row, 'ipuserlist')">{{$t('buttons.view_IP_user_overview')}}</el-button>
          <el-button size="mini" type="primary" v-if="$store.getters.roles.indexOf('iptradelist')>-1"  @click="detailClick(row, 'iptradelist')">{{$t('buttons.view_IP_transaction_detailsv')}}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[200,100,50,20]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
//引入封装接口
import {
  ipstatisticslist,
} from "@/api/riskAdminister";

export default {
  name: "ipstatisticslist",
  data() {
    
    return {
      total: 0,
      tableData: [],
      listLoading: true,
      filterTime: [],
      listQuery: {
        ip_address: "",
        account_amount: 2,
        trade_times: undefined,
        pure_pnl: undefined,
        pnl: undefined,
        device_amount: undefined,
        pageNo: 1,
        pagesize: 20,
      },
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.getList();
  },

  methods: {
    //跳转详情页
    detailClick(row, url) {
      this.$router.push({
        path: "/riskAdminister/"+url,
        query: { 
          ip: row.ip_address
        },
      });
    },
    // 回车搜索事件
    handleFilter(){
      this.listQuery.pageNo = 1
      this.getList();
    },
    //  table列表数据
    getList() {
      //开始有加载中效果
      this.listLoading = true;
      let data = {}
      Object.assign(data, this.listQuery)
      data.account_amount = this.listQuery.account_amount?Number(this.listQuery.account_amount):undefined
      data.trade_times = this.listQuery.trade_times?Number(this.listQuery.trade_times):undefined
      data.pure_pnl = this.listQuery.pure_pnl?Number(this.listQuery.pure_pnl):undefined
      data.pnl = this.listQuery.pnl?Number(this.listQuery.pnl):undefined
      data.device_amount = this.listQuery.device_amount?Number(this.listQuery.device_amount):undefined
      ipstatisticslist(data).then((res) => {
        if(res.data){
          this.tableData = res.data.list;
          this.total = res.data.total;
        }else{
          this.tableData =[];
          this.total = 0;
        }
        this.listLoading = false;
      });
    },
    dataTimeChange(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val && val[1] || '';
    },
  },
};
</script>
<style lang="scss" scoped>
.wc_1 {
  width: 100%;
  height: 30px;
  display: flex;
  align-items: center;
  margin: 0 10px;
 
  // justify-content: space-around;
  .wc_1-one {
    width: 45%;
  }
  .idPhotoImg_wrap ::v-deep .el-image__inner{
    width: auto;
   
  }
  .idPhotoImg_wrap ::v-deep .el-image__error{
    min-width: 100px;
  }
}
</style>