<template>
  <div class="hold-cantainer">
    <div class="filter-container">
      <!-- <el-select
        size="mini"
        v-model="listQuery.contract_code"
        :placeholder="$t('tableHeader.contract')"
        clearable
        style="width: 120px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in contractOptions"
          :key="item.traderpairs"
          :label="item.traderpairs"
          :value="item.traderpairs"
        />
      </el-select> -->
      <span style="margin-right: 5px; font-size: 12px">{{$t('tableHeader.time')}}</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change='filterTimeTransform'
      >
      </el-date-picker>

      <el-button
        class="filter-item"
        size="mini"
        style="margin-left: 20px; margin-top: 5px"
        type="primary"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
      <el-button
        class="filter-item"
        v-if="$store.getters.roles.indexOf('throughhousexport')>-1"
        :loading="exportLoading"
        style="margin-left: 20px; margin-top: 5px"
        @click="handleExport"
        size="mini"
        type="success"
      >
        {{$t('buttons.export')}}
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="tableList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('filters.currency')" prop="coinname" min-width="90px" align="center"></el-table-column>
      <el-table-column :label="$t('tableHeader.uid')" prop="userid" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('filters.topNick')" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.type')" prop="top_agent_id" align="center" min-width="78"> 
        <template slot-scope="{ row }">
          <span>{{ opTypeOption[row.optype] || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.amount')" prop="amount" align="center" min-width="95"> </el-table-column>
      <el-table-column :label="$t('tableHeader.note')" prop="remark" align="center" min-width="95"> </el-table-column>
      <el-table-column :label="$t('tableHeader.time')" prop="createat" align="center" min-width="90px"> </el-table-column>
    </el-table>

    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[100,200,300,500]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";
//引入封装接口
import { getthroughhous, throughhousexport } from "@/api/platformFinance"; 
import { bcprocontractset } from "@/api/user";

export default {
  name: "throughPayList",
  data() {
    return {
      listLoading: false,
      total: 0,
      tableList: null,
      contractOptions: [],
      filterTime: [],
      listQuery: {
        // contract_code: "", //合约代码
        pageNo: 1,
        pagesize: 100,
        star: '', //开始
        end: '', //结束
      },
      exportLoading: false,//导出加载中效果
      opTypeOption:{
        350: this.$t('others.qpcczc'),
        351: this.$t('others.pccczc'),
      }
    };
  },

  components: {},

  computed: {
    // 默认时间
    timeDefault () {
      let date = new Date()
      // 通过时间戳计算
      let defalutStartTime = ((date.getTime() - 1 * 24 * 3600 * 1000)/1000).toDate('yyyy-MM-dd') // 转化为时间戳
      let defalutEndTime = ((date.getTime()/1000)).toDate('yyyy-MM-dd')
      return [defalutStartTime, defalutEndTime]
    }
  },

  mounted() {
    // bcprocontractset({}).then((res) => {
    //   this.contractOptions = res.data.filter(v=>v.isshow == 1)
    // })
    this.filterTime = this.timeDefault;
    this.getList();
  },

  methods: {
    //  请求表格数据
    getList() {
      this.listLoading = true;
      this.listQuery.star = this.filterTime ? this.filterTime[0] + " 00:00:00" : "";;
      this.listQuery.end = this.filterTime ? this.filterTime[1] + " 23:59:59" : "";
      getthroughhous(this.listQuery).then((response) => {
        this.tableList = response.data.list;
        this.total = response.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    filterTimeTransform(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val? val[1]+' 23:59:59':'';
    },
    //表格导出功能
    handleExport() {
      this.exportLoading = true;
      let data = {}
      Object.assign(data,this.listQuery)
      let newDate = parseInt(new Date().getTime()/1000);
      data.end = this.listQuery.end ? this.listQuery.end : (newDate.toDate('yyyy-MM-dd')+ ' 23:59:59')
      newDate = parseInt(newDate-518400)
      data.star = this.listQuery.star ? (this.listQuery.star + ' 00:00:00' ): (newDate.toDate('yyyy-MM-dd')+ ' 00:00:00')
      throughhousexport(data).then((res) => {
        if(res.ret == 0){
            window.location.href=res.data.download_url;
            this.exportLoading = false
        }
      }).catch(err=>{
        this.exportLoading = false
      })
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) =>
        filterVal.map((j) => {
          if (j === "transaction_time") {
            return parseTime(v[j]);
          } else {
            return v[j];
          }
        })
      );
    },
  },
};
</script>
<style lang="scss" scoped>
</style>