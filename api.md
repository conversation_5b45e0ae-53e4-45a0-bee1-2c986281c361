### 1 获用户列表
- URL: http://{host_addr}/managers/v1/user/list
- Method: POST
- Request Body:

```json
{
    "data": {
        "sname": "", // uid 手机号或邮箱
        "stype": 0, // 0全部 1 顶级代理 2代理，3普通用户
        "topid": "", // 顶级代理id
        "topnick": "", // 顶级代理昵称
        "sagent": "", // 代理或者id
        "labal_id": 0, //  标签id 0全部 
        "invite_code": "", // 邀请码
        "regstar":"2020-11-02", // 注册开始时间
        "regend": "2020-11-02 23:59:59", // // 结束时间
        "pageNo": 1, // 分页
        "pagesize": 10, //数量
        "loginstar": "2020-11-02", // 开始时间
        "loginend": "2020-11-02 23:59:59" // 结束时间
       
    }
}
```
- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": {
            "total": 2, // 总条数
            "list": [
                {
                    "user_id": "7199248", // 用户id
                    "user_name": "<EMAIL>", // 用户名
                    "user_type": 1, // 1 顶级代理 2代理，3普通用户
                    "verify": 1, // 认证状态: 0-未认证 1-身份信息认证中 2-身份信息认证失败 3-身份信息认证通过 4-人脸信息认证中 5-人脸信息认证失败 6-人脸信息认证通过,7重置
                    "invite_code": "weqree", // 邀请码
                    "invite_parent": ".hdhsjw.wwiiw", // 上级
                    "top_agent_id": 22123, //顶级代理id
                    "petname":"hdhsj", // 顶级代理名字
                    "pareid": 32233, // 上级代理id
                    "parename":"uwuwiq", //上级代理名字
                    "labal_name":"标签1", //标签名字
                    "created_time":"2020-11-01 22:12:12", //注册时间
                    "last_login_time":"2020-11-01 22:12:12", //最后登陆时间
                    "last_login_ip":"************", //最后登陆ip
                    "content":"ueueieqqw", //备注
                    "is_agent": 1, // 是否是代理 1是 0否
                    "agent_status": 1 // 1正常 3注销

                },{
                    "user_id": "7199248", // 用户id
                    "user_name": "<EMAIL>", // 用户名
                    "user_type": 1, // 1 顶级代理 2代理，3普通用户
                    "verify": 1, // 认证状态: 0-未认证 1-身份信息认证中 2-身份信息认证失败 3-身份信息认证通过 4-人脸信息认证中 5-人脸信息认证失败 6-人脸信息认证通过,7重置
                    "invite_code": "weqree", // 邀请码
                    "invite_parent": ".hdhsjw.wwiiw", // 上级
                    "top_agent_id": 22123, //顶级代理id
                    "petname":"hdhsj", // 顶级代理名字
                    "pareid": 32233, // 上级代理id
                    "parename":"uwuwiq", //上级代理名字
                    "labal_name":"标签1", //标签名字
                    "created_time":"2020-11-01 22:12:12", //注册时间
                    "last_login_time":"2020-11-01 22:12:12", //最后登陆时间
                    "last_login_ip":"************", //最后登陆ip
                    "content":"ueueieqqw", //备注
                    "is_agent": 1, // 是否是代理 1是 0否
                    "agent_status": 1 // 1正常 3注销
                      }
            ]
        } 
}
```

### 2 添加备注
- URL: http://{host_addr}/managers/v1/user/addcont
- Method: POST
- Request Body:

```json
{
    "data": {
        "content": "hsjsjdid", // 备注
        "user_id": 1233112 //用户id
     
    }
}
```
- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": {
        } 
}
```

### 3 获取用户详细信息
- URL: http://{host_addr}/managers/v1/user/userinfo
- Method: POST
- Request Body:

```json
{
   "data": {
           "user_id": 12222 //用户id
       }
}
```

- Response Body:

```json
{
    "ret": 0,
    "msg": "",
     "data": {
                "user_id": 12222, // 用户id
                "user_name": "<EMAIL>", // 用户名
                "invite_code": "wwweeq", //邀请码
                "invite_parent": ".iwiweo.wwowo", //上级邀请码
                "user_type": 1, // 1顶级代理 2代理，3普通用户
                "created_time": "2020-11-12 10:00:23", //注册时间
                "last_login_time": "2020-12-02 12:33:11", //最后登陆时间
                "last_login_ip": "*************", // 登陆ip
                "content": "ieiejejjq", // 备注
                "is_agent": 1, // 1 代理 0普通用户
                "topagent": 22231, // 顶级代理id
                "paregant": 12213, //上级代理id
                "childagent": 12, // 下级代理人数
                "agent_rebate_ratio":55.1, // 返佣比例
                "up_agent_time": "2020-12-01 12:11:32", //返佣最后修改时间
                "childcount": 123, // 关联直推人数
                "real_name": "渣渣灰", // 真实名字
                "state": 1, // 认证状态 1-身份信息认证中 2-身份信息认证失败 3-身份信息认证通过 4-人脸信息认证中 5-人脸信息认证失败 6-人脸信息认证通过，7-身份认证重置
                "last_submit": "2020-11-29 21:11:32", // 最后提交
                "wallettotal": 1222.12, //钱包资产
                "walletbalance": 2211.11, //钱包可用
                "walletlock": 122.11, // 真实名字
                "address": "ox2eweowoeieieie", // 钱包地址
                "accbalance": 12344444.222, // 合约资产
                "accavailable": 2333.11, // 合约可用
                "accbond": 3322.11, // 冻结保证金
                "accprofit": 233.11, // 浮动盈亏
                "accequity": 233455.11  // 账户权益
                 
            } 
}
```

### 4 编辑用用户
- URL: http://{host_addr}/managers/v1/user/bcprouserup
- Method: POST
- Request Body:

```json
{
    "data": {
        "uid": "32321", //用户id
        "enablelogin": 1, //1: 允许登录 0: 禁止登录
        "enablewithdraw":0, //1: 允许提币 0: 禁止提币
        "enabletrade": 0, //1: 允许交易 0: 禁止交易
        "upagent": 1, //1 更新代理信息 0不更新
        "agent_rebate_ratio": 50.31, //返佣比例
        "rake_back_time":1, //返佣发放期限
        "is_view_profit": 1 //是否可查看盈亏 1可以查看 0不可以
    }
}
```
- Response Body:

```json
{
    "ret": 0,
    "msg": "",
     "data": {
            } 
}
```

### 5 设置代理
- URL: http://{host_addr}/managers/v1/user/setagent
- Method: POST
- Request Body:

```json
{
    "data": {
            "uid": "12222", //用户id
            "petname": "uwuwuwu", //代理昵称
            "setype": 1, // 1添加顶级代理 0 非顶级代理
            "parcode": "wdsdsd", // 归属代理邀请码
            "agent_rebate_ratio": 43.4, //返佣
            "rake_back_time": 1,//返佣期限
            "is_view_profit": 1 //是否可查看盈亏 1可以查看 0不可以
        }
}
```

- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": {
       
    } 
}
```
### 6 海外kyc审核列表
- URL: http://{host_addr}/managers/v1/user/verifyhistorylist
- Method: POST
- Request Body:

```json
{
    "data": {
            "uid":"11229299", //用户uid
            "ttype":0, //-1全部，0-未认证 1-身份信息认证中 2-身份信息认证失败 3-身份信息认证通过 4-人脸信息认证中 5-人脸信息认证失败 6-人脸信息认证通过，7-认证重置
            "pageNo":1, //页数
            "pagesize":10, //数量
            "star":"2020-11-02", //开始
            "end":"2020-11-02 23:00:11" //结束
        }
}
```

- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": {
        "total": 2, // 总条数
        "list": [
            {
                "id": 11, // 数据id
                "user_id": 2231 //用户id
                "real_name": "是问问, //真实名字
                "surname": "122" //姓名-姓
                "forename": "2828", //姓名-名
                "id_number": "********", //证件号
                "type": 1, //0-大陆身份证，1-非大陆证件
                "state": 1, // 认证状态: 0-未认证 1-身份信息认证中 2-身份信息认证失败 3-身份信息认证通过 4-人脸信息认证中 5-人脸信息认证失败 6-人脸信息认证通过，7-认证重置
                "err_code": 1, //错误码
                "err_info": "2333", // 错误信息
                "create_at": "2020-11-12 22:11:22", //提交时间
                "identity_update_at":"2020-11-12 22:11:22", //身份信息认证更新时间
                "face_update_at": "2020-11-12 22:11:22", // 人脸信息认证更新时间
                "operator": "管理员", //操作人
                "id_photo": "http://img.offocrqb.com", // 图片url
                "birthday": "1972-12-11", // 生日
                "age": 51, // 年龄
                "ckeck_operator": "超级管理员", //审核员
                "country_code":32 //国家代码
            },{
                "id": 11, // 数据id
                "user_id": 2231 //用户id
                "real_name": "是问问, //真实名字
                "surname": "122" //姓名-姓
                "forename": "2828", //姓名-名
                "id_number": "********", //证件号
                "type": 1, //0-大陆身份证，1-非大陆证件
                "state": 1, // 认证状态: 0-未认证 1-身份信息认证中 2-身份信息认证失败 3-身份信息认证通过 4-人脸信息认证中 5-人脸信息认证失败 6-人脸信息认证通过，7-认证重置
                "err_code": 1, //错误码
                "err_info": "2333", // 错误信息
                "create_at": "2020-11-12 22:11:22", //提交时间
                "identity_update_at":"2020-11-12 22:11:22", //身份信息认证更新时间
                "face_update_at": "2020-11-12 22:11:22", // 人脸信息认证更新时间
                "operator": "管理员", //操作人
                "id_photo": "http://img.offocrqb.com", // 图片url
                "birthday": "1972-12-11", // 生日
                "age": 51, // 年龄
                "ckeck_operator": "超级管理员", //审核员
                "country_code":32 //国家代码
            }
        ]
    } 
}
```
### 7 海外kyc审核
- URL: http://{host_addr}/managers/v1/user/bcupverifyhistory
- Method: POST
- Request Body:

```json
{
    "data": {
            "uid":"11229299", //用户uid
            "stats":3, //3通过，5拒绝，7 重置
            "errcode":11, //错误码
            "errmsg":"u我i我i我", //错误信息
            "birthday":"2020-11-02", //开始
            "age": 22,//生日
            "upid":222 //更新数据id
        }
}
```

- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": {
    } 
}
```
### 8 重置海外kyc
- URL: http://{host_addr}/managers/v1/user/bcupverifyreset
- Method: POST
- Request Body:

```json
{
    "data": {
            "uid":"11229299", //用户uid
            "stats":3, //3通过，5拒绝，7 重置
            "upid":222 //更新数据id
        }
}
```

- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": {
    } 
}

```
### 9 标签列表
- URL: http://{host_addr}/managers/v1/comm/getlabllist
- Method: POST
- Request Body:

```json
{
    "data": {
        }
}
```

- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": [
              {"labelid": 21333, // 标签id
                "label_name": "标签" //标签名字
             },
              {"labelid": 21333, // 标签id
                "label_name": "标签" //标签名字
             }
          
           ]
}

```
### 10 持仓查询
- URL: http://{host_addr}/managers/v1/trade/positionlist
- Method: POST
- Request Body:

```json
{
    "data": {
        "sname": "272727",//用户id,手机号，邮箱
        "sectop": "ywywy",//顶级代理id或昵称
        "sagent": "wuueww",//代理id或者名字
        "account_type": 1,//账户模式 1：全仓 2：逐仓
        "contract_code": "btc/usdt",//合约代码
        "side": "S",//方向 B买S卖
        "pageNo":1,
        "pagesize":10      
        }
}
```
```json
{
    "ret": 0,
    "msg": "",
     "data": {
                "total": 2, // 总条数
                "list": [
                    {
                        "id": 2323442, //订单id
                        "user_id": "********", // 用户id
                        "contract_code": "BTCUSDT", //合约
                        "side": "S", // 方向 B买S卖
                        "price": 123.00, // 持仓均价
                        "volume": 111, // 持仓张数
                        "force_price": 125.00, // 强平价
                        "limit":100.22, //止盈价
                        "stop": 124.00, // 止损价
                        "trader_uid": 22122, // 交易员uid
                        "lever": 100, // 杠杆
                        "init_margin": 222.00, // 初始保证金
                        "margin": 224.00, // 保证金余额（起始保证金+手续费+调整）
                        "adjust_margin": 22.00, // 调整保证金
                        "float_profit": 122, // 为实现盈亏
                        "available": 122222.00, //可用余额
                        "profit_ratio": 325, //盈亏率
                        "margin_ratio": 322.1, //保证金率
                        "contract_index": 112.12, //合约指数
                        "buy_price": 124.00, // 买入价
                        "sell_price": 126, //卖出价
                        "commission": 2, //手续费
                        "create_time": "2020-11-02 11:34:12",//订单
                        "top_agent_id": 1122,//顶级代理id
                        "petname": "uwuie",//顶级昵称
                        "pareid": 112,//上级代理id
                        "parename": "代理1",//上级代理名字
                        "user_name": "用户"//用户名字

           }, {
                     "id": 2323442, //订单id
                     "user_id": "********", // 用户id
                     "contract_code": "BTCUSDT", //合约
                     "side": "S", // 方向 B买S卖
                     "price": 123.00, // 持仓均价
                     "volume": 111, // 持仓张数
                     "force_price": 125.00, // 强平价
                     "limit":100.22, //止盈价
                     "stop": 124.00, // 止损价
                     "trader_uid": 22122, // 交易员uid
                     "lever": 100, // 杠杆
                     "init_margin": 222.00, // 初始保证金
                     "margin": 224.00, // 保证金余额（起始保证金+手续费+调整）
                     "adjust_margin": 22.0 0, // 调整保证金
                     "float_profit": 122, // 为实现盈亏
                     "available": 122222.00, //可用余额
                     "profit_ratio": 325, //盈亏率
                     "margin_ratio": 322.1, //保证金率
                     "contract_index": 112.12, //合约指数
                     "buy_price": 124.00, // 买入价
                     "sell_price": 126, //卖出价
                     "commission": 2, //手续费
                     "create_time": "2020-11-02 11:34:12"//订单时间
                     "top_agent_id": 1122,//顶级代理id
                     "petname": "uwuie",//顶级昵称
                     "pareid": 112,//上级代理id
                     "parename": "代理1",//上级代理名字
                     "user_name": "用户"//用户名字

                 }
                ]
            } 
}
```

### 11 平仓查询
- URL: http://{host_addr}/managers/v1/trade/closetradelist
- Method: POST
- Request Body:

```json
{
    "data": {
        "sname": "272727",//用户id,手机号，邮箱
        "sectop": "ywywy",//顶级代理id或昵称
        "sagent": "wuueww",//代理id或者名字
        "account_type": 1,//账户模式 1：全仓 2：逐仓
        "contract_code": "btc/usdt",//合约代码
        "trade_id": "2111",交易id
        "side": "S",//方向 B买S卖
        "pageNo":1,
        "pagesize":10,
         "star":"2020-11-02", //开始
         "end":"2020-11-02 23:00:11" //结束     
        }
}
```
```json
{
    "ret": 0,
    "msg": "",
     "data": {
                "total": 2, // 总条数
                "list": [
                    {
                        "deal_id": 21333, // 成交编号
                       "user_id": 2231 //用户id
                       "trader_uid": 3234, //交易员id
                       "order_id": 321112 //订单id
                       "follow_position_id": 2323442, //跟随持仓id
                       "position_id": ********, //持仓id
                       "contract_code": "BTCUSDT", //合约
                       "side": "S", // 方向 B买S卖
                       "offset": O, // O: 开仓 C: 平仓
                       "volume": 111, // 持仓张数
                       "price": 125.00, // 成交价
                       "volume":1, //张数
                       "trade_value": 12114.00, // 成交价值
                       "commission": 0.112, // 手续费
                       "lever": 100, // 杠杆
                       "trade_amount": 100, // 成交金额
                       "back_profit": 21.00, // 成交分佣
                       "profit": 224.00, // 实现盈亏
                       "close_profit": 22.0 0, // 平仓盈亏
                       "order_type": 1, //0:   市价单 1：计划单 2：止盈单 4：止损单 5：强平单
                       "limit": 122.00, //止盈价
                       "stop": 325, //止损价
                       "trade_time":"2020-11-02 11:34:12",//成交时间
                       "balance": 11222.12, //账户资产
                       "available": 12412.00, //可用余额
                       "total_profit": 1226, //累计以实现盈亏
                       "broke_price": 222, //破产价格
                       "buy_price": 123, //买入价
                       "sell_price":222, //卖出价
                       "top_agent_id": 1122,//顶级代理id
                       "petname": "uwuie",//顶级昵称
                       "pareid": 112,//上级代理id
                       "parename": "代理1",//上级代理名字
                       "user_name": "用户"//用户名字

           }, {
                        "deal_id": 21333, // 成交编号
                       "user_id": 2231 //用户id
                       "trader_uid": 3234, //交易员id
                       "order_id": 321112 //订单id
                       "follow_position_id": 2323442, //跟随持仓id
                       "position_id": ********, //持仓id
                       "contract_code": "BTCUSDT", //合约
                       "side": "S", // 方向 B买S卖
                       "offset": O, // O: 开仓 C: 平仓
                       "volume": 111, // 持仓张数
                       "price": 125.00, // 成交价
                       "volume":1, //张数
                       "trade_value": 12114.00, // 成交价值
                       "commission": 0.112, // 手续费
                       "lever": 100, // 杠杆
                       "trade_amount": 100, // 成交金额
                       "back_profit": 21.00, // 成交分佣
                       "profit": 224.00, // 实现盈亏
                       "close_profit": 22.0 0, // 平仓盈亏
                       "order_type": 1, //0:   市价单 1：计划单 2：止盈单 4：止损单 5：强平单
                       "limit": 122.00, //止盈价
                       "stop": 325, //止损价
                       "trade_time":"2020-11-02 11:34:12",//成交时间
                       "balance": 11222.12, //账户资产
                       "available": 12412.00, //可用余额
                       "total_profit": 1226, //累计以实现盈亏
                       "broke_price": 222, //破产价格
                       "buy_price": 123, //买入价
                       "sell_price":222, //卖出价
                       "top_agent_id": 1122,//顶级代理id
                       "petname": "uwuie",//顶级昵称
                       "pareid": 112,//上级代理id
                       "parename": "代理1",//上级代理名字
                       "user_name": "用户"//用户名字
                 }
                ],
            "protradepnl": {
               "netpnl": 123.12,//净netpnl
               "clospnl": 123.12,//平仓pnl
               "commission":123.12//手续费
             }
            } 
}
```

### 12 开仓查询
- URL: http://{host_addr}/managers/v1/trade/opentradelist
- Method: POST
- Request Body:

```json
{
    "data": {
        "sname": "272727",//用户id,手机号，邮箱
        "sectop": "ywywy",//顶级代理id或昵称
        "sagent": "wuueww",//代理id或者名字
        "account_type": 1,//账户模式 1：全仓 2：逐仓
        "contract_code": "btc/usdt",//合约代码
        "trade_id": "2111",交易id
        "side": "S",//方向 B买S卖
        "pageNo":1,
        "pagesize":10,
         "star":"2020-11-02", //开始
         "end":"2020-11-02 23:00:11" //结束     
        }
}
```
```json
{
    "ret": 0,
    "msg": "",
     "data": {
                "total": 2, // 总条数
                "list": [
                    {
                       "tradeid": 21333, // 成交编号
                       "user_id": 2231 //用户id
                       "trader_uid": 3234, //交易员id
                       "order_id": 321112 //订单id
                       "follow_position_id": 2323442, //跟随持仓id
                       "position_id": ********, //持仓id
                       "contract_code": "BTCUSDT", //合约
                       "side": "S", // 方向 B买S卖
                       "offset": O, // O: 开仓 C: 平仓
                       "volume": 111, // 持仓张数
                       "price": 125.00, // 成交价
                       "volume":1, //张数
                       "trade_value": 12114.00, // 成交价值
                       "commission": 0.112, // 手续费
                       "lever": 100, // 杠杆
                       "trade_amount": 100, // 成交金额
                       "back_profit": 21.00, // 成交分佣
                       "profit": 224.00, // 实现盈亏
                       "close_profit": 22.0 0, // 平仓盈亏
                       "order_type": 1, //0:   市价单 1：计划单 2：止盈单 4：止损单 5：强平单
                       "limit": 122.00, //止盈价
                       "stop": 325, //止损价
                       "trade_time":"2020-11-02 11:34:12",//成交时间
                       "balance": 11222.12, //账户资产
                       "available": 12412.00, //可用余额
                       "total_profit": 1226, //累计以实现盈亏
                       "broke_price": 222, //破产价格
                       "buy_price": 123, //买入价
                       "sell_price":222, //卖出价
                       "top_agent_id": 1122,//顶级代理id
                       "petname": "uwuie",//顶级昵称
                       "pareid": 112,//上级代理id
                       "parename": "代理1",//上级代理名字
                       "user_name": "用户"//用户名字

           }, {
                       "deal_id": 21333, // 成交编号
                       "user_id": 2231 //用户id
                       "trader_uid": 3234, //交易员id
                       "order_id": 321112 //订单id
                       "follow_position_id": 2323442, //跟随持仓id
                       "position_id": ********, //持仓id
                       "contract_code": "BTCUSDT", //合约
                       "side": "S", // 方向 B买S卖
                       "offset": O, // O: 开仓 C: 平仓
                       "volume": 111, // 持仓张数
                       "price": 125.00, // 成交价
                       "volume":1, //张数
                       "trade_value": 12114.00, // 成交价值
                       "commission": 0.112, // 手续费
                       "lever": 100, // 杠杆
                       "trade_amount": 100, // 成交金额
                       "back_profit": 21.00, // 成交分佣
                       "profit": 224.00, // 实现盈亏
                       "close_profit": 22.0 0, // 平仓盈亏
                       "order_type": 1, //0:   市价单 1：计划单 2：止盈单 4：止损单 5：强平单
                       "limit": 122.00, //止盈价
                       "stop": 325, //止损价
                       "trade_time":"2020-11-02 11:34:12",//成交时间
                       "balance": 11222.12, //账户资产
                       "available": 12412.00, //可用余额
                       "total_profit": 1226, //累计以实现盈亏
                       "broke_price": 222, //破产价格
                       "buy_price": 123, //买入价
                       "sell_price":222, //卖出价
                       "top_agent_id": 1122,//顶级代理id
                       "petname": "uwuie",//顶级昵称
                       "pareid": 112,//上级代理id
                       "parename": "代理1",//上级代理名字
                       "user_name": "用户"//用户名字
                 }
                ]
            } 
}
```

### 13 用户资金查询
- URL: http://{host_addr}/managers/v1/account/getaccinfo
- Method: POST
- Request Body:

```json
{
    "data": {
        "sname": "272727",//用户id,手机号，邮箱
        "sectop": "ywywy",//顶级代理id或昵称
        "sagent": "wuueww",//代理id或者名字
        "incashmin": 0,//最小入金
        "incashmax": 10000,//最大入金
        "closmin": 1,//最小平仓数
        "closmax":111,//最大平仓数
        "marmin":1,//最小盈亏
        "marmax":111,//最大盈亏
        "commismin":1,//最小手续费
        "commismax":111,//最大手续费
        "rightsmin":1,//最小权益
        "rightsmax":111,//最大权益
        "pageNo":1,
        "pagesize":10 
        }
}
```

```json
{
    "ret": 0,
    "msg": "",
     "data": {
                "total": 2, // 总条数
                "list": [
                    { 
                       "user_id": 2231 //用户id
                       "top_agent_id": 1122,//顶级代理id
                       "petname": "uwuie",//顶级昵称
                       "pareid": 112,//上级代理id
                       "parename": "代理1",//上级代理名字
                       "user_name": "用户",//用户名字
                       "closcount": 11,//平仓次数
                       "totalincash": 1233,//总入金
                       "totaloutcash": 2231,//总出金
                       "capital": 112.1,//总资金费用
                       "netcash": 112.1,//出入金净值
                       "available": 2122,//账户可用
                       "lockbond": 1222,//保证金
                       "rights": 1222,//账户权益
                       "commission": 1222,//手续费
                       "ripplepnl": 1222,//浮动pnl
                       "clospnl": 1222,//平仓pnl
                       "netpnl": 1222//净pnl


           }, {
                       "user_id": 2231 //用户id
                       "top_agent_id": 1122,//顶级代理id
                       "petname": "uwuie",//顶级昵称
                       "pareid": 112,//上级代理id
                       "parename": "代理1",//上级代理名字
                       "user_name": "用户",//用户名字
                       "closcount": 11,//平仓次数
                       "totalincash": 1233,//总入金
                       "totaloutcash": 2231,//总出金
                       "capital": 112.1,//总资金费用
                       "netcash": 112.1,//出入金净值
                       "available": 2122,//账户可用
                       "lockbond": 1222,//保证金
                       "rights": 1222,//账户权益
                       "commission": 1222,//手续费
                       "ripplepnl": 1222,//浮动pnl
                       "clospnl": 1222,//平仓pnl
                       "netpnl": 1222//净pnl

                 }
                ]
            } 
}
```

### 14 设置标签
- URL: http://{host_addr}/managers/v1/account/setuserlabl
- Method: POST
- Request Body:

```json
{
    "data": {
        "lablid": 111,//标签id
        "user_id":1112 //用户id
        }
}
```

```json
{
    "ret": 0,
    "msg": "",
     "data": {
            } 
}
```


### 15 用户出入金查询
- URL: http://{host_addr}/managers/v1/account/getwalletbill
- Method: POST
- Request Body:

```json
{
    "data": {
        "sname": "272727",//用户id,手机号，邮箱
        "sectop": "ywywy",//顶级代理id或昵称
        "sagent": "wuueww",//代理id或者名字
        "coinid": 0,//币种id -1全部
        "star":"2020-11-02", //开始
        "end":"2020-11-02 23:00:11" //结束
        "stype": 1,//1充值 2 提币 64 法币购买
        "pageNo":1,
        "pagesize":10 
        }
}
```

```json
{
    "ret": 0,
    "msg": "",
     "data": {
                "total": 2, // 总条数
                "list": [
                    { 
                       "user_id": 2231 //用户id
                       "top_agent_id": 1122,//顶级代理id
                       "petname": "uwuie",//顶级昵称
                       "pareid": 112,//上级代理id
                       "parename": "代理1",//上级代理名字
                       "user_name": "用户",//用户名字
                       "type": 1,//1：充值  2：提现  64-法币订单到账
                       "status": 2,//充值 1：未到账 2：已到账 提现 1：申请已提交 2：已到账 3：总后台审核通过 4：总后台拒绝 其它为，5 平台拒绝，6 平台审核通过  2：已到账
                       "currencyid": 36,//币种id
                       "currencyname": "usdt",//币种名字
                       "tag": "tag标记",//tag
                       "amount": 122,//数量
                       "fromaddr": "12ww333",//来源地址
                       "toaddr": "w23311",//目标地址
                       "balance": 122,//用户账户可用数量
                       "lockamount": 12,//锁定数
                       "commission": 1,//手续费
                       "remarks": "备注",//备注
                       "createdtime": "2020-11-12 09:11:12",//创建时间
                       "platfom_mange": "管理员",// 审核人员
                       "platfom_time": "2020-11-12 09:11:12",//审核时间
                       "orderid": 12233,//订单id
                       "ipaddress": "*************",//
                       "orderclient":1,//委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)
                       "imei": "qwqweqweq"//imei
   

           }, {
                       "user_id": 2231 //用户id
                       "top_agent_id": 1122,//顶级代理id
                       "petname": "uwuie",//顶级昵称
                       "pareid": 112,//上级代理id
                       "parename": "代理1",//上级代理名字
                       "user_name": "用户",//用户名字
                       "type": 1,//1：充值  2：提现  64-法币订单到账
                       "status": 2,//充值 1：未到账 2：已到账 提现 1：申请已提交 2：已到账 3：总后台审核通过 4：总后台拒绝 其它为，5 平台拒绝，6 平台审核通过  2：已到账
                       "currencyid": 36,//币种id
                       "currencyname": "usdt",//币种名字
                       "tag": "tag标记",//tag
                       "amount": 122,//数量
                       "fromaddr": "12ww333",//来源地址
                       "toaddr": "w23311",//目标地址
                       "balance": 122,//用户账户可用数量
                       "lockamount": 12,//锁定数
                       "commission": 1,//手续费
                       "remarks": "备注",//备注
                       "createdtime": "2020-11-12 09:11:12",//创建时间
                       "platfom_mange": "管理员",// 审核人员
                       "platfom_time": "2020-11-12 09:11:12",//审核时间
                       "orderid": 12233,//订单id
                       "ipaddress": "*************",//
                       "orderclient":1,//委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)
                       "imei": "qwqweqweq"//imei
                 }
                ]
            } 
}
```

### 16 用户钱包记录
- URL: http://{host_addr}/managers/v1/account/getwallerhistory
- Method: POST
- Request Body:

```json
{
    "data": {
        "sname": "272727",//用户id,手机号，邮箱
        "sectop": "ywywy",//顶级代理id或昵称
        "sagent": "wuueww",//代理id或者名字
        "coinid": 0,//币种id -1全部
        "star":"2020-11-02", //开始
        "end":"2020-11-02 23:00:11" //结束
        "stype": 1,//1：充值  2：提现  4：划转到交易账户 8：从交易账户转入 16-邀请佣金奖励 32-代理佣金奖励 64-法币订单到账,128 空投 256-资产账户划转到跟单账户  512-跟单账户划转到资产账户  1024:佣金收入
        "bill_id": 111,成交编号
        "pageNo":1,
        "pagesize":10 
        }
}
```

```json
{
    "ret": 0,
    "msg": "",
     "data": {
                "total": 2, // 总条数
                "list": [
                    { 
                       "user_id": 2231 //用户id
                       "top_agent_id": 1122,//顶级代理id
                       "petname": "uwuie",//顶级昵称
                       "pareid": 112,//上级代理id
                       "parename": "代理1",//上级代理名字
                       "user_name": "用户",//用户名字
                       "type": 1,//1：充值  2：提现  64-法币订单到账
                       "status": 2,//充值 1：未到账 2：已到账 提现 1：申请已提交 2：已到账 3：总后台审核通过 4：总后台拒绝 其它为，5 平台拒绝，6 平台审核通过  2：已到账
                       "currencyid": 36,//币种id
                       "currencyname": "usdt",//币种名字
                       "tag": "tag标记",//tag
                       "amount": 122,//数量
                       "fromaddr": "12ww333",//来源地址
                       "toaddr": "w23311",//目标地址
                       "balance": 122,//用户账户可用数量
                       "lockamount": 12,//锁定数
                       "commission": 1,//手续费
                       "remarks": "备注",//备注
                       "createdtime": "2020-11-12 09:11:12",//创建时间
                       "platfom_mange": "管理员",// 审核人员
                       "platfom_time": "2020-11-12 09:11:12",//审核时间
                       "orderid": 12233,//订单id
                       "ipaddress": "*************",//
                       "orderclient":1,//委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)
                       "imei": "qwqweqweq"//imei
   

           }, {
                       "user_id": 2231 //用户id
                       "top_agent_id": 1122,//顶级代理id
                       "petname": "uwuie",//顶级昵称
                       "pareid": 112,//上级代理id
                       "parename": "代理1",//上级代理名字
                       "user_name": "用户",//用户名字
                       "type": 1,//1：充值  2：提现  64-法币订单到账
                       "status": 2,//充值 1：未到账 2：已到账 提现 1：申请已提交 2：已到账 3：总后台审核通过 4：总后台拒绝 其它为，5 平台拒绝，6 平台审核通过  2：已到账
                       "currencyid": 36,//币种id
                       "currencyname": "usdt",//币种名字
                       "tag": "tag标记",//tag
                       "amount": 122,//数量
                       "fromaddr": "12ww333",//来源地址
                       "toaddr": "w23311",//目标地址
                       "balance": 122,//用户账户可用数量
                       "lockamount": 12,//锁定数
                       "commission": 1,//手续费
                       "remarks": "备注",//备注
                       "createdtime": "2020-11-12 09:11:12",//创建时间
                       "platfom_mange": "管理员",// 审核人员
                       "platfom_time": "2020-11-12 09:11:12",//审核时间
                       "orderid": 12233,//订单id
                       "ipaddress": "*************",//
                       "orderclient":1,//委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)
                       "imei": "qwqweqweq"//imei
                 }
                ]
            } 
}
```

### 17 用户合约钱包账户
- URL: http://{host_addr}/managers/v1/account/getacchistory
- Method: POST
- Request Body:

```json
{
    "data": {
        "sname": "272727",//用户id,手机号，邮箱
        "sectop": "ywywy",//顶级代理id或昵称
        "sagent": "wuueww",//代理id或者名字
        "coinid": 0,//币种id -1全部
        "star":"2020-11-02", //开始
        "end":"2020-11-02 23:00:11" //结束
        "stype": 1,//1：开仓手续费 2：资金费用 4：从资产账户转入 8：划转到资产账户  16：平仓盈亏  32：平仓手续费 64: 模拟盘补充资产 128-调整保证金 256-预扣佣金 512-佣金退款 1024-佣金收入 2048-交易账户划转到跟单账户 4096-跟单账户划转到交易账户 8192-资产账户划转到跟单账户 16384-跟单账户划转到资产账户
        "bill_id": 0,//固定死传0
        "pageNo":1,
        "pagesize":10 
        }
}
```

```json
{
    "ret": 0,
    "msg": "",
     "data": {
                "total": 2, // 总条数
                "list": [
                    { 
                       "user_id": 2231 //用户id
                       "top_agent_id": 1122,//顶级代理id
                       "petname": "uwuie",//顶级昵称
                       "pareid": 112,//上级代理id
                       "parename": "代理1",//上级代理名字
                       "user_name": "用户",//用户名字
                       "type": 1,//1：开仓手续费 2：资金费用 4：从资产账户转入 8：划转到资产账户  16：平仓盈亏  32：平仓手续费 64: 模拟盘补充资产 128-调整保证金 256-预扣佣金 512-佣金退款 1024-佣金收入 2048-交易账户划转到跟单账户 4096-跟单账户划转到交易账户 8192-资产账户划转到跟单账户 16384-跟单账户划转到资产账户
                       "currencyid": 36,//币种id
                       "currencyname": "usdt",//币种名字
                       "amount": 122,//数量
                       "balance": 122,//资产数量
                       "available": 12,//可用  
                       "created_time": "2020-11-12 09:11:12",//创建时间
                       "ipaddress": "*************",//
                       "orderclient":1,//委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)
                       "imei": "qwqweqweq"//imei
           }, {
                       "user_id": 2231 //用户id
                       "top_agent_id": 1122,//顶级代理id
                       "petname": "uwuie",//顶级昵称
                       "pareid": 112,//上级代理id
                       "parename": "代理1",//上级代理名字
                       "user_name": "用户",//用户名字
                       "type": 1,//1：开仓手续费 2：资金费用 4：从资产账户转入 8：划转到资产账户  16：平仓盈亏  32：平仓手续费 64: 模拟盘补充资产 128-调整保证金 256-预扣佣金 512-佣金退款 1024-佣金收入 2048-交易账户划转到跟单账户 4096-跟单账户划转到交易账户 8192-资产账户划转到跟单账户 16384-跟单账户划转到资产账户
                       "currencyid": 36,//币种id
                       "currencyname": "usdt",//币种名字
                       "amount": 122,//数量
                       "balance": 122,//资产数量
                       "available": 12,//可用  
                       "created_time": "2020-11-12 09:11:12",//创建时间
                       "ipaddress": "*************",//
                       "orderclient":1,//委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)
                       "imei": "qwqweqweq"//imei
                 }
                ]
            } 
}
```

### 18 用户提币审核列表
- URL: http://{host_addr}/managers/v1/account/getwithdrawlist
- Method: POST
- Request Body:

```json
{
    "data": {
        "sname": "272727",//用户id,手机号，邮箱
        "sectop": "ywywy",//顶级代理id或昵称
        "sagent": "wuueww",//代理id或者名字
        "substar":"2020-11-02", //提交开始
        "subend":"2020-11-02 23:00:11" //提交结束
        "checkstar":"2020-11-02", //审核开始
        "checkend":"2020-11-02 23:00:11" //审核结束
        "stype": 1,// 1：待审核 2：已到账  5平台拒绝，6 平台审核通过  
        "pageNo":1,
        "pagesize":10 
        }
}
```

```json
{
    "ret": 0,
    "msg": "",
     "data": {
                "total": 2, // 总条数
                "list": [
                    { 
                       "user_id": 2231 //用户id
                       "top_agent_id": 1122,//顶级代理id
                       "petname": "uwuie",//顶级昵称
                       "pareid": 112,//上级代理id
                       "parename": "代理1",//上级代理名字
                       "user_name": "用户",//用户名字
                       "type": 1,//1：充值  2：提现  64-法币订单到账
                       "status": 2,//充值 1：未到账 2：已到账 提现 1：申请已提交 2：已到账 3：总后台审核通过 4：总后台拒绝 其它为，5 平台拒绝，6 平台审核通过  
                       "currencyid": 36,//币种id
                       "currencyname": "usdt",//币种名字
                       "tag": "tag标记",//tag
                       "amount": 122,//数量
                       "fromaddr": "12ww333",//来源地址
                       "toaddr": "w23311",//目标地址
                       "balance": 122,//用户账户可用数量
                       "lockamount": 12,//锁定数
                       "commission": 1,//手续费
                       "remarks": "备注",//备注
                       "createdtime": "2020-11-12 09:11:12",//创建时间
                       "platfom_mange": "管理员",// 审核人员
                       "platfom_time": "2020-11-12 09:11:12",//审核时间
                       "orderid": 12233,//订单id
                       "ipaddress": "*************",//
                       "orderclient":1,//委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)
                       "imei": "qwqweqweq"//imei
           }, {
                       "user_id": 2231 //用户id
                       "top_agent_id": 1122,//顶级代理id
                       "petname": "uwuie",//顶级昵称
                       "pareid": 112,//上级代理id
                       "parename": "代理1",//上级代理名字
                       "user_name": "用户",//用户名字
                       "type": 1,//1：充值  2：提现  64-法币订单到账
                       "status": 2,//充值 1：未到账 2：已到账 提现 1：申请已提交 2：已到账 3：总后台审核通过 4：总后台拒绝 其它为，5 平台拒绝，6 平台审核通过  2：已到账
                       "currencyid": 36,//币种id
                       "currencyname": "usdt",//币种名字
                       "tag": "tag标记",//tag
                       "amount": 122,//数量
                       "fromaddr": "12ww333",//来源地址
                       "toaddr": "w23311",//目标地址
                       "balance": 122,//用户账户可用数量
                       "lockamount": 12,//锁定数
                       "commission": 1,//手续费
                       "remarks": "备注",//备注
                       "createdtime": "2020-11-12 09:11:12",//创建时间
                       "platfom_mange": "管理员",// 审核人员
                       "platfom_time": "2020-11-12 09:11:12",//审核时间
                       "orderid": 12233,//订单id
                       "ipaddress": "*************",//
                       "orderclient":1,//委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)
                       "imei": "qwqweqweq"//imei
                 }
                ]
            } 
}
```

### 19 用户提币审核
- URL: http://{host_addr}/managers/v1/account/withdrawcheck
- Method: POST
- Request Body:

```json
{
    "data": {
        "id": 272727,//数据id
        "pass": true,//true 通过 false 驳回
        "content": "uuwuwo2"//备注
        }
}
```

```json
{
    "ret": 0,
    "msg": "",
     "data": {
            } 
}
```

### 20 用户返佣手续费列表
- URL: http://{host_addr}/managers/v1/period/getrebotlist
- Method: POST
- Request Body:

```json
{
    "data": {
        "sname": "272727",//用户id,手机号，邮箱
        "sectop": "ywywy",//顶级代理id或昵称
        "sagent": "wuueww",//代理id或者名字
        "star":"2020-11-02", //开始
        "end":"2020-11-02 23:00:11" //结束
        "stype": 1,// 0：全部 1待发放 2已发放 
        "pageNo":1,
        "pagesize":10 
        }
}
```
```json
{
    "ret": 0,
    "msg": "",
     "data": {
             "total": 2, // 总条数
             "list": [
                 { 
                    "user_id": 2231 //用户id
                    "top_agent_id": 1122,//顶级代理id
                    "petname": "uwuie",//顶级昵称
                    "pareid": 112,//上级代理id
                    "parename": "代理1",//上级代理名字
                    "user_name": "用户",//用户名字
                    "agent_rebate_ratio": 31,//返佣点位
                    "trading_fee": 111.111,//交易手续费
                    "trading_amount": 2211,//交易金额
                    "rebate_commission": 11.1,//返佣金额
                    "status_stype": 1,//1待发放 2已发放 
                    "period": 1,//结算周期
                    "end_period": "2020-11-02 23:59:59",//结算周期结束
                    "star_period": "2020-11-02",//结算周期开始
                    "send_time":"2020-11-03 23:59:59",//发放时间
                    "send_manage":"管理员1"//发放人
         }, {
                    "user_id": 2231 //用户id
                    "top_agent_id": 1122,//顶级代理id
                    "petname": "uwuie",//顶级昵称
                    "pareid": 112,//上级代理id
                    "parename": "代理1",//上级代理名字
                    "user_name": "用户",//用户名字
                    "agent_rebate_ratio": 31,//返佣点位
                    "trading_fee": 111.111,//交易手续费
                    "trading_amount": 2211,//交易金额
                    "rebate_commission": 11.1,//返佣金额
                    "status_stype": 1,//1待发放 2已发放 
                    "period": 1,//结算周期
                    "end_period": "2020-11-02 23:59:59",//结算周期结束
                    "star_period": "2020-11-02",//结算周期开始
                    "send_time":"2020-11-03 23:59:59",//发放时间
                    "send_manage":"管理员1"//发放人
              }
             ]
        } 
}
```

### 21 用户返佣发放
- URL: http://{host_addr}/managers/v1/period/sendrebot
- Method: POST
- Request Body:

```json
{
    "data": {
        "id": 111 //发放数据id
        }
}
```
```json
{
    "ret": 0,
    "msg": "",
     "data": {
        } 
}
```

### 22 用户返佣详情
- URL: http://{host_addr}/managers/v1/period/getuserrebot
- Method: POST
- Request Body:

```json
{
    "data": {
        "sname": "272727",//用户id,手机号，邮箱
        "sagent": "wuueww",//代理id或者名字
        "star":"2020-11-02", //结算开始
        "end":"2020-11-02 23:00:11" //结算结束
        }
}
```
```json
{
    "ret": 0,
    "msg": "",
     "data": {
             "total": 2, // 总条数
             "list": [
                 { 
                    "contributor": 2231 //结算用户id
                    "order_amount": 1122,//交易金额
                    "order_commission":112.1,//交易手续费
                    "rebate_ratio": 52,//返佣点位
                    "reward": 122.1,//返佣金额
                    "pareid": 112,//上级代理id
                    "user_name": "用户",//用户名字
                    "parename": "上级代理名字"//上级代理名字
         }, {
                    "contributor": 2231 //结算用户id
                    "order_amount": 1122,//交易金额
                    "order_commission":112.1,//交易手续费
                    "rebate_ratio": 52,//返佣点位
                    "reward": 122.1,//返佣金额
                    "pareid": 112,//上级代理id
                    "user_name": "用户",//用户名字
                    "parename": "上级代理名字"//上级代理名字
              }
             ]
        } 
}
```

### 23 用户返佣交易记录
- URL: http://{host_addr}/managers/v1/period/getusertrade
- Method: POST
- Request Body:

```json
{
    "data": {
        "user_id": 12221,//用户id
        "star":"2020-11-02", //结算开始
        "end":"2020-11-02 23:00:11", //结算结束
        "pageNo":1,
        "pagesize":10 
        }
}
```
```json
{
    "ret": 0,
    "msg": "",
     "data": {
             "total": 2, // 总条数
             "list": [
                 { 
                        "deal_id": 21333, // 成交编号
                       "user_id": 2231 //用户id
                       "trader_uid": 3234, //交易员id
                       "order_id": 321112 //订单id
                       "follow_position_id": 2323442, //跟随持仓id
                       "position_id": ********, //持仓id
                       "contract_code": "BTCUSDT", //合约
                       "side": "S", // 方向 B买S卖
                       "offset": O, // O: 开仓 C: 平仓
                       "volume": 111, // 持仓张数
                       "price": 125.00, // 成交价
                       "volume":1, //张数
                       "trade_value": 12114.00, // 成交价值
                       "commission": 0.112, // 手续费
                       "lever": 100, // 杠杆
                       "trade_amount": 100, // 成交金额
                       "back_profit": 21.00, // 成交分佣
                       "profit": 224.00, // 实现盈亏
                       "close_profit": 22.0 0, // 平仓盈亏
                       "order_type": 1, //0:   市价单 1：计划单 2：止盈单 4：止损单 5：强平单
                       "limit": 122.00, //止盈价
                       "stop": 325, //止损价
                       "trade_time":"2020-11-02 11:34:12",//成交时间
                       "balance": 11222.12, //账户资产
                       "available": 12412.00, //可用余额
                       "total_profit": 1226, //累计以实现盈亏
                       "broke_price": 222, //破产价格
                       "buy_price": 123, //买入价
                       "sell_price":222 //卖出价
         }, {
                        "deal_id": 21333, // 成交编号
                       "user_id": 2231 //用户id
                       "trader_uid": 3234, //交易员id
                       "order_id": 321112 //订单id
                       "follow_position_id": 2323442, //跟随持仓id
                       "position_id": ********, //持仓id
                       "contract_code": "BTCUSDT", //合约
                       "side": "S", // 方向 B买S卖
                       "offset": O, // O: 开仓 C: 平仓
                       "volume": 111, // 持仓张数
                       "price": 125.00, // 成交价
                       "volume":1, //张数
                       "trade_value": 12114.00, // 成交价值
                       "commission": 0.112, // 手续费
                       "lever": 100, // 杠杆
                       "trade_amount": 100, // 成交金额
                       "back_profit": 21.00, // 成交分佣
                       "profit": 224.00, // 实现盈亏
                       "close_profit": 22.0 0, // 平仓盈亏
                       "order_type": 1, //0:   市价单 1：计划单 2：止盈单 4：止损单 5：强平单
                       "limit": 122.00, //止盈价
                       "stop": 325, //止损价
                       "trade_time":"2020-11-02 11:34:12",//成交时间
                       "balance": 11222.12, //账户资产
                       "available": 12412.00, //可用余额
                       "total_profit": 1226, //累计以实现盈亏
                       "broke_price": 222, //破产价格
                       "buy_price": 123, //买入价
                       "sell_price":222//卖出价
              }
             ]
        } 
}
```

### 24 用户返佣交易记录
- URL: http://{host_addr}/managers/v1/period/getusertrade
- Method: POST
- Request Body:

```json
{
    "data": {
        "user_id": 12221,//用户id
        "star":"2020-11-02", //结算开始
        "end":"2020-11-02 23:00:11", //结算结束
        "pageNo":1,
        "pagesize":10 
        }
}
```
```json
{
    "ret": 0,
    "msg": "",
     "data": {
             "total": 2, // 总条数
             "list": [
                 { 
                        "deal_id": 21333, // 成交编号
                       "user_id": 2231 //用户id
                       "trader_uid": 3234, //交易员id
                       "order_id": 321112 //订单id
                       "follow_position_id": 2323442, //跟随持仓id
                       "position_id": ********, //持仓id
                       "contract_code": "BTCUSDT", //合约
                       "side": "S", // 方向 B买S卖
                       "offset": O, // O: 开仓 C: 平仓
                       "volume": 111, // 持仓张数
                       "price": 125.00, // 成交价
                       "volume":1, //张数
                       "trade_value": 12114.00, // 成交价值
                       "commission": 0.112, // 手续费
                       "lever": 100, // 杠杆
                       "trade_amount": 100, // 成交金额
                       "back_profit": 21.00, // 成交分佣
                       "profit": 224.00, // 实现盈亏
                       "close_profit": 22.0 0, // 平仓盈亏
                       "order_type": 1, //0:   市价单 1：计划单 2：止盈单 4：止损单 5：强平单
                       "limit": 122.00, //止盈价
                       "stop": 325, //止损价
                       "trade_time":"2020-11-02 11:34:12",//成交时间
                       "balance": 11222.12, //账户资产
                       "available": 12412.00, //可用余额
                       "total_profit": 1226, //累计以实现盈亏
                       "broke_price": 222, //破产价格
                       "buy_price": 123, //买入价
                       "sell_price":222 //卖出价
         }, {
                        "deal_id": 21333, // 成交编号
                       "user_id": 2231 //用户id
                       "trader_uid": 3234, //交易员id
                       "order_id": 321112 //订单id
                       "follow_position_id": 2323442, //跟随持仓id
                       "position_id": ********, //持仓id
                       "contract_code": "BTCUSDT", //合约
                       "side": "S", // 方向 B买S卖
                       "offset": O, // O: 开仓 C: 平仓
                       "volume": 111, // 持仓张数
                       "price": 125.00, // 成交价
                       "volume":1, //张数
                       "trade_value": 12114.00, // 成交价值
                       "commission": 0.112, // 手续费
                       "lever": 100, // 杠杆
                       "trade_amount": 100, // 成交金额
                       "back_profit": 21.00, // 成交分佣
                       "profit": 224.00, // 实现盈亏
                       "close_profit": 22.0 0, // 平仓盈亏
                       "order_type": 1, //0:   市价单 1：计划单 2：止盈单 4：止损单 5：强平单
                       "limit": 122.00, //止盈价
                       "stop": 325, //止损价
                       "trade_time":"2020-11-02 11:34:12",//成交时间
                       "balance": 11222.12, //账户资产
                       "available": 12412.00, //可用余额
                       "total_profit": 1226, //累计以实现盈亏
                       "broke_price": 222, //破产价格
                       "buy_price": 123, //买入价
                       "sell_price": 222 //卖出价
              }
             ]
        } 
}
```

### 25 平台财务记录
- URL: http://{host_addr}/managers/v1/platform/getfinacewallet
- Method: POST
- Request Body:

```json
{
    "data": {
        "tdate":"2020-11-02" //查询日期，默认当前
        }
}
```
```json
{
    "ret": 0,
    "msg": "",
     "data": {
        "coinid": 36, //币种id
        "coinname": "usdt", //币种名字
        "walletbalance": 122.1, //钱包资产
        "withdrawlock": 212.1, //提币锁定
        "totalwallet":2216, //钱包总资产
        "accountbalance": 122.1, //合约账户资产
        "accountavailable":1236, //合约可用
        "accountprofit": 1236, //浮动盈亏
        "totalaccount": 2221.1, //合约账户总资产
        "platform_balance": 1136, //平台资产
        "platformliabilities": 216, //平台负债
        "platformc2cmaccout":11, //平台c2c资产
        "platformaccout": 216, //平台合约账户资产
        "platformaccoutporit": 216 //平台合约盈亏资产
        } 

}
```

### 26 平台钱包账户
- URL: http://{host_addr}/managers/v1/period/platwalletlist
- Method: POST
- Request Body:

```json
{
    "data": {

        }
}
```
```json
{
    "ret": 0,
    "msg": "",
     "data": [
                 { 
                        "coin_id": 36, // 币种id
                       "coin_name": "usdt" //币种名字
                       "amount": 3234, //余额
                       "assetaward": 321112 //负债
                       "mix_charge": 11, //最小充币
                       "mixwith": 122, //最小提币
                       "fee": 11.2, //手续费
                       "amountlock": 11.2, //锁定
                       "address": "0xwu1wwwqqewrqwreqwqw" //充币地址
         }, {
                        "coin_id": 36, // 币种id
                       "coin_name": "usdt" //币种名字
                       "amount": 3234, //余额
                       "assetaward": 321112 //负债
                       "mix_charge": 11, //最小充币
                       "mixwith": 122, //最小提币
                       "fee": 11.2, //手续费
                       "amountlock": 11.2, //锁定
                       "address": "0xwu1wwwqqewrqwreqwqw" //充币地址
              }
             ]
        
}
```

### 27 钱包平台流水
- URL: http://{host_addr}/managers/v1/platform/platwalletdaillist
- Method: POST
- Request Body:

```json
{
    "data": {
        "star":"2020-11-02", //开始
        "end":"2020-11-02 23:00:11", //结束
        "pageNo":1,
        "pagesize":10 
        }
}
```
```json
{
    "ret": 0,
    "msg": "",
     "data": {
             "total": 2, // 总条数
             "list": [
                 { 
                       "userid": 2231 //用户id
                       "coinid": 36, //币种id
                       "coinname": "usdt" //币种名字
                       "amount": 2323442, //数量
                       "amountafter": ********, //更新后数量
                       "optype": 1, // 1-交易手续费 4-提币手续费 8-盈亏收入 9-资金费用 100-平台账户充币 200-平台账户提币 300-转入 301-转入到交易账户 302-转入到资产账户 310-转出 311-转出到交易账户 312-转出到资产账户 320-平台提币手续费 330-邀请佣金奖励 331-代理佣金奖励 332-空投 333-C2C结算，334-C2C其他用途 340-模拟盘补领资产
                       "remark": "交易手续费", // 备注
                       "opusername": "管理员", //操作人
                       "sourceid": 1111, //来源id
                       "createat": "2020-12-12 11:11:11", //时间
                       "mark":"交易" //备注
         }, {
                       "userid": 2231 //用户id
                       "coinid": 36, //币种id
                       "coinname": "usdt" //币种名字
                       "amount": 2323442, //数量
                       "amountafter": ********, //更新后数量
                       "optype": 1, // 1-交易手续费 4-提币手续费 8-盈亏收入 9-资金费用 100-平台账户充币 200-平台账户提币 300-转入 301-转入到交易账户 302-转入到资产账户 310-转出 311-转出到交易账户 312-转出到资产账户 320-平台提币手续费 330-邀请佣金奖励 331-代理佣金奖励 332-空投 333-C2C结算，334-C2C其他用途 340-模拟盘补领资产
                       "remark": "交易手续费", // 备注
                       "opusername": "管理员", //操作人
                       "sourceid": 1111, //来源id
                       "createat": "2020-12-12 11:11:11", //时间
                       "mark":"交易" //备注
              }
             ]
        } 
}
```

### 28 平台合约账户
- URL: http://{host_addr}/managers/v1/period/getplataccount
- Method: POST
- Request Body:

```json
{
    "data": {

        }
}
```
```json
{
    "ret": 0,
    "msg": "",
     "data": [
                 { 
                        "coin_id": 36, // 币种id
                       "coin_name": "usdt" //币种名字
                       "amount": 3234, //余额
                       "assetaward": 321112 //负债
                       "mix_charge": 11, //最小充币
                       "mixwith": 122, //最小提币
                       "fee": 11.2, //手续费
                       "amountlock": 11.2, //锁定
                       "address": "0xwu1wwwqqewrqwreqwqw" //充币地址
         }, {
                        "coin_id": 36, // 币种id
                       "coin_name": "usdt" //币种名字
                       "amount": 3234, //余额
                       "assetaward": 321112 //负债
                       "mix_charge": 11, //最小充币
                       "mixwith": 122, //最小提币
                       "fee": 11.2, //手续费
                       "amountlock": 11.2, //锁定
                       "address": "0xwu1wwwqqewrqwreqwqw" //充币地址
              }
             ]
        
}
```

### 29 平台合约账户流水
- URL: http://{host_addr}/managers/v1/platform/plataccountdail
- Method: POST
- Request Body:

```json
{
    "data": {
        "star":"2020-11-02", //开始
        "end":"2020-11-02 23:00:11", //结束
        "pageNo":1,
        "pagesize":10 
        }
}
```
```json
{
    "ret": 0,
    "msg": "",
     "data": {
             "total": 2, // 总条数
             "list": [
                 { 
                       "userid": 2231 //用户id
                       "coinid": 36, //币种id
                       "coinname": "usdt" //币种名字
                       "amount": 2323442, //数量
                       "amountafter": ********, //更新后数量
                       "optype": 1, // 操作类型id,1盈利结算
                       "remark": "交易手续费", // 备注
                       "opusername": "管理员", //操作人
                       "sourceid": 1111, //来源id
                       "createat": "2020-12-12 11:11:11", //时间
                       "mark":"交易" //备注
         }, {
                       "userid": 2231 //用户id
                       "coinid": 36, //币种id
                       "coinname": "usdt" //币种名字
                       "amount": 2323442, //数量
                       "amountafter": ********, //更新后数量
                       "optype": 1, // 操作类型id,1盈利结算
                       "remark": "交易手续费", // 备注
                       "opusername": "管理员", //操作人
                       "sourceid": 1111, //来源id
                       "createat": "2020-12-12 11:11:11", //时间
                       "mark":"交易" //备注
              }
             ]
        } 
}
```

### 30 获取标签列表
- URL: http://{host_addr}/managers/v1/risk/getlablinfo
- Method: POST
- Request Body:

```json
{
    "data": {
        "pageNo":1,
        "pagesize":10 
        }
}
```
```json
{
    "ret": 0,
    "msg": "",
     "data": {
             "total": 2, // 总条数
             "list": [
                 { 
                       "id": 2231 //数据id
                       "label_id": 16, //标签id
                       "contract_code": "btc/usdt" //合约代码
                       "max_lever": 100, //最大杠杆
                       "max_order_volume": 11, //最大下单
                       "min_order_volume": 1, // 最小下单
                       "max_posi_volume": 112, // 最大持仓
                       "fee": 11, //手续费加点
                       "funding": 11, //资金费用加点
                       "slippage": 11, //滑点
                       "creat_time":"2020-12-01 09:11:21", //创建时间
                       "status_stype":1, //1开启 0关闭
                       "risk_rate":11, //风险率加点
                       "label_name":"标签1" //标签名字

         }, {
                       "id": 2231 //数据id
                       "label_id": 16, //标签id
                       "contract_code": "btc/usdt" //合约代码
                       "max_lever": 100, //最大杠杆
                       "max_order_volume": 11, //最大下单
                       "min_order_volume": 1, // 最小下单
                       "max_posi_volume": 112, // 最大持仓
                       "fee": 11, //手续费加点
                       "funding": 11, //资金费用加点
                       "slippage": 11, //滑点
                       "creat_time":"2020-12-01 09:11:21", //创建时间
                       "status_stype":1, //1开启 0关闭
                       "risk_rate":11, //风险率加点
                       "label_name":"标签1" //标签名字
              }
             ]
        } 
}
```


### 31 添加主标签
- URL: http://{host_addr}/managers/v1/risk/addlabl
- Method: POST
- Request Body:

```json
{
    "data": {
        "lablname":"标签1" //主标签 
        }
}
```
```json
{
    "ret": 0,
    "msg": "",
     "data": {     } 
}
```

### 32 添加子标签
- URL: http://{host_addr}/managers/v1/risk/addlablinfo
- Method: POST
- Request Body:

```json
{
  "data": {
           "label_id":1, //主标签 id
           "contract_code": "btc/usdt" //合约代码
           "max_lever": 100, //最大杠杆
           "max_order_volume": 11, //最大下单
           "min_order_volume": 1, // 最小下单
           "max_posi_volume": 112, // 最大持仓
           "fee": 11, //手续费加点
           "funding": 11, //资金费用加点
           "slippage": 11 //滑点
        }
}
```
```json
{
    "ret": 0,
    "msg": "",
     "data": {} 
}
```

### 33 修改子标签
- URL: http://{host_addr}/managers/v1/risk/updatelablinfo
- Method: POST
- Request Body:

```json
{
  "data": {
           "id":11 , //子标签id 
           "contract_code": "btc/usdt" //合约代码
           "max_lever": 100, //最大杠杆
           "max_order_volume": 11, //最大下单
           "min_order_volume": 1, // 最小下单
           "max_posi_volume": 112, // 最大持仓
           "fee": 11, //手续费加点
           "funding": 11, //资金费用加点
           "slippage": 11, //滑点
           "status_stype": 1 //1 开启 0 关闭

        }
}
```
```json
{
    "ret": 0,
    "msg": "",
     "data": {} 
}
```

### 34 获取管理员log
- URL: http://{host_addr}/managers/v1/manager/getmanagelogs
- Method: POST
- Request Body:

```json
{
    "data": {
        "uid":"11", //用户id
        "star":"2020-09-11",
        "end":"2020-09-11 23:59:59",
        "pageNo":1,
        "pagesize":10 
        }
}
```
```json
{
    "ret": 0,
    "msg": "",
     "data": {
             "total": 2, // 总条数
             "list": [
                 { 
                       "id": 2231 //数据id
                       "stype": 1, //1 修改标签，2返佣比例，3修改备注，4添加备注，5添加标签，6修改标签，7身份审核，8审核提币
                       "user_id": 111, //被操作用户id
                       "manage": "管理员", //操作员
                       "conten": "hsjkak", //操作内容参数
                       "creat_time":"2020-12-01 09:11:21" //创建时间

         }, {
                       "id": 2231 //数据id
                       "stype": 1, //1 修改标签，2返佣比例，3修改备注，4添加备注，5添加标签，6修改标签，7身份审核，8审核提币
                       "user_id": 111, //被操作用户id
                       "manage": "管理员", //操作员
                       "conten": "hsjkak", //操作内容参数
                       "creat_time":"2020-12-01 09:11:21" //创建时间
              }
             ]
        } 
}
```

### 35 获取合约列表
- URL: http://{host_addr}/managers/v1/comm/bcprocontractset
- Method: POST
- Request Body:

```json
{
    "data": {
        }
}
```
```json
{
    "ret": 0,
    "msg": "",
     "data": [
                 { 
                       "contract_id": 2231 //合约id
                       "contract_code": "btc/usdt", //合约代码
                       "is_show": 1 //显示0 不显示

         }, {
                       "contract_id": 2231 //合约id
                       "contract_code": "btc/usdt", //合约代码
                       "is_show": 1 //显示0 不显示
              }
             ]

}
```
### 36 获取币种列表
- URL: http://{host_addr}/managers/v1/comm/getprocoinList
- Method: POST
- Request Body:

```json
{
    "data": {
        }
}
```
```json
{
    "ret": 0,
    "msg": "",
     "data": [
                 { 
                       "currencyid": 2231 //币种id
                       "currencyname": "usdt", //币种名字
                       "status": 1 //0：下架 1：正常 

         }, {
                       "currencyid": 2231 //币种id
                       "currencyname": "usdt", //币种名字
                       "status": 1 //0：下架 1：正常 
              }
             ]

}
```

### 37 修改代理状态
- URL: http://{host_addr}/managers/v1/user/upagentstatus
- Method: POST
- Request Body:

```json
{
    "data": {
        "agentstatus": 1, // 1正常 3注销
        "user_id": 1233112 //用户id
     
    }
}
```
- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": {
        } 
}
```

### 38 谷歌验证码验证
- URL: http://{host_addr}/managers/v1/comm/commckeckvkey
- Method: POST
- Request Body:

```json
{
    "data": {
        "code": "122331", // 谷歌验证码
        "vkey": "eeew" //谷歌密钥 默认不传
     
    }
}
```
- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": {
        } 
}
```


### 39 交易频次
- URL: http://{host_addr}/managers/v1/trade/tradefrequency
- Method: POST
- Request Body:

```json
{
    "data": {
        "sname": "272727",//用户id,手机号，邮箱
        "sectop": "ywywy",//顶级代理id或昵称
        "sagent": "wuueww",//代理id或者名字
        "contract_code": "BTCUSDT",
        "star":"2020-11-02", //开始
        "end":"2020-11-02 23:00:11", //结束
        "pageNo":1,
        "pagesize":10 
        }
}
```
```json
{
    "ret": 0,
    "msg": "",
     "data": {
             "total": 2, // 总条数
             "list": [
                 { 
                       "userid": 2231 //用户id
                       "contractcode": "BTCUSDT", //合约
                        "top_agent_id": 1122,//顶级代理id
                        "petname": "uwuie",//顶级昵称
                        "pareid": 112,//上级代理id
                        "parename": "代理1",//上级代理名字
                        "user_name": "用户",//用户名字
                        "ip_address": "************",//ip
                        "frequency": 112, //交易次数
                         "opening": 112, //开仓
                         "closeing": 112, //平仓次数
          
         }, {
                        "userid": 2231 //用户id
                        "contractcode": "BTCUSDT", //合约
                         "top_agent_id": 1122,//顶级代理id
                         "petname": "uwuie",//顶级昵称
                         "pareid": 112,//上级代理id
                         "parename": "代理1",//上级代理名字
                         "user_name": "用户",//用户名字
                         "ip_address": "************",//ip
                         "frequency": 112, //交易次数
                          "opening": 112, //开仓
                          "closeing": 112, //平仓次数
              }
             ]
        } 
}
```


### 40  交易频次详情
- URL: http://{host_addr}/managers/v1/trade/tradefrequencview
- Method: POST
- Request Body:

```json
{
    "data": {
        "uid":"19299202",
        "contract_code": "BTCUSDT",
        "star":"2020-11-02", //开始
        "end":"2020-11-02 23:00:11", //结束
        "pageNo":1,
        "pagesize":10 
        }
}
```
```json
{
    "ret": 0,
    "msg": "",
     "data": {
             "total": 2, // 总条数
             "list": [
                 { 
                      "tradeid": 21333, // 成交编号
                        "user_id": 2231 //用户id
                        "trader_uid": 3234, //交易员id
                        "order_id": 321112 //订单id
                        "follow_position_id": 2323442, //跟随持仓id
                        "position_id": ********, //持仓id
                        "contract_code": "BTCUSDT", //合约
                        "side": "S", // 方向 B买S卖
                        "kai": O, // O: 开仓 C: 平仓
                        "volume": 111, // 持仓张数
                        "price": 125.00, // 成交价
                        "volume":1, //张数
                        "trade_value": 12114.00, // 成交价值
                        "commission": 0.112, // 手续费
                        "lever": 100, // 杠杆
                        "trade_amount": 100, // 成交金额
                        "back_profit": 21.00, // 成交分佣
                        "profit": 224.00, // 实现盈亏
                        "close_profit": 22.0 0, // pnl
                        "order_type": 1, //0:   市价单 1：计划单 2：止盈单 4：止损单 5：强平单
                        "limit": 122.00, //止盈价
                        "stop": 325, //止损价
                        "trade_time":"2020-11-02 11:34:12",//成交时间
                        "balance": 11222.12, //账户资产
                        "available": 12412.00, //可用余额
                        "total_profit": 1226, //累计以实现盈亏
                        "broke_price": 222, //破产价格
                        "buy_price": 123, //买入价
                        "sell_price":222, //卖出价
                        "profitpnl": 22.0 0, // 净pnl
          
         }, {
                       "tradeid": 21333, // 成交编号
                         "user_id": 2231 //用户id
                         "trader_uid": 3234, //交易员id
                         "order_id": 321112 //订单id
                         "follow_position_id": 2323442, //跟随持仓id
                         "position_id": ********, //持仓id
                         "contract_code": "BTCUSDT", //合约
                         "side": "S", // 方向 B买S卖
                         "offset": O, // O: 开仓 C: 平仓
                         "volume": 111, // 持仓张数
                         "price": 125.00, // 成交价
                         "volume":1, //张数
                         "trade_value": 12114.00, // 成交价值
                         "commission": 0.112, // 手续费
                         "lever": 100, // 杠杆
                         "trade_amount": 100, // 成交金额
                         "back_profit": 21.00, // 成交分佣
                         "profit": 224.00, // 实现盈亏
                         "close_profit": 22.0 0, // pnl
                         "order_type": 1, //0:   市价单 1：计划单 2：止盈单 4：止损单 5：强平单
                         "limit": 122.00, //止盈价
                         "stop": 325, //止损价
                         "trade_time":"2020-11-02 11:34:12",//成交时间
                         "balance": 11222.12, //账户资产
                         "available": 12412.00, //可用余额
                         "total_profit": 1226, //累计以实现盈亏
                         "broke_price": 222, //破产价格
                         "buy_price": 123, //买入价
                         "sell_price":222, //卖出价
                         "profitpnl": 22.0 0, // 净pnl

              }
             ]
        } 
}
```


### 41 用户提币初审审核
- URL: http://{host_addr}/managers/v1/account/firstwithdrawcheck
- Method: POST
- Request Body:

```json
{
    "data": {
        "id": 272727,//数据id
        "pass": true,//true 通过 false 驳回
        "content": "uuwuwo2"//备注
        }
    status:7初审通过，8初审拒绝
}
```

```json
{
    "ret": 0,
    "msg": "",
     "data": {
            } 
}
```


### 42  联系我们列表
- URL: http://{host_addr}/managers/v1/notice/getcontactuslist
- Method: POST
- Request Body:

```json
{
    "data": {
        }
}
```
```json
{
    "ret": 0,
    "msg": "",
     "data": {
             "total": 2, // 总条数
             "list": [
                 { 
                        "id": 21333, // 数据id
                        "mobile": "*********", //手机号
                        "relname": "四十多", //交易员id
                        "email": "<EMAIL>" //邮箱
                        "content": "内容", //备注
                        "creat_time":"2020-11-02 11:34:12",//提交时间
          
         }, {
                        "id": 21333, // 数据id
                        "mobile": "*********", //手机号
                        "relname": "四十多", //交易员id
                        "email": "<EMAIL>" //邮箱
                        "content": "内容", //备注
                        "creat_time":"2020-11-02 11:34:12",//提交时间

              }
             ]
        } 
}
```