<template>
  <div class="financial-container">
    <el-tabs v-model="tabActive" @tab-click="handleTab">
      <el-tab-pane :label="$t('others.wallet_account_financial_record')" name="first"></el-tab-pane>
      <el-tab-pane :label="$t('menus.Spot_financial_records')" name="second"></el-tab-pane>
      <el-tab-pane :label="$t('menus.USDT_contract_financial_records')" name="third"></el-tab-pane>
      <el-tab-pane :label="$t('menus.USD_contract_financial_records')" name="fourth"></el-tab-pane>
      <el-tab-pane :label="$t('others.documentary_account_financial_record')" name="fifth"></el-tab-pane>
    </el-tabs>
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.uid"
        v-if="tabActive == 'second'"
        :placeholder="$t('tableHeader.uid')"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        size="mini"
        v-if="tabActive !== 'second'"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        v-if="tabActive !== 'second'"
        size="mini"
        :placeholder="$t('filters.topIDNick')"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        v-if="tabActive !== 'second'"
        size="mini"
        :placeholder="$t('filters.agent')"
        style="width: 150px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-if="tabActive !== 'second'"
        v-model="listQuery.coinid"
        :placeholder="$t('filters.currency')"
        clearable
        style="width: 120px; margin-right: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in coinOptions"
          :key="item.currencyid"
          :label="item.currencyname"
          :value="item.currencyid"
        />
      </el-select>
      <el-select
        size="mini"
        v-if="tabActive == 'second'"
        v-model="listQuery.coinid"
        :placeholder="$t('filters.currency')"
        clearable
        style="width: 120px; margin-right: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in coinOptions"
          :key="item.currencyid"
          :label="item.currencyname"
          :value="item.currencyname"
        />
      </el-select>
      <el-select
        v-if="tabActive == 'first'"
        size="mini"
        v-model="listQuery.stype"
        :placeholder="$t('filters.orderType')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in stypeOptions1"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
       <el-select
         v-else-if="tabActive == 'third' || tabActive == 'fourth'"
        size="mini"
        v-model="listQuery.stype"
        :placeholder="$t('filters.orderType')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in stypeOptions34"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-select
        v-else-if="tabActive == 'second'"
        size="mini"
        v-model="listQuery.stype"
        :placeholder="$t('filters.orderType')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in stypeOptions2"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-select
        v-else-if="tabActive == 'fifth'"
        size="mini"
        v-model="listQuery.stype"
        :placeholder="$t('filters.orderType')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in stypeOptions5"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      
      <span style="margin-right: 20px; font-size: 12px">{{$t('tableHeader.clinchDealTime')}}</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px; margin-right: 20px; "
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change="filterTimeTransform"
      />
      <el-input
        v-if="tabActive == 'first' || tabActive == 'second'"
        v-model="listQuery.bill_id"
        size="mini"
        :placeholder="$t('tableHeader.clinchDealRecord')"
        style="width: 150px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
    </div>

    <!-- 钱包账户财务记录 -->
    <el-table
      v-if="tabActive == 'first'"
      v-loading="listLoading"
      :data="financiaList"
      border
      fit
      size="mini"
      highlight-current-row
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="userid" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.userid || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.user_name || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topID')" prop="top_agent_id" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.top_agent_id == 0 ? '--' : JSON.stringify(row.top_agent_id) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topNick')" prop="petname" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorID')" prop="pareid" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.pareid == 0 ? '--' : JSON.stringify(row.pareid) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" prop="parename" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.currency')" prop="currencyname" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.currencyname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.amount')" prop="amount" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ JSON.stringify(row.amount) || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.walletAccountBalance')" prop="balance" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ JSON.stringify(row.balance) || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('others.freezeNum')" prop="lockamount" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ JSON.stringify(row.lockamount) || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.orderType')" prop="type" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>
            {{
              row.type == 1 ?  $t('buttons.chargeMoney') :
              row.type == 2 ?  $t('filters.Withdraw') :
              row.type == 4 ?  $t('filters.Transfer_to_trading_account') :
              row.type == 8 ?  $t('filters.Transfer_to_assets_account') :
              row.type == 16 ?  $t('filters.Invitation_commission_Award'):
              row.type == 32 ?  $t('filters.Agency_commission_Award') :
              row.type == 64 ?  $t('filters.Fiat_order_receipt') :
              row.type == 128 ?  $t('filters.Airdrop_rewards') :
              row.type == 256 ?  $t('filters.Transfer_from_asset_account_to_documentary_account'):
              row.type == 512 ?  $t('filters.Transfer_from_documentary_account_to_asset_account') :
              row.type == 1024 ?  $t('filters.Commission_income') :
              row.type == 2048 ?  $t('filters.Activities_to_reward') :
              row.type == 4096 ?  $t('filters.Fiat_sold') :
              row.type == 8192 ?  $t('filters.Deduction_of_abnormal_assets') :
              row.type == 16384 ?  $t('filters.Currency_exchange_exchange_out') : 
              row.type == 32768 ?  $t('filters.Currency_exchange_exchange') :
              row.type == 65536 ?  $t('filters.Currency_exchange_exchange_and_refund') :
              row.type == 131072 ?  $t('filters.The_asset_account_is_transferred_to_the_USD_contract_account') :
              row.type == 262144 ?  $t('filters.Transfer_USD_contract_account_to_asset_account') :
              row.type == 524288 ?  $t('filters.The_asset_account_is_transferred_to_the_spot_trading_account') :
              row.type == 1048576 ?  $t('filters.Transfer_from_spot_trading_account_to_asset_account') : '--'
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.clinchDealSerial')" prop="billid" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ JSON.stringify(row.billid) || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.time')" prop="createdtime" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.createdtime || '--' }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 现货财务记录 -->
    <el-table
      v-if="tabActive == 'second'"
      v-loading="listLoading"
      :data="financiaList"
      border
      fit
      size="mini"
      highlight-current-row
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="userid" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.userid || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.user_name || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topID')" prop="top_agent_id" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.top_agent_id == 0 ? '--' : JSON.stringify(row.top_agent_id) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topNick')" prop="petname" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorID')" prop="pareid" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.pareid == 0 ? '--' : JSON.stringify(row.pareid) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" prop="parename" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.currency')" prop="currency_name" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.currency_name || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.amount')" prop="balance" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ JSON.stringify(row.balance) || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.Spot_account_balance')" prop="available" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ JSON.stringify(row.available) || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.Spot_account_freeze')" prop="balance,available" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ Number(row.balance) - Number(row.available) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.orderType')" prop="type" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>
            {{
              row.type == 1 ? $t('filters.Transaction_Fees_Taker'):
              row.type == 2 ? $t('filters.Transfer_in_from_asset_account'):
              row.type == 4 ? $t('filters.Transfer_out_to_the_asset_account'):
              row.type == 8 ? $t('filters.Transaction_transfer_out'):
              row.type == 16 ? $t('filters.Transaction_transfer_in'):
              row.type == 32 ? $t('filters.Transaction_Fees_Maker') : '--'
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.clinchDealSerial')" prop="id" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.id || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.time')" prop="created_time" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.created_time || '--' }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- USDT合约财务记录 -->
    <el-table
      v-if="tabActive == 'third'"
      v-loading="listLoading"
      :data="financiaList"
      border
      fit
      size="mini"
      highlight-current-row
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="userid" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.userid || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.user_name || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topID')" prop="top_agent_id" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.top_agent_id == 0 ? '--' : JSON.stringify(row.top_agent_id) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topNick')" prop="petname" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorID')" prop="pareid" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.pareid == 0 ? '--' : JSON.stringify(row.pareid) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" prop="parename" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.currency')" prop="currencyname" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.currencyname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.amount')" prop="amount" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ JSON.stringify(row.amount) || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.contract_account_balance')" prop="balance" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ JSON.stringify(row.balance) || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.availableNum')" prop="available" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ JSON.stringify(row.available) || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.orderType')" prop="type" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>
            {{
              row.type == 1 ? $t('filters.Opening_charge') :
              row.type == 2 ? $t('tableHeader.moneyCost') :
              row.type == 4 ? $t('filters.Into') :
              row.type == 8 ? $t('filters.Roll_out') :
              row.type == 16 ? $t('filters.Unwinding_of_profit_and_loss') :
              row.type == 32 ? $t('filters.Closing_charge') :
              row.type == 64 ? $t('filters.Analog_disk_supplement_assets') :
              row.type == 128 ? $t('filters.Adjustment_margin') :
              row.type == 256 ? $t('filters.Withhold_commissions') :
              row.type == 512 ? $t('filters.Commission_refund') :
              row.type == 1024 ? $t('filters.Commission_income') :
              row.type == 2048 ? $t('filters.Transfer_from_trading_account_to_documentary_account') :
              row.type == 4096 ? $t('filters.Transfer_from_documentary_account_to_trading_account') :
              row.type == 8192 ? $t('filters.Transfer_from_asset_account_to_documentary_account') :
              row.type == 16384 ? $t('filters.Transfer_from_documentary_account_to_asset_account') :
              row.type == 36768 ? $t('filters.Strong_flat_back') :
              row.type == 65536 ? $t('filters.Analog_disk_reduces_assets') :
              row.type == 131072 ? $t('filters.Liquidation_cost') :
              row.type == 262144 ? $t('filters.Deduction_of_abnormal_assets') :
              row.type == 524288 ? $t('filters.Margin_increase') :
              row.type == 1048576 ? $t('filters.Give_gold_to_receive') :
              row.type == 2097152 ? $t('filters.Give_gold_failure') :
              row.type == 4194304 ? $t('filters.Give_gold_recovery') : '--'
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.time')" prop="createdtime" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.createdtime || '--' }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 币本位合约财务记录 -->
    <el-table
      v-if="tabActive == 'fourth'"
      v-loading="listLoading"
      :data="financiaList"
      border
      fit
      size="mini"
      highlight-current-row
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="userid" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.userid || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.user_name || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topID')" prop="top_agent_id" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.top_agent_id == 0 ? '--' : JSON.stringify(row.top_agent_id) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topNick')" prop="petname" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorID')" prop="pareid" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.pareid == 0 ? '--' : JSON.stringify(row.pareid) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" prop="parename" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.currency')" prop="currencyname" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.currencyname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.amount')" prop="amount" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ JSON.stringify(row.amount) || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.contract_account_balance')" prop="balance" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ JSON.stringify(row.balance) || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.availableNum')" prop="available" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ JSON.stringify(row.available) || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.orderType')" prop="type" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>
            {{
              row.type == 1 ? $t('filters.Opening_charge') :
              row.type == 2 ? $t('tableHeader.moneyCost') :
              row.type == 4 ? $t('filters.Into') :
              row.type == 8 ? $t('filters.Roll_out') :
              row.type == 16 ? $t('filters.Unwinding_of_profit_and_loss') :
              row.type == 32 ? $t('filters.Closing_charge') :
              row.type == 64 ? $t('filters.Analog_disk_supplement_assets') :
              row.type == 128 ? $t('filters.Adjustment_margin') :
              row.type == 256 ? $t('filters.Withhold_commissions') :
              row.type == 512 ? $t('filters.Commission_refund') :
              row.type == 1024 ? $t('filters.Commission_income') :
              row.type == 2048 ? $t('filters.Transfer_from_trading_account_to_documentary_account') :
              row.type == 4096 ? $t('filters.Transfer_from_documentary_account_to_trading_account') :
              row.type == 8192 ? $t('filters.Transfer_from_asset_account_to_documentary_account') :
              row.type == 16384 ? $t('filters.Transfer_from_documentary_account_to_asset_account') :
              row.type == 36768 ? $t('filters.Strong_flat_back') :
              row.type == 65536 ? $t('filters.Analog_disk_reduces_assets') :
              row.type == 131072 ? $t('filters.Liquidation_cost') :
              row.type == 262144 ? $t('filters.Deduction_of_abnormal_assets') :
              row.type == 524288 ? $t('filters.Margin_increase') :
              row.type == 1048576 ? $t('filters.Give_gold_to_receive') :
              row.type == 2097152 ? $t('filters.Give_gold_failure') :
              row.type == 4194304 ? $t('filters.Give_gold_recovery') : '--'
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.availableNum')" prop="createdtime" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.createdtime || '--' }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 跟单账户财务记录 -->
    <el-table
      v-if="tabActive == 'fifth'"
      v-loading="listLoading"
      :data="financiaList"
      border
      fit
      size="mini"
      highlight-current-row
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="userid" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.userid || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.user_name || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topID')" prop="top_agent_id" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.top_agent_id == 0 ? '--' : JSON.stringify(row.top_agent_id) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topNick')" prop="petname" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorID')" prop="pareid" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.pareid == 0 ? '--' : JSON.stringify(row.pareid) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" prop="parename" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.currency')" prop="currencyname" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.currencyname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.amount')" prop="amount" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ JSON.stringify(row.amount) || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.availableNum')" prop="available" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ JSON.stringify(row.available) || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.balance_documentary_account')" prop="balance" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ JSON.stringify(row.balance) || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.orderType')" prop="type" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>
            {{
              row.type == 1 ? $t('filters.Opening_charge') :
              row.type == 2 ? $t('tableHeader.moneyCost') :
              row.type == 4 ? $t('filters.Into') :
              row.type == 8 ? $t('filters.Roll_out') :
              row.type == 16 ? $t('filters.Unwinding_of_profit_and_loss') :
              row.type == 32 ? $t('filters.Closing_charge') :
              row.type == 64 ? $t('filters.Analog_disk_supplement_assets') :
              row.type == 128 ? $t('filters.Adjustment_margin') :
              row.type == 256 ? $t('filters.Withhold_commissions') :
              row.type == 512 ? $t('filters.Commission_refund') :
              row.type == 1024 ? $t('filters.Commission_income') :
              row.type == 2048 ? $t('filters.Transfer_from_trading_account_to_documentary_account') :
              row.type == 4096 ? $t('filters.Transfer_from_documentary_account_to_trading_account') :
              row.type == 8192 ? $t('filters.Transfer_from_asset_account_to_documentary_account') :
              row.type == 16384 ? $t('filters.Transfer_from_documentary_account_to_asset_account') :
              row.type == 36768 ? $t('filters.Strong_flat_back') :
              row.type == 65536 ? $t('filters.Analog_disk_reduces_assets') :
              row.type == 131072 ? $t('filters.Liquidation_cost') : '--'
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.time')" prop="createdtime" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.createdtime || '--' }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,50,100,200,300]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getwallerhistory, getacchistory, getusdacchistory, followhistory } from "@/api/fundQuery";
import { getprocoinList } from "@/api/user";
import { useraccounthis } from "@/api/spotTrading";
export default {
  name: "financialrecords",
  data() {
    return {
      tabActive: 'first',
      listLoading: false,
      total: 0,
      financiaList: null,
      filterTime: [],
      listQuery: {
        uid: "",  // uid
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        coinid: null, //币种id -1全部
        star: "", //开始
        end: "", //结束
        stype: undefined, //1：充值  2：提现  4：划转到交易账户 8：从交易账户转入 16-邀请佣金奖励 32-代理佣金奖励 64-法币订单到账,128 空投 256-资产账户划转到跟单账户  512-跟单账户划转到资产账户  1024:佣金收入
        bill_id: null, //成交编号
        pageNo: 1,
        pagesize: 10,
      },
      coinOptions: [],
      // 钱包财务记录
      stypeOptions1: [
        { key: 1, name: this.$t('buttons.chargeMoney') },
        { key: 2, name: this.$t('filters.Withdraw') },
        { key: 4, name: this.$t('filters.Transfer_to_trading_account') },
        { key: 8, name: this.$t('filters.Transfer_to_assets_account') },
        { key: 16, name: this.$t('filters.Invitation_commission_Award')},
        { key: 32, name: this.$t('filters.Agency_commission_Award') },
        { key: 64, name: this.$t('filters.Fiat_order_receipt') },
        { key: 128, name: this.$t('filters.Airdrop_rewards') },
        { key: 256, name: this.$t('filters.Transfer_from_asset_account_to_documentary_account')},
        { key: 512, name: this.$t('filters.Transfer_from_documentary_account_to_asset_account') },
        { key: 1024, name: this.$t('filters.Commission_income') },
        { key: 2048, name: this.$t('filters.Activities_to_reward') },
        { key: 4096, name: this.$t('filters.Fiat_sold') },
        { key: 8192, name: this.$t('filters.Deduction_of_abnormal_assets') },
        { key: 16384, name: this.$t('filters.Currency_exchange_exchange_out') },
        { key: 32768, name: this.$t('filters.Currency_exchange_exchange') },
        { key: 65536, name: this.$t('filters.Currency_exchange_exchange_and_refund') },
        { key: 131072, name: this.$t('filters.The_asset_account_is_transferred_to_the_USD_contract_account') },
        { key: 262144, name: this.$t('filters.Transfer_USD_contract_account_to_asset_account') },
        { key: 524288, name: this.$t('filters.The_asset_account_is_transferred_to_the_spot_trading_account') },
        { key: 1048576, name: this.$t('filters.Transfer_from_spot_trading_account_to_asset_account') },
      ],
      // USDT/USD财务记录
      stypeOptions34: [
        { key: 1, name:this.$t('filters.Opening_charge') },
        { key: 2, name:this.$t('tableHeader.moneyCost') },
        { key: 4, name:this.$t('filters.Into') },
        { key: 8, name:this.$t('filters.Roll_out') },
        { key: 16, name:this.$t('filters.Unwinding_of_profit_and_loss') },
        { key: 32, name:this.$t('filters.Closing_charge') },
        { key: 64, name:this.$t('filters.Analog_disk_supplement_assets') },
        { key: 128, name:this.$t('filters.Adjustment_margin') },
        { key: 256, name:this.$t('filters.Withhold_commissions') },
        { key: 512, name:this.$t('filters.Commission_refund') },
        { key: 1024, name:this.$t('filters.Commission_income') },
        { key: 2048, name:this.$t('filters.Transfer_from_trading_account_to_documentary_account') },
        { key: 4096, name:this.$t('filters.Transfer_from_documentary_account_to_trading_account') },
        { key: 8192, name:this.$t('filters.Transfer_from_asset_account_to_documentary_account') },
        { key: 16384, name:this.$t('filters.Transfer_from_documentary_account_to_asset_account') },
        { key: 36768, name:this.$t('filters.Strong_flat_back') },
        { key: 65536, name:this.$t('filters.Analog_disk_reduces_assets') },
        { key: 131072, name:this.$t('filters.Liquidation_cost') },
        { key: 262144, name: this.$t('filters.Deduction_of_abnormal_assets') },
        { key: 524288, name: this.$t('filters.Margin_increase') },
        { key: 1048576, name: this.$t('filters.Give_gold_to_receive') },
        { key: 2097152, name: this.$t('filters.Give_gold_failure') },
        { key: 4194304, name: this.$t('filters.Give_gold_recovery') },
      ],
      // 跟单账户财务记录
      stypeOptions5: [
        { key: 1, name:this.$t('filters.Opening_charge') },
        { key: 2, name:this.$t('tableHeader.moneyCost') },
        { key: 4, name:this.$t('filters.Into') },
        { key: 8, name:this.$t('filters.Roll_out') },
        { key: 16, name:this.$t('filters.Unwinding_of_profit_and_loss') },
        { key: 32, name:this.$t('filters.Closing_charge') },
        { key: 64, name:this.$t('filters.Analog_disk_supplement_assets') },
        { key: 128, name:this.$t('filters.Adjustment_margin') },
        { key: 256, name:this.$t('filters.Withhold_commissions') },
        { key: 512, name:this.$t('filters.Commission_refund') },
        { key: 1024, name:this.$t('filters.Commission_income') },
        { key: 2048, name:this.$t('filters.Transfer_from_trading_account_to_documentary_account') },
        { key: 4096, name:this.$t('filters.Transfer_from_documentary_account_to_trading_account') },
        { key: 8192, name:this.$t('filters.Transfer_from_asset_account_to_documentary_account') },
        { key: 16384, name:this.$t('filters.Transfer_from_documentary_account_to_asset_account') },
        { key: 36768, name:this.$t('filters.Strong_flat_back') },
        { key: 65536, name:this.$t('filters.Analog_disk_reduces_assets') },
        { key: 131072, name:this.$t('filters.Liquidation_cost') },
      ],
      // 现货财务记录
      stypeOptions2: [
        { key: 1, name: this.$t('filters.Transaction_Fees_Taker') },
        { key: 2, name: this.$t('filters.Transfer_in_from_asset_account') },
        { key: 4, name: this.$t('filters.Transfer_out_to_the_asset_account') },
        { key: 8, name: this.$t('filters.Transaction_transfer_out') },
        { key: 16, name: this.$t('filters.Transaction_transfer_in') },
        { key: 32, name: this.$t('filters.Transaction_Fees_Maker') },
      ]
    }
  },
  components: {},
  computed: {},
  mounted() {
    getprocoinList({}).then((res)=>{
      this.coinOptions = res.data.filter(v=>v.status)
    })
    this.getList();
  },
  methods: {
    getList() {
      this.listLoading = true;
      let data = {}
      // 钱包账户财务记录
      if (this.tabActive == 'first') {
        Object.assign(data,this.listQuery)
        data.coinid = data.coinid || -1
        data.bill_id = data.bill_id == '' ? null : JSON.parse(data.bill_id)
        data.stype = data.stype || undefined
        getwallerhistory(data).then((response) => {
          this.financiaList = response.data.list;
          this.total = response.data.total;
          this.listLoading = false;
        });
      } else if (this.tabActive == 'second') {
        // 现货财务记录
        Object.assign(data)
        data.uid = this.listQuery.uid
        data.order_type = this.listQuery.stype || 0
        data.coinname = this.listQuery.coinid
        data.billid = this.listQuery.bill_id == '' ? null : JSON.parse(this.listQuery.bill_id)
        data.star = this.listQuery.star
        data.end = this.listQuery.end
        data.pageNo = this.listQuery.pageNo
        data.pagesize = this.listQuery.pagesize
        useraccounthis(data).then((response) => {
          this.financiaList = response.data.list;
          this.total = response.data.total;
          this.listLoading = false;
        });
      } else if(this.tabActive == 'third') {
        // USDT合约财务记录
        Object.assign(data,this.listQuery)
        data.coinid = data.coinid || -1
        data.bill_id = 0
        data.stype = data.stype || undefined
        getacchistory(data).then((response) => {
          this.financiaList = response.data.list;
          this.total = response.data.total;
          this.listLoading = false;
        });
      } else if(this.tabActive == 'fourth') {
        // 币本位合约财务记录
        Object.assign(data,this.listQuery)
        data.coinid = data.coinid || -1
        data.bill_id = 0
        data.stype = data.stype || undefined
        getusdacchistory(data).then((response) => {
          this.financiaList = response.data.list;
          this.total = response.data.total;
          this.listLoading = false;
        });
      } else if(this.tabActive == 'fifth') {
        // 跟单账户财务记录
        Object.assign(data,this.listQuery)
        data.coinid = data.coinid || -1
        data.bill_id = 0
        data.stype = data.stype || undefined
        followhistory(data).then((response) => {
          this.financiaList = response.data.list;
          this.total = response.data.total;
          this.listLoading = false;
        });
      }
    },
    handleTab(tab, event) {
      this.listQuery.sname = ""; //用户id,手机号，邮箱
      this.listQuery.sectop = ""; //顶级代理id或昵称
      this.listQuery.sagent = ""; //代理id或者名字
      this.listQuery.coinid = null; //币种id -1全部
      this.listQuery.star = ""; //开始
      this.listQuery.end = ""; //结束
      this.listQuery.stype = undefined; //1：充值  2：提现  4：划转到交易账户 8：从交易账户转入 16-邀请佣金奖励 32-代理佣金奖励 64-法币订单到账,128 空投 256-资产账户划转到跟单账户  512-跟单账户划转到资产账户  1024:佣金收入
      this.listQuery.bill_id = null;//成交编号
      this.listQuery.pageNo = 1;
      this.getList();
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList()
    },
    filterTimeTransform(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val && val[1]+' 23:59:59' || '';
    },
  },
}
</script>

<style lang="scss" scoped>
.financial-container {
  padding-top: 10px;
  &::v-deep .el-tabs{
    margin-left: 20px;
  }
}
</style>