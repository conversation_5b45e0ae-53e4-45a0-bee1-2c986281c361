<template>
  <div class="highwinning-container">
    <div class="filter-container">
      <!-- UID/手机/邮箱 sname-->
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <!-- 顶级代理ID top_agent_id-->
      <el-input
        size="mini"
        v-model="listQuery.top_agent_id"
        :placeholder="$t('filters.topID')"
        style="width: 150px; margin-left: 20px;"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <!-- 上级ID pareid-->
      <el-input
        size="mini"
        v-model="listQuery.pareid"
        :placeholder="$t('tableHeader.superiorID')"
        style="width: 150px; margin-left: 20px;"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <!-- 净PNL pure_pnl -->
      <span style="margin-left: 20px; font-size: 12px">{{$t('tableHeader.net_PNl')}}≥</span>
       <el-input
        v-model="listQuery.pure_pnl"
        size="mini"
        :placeholder="$t('filters.pleaseInput')"
        style="width: 150px; margin-left: 5px"
        class="filter-item"
        clearable
        oninput="value=value.replace(/^\D*(\d*(?:\.\d{0,6})?).*$/g, '$1')"
        @keyup.enter.native="handleFilter"
      />
      <!-- PNL pnl -->
      <span style="margin-left: 20px; font-size: 12px">{{$t('tableHeader.PNL')}}≥</span>
       <el-input
        v-model="listQuery.pnl"
        size="mini"
        :placeholder="$t('filters.pleaseInput')"
        style="width: 150px; margin-left: 5px; margin-left: 10px;"
        class="filter-item"
        clearable
        oninput="value=value.replace(/^\D*(\d*(?:\.\d{0,6})?).*$/g, '$1')"
        @keyup.enter.native="handleFilter"
      />
      <!-- 浮动PNL  float_profit-->
      <span style="margin-left: 20px; font-size: 12px">{{$t('tableHeader.float_PNL')}}≥</span>
       <el-input
        v-model="listQuery.float_profit"
        size="mini"
        :placeholder="$t('filters.pleaseInput')"
        style="width: 150px; margin-left: 5px; margin-left: 10px;"
        class="filter-item"
        clearable
        oninput="value=value.replace(/^\D*(\d*(?:\.\d{0,6})?).*$/g, '$1')"
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-left: 20px; font-size: 12px">{{$t('tableHeader.time')}}</span>
      <el-date-picker
        style="width: 220px; margin-top: 5px; margin-left: 10px;"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change='filterTimeTransform'
      >
      </el-date-picker>
      <!-- <el-date-picker
        style="width: 220px; margin-top: 10px; margin-left: 5px"
        class="picker"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        start-placeholder="起始日期"
        end-placeholder="结束日期"
        @change='dataTimeChange'
      > 
      </el-date-picker> -->
      <!-- <el-select
        size="mini"
        v-model="listQuery.exclude_condition"
        :placeholder="$t('filters.noEliminateUser')"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 5px"
        class="filter-item"
      >
        <el-option
          v-for="item in dayOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select> -->
      <el-button
        style="margin-left: 20px; margin-top: 5px"
        class="filter-item"
        size="mini"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
      
    </div>

    <el-tabs v-model="activeName"  @tab-click="handleClick()">
      <el-tab-pane
        :key="item.currencyid"
        v-for="item in tabArr"
        :label="item.currencyname"
        :name="item.currencyname"
      >
      </el-tab-pane>
    </el-tabs>

    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="user_id" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.user_id || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.unwindNum')" prop="trading_time" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.trading_time || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.profitnum')" prop="win_close" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.win_close || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.historyOdds')" prop="win_rate" align="center" min-width="78">
        <template slot-scope="{row}">
          <span>{{row.win_rate + '%'}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.net_PNl')" prop="pure_pnl" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.pure_pnl || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.PNL')" prop="win_pnl" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.win_pnl || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.ershisi_h_tradingNum')" prop="within24_tradenum" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.within24_tradenum || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.ershisi_h_net_pnl')" prop="within24_pnl" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.within24_pnl || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.maxSingleLoss')" prop="max_loss_per" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.max_loss_per || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.maxSingleProfit')" prop="max_profit_per" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.max_profit_per || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.currentPositionValue')" prop="position_value" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.position_value || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.float_PNL')" prop="float_profit" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.float_profit || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.operation')" align="center" width="100" v-if="$store.getters.roles.indexOf('usdhighwinningoperation')>-1">
        <template slot-scope="{ row }">
          <el-button
            size="mini"
            type="primary"
            @click="detailClick(row)"
            >{{$t('buttons.toView')}}</el-button
          >
        </template>
      </el-table-column>
    </el-table>


    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10, 20, 30, 50]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
//引入封装接口
import {
  highusdwinning,
} from "@/api/riskAdminister";
import { getprocoinList } from "@/api/user";

export default {
  name: "usdhighwinning",
  data() {
    return {
      tableData: [],
      listLoading: true,
      total: 0,
      filterTime: [],
      listQuery: {
        sname: "",
        top_agent_id: "",
        pareid: "",
        pure_pnl: "", // 净pnl
        pnl: "", // PNL
        float_profit: "",
        // exclude_condition: "",
        pageNo: 1,
        pagesize: 10,
        star: '', //开始
        end: '', //结束
      },
      dayOptions: [
        { key: 3, name: this.$t('filters.There_was_no_trading_on_the_3_day') },
        { key: 5, name: this.$t('filters.There_was_no_trading_on_the_5_day') },
        { key: 7, name: this.$t('filters.There_was_no_trading_on_the_7_day') },
        { key: 15, name: this.$t('filters.There_was_no_trading_on_the_15_day') },
      ], 

      tabArr: [],
      activeName: ''
    };
  },

  components: {},

  computed: {
    // 默认时间
    timeDefault () {
      let date = new Date()
      // 通过时间戳计算
      let defalutStartTime = ((date.getTime() - 1 * 24 * 3600 * 1000)/1000).toDate('yyyy-MM-dd') // 转化为时间戳
      let defalutEndTime = ((date.getTime()/1000)).toDate('yyyy-MM-dd')
      return [defalutStartTime, defalutEndTime]
    }
  },

  mounted() {
    getprocoinList({}).then((res) => {
      res.data.map(v => {
        v['currencyid'] = v['currencyid'].toString()
      })
      for(var i=0; i<res.data.length; i++) {
        if(res.data[i].currencyname == "USDT") {
          res.data.splice(i,1)
        }
      }
      this.tabArr = res.data.filter(v => v.status)
      if(this.tabArr.length>0){
        this.activeName = this.tabArr[0].currencyname
      }
      this.getList();
    })
    // getprocoinList({}).then((res) => {
    //   this.tabArr = res.data.filter(v => v.status)
    //   // console.log(this.marginCurrencyOptions)
    // })
  },

  methods: {
    //  table列表数据
    getList() {
      //开始有加载中效果
      this.listLoading = true;
      let data = {}
      Object.assign(data, this.listQuery)
      // data.star = (this.filterTime && this.filterTime[0]) || "";
      // data.end = this.filterTime ? this.filterTime[1] + " 23:59:59" : "";
      data.pure_pnl = this.listQuery.pure_pnl?Number(this.listQuery.pure_pnl):0
      data.pnl = this.listQuery.pnl?Number(this.listQuery.pnl):0
      data.float_profit = this.listQuery.float_profit?Number(this.listQuery.float_profit):0
      // data.exclude_condition = this.listQuery.exclude_condition?parseInt(this.listQuery.exclude_condition):""
      data.currency_name = this.activeName
      highusdwinning(data).then((res) => {
        // console.log(res)
        this.tableData = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    filterTimeTransform(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val? val[1]+' 23:59:59':'';
    },
    //点击查看跳转详情页
    detailClick(row){
      // var user_id = JSON.parse(row.user_id)
      this.$router.push({
        path: "/riskAdminister/highwinningdetails",
        query: { 
          id:JSON.parse(row.user_id),
          details: JSON.stringify(row)
        },
      });
    },
    // 回车搜索事件
    handleFilter(){
      this.listQuery.pageNo = 1
      this.getList();
    },

    // tab切换
    handleClick() {
      this.getList()
    }
  },
};
</script>
<style lang="scss" scoped>
.wc_1 {
  width: 100%;
  height: 30px;
  display: flex;
  align-items: center;
  margin: 0 10px;
 
  // justify-content: space-around;
  .wc_1-one {
    width: 45%;
  }
  .idPhotoImg_wrap ::v-deep .el-image__inner{
    width: auto;
   
  }
  .idPhotoImg_wrap ::v-deep .el-image__error{
    min-width: 100px;
  }
}
</style>