<template>
	<div class="add-link">
		<div class="title">
			<div class="titleTop">
				<span>{{ $t('forms.Digital_version_number') }}</span>
				<span>{{ $t('forms.client') }}</span>
				<span>{{ $t('tableHeader.state') }}</span>
			</div>
			<div class="titleButton">
				<span>{{ this.version }}</span>
				<span>{{ this.os_type == 1 ? 'Android' : 'IOS' }}</span>
				<span>{{ this.valid == 1 ? $t('forms.To_enable') : $t('forms.Disable') }}</span>
			</div>
		</div>

		<div class="btn">
			<div class="btnTitle">{{ $t('tableHeader.Current_link') }}</div>
			<el-button type="primary" v-if="$store.getters.roles.indexOf('configurationDownloadOperation') > -1"
				@click="handleAddLink">{{ $t('menus.Add_link') }}</el-button>
		</div>

		<el-table v-loading="listLoading" :data="tableData" border fit highlight-current-row size="mini"
			style="width: 100%; margin-top: 30px" :header-cell-style="{ background: '#F0F8FF' }">
			<el-table-column prop="lang_type" :label="$t('dialog.language')" align="center">
				<template slot-scope="{ row }">
					<span>
						{{ row.lang_type == 0 ? $t('tableHeader.Chinese') :
							row.lang_type == 1 ? $t('tableHeader.English') :
								row.lang_type == 2 ? $t('tableHeader.Traditional') :
									row.lang_type == 3 ? $t('tableHeader.Korean') :
										row.lang_type == 4 ? $t('tableHeader.Vietnam') : $t('tableHeader.Indonesia') }}
					</span>
				</template>
			</el-table-column>
			<el-table-column prop="channel_id" :label="$t('tableHeader.Download_the_way')" align="center">
				<template slot-scope="{ row }">
					<span v-if="os_type == 1">{{ row.channel_id == 0 ? $t('tableHeader.Download_speed') : row.channel_id
						== 1 ? $t('tableHeader.Local_download') : $t('tableHeader.Alternate_download') }}</span>
					<span v-if="os_type == 2">{{ row.channel_id == 0 ? $t('tableHeader.Download_speed') : row.channel_id
						== 1 ? $t('tableHeader.Download_speed_two') : $t('tableHeader.Alternate_download') }}</span>
				</template>
			</el-table-column>
			<el-table-column prop="url" :label="$t('tableHeader.link')" align="center">
				<template slot-scope="{ row }">
					<span>{{ row.url }}</span>
				</template>
			</el-table-column>
			<el-table-column prop="version_string" :label="$t('tableHeader.Display_version_number')" align="center">
				<template slot-scope="{ row }">
					<span>{{ row.version_string }}</span>
				</template>
			</el-table-column>
			<el-table-column prop="force_upgrade" :label="$t('tableHeader.Update_way')" align="center">
				<template slot-scope="{ row }">
					<span>{{ row.force_upgrade == 0 ? $t('tableHeader.Prompt_update') : $t('tableHeader.Forced_update')
					}}</span>
				</template>
			</el-table-column>
			<el-table-column prop="content" :label="$t('tableHeader.Prompt_content')" align="center">
				<template slot-scope="{ row }">
					<span>{{ row.content }}</span>
				</template>
			</el-table-column>
			<el-table-column :label="$t('tableHeader.operation')" align="center" min-width="250"
				v-if="$store.getters.roles.indexOf('configurationDownloadOperation') > -1">
				<template slot-scope="scope">
					<el-button type="info" size="mini" icon="el-icon-view" plain @click="handleClick(scope.row, 1)">
						{{ $t('buttons.toView') }}
					</el-button>
					<el-button type="danger" size="mini" icon="el-icon-delete" plain @click="handleClick(scope.row, 2)">
						{{ $t('buttons.delete') }}
					</el-button>
					<el-button type="primary" size="mini" icon="el-icon-edit" plain @click="handleClick(scope.row, 3)">
						{{ $t('buttons.edit') }}
					</el-button>
				</template>
			</el-table-column>
		</el-table>

		<el-dialog
			:title="dialogVisibleType == 1 ? $t('buttons.add') + $t('tableHeader.link') : dialogVisibleType == 2 ? $t('buttons.edit') + $t('tableHeader.link') : $t('buttons.toView') + $t('tableHeader.link')"
			:visible.sync="dialogVisible" width="650px" :before-close="handleClose">

			<el-form ref="form" :model="form" :rules="rulesForm" label-width="100px" :label-position="labelPosition">
				<el-form-item :label="$t('forms.Select_language')" prop="selectLanguage">
					<el-select v-model="form.selectLanguage" :placeholder="$t('dialog.Please_select_language')"
						v-bind:disabled="isDisabled" filterable style="width: 100%">
						<el-option :label="$t('tableHeader.Chinese')" value="0"></el-option>
						<el-option :label="$t('tableHeader.English')" value="1"></el-option>
						<el-option :label="$t('tableHeader.Korean')" value="3"></el-option>
						<el-option :label="$t('tableHeader.Vietnam')" value="4"></el-option>
						<el-option :label="$t('tableHeader.Indonesia')" value="5"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item :label="$t('forms.Select_download_way')" prop="downloadWay">
					<el-select v-model="form.downloadWay" :placeholder="$t('dialog.Please_select_download_way')"
						v-bind:disabled="isDisabled" filterable style="width: 100%">
						<el-option :label="$t('tableHeader.Download_speed')" value="0"></el-option>
						<el-option :label="$t('tableHeader.Local_download')" value="1" v-if="os_type == 1"></el-option>
						<el-option :label="$t('tableHeader.Download_speed_two')" value="1"
							v-if="os_type == 2"></el-option>
						<el-option :label="$t('tableHeader.Alternate_download')" value="3"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item :label="$t('forms.Download_address')" prop="downloadAddress">
					<el-input v-model="form.downloadAddress" v-bind:disabled="isDisabled"></el-input>
				</el-form-item>
				<el-form-item :label="$t('tableHeader.Display_version_number')" prop="showVersion">
					<el-input v-model="form.showVersion" v-bind:disabled="isDisabled" />
				</el-form-item>
				<el-form-item :label="$t('tableHeader.Update_way')">
					<el-radio-group v-model="form.updateWay">
						<el-radio label="0" v-bind:disabled="isDisabled">
							{{ $t('tableHeader.Prompt_update') }}
						</el-radio>
						<el-radio label="1" v-bind:disabled="isDisabled">
							{{ $t('tableHeader.Forced_update') }}
						</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item :label="$t('tableHeader.Prompt_content')" prop="promptContent">
					<el-input type="textarea" v-model="form.promptContent" v-bind:disabled="isDisabled" />
				</el-form-item>
			</el-form>

			<span slot="footer" class="dialog-footer">
				<el-button @click="handleCancel" v-if="dialogVisibleType !== 3">{{ $t('buttons.cancel') }}</el-button>
				<el-button type="primary" @click="handleSure">{{ $t('buttons.determine') }}</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
import {
	versiondetaillist,              // 链接版本
	addversiondetail,               // 新增版本链接
	updateversiondetail,            // 编辑版本链接
	deleteversiondetailbyidarg,     // 删除版本链接
} from "@/api/systemManagement";
export default {
	name: "addLink",
	data() {
		// 选择语言
		let selectLanguage_v = (rule, value, callback) => {
			if (!value) {
				return callback(new Error(this.$t('dialog.Please_select_language')))
			} else {
				callback()
			}
		}
		// 下载方式
		let downloadWay_v = (rule, value, callback) => {
			if (!value) {
				return callback(new Error(this.$t('dialog.Please_select_download_way')))
			} else {
				callback()
			}
		}
		// 下载地址
		let downloadAddress_v = (rule, value, callback) => {
			if (!value) {
				return callback(new Error(this.$t('dialog.Please_enter_download_address')))
			} else {
				callback()
			}
		}
		// 显示版本号
		let showVersion_v = (rule, value, callback) => {
			if (!value) {
				return callback(new Error(this.$t('dialog.Please_enter_text_version')))
			} else {
				callback()
			}
		}
		// 提示内容
		let promptContent_v = (rule, value, callback) => {
			if (!value) {
				return callback(new Error(this.$t('dialog.Please_enter_prompt_content')))
			} else {
				callback()
			}
		}

		return {
			listLoading: false,           // 懒加载
			tableData: [],                // 表格数据
			dialogVisible: false,         // 弹框
			dialogVisibleType: 1,         // 弹框类型 1.添加 2.编辑 3.查看

			labelPosition: 'left',        // el-form left
			form: {
				selectLanguage: '',         // 选择语言
				downloadWay: '',            // 下载方式
				downloadAddress: '',        // 下载地址
				showVersion: '',            // 显示版本号
				updateWay: '0',             // 更新方式
				promptContent: '',          // 提示内容
			},
			version_detail_id: '',        // 详情列表数据id
			version_id: '',               // 版本id
			version: '',                  // 数字版本号
			os_type: '',                  // 客户端
			valid: '',                    // 状态

			isDisabled: false,

			rulesForm: {
				selectLanguage: [{ validator: selectLanguage_v, trigger: 'change' }],  // 选择语言
				downloadWay: [{ validator: downloadWay_v, trigger: 'change' }],  // 下载方式
				downloadAddress: [{ validator: downloadAddress_v, trigger: 'blur' }],    // 下载地址
				showVersion: [{ validator: showVersion_v, trigger: 'blur' }],    // 显示版本号
				promptContent: [{ validator: promptContent_v, trigger: 'blur' }],    // 提示内容
			},
		};
	},

	components: {},

	computed: {},

	mounted() {
		this.version_id = this.$route.query.version_id  // 版本id
		this.version = this.$route.query.version        // 数字版本号
		this.os_type = this.$route.query.os_type        // 客户端
		this.valid = this.$route.query.valid            // 状态
		this.getList()
	},

	activated() {
		this.version_id = this.$route.query.version_id  // 版本id
		this.version = this.$route.query.version        // 数字版本号
		this.os_type = this.$route.query.os_type        // 客户端
		this.valid = this.$route.query.valid            // 状态
		this.getList()
	},

	methods: {
		// 点击添加链接
		handleAddLink() {
			this.dialogVisible = true;
			this.dialogVisibleType = 1;
			this.isDisabled = false;
		},
		// icon取消
		handleClose() {
			this.dialogVisible = false;
			this.form = {
				selectLanguage: '',         // 选择语言
				downloadWay: '',            // 下载方式
				downloadAddress: '',        // 下载地址
				showVersion: '',            // 显示版本号
				updateWay: '0',             // 更新方式
				promptContent: '',          // 提示内容
			},
				this.$refs['form'].resetFields();
		},
		// 取消
		handleCancel() {
			this.dialogVisible = false;
			this.form = {
				selectLanguage: '',         // 选择语言
				downloadWay: '',            // 下载方式
				downloadAddress: '',        // 下载地址
				showVersion: '',            // 显示版本号
				updateWay: '0',             // 更新方式
				promptContent: '',          // 提示内容
			},
				this.$refs['form'].resetFields();
		},
		// 确定
		handleSure() {
			// 添加
			if (this.dialogVisibleType == 1) {
				this.$refs['form'].validate((valid) => {
					if (valid) {
						addversiondetail({
							os_type: Number(this.os_type),             // 客户端 1.Android 2.ios int
							lang_type: Number(this.form.selectLanguage), // 语言类型 0.中文 1.英文 2.繁体 3.韩文 4.越南 5.印尼 int
							channel_id: Number(this.form.downloadWay),    // 渠道id 安卓{0-极速下载 1-本地下载 2-备用下载} IOS{0-testflight 1-极速下载 2-极速下载2 3-备用下载} int
							version: Number(this.version),             // 数字版本号 int
							version_string: this.form.showVersion,            // 文字版本号 string
							url: this.form.downloadAddress,        // 下载链接 string
							content: this.form.promptContent,          // 提示内容 string  
							force_upgrade: Number(this.form.updateWay),      // 是否强制更新 int
						}).then(() => {
							this.dialogVisible = false;
							this.form = {
								selectLanguage: '',         // 选择语言
								downloadWay: '',            // 下载方式
								downloadAddress: '',        // 下载地址
								showVersion: '',            // 显示版本号
								updateWay: '0',             // 更新方式
								promptContent: '',          // 提示内容
							},
								this.$refs['form'].resetFields();
							this.$notify({
								title: this.$t('dialog.Add_a_success'),
								type: "success",
								duration: 2000,
							});
							this.getList()
						})
					} else {
						return false
					}
				})
				// 修改
			} else if (this.dialogVisibleType == 2) {
				this.$refs['form'].validate((valid) => {
					if (valid) {
						updateversiondetail({
							version_detail_id: Number(this.version_detail_id),   // 版本详情id
							os_type: Number(this.os_type),             // 客户端 1.Android 2.ios int
							lang_type: Number(this.form.selectLanguage), // 语言类型 0.中文 1.英文 2.繁体 3.韩文 4.越南 5.印尼 int
							channel_id: Number(this.form.downloadWay),    // 渠道id 安卓{0-极速下载 1-本地下载 2-备用下载} IOS{0-testflight 1-极速下载 2-极速下载2 3-备用下载} int
							version: Number(this.version),             // 数字版本号 int
							version_string: this.form.showVersion,            // 文字版本号 string
							url: this.form.downloadAddress,        // 下载链接 string
							content: this.form.promptContent,          // 提示内容 string  
							force_upgrade: Number(this.form.updateWay),      // 是否强制更新 int
						}).then(() => {
							this.dialogVisible = false;
							this.form = {
								selectLanguage: '',         // 选择语言
								downloadWay: '',            // 下载方式
								downloadAddress: '',        // 下载地址
								showVersion: '',            // 显示版本号
								updateWay: '0',             // 更新方式
								promptContent: '',          // 提示内容
							},
								this.$refs['form'].resetFields();
							this.$notify({
								title: this.$t('dialog.Edit_successful'),
								type: "success",
								duration: 2000,
							});
							this.getList()
						})
					} else {
						return false
					}
				})
				// 查看
			} else if (this.dialogVisibleType == 3) {
				this.dialogVisible = false;
				this.form = {
					selectLanguage: '',         // 选择语言
					downloadWay: '',            // 下载方式
					downloadAddress: '',        // 下载地址
					showVersion: '',            // 显示版本号
					updateWay: '0',             // 更新方式
					promptContent: '',          // 提示内容
				},
					this.$refs['form'].resetFields();
			}
		},

		// 操作
		handleClick(v, type) {
			switch (type) {
				// 查看
				case 1:
					this.dialogVisibleType = 3;
					this.dialogVisible = true;
					this.isDisabled = true;
					this.form = {
						selectLanguage: v.lang_type.toString(),     // 选择语言
						downloadWay: v.channel_id.toString(),       // 下载方式
						downloadAddress: v.url,                     // 下载地址
						showVersion: v.version_string,              // 显示版本号
						updateWay: v.force_upgrade.toString(),      // 更新方式
						promptContent: v.content,                   // 提示内容
					};
					break;
				// 删除
				case 2:
					this.$confirm(this.$t('dialog.Confirm_deletion'), {
						distinguishCancelAndClose: true,
						confirmButtonText: this.$t('buttons.determine'),
						cancelButtonText: this.$t('buttons.cancel')
					})
						.then(() => {
							deleteversiondetailbyidarg({
								version_detail_id: Number(v.version_detail_id),// 当前链接id
							}).then(() => {
								this.$notify({
									title: this.$t('dialog.Delete_the_success'),
									type: "success",
									duration: 2000,
								});
								this.getList()
							})
						})
						.catch(action => { })
					break;
				// 编辑
				case 3:
					this.dialogVisibleType = 2;
					this.dialogVisible = true;
					this.isDisabled = false;
					this.version_detail_id = v.version_detail_id  // 详情列表数据id
					this.form = {
						selectLanguage: v.lang_type.toString(),     // 选择语言
						downloadWay: v.channel_id.toString(),       // 下载方式
						downloadAddress: v.url,                     // 下载地址
						showVersion: v.version_string,              // 显示版本号
						updateWay: v.force_upgrade.toString(),      // 更新方式
						promptContent: v.content,                   // 提示内容
					};
					break;
			}
		},


		getList() {
			this.listLoading = true;
			versiondetaillist({
				version_id: Number(this.version_id)
			}).then((res) => {
				console.log(res)
				this.tableData = res.data.list
				this.listLoading = false;
			}).catch((err) => {
				console.error(err);
				this.listLoading = false;
			})
		},
	},
};
</script>

<style lang="scss" scoped>
.add-link {
	.title {
		margin: 20px 0;

		div {
			padding: 10px 20px;
			margin: 15px 0;

			span {
				display: inline-block;
				width: 125px;
			}
		}

		.titleTop {
			background: #F0F8FF
		}
	}

	.btn {
		margin: 15px 0;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;

		.btnTitle {
			font-size: 18px;
			//   font-weight: bold; 
		}
	}
}
</style>
