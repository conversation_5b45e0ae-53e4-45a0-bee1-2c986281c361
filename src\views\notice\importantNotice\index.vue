<template>
  <div class="important_container">
    <div class="filter-container">
      <el-select
        size="mini"
        v-model="listQuery.lang_type"
        style="width: 120px; margin-top: 5px"
        class="filter-item"
      >
        <el-option
          v-for="item in langOptions"
          :key="item.value"
          :label="$t(`filters.${item.name}`)"
          :value="item.value"
        />
      </el-select>
      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-left: 20px; margin-top: 10px"
        icon="el-icon-search"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
      <el-button
        style="margin-top: 10px; margin-left: 10px; float: right"
        class="filter-item"
        size="mini"
        type="success"
        @click="handleAdd"
        v-if="$store.getters.roles.indexOf('noticeimportantadd') > -1"
      >
        {{$t('buttons.addHomeFloatingLayer')}}
      </el-button>
      <!-- v-if="$store.getters.roles.indexOf('') > -1" -->
    </div>
    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      style="width: 100%; margin-top: 10px"
      size="mini"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column
        :label="$t('tableHeader.title')"
        prop="title"
        align="center"
        min-width="78"
      ></el-table-column>
      <el-table-column :label="$t('tableHeader.content')" align="center" min-width="90">
        <template slot-scope="{ row }">
          <el-image
            v-if="row.content && row.notice_type == 2"
            :src="row.content"
            style="width: 50px;height: 50px;"
            fit="cover"
            :preview-src-list="row.imgList"
          >
            <!-- <i slot="placeholder" class="el-icon-loading"></i>
            <i slot="error" class="el-icon-warning"></i> -->
          </el-image>
          <span v-if="row.notice_type == 1">{{ row.content }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.link')" prop="pnl" align="center" min-width="125">
        <template slot-scope="{ row }">
          <span>{{ row.link || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.releaseTime')"
        prop="create_time"
        align="center"
        min-width="125"
      >
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.downShelvesTime')"
        prop="end_time"
        align="center"
        min-width="125"
      >
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.operation')"
        prop="agent_rebate_ratio"
        align="center"
        min-width="180"
        v-if="
          $store.getters.roles.indexOf('noticeimportantadd') > -1 ||
          $store.getters.roles.indexOf('noticeimportantdel') > -1 ||
          $store.getters.roles.indexOf('noticeimportantvalid') > -1
        "
      >
        <template slot-scope="{ row }">
          <el-button
            v-if="$store.getters.roles.indexOf('noticeimportantadd') > -1"
            type="primary"
            size="mini"
            @click="handleUpd(row)"
            >{{$t('buttons.modify')}}</el-button
          ><el-button
            v-if="$store.getters.roles.indexOf('noticeimportantdel') > -1"
            type="danger"
            size="mini"
            @click="handleDel(row)"
            >{{$t('buttons.delete')}}</el-button
          >
          <el-button
            v-if="$store.getters.roles.indexOf('noticeimportantvalid') > -1"
            :type="row.valid ? 'info' : 'success'"
            size="mini"
            @click="handleVld(row)"
            >{{ row.valid ? $t('buttons.down') : $t('buttons.up') }}{{$t('buttons.frame')}}</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[200, 100, 50, 20]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
    <el-dialog
      :title="$t('buttons.addHomeFloatingLayer')"
      :visible.sync="dialogVisible"
      width="483px"
      height="320px"
      :before-close="handleDialogClose"
    >
      <div class="dialogc">
        <div>
          <el-form
            :rules="rules"
            ref="ruleFormmr"
            style="margin: 0 10px"
            :model="ruleForm"
            label-width="90px"
            label-position="left"
          >
            <div class="divliang">
              <el-form-item :label="$t('tableHeader.title')" prop="title">
                <el-input
                  v-model="ruleForm.title"
                  :placeholder="$t('filters.pleaseEnterTheContent')"
                ></el-input>
              </el-form-item>
              <el-form-item :label="$t('dialog.language')" prop="lang_type">
                <el-select
                  v-model="ruleForm.lang_type"
                  :placeholder="$t('filters.pleaseLanguageType')"
                >
                  <el-option
                    v-for="item in langOptions"
                    :key="item.value"
                    :label="$t(`filters.${item.name}`)"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('tableHeader.content')" required>
                <el-radio-group
                  @change="noticeTypeChange"
                  v-model="ruleForm.notice_type"
                >
                  <el-radio :label="1">{{$t('others.importantNotice')}}</el-radio>
                  <el-radio :label="2">{{$t('others.picture')}}</el-radio>
                </el-radio-group>
                <el-row>
                  <el-form-item
                    v-show="ruleForm.notice_type == 1"
                    prop="content"
                  >
                    <el-input
                      type="textarea"
                      :rows="4"
                      :placeholder="$t('filters.pleaseEnterTheContent')"
                      v-model="ruleForm.content"
                      ref="contentInput"
                    >
                    </el-input>
                  </el-form-item>
                  <el-form-item
                    v-show="ruleForm.notice_type == 2"
                    prop="content"
                  >
                    <div class="uploaddisplay">
                      <el-upload
                        class="avatar-uploader"
                        :action="actionUrl"
                        accept="image/png"
                        drag
                        :data="uploadData"
                        :before-upload="beforeAvatarUpload"
                        :on-error="uploadError"
                        ref="uploadzh"
                        :on-success="uploadSuccessZh"
                        :show-file-list="false"
                      >
                        <img v-if="imageUrl" :src="imageUrl" class="avatar" />
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                      </el-upload>
                      <div style="margin-left: 10px; line-height: 20px">
                        {{$t('others.pictureSize')}}
                      </div>
                    </div>
                  </el-form-item>
                </el-row>
              </el-form-item>
              <el-form-item :label="$t('forms.skipLinks')" prop="link">
                <el-input
                  class="w156"
                  :placeholder="$t('filters.pleaseLinkAddr')"
                  v-model="ruleForm.link"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item :label="$t('tableHeader.downShelvesTime')" required>
                <el-col :span="11">
                  <el-form-item prop="end_time1">
                    <el-date-picker
                      type="date"
                      :placeholder="$t('filters.optionDate')"
                      v-model="ruleForm.end_time1"
                      style="width: 100%"
                      value-format="yyyy-MM-dd"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col class="line" :span="2">-</el-col>
                <el-col :span="11">
                  <el-form-item prop="end_time2">
                    <el-time-picker
                      :placeholder="$t('filters.optionTime')"
                      v-model="ruleForm.end_time2"
                      style="width: 100%"
                      value-format="HH:mm:ss"
                    ></el-time-picker>
                  </el-form-item>
                </el-col>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="handleDialogClose">{{$t('buttons.cancel')}}</el-button>
        <el-button size="small" type="primary" @click="handleDialogOkClick"
          >{{$t('buttons.confirm')}}</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
// 封装api
import {
  getnoticeimportant,
  noticeimportantadd,
  noticeimportantvalid,
  noticeimportantdel,
} from "@/api/notice";
let that;
export default {
  name: "importantNotice",
  data() {
    let validate_content = (rule, value, callback) => {
      if (!value) {
        return callback(
          new Error(
            this.ruleForm.notice_type == 1 ? this.$t('filters.pleaseEnterTheContent') : this.$t('dialog.Please_upload_pictures')
          )
        );
      } else {
        callback();
      }
    };
    let validate_http = (rule, value, callback) => {
      if (value === "") {
        callback();
      }
      let reg =
        /(http|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?/;
      if (!reg.test(value)) {
        return callback(new Error(this.$t('dialog.The_link_is_not_formatted_correctly')));
      } else {
        callback();
      }
    };
    return {
      imgHostUrl: "",
      listLoading: false,
      total: 0,
      tableData: null,
      listQuery: {
        lang_type: "0",
        pageNo: 1,
        pagesize: 20,
      },
      exportLoading: false, //导出加载中效果
      langOptions: [
        { name: 'ChineseSimplifiedNotice',  value: "0",},
        { name: 'EnglishNotice',  value: "1",},
        { name: 'ChineseTraditionalNotice',  value: "2",},
        { name: 'KoreanNotice',  value: "3",},
        { name: 'VietnameseNotice',  value: "4",},
        { name: 'IndonesianNotice',  value: "5",},
        { name: 'RussianNotice',  value: "6",},
        { name: 'GermanNotice',  value: "7",},
        { name: 'JapaneseNotice',  value: "8",},
      ],
      dialogVisible: false,
      uploadData: {},
      actionUrl: "",
      imageUrl: "",
      uploading: false,
      ruleForm: {
        id: 0,
        title: "",
        lang_type: "0",
        notice_type: 1,
        content: "",
        link: "",
        start_time: "",
        end_time1: "",
        end_time2: "",
      },
      rules: {
        title: [
          { required: true, message: this.$t('dialog.Please_enter_a_title'), trigger: "blur" },
          { max: 10, message: this.$t('dialog.Length_10'), trigger: "blur" },
        ],
        link: [{ validator: validate_http, trigger: "blur" }],
        lang_type: [
          { required: true, message: this.$t('filters.pleaseLanguageType'), trigger: "change" },
        ],
        content: [{ validator: validate_content, trigger: "blur" }],
        end_time1: [
          {
            type: "string",
            required: true,
            message: this.$t('dialog.Please_select_a_date'),
            trigger: "change",
          },
        ],
        end_time2: [
          {
            type: "string",
            required: true,
            message: this.$t('dialog.Please_select_the_time'),
            trigger: "change",
          },
        ],
      },
    };
  },
  components: {},

  computed: {},

  mounted() {
    this.actionUrl =
      (process.env.VUE_APP_API == "/"
        ? window.location.protocol + "//" + window.location.host
        : process.env.VUE_APP_API) + "/managers/v1/banner/bcprobannerupload";
    this.getList();
    that = this;
  },

  methods: {
    noticeTypeChange() {
      this.ruleForm.content = "";
      this.imageUrl = "";
      this.$refs.ruleFormmr.clearValidate("content");
    },
    handleDialogOkClick() {
      this.$refs["ruleFormmr"].validate((valid) => {
        if (valid) {
          let end_time = this.ruleForm.end_time1 + " " + this.ruleForm.end_time2
          if(new Date(end_time).getTime()<=new Date().getTime()){
            this.$notify({
              title: this.$t('dialog.Prompt'),
              message: this.$t('dialog.Please_enter_the_correct_takedown_time'),
              type: "warning",
            });
            return false
          }
          let bb = this.ruleForm.content
          let imgName = bb.split('/')[bb.split('/').length-1]
          let content = this.ruleForm.notice_type == 2?imgName:bb
          var data = {
            id: this.ruleForm.id, //  (默认传0)，
            title: this.ruleForm.title, //  标题，
            content: content, //  内容，
            link: this.ruleForm.link || undefined, //  跳转链接，
            lang_type: Number(this.ruleForm.lang_type),
            notice_type: this.ruleForm.notice_type, //  通知类型 1文本 2图片，
            start_time: new Date().dateHandle(),
            end_time,
          };
          noticeimportantadd(data).then((res) => {
            this.$notify({
              title: this.$t('dialog.Successful'),
              message: this.$t('dialog.Operation_is_successful'),
              type: "success",
            });
            this.getList();
            this.dialogVisible = false;
            this.$refs["ruleFormmr"].resetFields();
          });
        }
      });
    },
    uploadSuccessZh(response, file, fileList) {
      this.uploading = false;
      if (response.ret === 0) {
        that.ruleForm.content = response.data;
        this.$refs.ruleFormmr.clearValidate("content");
        var reader = new FileReader();
        reader.readAsDataURL(file.raw);
        reader.onload = function (event) {
          that.imageUrl = event.target.result;
        };
      } else {
        this.ruleForm.content = "";
        this.imageUrl = "";
        this.$notify({
          title: this.$t('dialog.Failure'),
          message: this.$t('dialog.Chinese_BANNER_Please_try_again'),
          type: "error",
        });
      }
    },
    uploadError(error, file, fileList) {
      this.$refs.uploadzh.clearFiles();
      this.uploading = false;
      this.$notify({
        title: this.$t('dialog.Failure'),
        message: this.$t('dialog.BANNER_Please_try_again'),
        type: "error",
      });
    },
    beforeAvatarUpload(file) {
      let _this = this;
      let imgWidth = "";
      let imgHight = "";
      const isSize = new Promise(function (resolve, reject) {
        let width = 640;
        let height = 700;
        let _URL = window.URL || window.webkitURL;
        let img = new Image();
        img.onload = function () {
          imgWidth = img.width;
          imgHight = img.height;
          let valid = img.width == width && img.height == height;
          valid ? resolve() : reject();
        };
        img.src = _URL.createObjectURL(file);
      }).then(
        () => {
          let time = parseInt(new Date().getTime() / 1000) + "";
          let sin = md5(md5(process.env.VUE_APP_APIKEY) + time);
          this.uploadData["sign"] = sin;
          this.uploadData["ts"] = time;
          return file;
        },
        () => {
          _this.$notify({
            title: this.$t('dialog.Failure'),
            message:
              this.$t('dialog.The_picture_is_not_up_to_standard') +
              imgWidth +
              "px和" +
              imgHight +
              "px",
            type: "error",
          });
          return Promise.reject();
        }
      );
      // return isPNG && isLt700K && isSize;
      return isSize;
    },
    handleDialogClose() {
      this.$refs.uploadzh.clearFiles();
      this.$refs["ruleFormmr"].resetFields();
      this.dialogVisible = false;
    },
    handleAdd() {
      (this.ruleForm = {
        id: 0,
        title: "",
        lang_type: "0",
        notice_type: 1,
        content: "",
        link: "",
        start_time: "",
        end_time1: "",
        end_time2: "",
      }),
        (this.dialogVisible = true);
    },
    handleUpd(row) {
      Object.assign(this.ruleForm, row);
      if (row.notice_type == 2) {
        this.imageUrl = row.content;
      }
      this.dialogVisible = true;
    },
    handleDel(row) {
      this.$confirm(`${this.$t('dialog.Do_you_want_to_delete_the_heading')}${row.title} ${this.$t('dialog.Important_announcement')} `, this.$t('dialog.Prompt'), {
        confirmButtonText: this.$t('dialog.Sure_to_delete'),
        cancelButtonText: this.$t('buttons.cancel'),
        type: "error",
      })
        .then(() => {
          noticeimportantdel({ id: row.id }).then((res) => {
            this.$notify({
              title: this.$t('dialog.Successful'),
              message: this.$t('dialog.Operation_is_successful'),
              type: "success",
            });
            this.getList();
          });
        })
        .catch(() => {});
    },
    handleVld(row) {
      this.$confirm(
        ` ${this.$t('dialog.Whether_or_not_to')} ${row.valid ? this.$t('dialog.The_shelves') : this.$t('dialog.Shelves')} ${this.$t('dialog.The_headline')} ${row.title} ${this.$t('dialog.Important_announcement')} `,
        this.$t('dialog.Prompt'),
        {
          confirmButtonText: `${this.$t('buttons.determine')}${row.valid ? this.$t('dialog.The_shelves') : this.$t('dialog.Shelves')}`,
          cancelButtonText: this.$t('buttons.cancel'),
          type: row.valid ? "warning" : "success",
        }
      )
        .then(() => {
          noticeimportantvalid({ id: row.id, valid: row.valid ? 0 : 1 }).then(
            (res) => {
              this.$notify({
                title: this.$t('dialog.Successful'),
                message: this.$t('dialog.Operation_is_successful'),
                type: "success",
              });
              this.getList();
            }
          );
        })
        .catch(() => {});
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.page = 1;
      this.getList();
    },
    //  渲染table列表
    getList() {
      this.listLoading = true;
      getnoticeimportant(
        Object.assign({}, this.listQuery, {
          lang_type: Number(this.listQuery.lang_type),
        })
      ).then((res) => {
        this.listLoading = false;
        this.total = res.data.total;
        if (res.data.list) {
          this.tableData = res.data.list.map((v) => {
            v.lang_type = String(v.lang_type);
            if (v.notice_type == 2) {
              v.imgList = [v.content];
              v.end_time1 = v.end_time.split(" ")[0];
              v.end_time2 = v.end_time.split(" ")[1];
            }
            return v;
          });
        } else {
          this.tableData = [];
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.important_container {
  .uploaddisplay {
    width: 100%;
    white-space: nowrap;
    & > div {
      display: flex;
    }
  }
  ::v-deep .el-upload {
    width: 100%;
    .el-upload-dragger {
      width: 100%;
      height: auto;
      min-height: 180px;
      display: flex;
      justify-content: center;
      align-items: center;
      .avatar-uploader-icon {
        font-size: 28px;
      }
    }
  }
  ::v-deep .el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    width: 100%;
  }
  .line {
    text-align: center;
  }
}
</style>