<template>
  <div class="asset-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        :placeholder="$t('filters.topID')"
        style="width: 180px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-left: 20px; font-size: 12px">{{ $t('tableHeader.time') }}</span>
      <el-date-picker
        style="width: 180px; margin-top: 5px"
        v-model="listQuery.star"
        size="mini"
        type="date"
        value-format="yyyy-MM-dd"
      />
      <el-button
        style="margin-top: 10px; margin-left: 10px"
        class="filter-item"
        size="mini"
        type="primary"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
      <!-- <el-button
        class="filter-item"
        :loading="exportLoading"
        v-if="$store.getters.roles.indexOf('') > -1"
        @click="handleExport"
        size="mini"
        type="success"
      >
        导出
      </el-button> -->
    </div>

    <el-tabs v-model="activeName" @tab-click="handleClick" style="margin: 15px 15px">
      <el-tab-pane label="USDT" name="USDT">
        <el-table
          v-loading="listLoading"
          :data="tableData"
          border
          fit
          highlight-current-row
          style="width: 100%; margin-top: 30px"
          size="mini"
          :header-cell-style="{ background: '#F0F8FF' }"
        >
          <el-table-column :label="$t('filters.topNick')" align="center" min-width="90">
            <template slot-scope="{ row }">
              <span>{{ row.real_name || "--" }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.uid')" prop="user_id" align="center" min-width="78"></el-table-column>
          <el-table-column :label="$t('tableHeader.PNL')" prop="pnl" align="center" min-width="125"></el-table-column>
          <el-table-column :label="$t('tableHeader.Independent_commission')" prop="self_trade_fee" align="center" min-width="125"></el-table-column>
          <el-table-column :label="$t('tableHeader.documentaryPNL')" prop="follow_pnl" align="center" min-width="125">
          </el-table-column>
          <el-table-column :label="$t('tableHeader.documentaryPoundage')" prop="follow_fee" align="center" min-width="125">
          </el-table-column>
          <el-table-column :label="$t('tableHeader.combinedPNL')" prop="total_pnl" align="center" min-width="125"></el-table-column>
          <el-table-column :label="$t('tableHeader.combinedPNLPoundage')" prop="total_commiss" align="center" min-width="125"></el-table-column>
          <el-table-column :label="$t('tableHeader.Get_the_total_amount_of_bonus')" prop="child_gift_get" align="center" min-width="125">
          </el-table-column>
          <el-table-column :label="$t('tableHeader.Total_use_of_bonus')" prop="child_gift_used" align="center" min-width="125">
          </el-table-column>
          <el-table-column :label="$t('tableHeader.this_week_poundage')" prop="trade_fee" align="center" min-width="125"></el-table-column>
          <el-table-column :label="$t('tableHeader.poundage')" prop="commission" align="center" min-width="125"></el-table-column>
          <el-table-column :label="$t('filters.backRate')" prop="agent_rebate_ratio" align="center" min-width="125"></el-table-column>
          <el-table-column :label="$t('tableHeader.commissionMoney')" prop="agent_rebate_commission" align="center" min-width="125"></el-table-column>
          <el-table-column :label="$t('tableHeader.P1')" prop="p1" align="center" min-width="125"></el-table-column>
        </el-table>
      </el-tab-pane>

      <el-tab-pane label="USD"  name="USD">
        <el-table
          v-loading="listLoading"
          :data="tableData"
          border
          fit
          highlight-current-row
          style="width: 100%; margin-top: 30px"
          size="mini"
          :header-cell-style="{ background: '#F0F8FF' }"
        >
          <el-table-column :label="$t('filters.topNick')" align="center" min-width="90">
            <template slot-scope="{ row }">
              <span>{{ row.real_name || "--" }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.uid')" prop="user_id" align="center" min-width="78"></el-table-column>
          <el-table-column :label="$t('tableHeader.PNL')" prop="pnl" align="center" min-width="125">
            <template slot-scope="{ row }">
              <div v-for="(item,index) in row.childcommislist" :key="index">{{ item.currency_name }}&nbsp;{{ item.pnl }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.Independent_commission')" prop="self_trade_fee" align="center" min-width="125">
            <template slot-scope="{ row }">
              <div v-for="(item,index) in row.childcommislist" :key="index">{{ item.currency_name }}&nbsp;{{ item.self_trade_fee }}</div>
            </template>
          </el-table-column>
          <!-- <el-table-column :label="$t('tableHeader.documentaryPNL')" prop="follow_pnl" align="center" min-width="125">
          </el-table-column>
          <el-table-column :label="$t('tableHeader.documentaryPoundage')" prop="follow_fee" align="center" min-width="125">
          </el-table-column> -->
          <el-table-column :label="$t('tableHeader.combinedPNL')" prop="total_pnl" align="center" min-width="125">
            <template slot-scope="{ row }">
              <div v-for="(item,index) in row.childcommislist" :key="index">{{ item.currency_name }}&nbsp;{{ item.total_pnl }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.combinedPNLPoundage')" prop="total_commiss" align="center" min-width="125">
            <template slot-scope="{ row }">
              <div v-for="(item,index) in row.childcommislist" :key="index">{{ item.currency_name }}&nbsp;{{ item.total_commiss }}</div>
            </template>
          </el-table-column>
          <!-- <el-table-column :label="$t('tableHeader.Get_the_total_amount_of_bonus')" prop="child_gift_get" align="center" min-width="125">
          </el-table-column>
          <el-table-column :label="$t('tableHeader.Total_use_of_bonus')" prop="child_gift_used" align="center" min-width="125">
          </el-table-column> -->
          <el-table-column :label="$t('tableHeader.this_week_poundage')" prop="trade_fee" align="center" min-width="125">
            <template slot-scope="{ row }">
              <div v-for="(item,index) in row.childcommislist" :key="index">{{ item.currency_name }}&nbsp;{{ item.trade_fee }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.poundage')" prop="commission" align="center" min-width="125">
            <template slot-scope="{ row }">
              <div v-for="(item,index) in row.childcommislist" :key="index">{{ item.currency_name }}&nbsp;{{ item.commission }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('filters.backRate')" prop="agent_rebate_ratio" align="center" min-width="125">
            <template slot-scope="{row}">
              <div v-for="(item,index) in row.childcommislist" :key="index">{{ item.currency_name }}&nbsp;{{ (item.agent_rebate_ratio)*100 +'%' }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.commissionMoney')" prop="agent_rebate_commission" align="center" min-width="125">
            <template slot-scope="{ row }">
              <div v-for="(item,index) in row.childcommislist" :key="index">{{ item.currency_name }}&nbsp;{{ item.agent_rebate_commission }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.P1')" prop="p1" align="center" min-width="125">
            <template slot-scope="{ row }">
              <div v-for="(item,index) in row.childcommislist" :key="index">{{ item.currency_name }}&nbsp;{{ item.p1 }}</div>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10, 20, 50, 100]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
// 封装api
import { topagentlist, usdopagentlist } from "@/api/channelManage";

export default {
  name: "topAgentList",
  data() {
    return {
    //   pickerOptions: {
    //     disabledDate: (time) => {
    //       let defalutStartTime = new Date().getTime() - 30 * 24 * 3600 * 1000; // 转化为时间戳
    //       return (
    //         time.getTime() >= Date.now() || time.getTime() <= defalutStartTime
    //       );
    //     },
    //   },
      listLoading: false,
      total: 0,
      filterTime: [],
      tableData: null,
      listQuery: {
        sectop: "", //顶级代理id
        star: '',
        pageNo: 1,
        pagesize: 10,
      },
      exportLoading: false, //导出加载中效果

      activeName: 'USDT'
    };
  },
  components: {},

  computed: {
    // 默认时间
    timeDefault() {
      let date = new Date();
      // 通过时间戳计算
      let defalutStartTime = (
        (date.getTime() - 1 * 24 * 3600 * 1000) /
        1000
      ).toDate("yyyy-MM-dd"); // 转化为时间戳
      let defalutEndTime = (date.getTime() / 1000).toDate("yyyy-MM-dd");
      return defalutStartTime;
    },
  },

  mounted() {
    this.listQuery.star = this.timeDefault;
    this.getList();
  },

  methods: {
    // tab切换
    handleClick() {
      this.getList()
    },

    // 搜索事件
    handleFilter() {
      this.listQuery.page = 1;
      this.getList();
    },
    //  渲染table列表
    getList() {
      this.listLoading = true;
      let data = {};
      Object.assign(data, this.listQuery);
      // data.star = (this.filterTime && this.filterTime[0] + " 00:00:00") || "2019-01-01 00:00:00";
      // data.end = (this.filterTime && this.filterTime[1] + " 23:59:59") || (new Date().getTime() / 1000).toDate("yyyy-MM-dd HH:mm:ss"); 
      if (this.activeName == 'USDT') {
        topagentlist(data).then((res) => {
          this.tableData = res.data.list;
          this.total = res.data.total;
          this.listLoading = false;
        });
      } else if (this.activeName == 'USD') {
        usdopagentlist(data).then((res) => {
          this.tableData = res.data.list;
          this.total = res.data.total;
          this.listLoading = false;
        });
      }
    },
    //表格导出功能
    handleExport() {
      this.exportLoading = true;
      let data = {};
      Object.assign(data, this.listQuery);
      data.star = (this.filterTime && this.filterTime[0]) || "";
      data.end = this.filterTime ? this.filterTime[1] : "";
      userpnlexport(data)
        .then((res) => {
          if (res.ret == 0) {
            this.$notify.success({
              title: this.$t('dialog.Operation_is_successful'),
              message: this.$t('dialog.Please_jiaoyi_daochu_download'),
            });
            this.exportLoading = false;
          }
        })
        .catch((err) => {
          this.exportLoading = false;
        });
    },
    filterTimeTransform(val) {
      this.listQuery.star = (val && val[0] + " 00:00:00") || "";
      this.listQuery.end = (val && val[1] + " 23:59:59") || "";
    },
  },
};
</script>
<style lang="scss" scoped>
.asset-container {
  .filter-container {
    .highSwitch_wrap {
      margin-top: 15px;
      width: 100px;
      cursor: pointer;
      font-size: 14px;
    }
  }
  .select_wrap {
    // padding: 0 30px 0 30px;
    display: flex;
    align-items: center;
    span {
      width: 100px;
      // padding-right: 20px;
    }
  }
}
</style>