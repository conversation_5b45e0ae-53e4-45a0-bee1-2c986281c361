<template>
  <div class="asset-container">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        :placeholder="$t('filters.topIDtopNick')"
        style="width: 180px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        :placeholder="$t('filters.agent')"
        style="width: 180px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-button
        style="margin-top: 10px; margin-left: 10px"
        class="filter-item"
        size="mini"
        type="primary"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
      <el-button
        class="filter-item"
        :loading="exportLoading"
        v-if="$store.getters.roles.indexOf('followaccinfoexport')>-1"
        @click="handleExport"
        size="mini"
        type="success"
      >
        {{$t('buttons.export')}}
      </el-button>
    </div>
     

    <el-table
      v-loading="listLoading"
      :data="assetList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="user_id" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center" min-width="95"> </el-table-column>
      <el-table-column :label="$t('filters.topID')" prop="top_agent_id" align="center" min-width="78">
          <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('filters.topNick')" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorID')" prop="pareid" align="center" min-width="78">
          <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.documentaryAccountRights')" prop="balance" min-width="120px" align="center"></el-table-column>
      <!-- <el-table-column :label="$t('tableHeader.documentaryAvailable')" prop="available" min-width="115" align="center"></el-table-column> -->
      <el-table-column :label="$t('tableHeader.documentaryFrozenMargin')" prop="lockbond" min-width="120px" align="center"></el-table-column>
      <el-table-column :label="$t('tableHeader.moneyCost')" prop="capital" min-width="120px" align="center"></el-table-column>
      <el-table-column :label="$t('tableHeader.poundage')" prop="commission" align="center" min-width="100px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.float_PNL')" prop="ripplepnl" min-width="120px" align="center"></el-table-column>
      <el-table-column :label="$t('others.unwind_PNL')" prop="closepnl" min-width="120px" align="center"></el-table-column>
      <el-table-column :label="$t('tableHeader.net_PNl')" prop="netpnl" min-width="120px" align="center"></el-table-column>
      <el-table-column :label="$t('tableHeader.centCommission')" prop="netpnl" min-width="120px" align="center">
        <template slot-scope="{ row }">
          <span>{{ Number(row.intcap).sub(row.outcap) || '--' }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,50,100,200,300]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
// 封装api
import { followaccinfo, followaccinfoexport } from "@/api/documentaryQuery";

// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";
export default {
  name: "followaccinfo",
  data() {
    return {
      listLoading: false,
      total: 0,
      assetList: null,
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        pageNo: 1,
        pagesize: 10,
      },
      exportLoading: false, //导出加载中效果
    };
  },
  components: {},

  computed: {},

  mounted() {
    this.getList();
  },
  methods: {
    //  渲染table列表
    getList() {
      this.listLoading = true;
      followaccinfo(this.listQuery).then((res) => {
        this.assetList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.page = 1;
      this.getList();
    },
    //表格导出功能
    handleExport() {
      this.exportLoading = true;
      let data = {}
      Object.assign(data,this.listQuery)
      followaccinfoexport(data).then((res) => {
        if(res.ret == 0){
          this.$notify.success({title:this.$t('dialog.Operation_is_successful'),message:this.$t('dialog.Please_jiaoyi_daochu_download')})
          this.exportLoading = false
        }
      }).catch(err=>{
        this.exportLoading = false
      })
    },

  },
};
</script>
<style lang="scss" scoped>
.asset-container{
  .filter-container{
    .highSwitch_wrap{
      margin-top: 15px; 
      width: 100px; 
      cursor: pointer;
      font-size: 14px;
    }
  }
  .high_filter_wrap {
    width: 100%;
    &::v-deep .el-col{
      display: flex;
      flex-wrap: wrap;
    }
    .filter_item_wrap{
      white-space: nowrap;
      margin: 10px 15px 0 0 ;
    }
    .high_filter_key {
      font-size: 14px;
      margin-right: 10px;
    }
    .high_filter_btn_wrap{
      margin: 10px 0 0 0 ;
    }
  }
  .select_wrap{
    // padding: 0 30px 0 30px;
    display: flex;
    align-items: center;
    span{
      width:100px;
      // padding-right: 20px;
    }
  }
}
</style>