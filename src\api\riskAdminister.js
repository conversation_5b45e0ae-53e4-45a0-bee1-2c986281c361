import request from '@/utils/request'

//标签列表
export function getlablinfo(data) {
    return request({
        url: '/managers/v1/risk/getlablinfo',
        method: 'post',
        data: { data }
    })
}
//添加标签接口
export function addlabel(data) {
    return request({
        url: '/managers/v1/risk/addlabl',
        method: 'post',
        data: { data }
    })
}
//添加标签接口
export function addlablinfo(data) {
    return request({
        url: '/managers/v1/risk/addlablinfo',
        method: 'post',
        data: { data }
    })
}
//修改标签接口
export function updatelablinfo(data) {
    return request({
        url: '/managers/v1/risk/updatelablinfo',
        method: 'post',
        data: { data }
    })
}
//删除标签接口
export function dellablinfo(data) {
    return request({
        url: '/managers/v1/risk/dellablinfo',
        method: 'post',
        data: { data }
    })
}
//交易频次接口
export function tradefrequency(data) {
    return request({
        url: '/managers/v1/trade/tradefrequency',
        method: 'post',
        data: { data }
    })
}
//交易频次接口 usd
export function usdhighfrequency(data) {
    return request({
        url: '/managers/v1/risk/usdhighfrequency',
        method: 'post',
        data: { data }
    })
}
//交易频次--详情接口
export function tradefrequencview(data) {
    return request({
        url: '/managers/v1/trade/tradefrequencview',
        method: 'post',
        data: { data }
    })
}
//获取高频用户
export function highfrequency(data) {
    return request({
        url: '/managers/v1/risk/highfrequency',
        method: 'post',
        data: { data }
    })
}
//高胜率用户 usdt
export function highwinning(data) {
    return request({
        url: '/managers/v1/risk/highwinning',
        method: 'post',
        data: { data }
    })
}
//高胜率用户 usd
export function highusdwinning(data) {
    return request({
        url: '/managers/v1/risk/highusdwinning',
        method: 'post',
        data: { data }
    })
}
//高胜率用户 - 导出
export function highwinningexport(data) {
    return request({
        url: '/managers/v1/risk/highwinningexport',
        method: 'post',
        data: { data }
    })
}
//最新报警记录
export function systemmonitoring(data) {
    return request({
        url: '/managers/v1/risk/systemmonitoring',
        method: 'post',
        data: { data }
    })
}
//获取报警明细
export function systemmonitoringlist(data) {
    return request({
        url: '/managers/v1/risk/systemmonitoringlist',
        method: 'post',
        data: { data }
    })
}
//获取指数稳定汇总表
export function getindexsummary(data) {
    return request({
        url: '/managers/v1/risk/getindexsummary',
        method: 'post',
        data: { data }
    })
}
//去除此用户的返佣
export function setignorerebate(data) {
    return request({
        url: '/managers/v1/trade/setignorerebate',
        method: 'post',
        data: { data }
    })
}
//交易频次 导出
export function tradefrequencyexport(data) {
    return request({
        url: '/managers/v1/trade/tradefrequencyexport',
        method: 'post',
        data: { data }
    })
}
//同IP登录分析
export function ipstatisticslist(data) {
    return request({
        url: '/managers/v1/ip/ipstatisticslist',
        method: 'post',
        data: { data }
    })
}
//同IP登录分析 - IP用户概况
export function ipuserlist(data) {
    return request({
        url: '/managers/v1/ip/ipuserlist',
        method: 'post',
        data: { data }
    })
}
//同IP登录分析 - IP用户概况 - 详情
export function ipdetaillist(data) {
    return request({
        url: '/managers/v1/ip/ipdetaillist',
        method: 'post',
        data: { data }
    })
}
//同IP登录分析 - IP交易详情
export function iptradelist(data) {
    return request({
        url: '/managers/v1/ip/iptradelist',
        method: 'post',
        data: { data }
    })
}
//观察用户列表
export function watchuserlist(data) {
    return request({
        url: '/managers/v1/watch/watchuserlist',
        method: 'post',
        data: { data }
    })
}
//观察用户列表 - 添加
export function watchusermark(data) {
    return request({
        url: '/managers/v1/watch/watchusermark',
        method: 'post',
        data: { data }
    })
}
//观察用户列表 - 取消观察
export function watchuserunmark(data) {
    return request({
        url: '/managers/v1/watch/watchuserunmark',
        method: 'post',
        data: { data }
    })
}
//获取风控组别列表
export function getgrouplist(data) {
    return request({
        url: '/managers/v1/risk/getgrouplist',
        method: 'post',
        data: { data }
    })
}
//获取风控组别列表 - 添加
export function addgroupinfo(data) {
    return request({
        url: '/managers/v1/risk/addgroupinfo',
        method: 'post',
        data: { data }
    })
}
//获取风控组别列表 - 修改
export function upgroupinfo(data) {
    return request({
        url: '/managers/v1/risk/upgroupinfo',
        method: 'post',
        data: { data }
    })
}
//获取风控用户列表
export function getriskuserlist(data) {
    return request({
        url: '/managers/v1/risk/getriskuserlist',
        method: 'post',
        data: { data }
    })
}
//获取风控用户列表 - 白名单列表
export function getwhitelist(data) {
    return request({
        url: '/managers/v1/risk/getwhitelist',
        method: 'post',
        data: { data }
    })
}
//获取风控用户列表 - 白名单列表 - 添加
export function addristwhite(data) {
    return request({
        url: '/managers/v1/risk/addristwhite',
        method: 'post',
        data: { data }
    })
}
//获取风控用户列表 - 白名单列表 - 删除
export function delristwhite(data) {
    return request({
        url: '/managers/v1/risk/delristwhite',
        method: 'post',
        data: { data }
    })
}
//获取风控用户列表 - 风控历史
export function getristlist(data) {
    return request({
        url: '/managers/v1/risk/getristlist',
        method: 'post',
        data: { data }
    })
}
//更改群组状态
export function getupgroupstatus(data) {
    return request({
        url: '/managers/v1/risk/upgroupstatus',
        method: 'post',
        data: { data }
    })
}

//获取usdt数据统计
export function getriskusdtpnl(data) {
    return request({
        url: '/managers/v1/risk/getriskusdtpnl',
        method: 'post',
        data: { data }
    })
}
//获取usd数据统计
export function getriskusdpnl(data) {
    return request({
        url: '/managers/v1/risk/getriskusdpnl',
        method: 'post',
        data: { data }
    })
}
//获取usdt合约曲线
export function getdayusdtpnl(data) {
    return request({
        url: '/managers/v1/risk/getdayusdtpnl',
        method: 'post',
        data: { data }
    })
}
//获取usd合约曲线
export function getdayusdpnl(data) {
    return request({
        url: '/managers/v1/risk/getdayusdpnl',
        method: 'post',
        data: { data }
    })
}