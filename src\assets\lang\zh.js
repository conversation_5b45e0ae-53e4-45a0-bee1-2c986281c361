import zhLocale from 'element-ui/lib/locale/lang/zh-CN'
export default {
    // 菜单、页面tab、面包屑等相关
    menus: {
        home: '首页',
        homepage: '主页',
        notice_management: '通知管理',
        notice: '公告管理',
        userKYC: 'KYC审核',
        name: '平台管理系统',
        userList: '用户列表',
        userQuery: '用户管理',
        noticeUpd: '公告修改',
        noticeList: '公告列表',
        unwind_query: '平仓查询',
        noticeRelease: '公告发布',
        position_query: '持仓查询',
        order_query: '计划委托查询',
        noticeImportant: '首页浮层',
        userListDetails: '用户列表',
        welcome_homepage: '欢迎主页',
        documentary_query: '跟单查询',
        openPositions_query: '开仓查询',
        open_unwind_query: '开平仓查询',
        num_login_accounts: '登录账号数',
        num_logged_devices: '登录设备数',
        home_title: '欢迎使用平台管理系统',
        overseas_KYC_audit: '海外KYC审核',
        announcement_management: '公告管理',
        num_transactions_IP: '此IP交易次数',
        exports_download_list: '导出下载列表',
        check_full_stop_query: '止盈止损查询',
        user_position_monitoring: '用户持仓监控',
        documentary_unwind_query: '跟单-平仓查询',
        documentary_assets_query: '跟单-资产查询',
        current_whitelisted_user: '当前白名单用户',
        documentary_order_query: '跟单-计划单查询',
        documentary_position_query: '跟单-持仓查询',
        documentary_data_monitoring: '跟单-数据监控',
        historical_whitelisted_users: '历史白名单用户',
        documentary_openPositions_query: '跟单-开仓查询',
        documentary_open_unwind_query: '跟单-开平仓查询',
        documentary_position_monitoring: '跟单-持仓监控',
        documentary_check_full_stop_query: '跟单-止盈止损查询',
        PNL_query: 'PNL查询',
        money_query: '资产管理',
        assetsAccount: '资产账户',
        trading_record: '交易记录',
        freeze_restore: '冻结/恢复',
        commission_query: '返佣查询',
        poundage_query: '手续费查询',
        historicalRecord: '历史记录',
        platform_financial: '平台财务',
        transactionAccount: '交易账户',
        carve_up_poundage: '瓜分手续费',
        daily_PNL_summary: '每日PNL汇总',
        user_assets_query: '钱包账户资产',
        activities_management: '活动管理',
        issue_cancelIssue: '发放/取消发放',
        capital_cost_query: '资金费用查询',
        user_financial_query: '用户财务记录',
        fiat_sell_management: '法币卖出管理',
        user_data_monitoring: '用户数据监控',
        user_extract_management: '用户提币管理',
        user_in_out_money_query: '用户出入金查询',
        carve_up_poundageDetails: '瓜分手续费详情',
        user_log: '用户日志',
        set_bonus: '设置奖金',
        add_group: '权限分组',
        contact_us: '联系我们',
        rights_group: '权限分组',
        add_Banner: '添加Banner',
        set_activity: '设置活动',
        operation_log: '操作日志',
        feedback_data: '反馈数据',
        giftCash_manage: '赠金管理',
        giftCash_active: '赠金活动',
        delete_Banner: '删除Banner',
        role_management: '角色管理',
        label_management: '标签管理',
        trading_frequency: '交易频次',
        system_management: '系统管理',
        system_monitoring: '系统监控',
        channel_management: '渠道管理',
        IP_user_overview: 'IP用户概况',
        CRM_operation_log: 'CRM操作日志',
        risk_control_management: '风控管理',
        observing_User_List: '观察用户列表',
        top_agent_statistical: '顶级代理统计',
        IP_transaction_detailsv: 'IP交易详情',
        list_risk_control_users: '风控用户列表',
        view_award_winning_users: '查看获奖用户',
        trading_frequencyDetails: '交易频次详情',
        list_risk_control_groups: '风控组别列表',
        same_IP_behavior_analysis: '同IP行为分析',
        viewing_historical_records: '历史记录查看',
        contract_PNL_query: '合约PNL查询',
        review_award_winning_users: '获奖用户审核',
        home_Banner_configuration: 'Banner配置',
        system_dictionary_management: '系统字典管理',
        system_dictionary_data_management: '字典数据管理',
        view_risk_control_whitelist: '风控白名单查看',
        agent_direct_drive_statistical: '代理直推统计',
        channel_statistics: '渠道统计',
        high_win_rate_user_details: '高胜率用户信息详情',
        high_frequency_user_identification: '高频用户识别',
        high_win_rate_user_identification: '高胜率用户识别',
        high_monetization_user_recognition: '高盈利用户识别',
        risk_control_whitelist_operation: '风控白名单操作',

        // 2021/12/1 新增
        Net_position_loss: '净持仓亏损',
        Abnormal_account_loss: '账户异常亏损',
        Continuous_shock_alarm: '持续冲击报警',
        Currency_exchange_management: '币币兑换管理',
        Abnormal_alarm_of_net_holding: '净持仓异常报警',
        Quick_single_side_market_alarm: '快速单边市报警',
        Latest_price_protection_alarm: '最新价格保护报警',
        Line_K_could_not_get_the_alarm: 'k线无法获取报警',
        Contract_point_difference_setting: '合约点差设置',
        Welcome_to_the_admin_background: '欢迎使用管理后台',
        K_line_transaction_data_abnormal: 'k线成交数据异常',
        Spot_index_get_abnormal_alarm: '现货指数获取异常报警',
        Spot_index_could_not_get_alarm: '现货指数无法获取报警',
        Shop_single_benchmark_price_to_follow: '铺单基准价跟随',
        Spot_index_calculation_anomaly_log: '现货指数计算异常记录日志',
        BTCUSDT_contract_spot_index_collection_alarm: 'BTCUSDT合约现货指数采集报警',
        Shop_single_reference_system_disk_mouth_abnormal_alarm: '铺单参照系盘口异常报警',
        NO_BTCUSDT_contract_spot_index_collection_alarm: '非BTCUSDT合约现货指数采集报警',
        Shop_single_reference_system_price_acquisition_failure_alarm: '铺单参照系价格获取失败报警',
        // 2021/12/21 new add
        throughPayFund: '穿仓支出资金',
        liquidationGetFund: '强平注入资金',
        liquidationListQuery: '强平单查询',

        // 2022/02/22 新增
        Download_the_configuration: '配置下载',
        // 2022/02/25 新增
        Add_link: '添加链接',

        // 2022/03/13 新增
        Always_accept: '总回收',
        Give_use_a_gold: '使用赠金',
        Total_claim_by_user: '用户总领取',
        Total_loss_of_users: '用户总亏损',
        Freezing_total_Users: '用户总冻结',
        Issue_experience_gold: '发放体验金',
        Total_User_expiration: '用户总过期',
        To_receive_a_gift_of_gold: '领取赠金',
        Recycle_experience_gold: '回收体验金',
        Total_user_availability: '用户总可用',

        // 2022/04/01 修改
        user_trade_pnl: '用户收益数据',

        // 2021/12/22 新增
        Position_inquiry: '持仓查询',
        Open_and_close_query: '开平仓查询',
        User_liquidation_data: '用户强平数据',
        User_position_monitoring: '用户持仓监控',
        Multi_currency_contract_management: '币本位合约查询', // 2022/05/12 修改
        Plan_entrust_stop_profit_and_stop_loss: '计划委托/止盈止损',

        // 2022/02/08 新增
        usd_assets_query: '币本位合约资产',    // 2022/05/12 修改
        usd_contract_commission: '币本位合约返佣', // 2022/05/12 修改
        user_total_assets_query: '用户总资产查询',
        usd_contract_commission_detail: '币本位合约返佣详情', // 2022/05/12 修改

        poundage_commission: 'USDT合约返佣',  // 2022/05/12 修改
        trading_assets_query: 'USDT合约资产',   // 2022/02/08 修改
        poundageCommissionDetails: 'USDT合约返佣详情',  // 2022/02/08 修改

        // 2022、05、11 新增
        Check_full_stop: '止盈止损',
        Plan_to_entrust: '计划委托',
        // 2022/04/22 修改
        trading_query: '合约交易',
        // 2022/04/22 新增
        spot_trading: '现货交易',
        plan_entrust: '计划委托',
        spot_account: '现货账户',
        spot_monitioring: '现货数据监控',
        commissioned_history: '历史委托',
        commissioned_current: '当前委托',
        spot_assets_query: '现货账户资产',
        spot_poundage_query: '现货手续费查询',

        // 2022/05/23 新增
        Spot_financial_records: '现货财务记录',
        USD_contract_financial_records: 'USD合约财务记录',
        USDT_contract_financial_records: 'USDT合约财务记录',

        // 2022/06/01 新增
        usdtrading_frequency: '币本位合约交易频次',
        usdtrading_frequencyDetails: '币本位交合约交易频次详情',
        usd_high_win_rate_user_details: '币本位合约高胜率用户信息详情',
        usd_high_win_rate_user_identification: '币本位合约高胜率用户识别',

        // 2022/06/20 新增
        giftoutDetail: '赠金支出详情',
        cointouDetail: '活动支出详情',
        Campaign_spending: '每日活动支出',

        // feature/daniel-academycoursemanagement-0507
        course_management: '课程管理',
        course_list: '课程列表',
        course_purchases: '购买记录',
        customer_tier: '客户等级',
        reward_list: '返佣列表',

        // feature/daniel-launchpadmanagement-0529
        launchpad: '发射台管理',
        subscriptions: '申购总览',
        subscription_records: '订阅购买历史',
        pledges: '质押总览',
        pledge_records: '质押记录查询',
        redemption_records: '救赎记录',
    },

    // 项目中所有的 筛选条件 
    filters: {
        type: '类型',
        labal: '标签',
        nick: '顶级昵称',
        currency: '币种',
        direction: '方向',
        regTime: '注册时间',
        topID: '顶级代理ID',
        endTime: '结束日期',
        inviteCode: '邀请码',
        userType: '用户类型',
        backRate: '返佣比例',
        new_users: '新增用户',
        pleaseInput: '请输入',
        name: 'UID/手机/邮箱',
        top_agent: '顶级代理',
        agentNick: '代理昵称',
        startTime: '起始日期',
        orderType: '订单类型',
        auditTime: '审核时间',
        eventDate: '活动日期',
        optionDate: '选择日期',
        optionTime: '选择时间',
        actionType: '动作类型',
        topNick: '顶级代理昵称',
        app_download: 'app下载',
        KoreanNotice: '韩文公告',
        delegateType: '委托类型',
        positionType: '仓位类型',
        GermanNotice: '德语公告',
        LightMode: '浅色模式',
        DarkMode: '深色模式',
        BannerImage: 'Banner图片',
        InvitePoster: '邀请海报',
        select_group: '选择分组',
        delete_label: '删除标签',
        role_account: '角色账号',
        no_commission: '不返佣金',
        RussianNotice: '俄语公告',
        EnglishNotice: '英文公告',
        agent: '上级代理ID/用户名',
        user_equipment: '用户设备',
        topAgentUID: '上级代理UID',
        JapaneseNotice: '日文公告',
        transactionType: '交易类型',
        topIDNick: '顶级代理ID/昵称',
        topAgentNick: '上级代理昵称',
        selectClient: '请选择客户端',
        riskControlGroup: '风控组别',
        advancedScreening: '高级筛选',
        noEliminateUser: '无剔除用户',
        advanced_filter: '高级筛选器',
        comprehensive_data: '综合数据',
        VietnameseNotice: '越南文公告',
        IndonesianNotice: '印尼语公告',
        restore_commission: '恢复返佣',
        please_new_pass: '请输入新密码',
        pleaseLinkAddr: '请输入链接地址',
        label_deletelabel: '标签/删除标签',
        topIDtopNick: '顶级代理ID/顶级昵称',
        pleaseEnterTheContent: '请输入内容',
        pleaseLanguageType: '请选择语言类型',
        pleaseBannerType: '请选择Banner类型',
        pleaseSystemColorMode: '请选择系统色彩模式',
        no_commission_restore: '不返佣金/恢复',
        tbxzhqx: '提币限制/取消',
        ChineseSimplifiedNotice: '中文简体公告',
        ChineseTraditionalNotice: '中文繁体公告',
        please_enter_group_name: '请输入分组名称',
        support_English_digital: '只支持英文和数字',

        // 2021/11/30 新增
        User: '用户',
        Drop: '空投',
        System: '系统',
        Normal: '正常',
        Korean: '韩语',
        Top_up: '充值',
        RollOut: '转出',
        English: '英文',
        Shift_to: '转入',
        Japanese: '日语',
        Issued: '已发放',
        Expired: '已过期',
        Stop_loss: '止损',
        The_agent: '代理',
        Stay_out: '待发放',
        To_audit: '待审核',
        Registered: '注册',
        Strong_flat: '强平',
        Market_price: '市价',
        First_prize: '一等奖',
        Third_prize: '三等奖',
        limited_price: '限价',
        Add_notes: '添加备注',
        Fiat_deal: '法币交易',
        Not_trigger: '未触发',
        Check_surplus: '止盈',
        Fiat_sold: '法币卖出',
        Mention_money: '提币',
        To_put_money: '待放币',
        Unauthorized: '未认证',
        Ban_unwind: '禁止平仓',
        Second_prize: '二等奖',
        Has_triggered: '已触发',
        In_the_export: '导出中',
        Reward_drop: '空头奖励',
        Market_orders: '市价单',
        Into: '转入(从资产账户)',
        API_to_unwind: 'API平仓',
        Modify_email: '修改邮箱',
        Has_refused_to: '已拒绝',
        Add_the_agent: '添加代理',
        Check_surplus: '止盈平仓',
        For_the_payment: '待付款',
        Have_to_account: '已到账',
        C2C_settlement: 'C2C结算',
        Export_success: '导出成功',
        Export_failure: '导出失败',
        Have_been_frozen: '已冻结',
        Ban_on_trading: '禁止交易',
        Banned_logging: '禁止登陆',
        Has_put_the_coin: '已放币',
        Stop_positions: '止损平仓',
        To_clinch_a_deal: '待成交',
        Korean_Banner: '韩文Banner',
        Open_the_export: '开仓导出',
        In_recent_7_days: '最近7天',
        Korean_Banner: '韩文Banner',
        Paid_commission: '发放返佣',
        Strong_flat_sheet: '强平单',
        Trigger_failure: '触发失败',
        A_key_positions: '一键平仓',
        Check_full_stop: '止盈止损',
        Plan_to_entrust: '计划委托',
        Users_to_unwind: '用户平仓/',
        Allowed_to_trade: '允许交易',
        Google_Captcha: '谷歌验证码',
        Englist_Banner: '英文Banner',
        Remove_the_agent: '解除代理',
        Allowed_to_login: '允许登陆',
        Strong_flat_back: '强平退回',
        Opening_charge: '开仓手续费',
        Closing_charge: '平仓手续费',
        The_average_user: '普通用户',
        Roll_out: '转出(到资产账户)',
        System_to_unwind: '系统平仓',
        System_refuse_to: '系统拒绝',
        KYC1_application: 'KYC1申请',
        KYC2_application: 'KYC2申请',
        Market_positions: '市价平仓',
        Has_been_cancelled: '已取消',
        Remove_the_agent: '解除代理',
        Unwind_to_cancel: '平仓撤销',
        Englist_Banner: '英文Banner',
        Commission_income: '佣金收入',
        C2C_Other_uses: 'C2C其他用途',
        Vietnamese_Banner: '越南文Banner',
        Indonesian_Banner: '印尼文Banner',
        Japanese_Banner: '日文Banner',
        Unwind_the_export: '平仓导出',
        Campaign_spending: '活动支出',
        Set_up_your_email: '设置邮箱',
        Commission_refund: '佣金退款',
        API_open_positions: 'API开仓',
        In_recent_14_days: '最近14天',
        In_recent_30_days: '最近30天',
        Conditions_unwind: '条件平仓',
        All_clinch_a_deal: '全部成交',
        Some_clinch_a_deal: '部分成交',
        Trades_export: '交易平仓 导出',
        Poundage_export: '手续费 导出',
        With_a_single_stop: '带单止损',
        Platform_to_refuse: '平台拒绝',
        Chinese_simplified: '中文简体',
        Chinese_traditional: '中文繁体',
        With_a_single_check: '带单止盈',
        Channel_new_users: '渠道新用户',
        Adjustment_margin: '调整保证金',
        Review_mention_money: '审核提币',
        The_agent_commission: '代理佣金',
        Market_price_is_open: '市价开仓',
        Liquidation_cost: '爆仓清算费用',
        Withhold_commissions: '预扣佣金',
        Activities_to_reward: '活动奖励',
        User_name_Settings: '用户名设置',
        Invite_the_commission: '邀请佣金',
        With_single_warehouse: '带单平仓',
        Face_authentication: '人脸认证中',
        Ban_on_open_positions: '禁止开仓',
        Set_fund_password: '设置资金密码',
        Identity_verification: '身份审核',
        Documentary_commission: '跟单返佣',
        Prohibit_mention_money: '禁止提币',
        Cancellation_of_plan: '计划单撤销',
        Withdrawal_application: '提现申请',
        Individual_new_users: '散客新用户',
        IOS_Download_speed: 'IOS-极速下载',
        IOS_Local_download: 'IOS-本地下载',
        User_assets_export: '用户资产 导出',
        Re_enable_the_agent: '重新启用代理',
        Withdrawal_commission: '提币手续费',
        Images_to_be_uploaded: '待上传图片',
        Fiat_order_received: '法币订单到账',
        Administrator_release: '管理员放币',
        Transaction_commission: '交易手续费',
        Capital_cost_export: '资金费用 导出',
        Change_fund_Password: '修改资金密码',
        Allowed_to_mention_money: '允许提币',
        Fund_password_setting: '资金密码设置',
        Profit_and_loss_of_income: '盈亏收入',
        Manual_KYC_application: '人工KYC申请',
        Set_mobile_phone_number: '设置手机号',
        Login_Password_Setting: '登录密码设置',
        Binding_payment_method: '绑定支付方式',
        Trading_assets_export: '交易资产 导出',
        Retrieve_login_password: '找回登录密码',
        Agency_commission_Award: '代理佣金奖励',
        Android_Download_speed: '安卓-极速下载',
        Android_Local_download: '安卓-本地下载',
        Setting_a_Login_Password: '设置登录密码',
        With_a_single_open_positions: '带单开仓',
        No_deal_has_been_withdrawn: '未成交已撤',
        No_deal_has_been_withdrawn: '未成交已撤',
        Unwinding_of_profit_and_loss: '平仓盈亏',
        Adding_Label_Information: '添加标签信息',
        Trading_frequency_export: '交易频次 导出',
        Google_Captcha_Settings: '谷歌验证码设置',
        Modifying_User_Information: '修改用户信息',
        Documentary_assets_export: '跟单资产 导出',
        Face_authentication_passed: '人脸认证通过',
        Changing_Mobile_phone_Number: '修改手机号',
        Passed_the_platform_review: '平台审核通过',
        On_chain_withdrawal_refused: '链上提币拒绝',
        Chinese_Simplified_Banner: '中文简体Banner',
        Platform_account_withdrawal: '平台账户提币',
        Face_authentication_failure: '人脸认证失败',
        Changing_the_Login_Password: '修改登录密码',
        Chinese_Simplified_Banner: '中文简体Banner',
        Modifying_label_Information: '修改标签信息',
        Invitation_commission_Award: '邀请佣金奖励',
        Chinese_traditional_Banner: '中文繁体Banner',
        Chinese_traditional_Banner: '中文繁体Banner',
        Transfer_to_assets_account: '划转到资产账户',
        Analog_disk_reduces_assets: '模拟盘减少资产',
        Trade_open_positions_export: '交易开仓 导出',
        Set_up_the_Google_validator: '设置谷歌验证器',
        With_single_warehouse_export: '跟单平仓 导出',
        Modify_the_Google_validator: '修改谷歌验证器',
        There_was_no_trading_on_the_3_day: '3日无交易',
        There_was_no_trading_on_the_5_day: '5日无交易',
        There_was_no_trading_on_the_7_day: '7日无交易',
        Platform_account_charging_coins: '平台账户充币',
        Transfer_to_a_trading_account: '转入到交易账户',
        Transfer_to_the_asset_account: '转入到资产账户',
        Analog_disk_reclaiming_assets: '模拟盘补领资产',
        Analog_disk_supplement_assets: '模拟盘补充资产',
        The_proxy_directly_pushes_users: '代理直推用户',
        There_was_no_trading_on_the_15_day: '15日无交易',
        Has_been_open_unauthorized: '已开启<br/>(未认证)',
        Face_information_authentication: '人脸信息认证中',
        Documentary_open_positions_export: '跟单开仓 导出',
        Transfer_out_to_the_asset_account: '转出到资产账户',
        Part_of_the_deal_has_been_withdrawn: '部分成交已撤',
        Platform_cash_withdrawal_commission: '平台提币手续费',
        Follow_up_open_derivation_export: '跟单开平导出 导出',
        Closed_authentication_success: '已关闭<br/>(认证成功)',
        Transfer_it_out_to_the_trading_account: '转出到交易账户',
        On_chain_withdrawal_has_been_submitted: '链上提币已提交',
        Face_authentication_failed_Procedure: '人脸信息认证失败',
        Authentication_of_identity_information: '身份信息认证中',
        The_face_information_is_authenticated: '人脸信息认证通过',
        Has_been_open_in_the_authentication: '已开启<br/>(认证中)',
        Has_been_open_authentication_failed: '已开启<br/>(认证失败)',
        Trade_opening_and_closing_export_export: '交易开平仓导出 导出',
        High_frequency_User_Identification_export: '高频用户识别 导出',
        High_win_rate_user_identification_export: '高胜率用户识别 导出',
        High_monetization_user_recognition_export: '高盈利用户识别 导出',
        The_identity_information_is_authenticated_Procedure: '身份信息认证通过',
        Transfer_from_asset_account_to_documentary_account: '资产账户划转到跟单账户',
        Transfer_from_documentary_account_to_asset_account: '跟单账户划转到资产账户',
        Failed_to_authenticate_the_identity_information_Procedure: '身份信息认证失败',
        Transfer_from_trading_account_to_documentary_account: '交易账户划转到跟单账户',
        Transfer_from_documentary_account_to_trading_account: '跟单账户划转到交易账户',
        Cash_in: "兑入",
        Cash_out: "兑出",
        system_error: "系统故障",
        Fee_activity: "手续费活动",
        Rebate_rewards: "返佣奖励",
        Airdrop_rewards: "空投奖励",
        Liquidation_fee: "爆仓手续费",
        Source_of_currency: '来源币种',
        Full_warehouse_wear: "全仓穿仓",
        The_target_currency: '目标币种',
        Marketing_activities: "营销活动",
        Currency_exchange_fee: "兑币手续费",
        Transfer_from_asset_account: "从资产账户转入",
        Transfer_from_currency_exchange_account: "从兑币账户转入",
        Abnormal_asset_deduction_asset: "异常资产扣除(用户资产账户)",
        Abnormal_asset_deduction_trading: "异常资产扣除(用户交易账户)",
        Transfer_from_deposit_and_withdrawal_account: "从充提账户转入",
        Transfer_out_to_deposit_and_withdrawal_account: "转出到充提账户",
        Abnormal_asset_deduction_documentary: "异常资产扣除(用户跟单账户)",

        // 2022/03/13 新增
        Take_back: '收回',
        Not_at_the: '未开始',
        To_receive: '待领取',
        Get_the_time: '领取时间',
        Have_to_receive: '已领取',
        Give_gold_type: '赠金类型',

        // 2022/03/14 新增
        Margin_increase: '增加保证金',
        Give_gold_failure: '赠金过期',
        Margin_reduction: '减少保证金',
        Give_gold_recovery: '赠金回收',
        Give_gold_to_receive: '赠金领取',
        Liquidation_commission: '爆仓结算手续费',
        Deduction_of_abnormal_assets: '异常资产扣除',
        Opening_and_closing_time: '开平仓时间＜',

        // 2022/03/28 新增
        By_warehouse_Points_storehouse: '逐仓-分仓',
        All_warehouse_Points_storehouse: '全仓-分仓',
        // 2021/12/22 新增
        Margin_currency: '保证金币种',

        // 2022/01/27 新增
        Types_of_profit_and_loss: '盈亏类型',

        // 2022/05/23 新增
        Buy: '买',
        Sell: '卖',
        Entrust: '委托',
        Withdraw: '提现',
        Trading: '交易对',
        Not_traded: '未成交',
        Delegate_status: '委托状态',
        Temporary_order: '临时订单',
        Transaction_fee: '交易费用',
        Increase_margin: '增加保证金',
        Margin_reduction: '减少保证金',
        Fiat_order_receipt: '法币订单收币',
        Transaction_transfer_in: '交易转入',
        Transaction_transfer_out: '交易转出',
        Conditional_order_status: '条件单状态',
        Transaction_Fees_Taker: '交易手续费(taker)',
        Transaction_Fees_Maker: '交易手续费(maker)',
        Currency_exchange_exchange: '币币兑换(兑入)',
        Transfer_to_trading_account: '划转到交易账户',
        Currency_exchange_exchange_out: '币币兑换(兑出)',
        Transfer_in_from_asset_account: '转入(从钱包账户)',
        Transfer_out_to_the_asset_account: '转出(到钱包账户)',
        Currency_exchange_exchange_and_refund: '币币兑换(兑出退还)',
        Transfer_USD_contract_account_to_asset_account: 'USD合约账户划转到资产账户',
        Transfer_from_spot_trading_account_to_asset_account: '现货交易账户划转到资产账户',
        The_asset_account_is_transferred_to_the_USD_contract_account: '资产账户划转到USD合约账户',
        The_asset_account_is_transferred_to_the_spot_trading_account: '资产账户划转到现货交易账户',
        // 2022/06/01 新增
        Coin_margined_contracts_open_cloes_export: '币本位合约开平仓 导出',
        Coin_margined_contracts_user_forced_balance_export: '币本位合约用户强平数据 导出',

        // 2022/06/21 新增
        Bonus_expired: '赠金失效',
        // 2022/7/14
        find_administrator: '查找管理员',
        agent_uid: '请输入管理员UID',
        In_recent_7_days: '最近7天',
        // 2025/05/24
        Simplified: '简体中文',
        Traditional: '繁体中文',
        Vietnamese: '越南语',
        Indonesian: '印尼语',
        Russian: '俄语',
        German: '德语',
    },

    // 项目所有的 表头
    tableHeader: {
        uid: 'UID',
        note: '备注',
        link: '链接',
        time: '时间',
        title: '标题',
        state: '状态',
        number: '数量',
        content: '内容',
        leverage: '杠杆',
        contract: '合约',
        operation: '操作',
        maxPos: '最大持仓',
        userName: '用户名',
        risk: '风险率增减',
        KYCstate: 'KYC状态',
        backTime: '返佣期限',
        superiorID: '上级ID',
        moneyReat: '资金费率',
        withSingle: '带单身份',
        minSomeBad: '最小点差',
        maxSomeBad: '最大点差',
        releaseTime: '发布时间',
        maxLeverage: '最大杠杆',
        actionObject: '操作对象',
        poundageReat: '手续费率',
        lastLoginIP: '最后登录IP',
        maxPosition: '最大持仓量',
        operationType: '操作类型',
        clinchDealTime: '成交时间',
        toApplyForTime: '申请时间',
        processingTime: '处理时间',
        withdrawalState: '提币状态',
        downShelvesTime: '下架时间',
        countriesRegions: '国家地区',
        lastLoginTime: '最后登录时间',
        orderSerialNumber: '订单编号',
        superiorUsername: '上级用户名',
        maxOrderQuantity: '单笔最大下单量',
        minOrderQuantity: '单笔最小下单量',
        IP: 'IP',
        PNL: 'PNL',
        moreFlat: '平多',
        net_PNl: '净PNL',
        IP_addr: 'IP地址',
        poundage: '手续费',
        sale: '卖单(开空)',
        unwindPri: '平仓价',
        orderID: '计划单ID',
        sidesJersey: '平空',
        float_PNL: '浮动PNL',
        positionID: '持仓ID',
        flatPrice: '强平价格',
        buyMore: '买单(开多)',
        entrustNum: '委托数量',
        updateTime: '更新时间',
        submitTime: '提交时间',
        triggerPrice: '触发价',
        cancelTime: '取消时间',
        moreFlatPNL: '平等PNL',
        currentPrice: '当前价',
        combinedPNL: '合计PNL',
        moreFlatNum: '平多数量',
        averageOpen: '开仓均价',
        unwindPrice: '平仓价格',
        StopLossPrice: '止损价',
        orderSource: '订单来源',
        accountType: '账户类型',
        entrustTime: '委托时间',
        triggerType: '触发类型',
        triggerTime: '触发时间',
        contractName: '合约名称',
        checkFullPrice: '止盈价',
        entrustPrice: '委托价格',
        triggerState: '触发状态',
        sidesJerseyPNL: '平空PNL',
        clinchDealPrice: '成交价',
        clinchDealNum: '成交数量',
        sidesJerseyNum: '平空数量',
        clinchDealState: '成交状态',
        positionAverage: '持仓均价',
        openPositionsPrice: '开仓价',
        transactionNumber: '交易编号',
        openPositionsTime: '开仓时间',
        stopLossConditions: '止损条件',
        entrustedClientIP: '委托客户IP',
        sidesJerseyPoundage: '平空手续费',
        combinedPNLPoundage: '合计手续费',
        clinchDealAveragePrice: '成交均价',
        checkSurplusConditions: '止盈条件',
        longPositionsPoundage: '多仓手续费',
        equipmentIdentificationCode: '设备识别码',
        buyingSellingSingleDifferential: '买卖单差',
        endTime: '结束时间',
        today_pnl: '当日PNL',
        startTime: '开始时间',
        moneyCost: '资金费用',
        centCommission: '分佣',
        creationTime: '创建时间',
        last_week_pnl: '上周PNL',
        customer_net_gold: '客户净入金',
        documentaryAvailable: '跟单可用',
        last_week_poundage: '上周手续费',
        customer_total_out: '客户总出金',
        customer_total_gold: '客户总入金',
        history_pnl: '历史PNL(截至上周日)',
        this_week_pnl: '本周PNL(起始本周一)',
        documentaryAccountRights: '跟单账户权益',
        documentaryFrozenMargin: '跟单冻结保证金',
        this_week_poundage: '本周手续费(起始本周一)',
        customer_contract_account_rights: '客户合约账户权益',
        amount: '金额',
        pieceNum: '张数',
        equipment: '设备',
        available: '可用',
        aggregate: '总计',
        net_gold: '净入金',
        difference: '差额',
        total_out: '总出金',
        total_gold: '总入金',
        unwindNum: '平仓次数',
        changeNum: '变化数量',
        equipmentID: '设备ID',
        issueTime: '发放时间',
        tradersID: '交易者ID',
        startDate: '开始日期',
        transferUID: '划转UID',
        rewardType: '奖励类型',
        rollOutAddr: '转出地址',
        by_the_time: '发起时间',
        uploadPhotos: '上传照片',
        moneyAccount: '账户资产',
        transactionAmo: '交易金',
        availableNum: '可用数量',
        activityName: '活动名称',
        activityState: '活动状态',
        userpoundage: '用户手续费',
        transactionNum: '交易数量',
        settlementTime: '结算时间',
        commissionTime: '返佣时间',
        statisticalTime: '统计时间',
        operationSystem: '操作系统',
        commissionMoney: '返佣金额',
        transactionTime: '交易时间',
        activityDescribe: '活动描述',
        clinchDealRecord: '成交记录',
        clinchDealSerial: '成交编号',
        withdrawalAmount: '提现数量',
        transactionAmount: '交易金额',
        getRidOfCommission: '去除返佣',
        platformLiabilities: '平台负债',
        alreadySentReward: '已发放奖励',
        eachIssueEndTime: '每期结束时间',
        merchantsExchangeRate: '商家汇率',
        moneyAccountRights: '资金账户权益',
        moneyAccountFreeze: '资金账户冻结',
        topAgentUserName: '上级代理用户名',
        eachIssueStartTime: '每期开始时间',
        Maker_poundageReat: 'Maker手续费率',
        Taker_poundageReat: 'Taker手续费率',
        lastTransactionTime: '最后交易时间',
        walletAccountBalance: '钱包账户余额',
        eachIssueLotteryTime: '每期开奖时间',
        jackpotHighestCeiling: '奖池最高上限',
        moneyAccountAvailable: '资金账户可用',
        userTransactionAmount: '用户交易金额',
        implementedProfitandLoss: '已实现盈亏',
        remainingAvailableBalance: '剩余可用余额',
        injectionPoundageProportion: '注入手续费比例',
        P1: 'P1',
        P2: 'P2',
        ID: 'ID',
        level: '级别',
        dayLive: '日活',
        regNum: '注册人数',
        regWay: '注册方式',
        timeKYC: 'KYC时间',
        agentPNl: '合约PNL',
        riskDegree: '风险度',
        labalName: '标签名称',
        profitnum: '盈利次数',
        historyOdds: '历史胜率',
        minRisk: '最低风险率增减',
        documentaryPNL: '跟单PNL',
        profitNum: '盈利人数/占比',
        floatProfitLoss: '浮动盈亏',
        num_login_IP: '此IP登录次数',
        maxSingleLoss: '最大单笔亏损',
        oddsUnwindNum: '胜率平仓次数',
        lastLoginTime: '最近登录时间',
        num_login_total: '总登录次数',
        transaction_number: '交易次数',
        occupancyDeposit: '占用保证金',
        maxSingleProfit: '最大单笔盈利',
        openPositions_number: '开仓次数',
        singleAverageTime: '单张平均时间',
        documentaryPoundage: '跟单手续费',
        ershisi_h_net_pnl: '最近24H净PNL',
        num_login_accounts: '登录账号数量',
        num_logged_devices: '登录设备数量',
        openPositions_pieceNum: '开仓数量', // 2022/02/08 修改
        averageTradingCycle: '平均交易周期',
        currentPositionValue: '当前持仓价值',
        num_transactions_total: '总交易次数',
        transactionsPerCapita: '人均交易次数',
        number_logged_devices: '登录设备个数',
        ershisi_h_tradingNum: '最近24H交易次数',
        num_labeled_observations: '标记观察次数',
        odds: '胜率',
        the_name: '姓名',
        handlers: '操作者',
        priority: '优先级',
        addpeople: '添加人',
        addTime: '添加时间',
        agentUID: '代理UID',
        serialNumber: '编号',
        error_code: '错误码',
        deletenote: '删除备注',
        deleteTime: '删除时间',
        deletepeople: '删除人',
        handlersID: '操作者ID',
        APP_version: 'APP版本',
        on_the_user: '日活用户',
        weeklyActiveUsers: '周活用户',
        monthlyActiveUsers: '月活用户',
        NumberOfTransactions: '交易人数',
        PNL_interval: 'PNL区间',
        categoryName: '组别名称',
        rate_of_return: '盈利率',
        trading_volume: '交易量',
        odds_interval: '胜率区间',
        error_content: '错误内容',
        executionResult: '执行结果',
        actionObjectID: '操作对象ID',
        role_of_grouping: '角色分组',
        net_PNL_interval: '净PNL区间',
        whitelist_Status: '白名单状态',
        user_equipment: '用户使用设备',
        poundage_interval: '手续费区间',
        net_gold_interval: '净入金区间',
        total_gold_interval: '总出金区间',
        whether_desensitization: '是否脱敏',
        total_golden_interval: '总入金区间',
        capital_cost_interval: '资金费用区间',
        rate_of_return_interval: '盈利率区间',
        poundage_net_gold: '手续费占净入金比率',
        occupancy_deposit_interval: '占用保证金区间',
        poundage_net_gold_interval: '手续费占净入金比率区间',
        buy: '买入',
        open: '开启',
        sell: '卖出',
        sells: '出售',
        group: '组别',
        unwind: '平仓',
        freeze: '冻结',
        order: '计划单',
        withSin: '带单',
        ranking: '名次',
        restore: '恢复',
        fivefile: '5档',
        purchase: '购买',
        positions: '开仓',
        unlimited: '不限',
        documentary: '跟单',
        effect_of: '生效中',
        by_warehouse: '逐仓',
        all_warehouse: '全仓',
        has_failure: '已失效',
        sell_empty: '卖出开空',
        check_single: '止盈单',
        sell_more_flat: '卖出平多',
        stop_loss_orders: '止损单',
        open_to_buy_more: '买入开多',
        buy_sides_jersey: '买入平空',
        contract_account_balance: '合约账户余额',
        balance_documentary_account: '跟单账户余额',

        // 2021/12/16 新增
        The_price: '价格',
        turnover: '交易额',
        Their_own_PNL: '自身PNL',
        Independent_PNL: '自主PNL',
        PNL_directly_under: '直属PNL',
        Self_handling_fee: '自身手续费',
        Direct_commission: '直属手续费',
        Keep_pushing_the_num: '直推人数',
        transactionTime_S: '交易时间(秒)',
        Independent_commission: '自主手续费',
        Num_of_direct_transactions: '直属交易次数',
        Max_spread_over_trading_time: '交易时间内最大点差',
        Min_point_spread_over_trading_time: '交易时间内最小点差',
        // 2021/12/21 new add
        clear_fee: '清算费',
        through_pay: '穿仓支出',

        // 2022/02/24 新增
        Dont_tip: '不提示',
        Update_way: '更新方式',
        Version_number: '版本号',
        Prompt_update: '提示更新',
        Forced_update: '强制更新',
        Prompt_content: '提示内容',

        // 2022/02/25 新增
        Korean: '韩文',
        Vietnam: '越南',
        Chinese: '中文',
        English: '英文',
        Indonesia: '印尼',
        Traditional: '繁体',
        Edit_version: '编辑版本',
        Current_link: '当前链接',
        Download_speed: '极速下载',
        Local_download: '本地下载',
        Download_the_way: '下载方式',
        Alternate_download: '备用下载',
        Download_speed_two: '极速下载2',
        Display_version_number: '显示版本号',
        Updated_version_number: '更新版本号',

        // 2022/03/13 新增
        With_gold: '赠金',
        All_users: '全部用户',
        Proxy_user: '代理用户',
        Participants: '参与者',
        Active_users: '活跃用户',
        Use_the_type: '使用类型',
        Top_up_rebate: '充值返利',
        Recovery_time: '回收时间',
        To_book_value: '到账金额',
        Invite_friends: '邀请好友',
        The_time_limit: '时间限制',
        Expiration_time: '过期时间',
        Friend_recharge: '好友充值',
        Overdue_time: '领取过期时限',
        Non_proxy_user: '非代理用户',
        Last_Updated: '最后更新时间',
        Recycling_amount: '回收金额',
        Give_name_of_gold: '赠金名称',
        The_activation_time: '激活时间',
        Give_gold_description: '赠金描述',
        Equipment_information: '设备信息',
        For_the_first_time_trade: '首次交易',
        Top_up_for_the_first_time: '首次充值',
        The_activation_time_limit: '激活时限',
        Effective_trading_days: '有效交易天数',
        Effective_amount_limit: '有效金额限制',
        Balance_of_users_bonus: '用户赠金余额',
        Transaction_recovery_time: '交易回收时限',
        Novice_transaction_volume_meets_the_standard: '新手交易量达标',

        // 2022/03/14 新增
        The_amount: '发放金额',
        Give_gold_balance: '赠金余额',
        With_gold_available: '赠金可用',
        Total_use_of_bonus: '赠金总使用',
        Bonus_handling_fee: '赠金手续费',
        Bonus_settlement_fee: '赠金清算费用',
        The_bonus_has_been_used: '已使用赠金',
        Amount_of_use_of_bonus: '赠金使用金额',
        Accumulated_credit_bonus: '累计入账赠金',
        Available_balance_of_bonus: '赠金可用余额',
        Free_use_of_gold_warehouse: ' 赠金平仓使用',
        Get_the_total_amount_of_bonus: '获取赠金总金额',
        Amount_of_money_donated_by_users: '用户赠金金额',
        Time_stamp_of_last_open_position: '最后一次开仓时间戳',

        // 2022/03/16 新增
        Top_up: '充值≥',
        Registration_deadline: '注册时限',
        Administrator_issue: '管理员发放',
        Bonus_experience_money: '奖励体验金',
        Administrator_recycling: '管理员回收',
        Open_and_close_interval: '开平仓间隔',
        Activity_expiration_time: '活动过期时间',

        // 2022/04/02 新增
        Nick_name: '昵称',
        // 2021/12/29 新增
        Strong_parity: '强平价',
        Trigger_price: '触发价格',
        Executive_price: '执行价格',
        Position_number: '持仓编号',

        // 2022/01/27 新增
        Balance: '余额',
        Transfer: '划转',
        Profit_and_loss: '盈亏',

        // 2022/04/29 新增
        net_into: '净转入',
        net_transfer: '净转出',

        // 2022/05/12 新增
        Total_assets: '总资产',
        Total_freeze: '总冻结',
        Total_available: '总可用',
        // 2022/05/19 新增
        Total_spot_sale: '现货总卖出',
        Total_spot_purchase: '现货总买入',
        Rights_and_interests_spot: '现货权益',

        // 2022/05/23 新增
        Spread: '差价',
        Sales: '卖出额',
        Volume: '成交量',
        Order_ID: '订单ID',
        Turnover: '成交额',
        Deal_done: '已成交',
        Buy_arg: '买入均价',
        Sell_arg: '卖出均价',
        Not_traded: '未成交',
        Entrust_ID: '委托ID',
        Buy_amount: '买入量',
        Buy_balnce: '买入额',
        Buy_fee: '买入手续费',
        Sell_amount: '卖出量',
        Sell_balnce: '卖出额',
        Sell_fee: '卖出手续费',
        Trading_area: '交易区',
        Turnover_USDT: '成交额',
        Spot_Freeze: '现货冻结',
        Buying_fee: '买入手续费',
        Total_transfer: '总转入',
        Total_outgoing: '总转出',
        Selling_fee: '卖出手续费',
        Purchase_amount: '买入额',
        Purchase_fee: '购买手续费',
        Lock_up_assets: '锁定资产',
        Stock_available: '现货可用',
        Purchasing_price: '购买金额',
        Commission_amount: '委托金额',
        Purchase_quantity: '购买数量',
        Transaction_details: '成交明细',
        Spot_account_freeze: '现货账户冻结',
        Spot_account_balance: '现货账户余额',
        Third_party_order_ID: '第三方订单ID',
        Average_purchase_price: '平均购买价',
        Subordinate_rebate_data: '下级返佣数据',
        Estimated_transaction_price: '预计成交价',
        Number_Subordinate_Registrations: '下级注册数量',

        // 2022/06/02 新增
        Transaction_value: '成交价值',

        // 2022/06/20 新增
        serial_number: '序号',
        Serial_number: '流水编号',
        Time_of_occurrence: '发生时间',
        Number_of_activities: '活动数量',
        The_statistical_date: '统计日期',
        Payout_coin_reward: '支出币奖励',
        Expenditure_bonus: '支出赠金奖励',
        The_total_amount_of_spending: '支出总额',
        Remaining_account_equity: '剩余账户资产',
        Remaining_frozen_quantity: '剩余冻结数量',
        // 2022/7/14
        channel_name: '渠道名称',
        agent_name: '管理员用户名',
        tatalreg: '累计注册人数',
        totaltrading: '累计交易额',
        yestdayreg: '昨日注册人数',
        yestdaytrading: '昨日交易额',
        yestdaycommiss: '昨日手续费',
        weekreg: '本周注册人数',
        weektrading: '本周交易额',
        weekcommiss: '本周手续费',
        mounthreg: '本月注册人数',
        mounthtrading: '本月交易额',
        mounthcommiss: '本月手续费',
        isopenagent: '是否可以提升代理',
        Opening_time: '开通时间',
        //2022.8.3
        todayCloseTotalProfit: '当日平仓累计盈亏',
        total_PNl: '总PNL',
        //2025.5.6
        evmaddress: 'EVM 地址',
        pseudoUID: '伪 UID'
    },

    // 项目所有的表格
    forms: {
        no: '否',
        day: '天',
        yes: '是',
        allow: '允许',
        account: '账号',
        someBad: '点差',
        client: '客户端',
        notAllow: '不允许',
        buyFiat: '可购买法币',
        skipLinks: '跳转链接',
        sellFiat: '可出售法币',
        pleaseUID: '请输入UID',
        traders: '可申请交易员',
        allUser: '显示所有用户',
        loginStatus: '登录状态',
        selectLabel: '选择标签',
        rakeBackTime: '发放期限',
        refuseReason: '拒绝原因',
        FirstPrize: '一等奖占比',
        ThirdPrize: '三等奖占比',
        canProAgent: '可提升代理',
        SecondPrize: '二等奖占比',
        grouping_name: '分组名称',
        needcode: '是否需要验证码',
        assetQuery: '可看资产查询',
        selectContract: '选择合约',
        uploadPictures: '上传图片',
        resetCRMPass: '重置CRM密码',
        catchPlate: '是否开通模拟盘',
        proStraightPush: '提升非直推',
        transactionStatus: '交易状态',
        APIpermissions: 'API管理权限',
        webBackRate: '前端显示返佣比例',
        seeHomeData: '是否可见首页数据',
        mentionMoneyStatus: '提币状态',
        pleaseSelectLabel: '请选择标签',
        point_to_link: 'BANNER指向链接',
        tradingToOneself: '自交易返自己',
        superiorInviteCode: '上级邀请码',
        desensitization: '非直推用户脱敏',
        toViewProfitLoss: '是否可查看盈亏',
        pleaseSelectContract: '请选择合约',
        mentionMoneyConfirm: '提币二次确认',
        positionMonitoring: '可查看持仓监控',
        commissionPercentage: '手续费分佣比例',
        inputContent: '可输入多个代理ID，“,”隔开',
        othersAPIpermissions: '其用户API管理权限',
        poundageMonitoring: '是否可查看手续费监控',
        // 2021/12/29 new add
        reGetcode: '重新获取',
        safety_verify: '安全验证',
        input_format_error: '输入格式错误',
        safety_verify_email: '用于安全验证的邮箱',
        input_re_code: '请输入 {email} 收到的验证码',

        // 2022/02/24 新增
        Optional: '选填',
        Mandatory: '必填',
        Standby_address: '备用地址',
        Download_address: '下载地址',
        Text_version_number: '文字版本号',
        Korean_speed_address: '韩文极速地址',
        Digital_version_number: '数字版本号',
        Chinese_speed_address: '中文极速地址',
        English_speed_address: '英文极速地址',
        Vietnam_speed_address: '越南极速地址',
        Korean_standby_address: '韩文备用地址',
        Vietnam_standby_address: '越南备用地址',
        Chinese_standby_address: '中文备用地址',
        Prompt_conten_In_Chinese: '提示内容中文',
        Prompt_conten_In_English: '提示内容英文',
        English_standby_address: '英文备用地址',
        Indonesia_speed_address: '印尼极速地址',
        Indonesia_standby_address: '印尼备用地址',

        // 2022/02/25 新增
        Disable: '停用',
        To_enable: '启用',
        Select_client: '选择客户端',
        Select_language: '选择语言',
        Select_download_way: '选择下载方式',
        // 2022/03/13 新增
        Input_the_UID: '输入UID',
        ZHInstructions: '中文说明',
        ENInstructions: '英文说明',
        TCInstructions: '繁中说明',
        KOInstructions: '韩文说明',
        VNInstructions: '越南说明',
        IDInstructions: '印尼说明',
        ZHWithGoldName: '中文赠金名称',
        ENWithGoldName: '英文赠金名称',
        TCWithGoldName: '繁中赠金名称',
        KOWithGoldName: '韩文赠金名称',
        VNWithGoldName: '越南赠金名称',
        IDWithGoldName: '印尼赠金名称',
        Select_the_language: '选择语言',
        Amount_of_grant_issued: '发放赠金金额',
        Effective_trading_volume: '有效交易额≥',
        Recovery_of_amount_of_the_bonus: '回收赠金金额',
        Time_limit_for_recovery_transaction: '回收交易时限',
        Type_of_the_user_who_participates_in_the_event: '参加活动用户类型',

        // 2022/02/10 新增
        USDT_contract: 'USDT合约',
        USD_contract: '币本位合约', /// 2022/05/12 修改
        select_contract_Type: '选择合约类型',
    },

    // 项目中所有的弹框
    dialog: {
        name: '名',
        surname: '姓',
        detail: '详情',
        language: '语言',
        reviewer: '审核人',
        setHASH: '设置HASH',
        IDnumber: '证件号码',
        currentAge: '当前年龄',
        pleaseSelect: '请选择',
        accountNum: '到账数量',
        app_version: 'app版本',
        dateOfBirth: '出生日期',
        tradingHASH: '交易HASH',
        reviewResults: '审核结果',
        jackpotAmount: '奖池金额',
        toApplyForNum: '申请数量',
        cumulativeDrop: '累计空投',
        cumulativeReward: '累计奖励',
        max_upload: '最大可上传700kb',
        submitIDPhoto: '提交证件照片',
        confirmInformation: '确认信息',
        certificationStatus: '认证状态',
        setJackpotAmount: '设置奖池金额',
        Image_size: '图片尺寸：1029*383',
        cumulativePoundage: '累计手续费',
        cumulativeWithdrawal: '累计提币',
        expectCanWithdrawal: '预计可提币',
        cumulativeFiatBuy: '累计法币买入',
        cumulativeChargeMoney: '累计充币',
        cumulativeFiatSell: '累计法币卖出',
        cumulativeUnwindPNL: '累计平仓PNL',
        el_upload__text: '将文件拖到此处，或',
        Image_format: '图片格式必须为png格式',
        cumulativeCapitalCost: '累计资金费用',
        cumulativeWinningAmount: '累计获奖金额',
        poundageCommissionDetail: '手续费返佣详情',
        enableQualification: '是否确认启用代理商资格',
        removeQualification: '是否确认解除代理商资格',
        endGreaterThanstart: '结束时间须 大于 开始时间',
        startLessThanendTime: '开始时间须 小于 结束时间',
        tooltip_content: '开奖时间须在 开始-结束 时间区间内',
        confirmWhetherResetCertification: '请确认是否重置当前认证',
        longContent: '解除后其下方代理关系不会解除，下方代理的返佣仍正常',

        // 2021/11/30 新增
        Set: '设置',
        Error: '错误',
        Prompt: '提示',
        Whether: '是否',
        Warning: '警告',
        Failure: '失败',
        Shelves: '上架',
        Level_1: '【1级】',
        The_user: '的用户',
        Successful: '成功',
        To_withdraw: '撤回',
        Mandatory: '必填项',
        The_shelves: '下架',
        The_headline: '标题为',
        Post_failure: '发布失败',
        Add_a_success: '添加成功',
        The_announcement: '该公告',
        Sure_to_delete: '确定删除',
        Whether_or_not_to: '是否要',
        Release_success: '发布成功',
        Confirm_to_add: '确认添加吗?',
        Confirm_audit: '是否确认审核',
        Reset_the_success: '重置成功',
        Whether_to_confirm: '是否确认',
        Cancelled_delete: '已取消删除',
        Delete_the_success: '删除成功',
        Confirm_deletion: '确认删除吗?',
        Length_10: '长度在 10 个字符以内',
        Uploaded_successfully: '上传成功',
        Please_enter_a_title: '请输入标题',
        Please_select_a_date: '请选择日期',
        Please_select_a_date: '请选择日期',
        Publication_cancelled: '已取消发布',
        Please_fill_in_the_UID: '请填写UID',
        Operation_is_successful: '操作成功',
        Please_upload_pictures: '请上传图片',
        Please_fill_in_remarks: '请填写备注',
        Please_select_the_time: '请选择时间',
        Modification_cancelled: '已取消修改',
        Image_insertion_failed: '图片插入失败',
        Please_check_permissions: '请勾选权限',
        Important_announcement: '的重要公告？',
        Confirm_one_click_pass: '确认一键通过?',
        Confirm_one_click_refuse: '确认将选中用户全部驳回?',
        The_login_timeout: '登录超时,请重新登录',
        Please_fill_in_the_text: '请填写文本内容',
        Confirm_to_send_this_user: '确认将此用户',
        Please_fill_in_the_title: '请填写文章标题',
        Confirm_one_click_release: '确认一键发放？',
        The_account_cannot_be_empty: '账号不能为空',
        Go_to_this_user_rebate: '去除此用户的返佣?',
        Please_select_a_group_name: '请选择分组名称',
        Please_enter_delete_remarks: '请输入删除备注',
        Whether_to_add_the_bulletin: '是否添加该公告?',
        Please_fill_in_the_group_name: '请填写分组名称',
        You_want_to_delete_a_label: '确认删除个标签吗?',
        Abnormal_changes: '变动异常，频繁高于委托卖一价',
        The_input_box_cannot_be_empty: '该输入框不能为空',
        Whether_to_delete_the_bulletin: '是否删除该公告?',
        One_price_below_the_commission: '或低于委托买一价',
        The_minimum_validity_period_is_1: '发放期限最小为1',
        Confirm_that_the_UID_is_set_to: '是否确认添加UID为：',
        The_link_is_not_formatted_correctly: '链接格式不正确',
        Are_you_sure_to_delete_the_UID: '是否确认删除UID为：',
        BANNER_Please_try_again: 'BANNER上传失败，请重新尝试',
        Please_confirm_whether_to_delete_it: '请确认是否删除"',
        The_contract_point_spread_range_is: '该合约点差区间为',
        Do_you_want_to_delete_the_heading: '是否要删除标题为:',
        Confirm_modification_of_the_bulletin: '确认修改该公告?',
        The_minimum_risk_of_the_contract_is: '该合约最低风险率为',
        Abnormal_conditions_exist: '存在异常情况，请立即查找原因',
        Confirm_resetting_the_current_users: '确认重置当前用户的',
        Please_select_the_image_to_upload: '请选择需要上传的图片',
        Confirm_to_cancel_observing_the_user: '确认取消观察此用户',
        Please_enter_the_article_title: '文章标题请输入4-50个字符',
        Contract_current_page_latest_price: '合约当前页面最新价格',
        Upload_images_in_PNG_format_only: '上传图片只能是 PNG 格式!',
        Min_less_than_Max: '单笔最小下单量不能大于单笔最大下单量!!!',
        Whether_you_want_to_delete_this_label: '是否确认删除此标签?',
        The_contract_procedures_rate_range_is: '该合约手续费率区间为',
        Select_a_language_for_the_bulletin: '请选择公告的对应语言类型',
        Please_enter_the_correct_takedown_time: '请输入正确的下架时间',
        Your_browser_does_not_support_sockets: '您的浏览器不支持socket',
        The_maximum_leverage_of_the_contract_is: '该合约杠杆最高倍数为',
        The_ratio_cannot_be_greater_than_95: '手续费分佣比例不能大于95%',
        The_maximum_fund_rate_for_the_contract_is: '该合约最高资金费率为',
        Are_you_sure_to_reset_the_account_password: '确认重置账号密码吗?',
        Chinese_BANNER_Please_try_again: '中文BANNER上传失败，请重新尝试',
        Are_you_sure_to_reset_your_Google_password: '确认重置谷歌密码吗?',
        The_maximum_open_position_of_the_contract_is: '该合约最大持仓量为',
        The_maximum_order_quantity_of_the_contract_is: '该合约最大下单量为',
        Please_jiaoyi_daochu_download: '请到‘交易查询/导出下载列表’进行下载',
        The_maximum_digit_of_the_processing_rate_is: '该合约手续费率最大小数位为',
        The_maximum_point_difference_digit_of_the_contract_is: '该合约点差最大小数位为',
        Position_greater_than_order_quantity: '最大持仓量必须大于等于单笔最大下单量!!!',
        Total_proportion_of_rewards_cannot_be_greater_than_100: '奖励总占比不能大于100%',
        Maximum_point_difference_is_greater_than_minimum_point_difference: '最大点差应大于最小点差',
        The_size_of_uploaded_image_cannot_exceed_700K: '上传图片大小不能超过 700K!',
        The_picture_is_not_up_to_standard: '上传文件的图片大小不合符标准,宽需要为640px，高需要为700px。当前上传图片的宽高分别为：',

        // 2022/02/24 新增
        Edit_IOS_new_Version: '编辑IOS新版本',
        Edit_Android_new_Version: '编辑Android新版本',

        Please_enter_text_version: '请输入文字版本号',
        Please_enter_prompt_content: '请输入提示内容',
        Please_enter_number_version: '请输入数字版本号',
        Please_enter_download_address: '请输入下载地址',
        Please_enter_Chinese_speed_address: '请输入中文极速地址',
        Please_enter_English_speed_address: '请输入英文极速地址',
        Please_enter_Chinese_standby_address: '请输入中文备用地址',
        Please_enter_English_standby_address: '请输入英文备用地址',

        // 2022/02/25 新增
        Edit_links: '编辑链接',
        To_view_links: '查看链接',
        Edit_successful: '编辑成功',
        Please_select_client: '请选择客户端',
        Please_select_language: '请选择语言',
        Please_select_download_way: '请选择下载方式',
        Please_enter_appStore_link: '请输入App Store链接',
        Please_enter_testFlight_link: '请输入TestFlight链接',
        Please_enter_googlePlay_link: '请输入Google Play链接',
        Whether_or_not_disable_this_version: '是否确认停用此版本号?',
        Whether_or_not_to_enable_this_version: '是否确认启用此版本号?',
        // 2022/03/13 新增
        Give_new_a_gold: '新建赠金',
        Give_change_a_gold: '修改赠金',
        Select_date_and_time: '选择日期时间',
        Please_select_a_language: '请选择语言',
        Modifying_the_Language_description: '修改语言描述',
        Please_select_the_active_user_type: '请选择活动用户类型',
        The_name_English_and_Chinese_is_mandatory: '中英文赠金名称为必选名称',
        Please_select_ZHWithGoldName: '请输入中文赠金名称',
        Please_select_ENWithGoldName: '请输入英文赠金名称',
        Please_select_TCWithGoldName: '请输入繁体中文赠金名称',
        Please_select_KOWithGoldName: '请输入韩文赠金名称',
        Please_select_VNWithGoldName: '请输入越南赠金名称',
        Please_select_IDWithGoldName: '请输入印尼赠金名称',
        Please_select_the_type_of_bonus: '请选择赠金类型',
        Please_enter_the_bonus: '请输入赠金',
        Please_enter_a_time_limit: '请输入时间限制',
        Please_enter_closing_times: '请输入平仓次数',
        Please_select_the_expiration_time: '请选择过期时间',
        Please_enter_valid_trading_days: '请输入有效交易天数',
        Please_enter_the_expiration_date: '请输入领取过期时限',
        Please_enter_the_activation_time_limit: '请输入激活时限',
        Please_enter_valid_transaction_amount: '请输入有效交易额',
        Please_input_the_friend_recharge_amount: '请输入好友充值金额',
        Please_enter_the_recycle_transaction_time_limit: '请输入回收交易时限',
        ZHInstructions: '请输入中文说明',
        ENInstructions: '请输入英文说明',
        TCInstructions: '请输入繁体中文说明',
        KOInstructions: '请输入韩文说明',
        VNInstructions: '请输入越南说明',
        IDInstructions: '请输入印尼说明',
        Recycling_success: '回收成功',
        Distribution_of_success: '发放成功',
        Please_enter_the_amount_of_refund: '请输入回收赠金金额',
        Please_enter_the_name_of_the_donation: '请输入赠金名称',
        Please_enter_the_amount_of_the_grant: '请输入发放赠金金额',
        Have_you_confirmed_creation_activity: '是否确认创建此次活动？',
        Confirm_the_payment_of_experience_money: '是否确认发放体验金？',
        Have_you_confirmed_modification_activity: '是否确认修改此次活动？',
        Whether_to_confirm_recovery_of_experience_money: '是否确认回收体验金？',

        // 2022/03/14 新增
        Please_enter_the_uid: '请输入UID',

        // 2022/03/16 新增
        Optional: '选填',
        The_amount_than_zero: '发放金额必须大于0',
        The_amount_than_fifty: '发放金额不能大于50',
        Recycling_amount_than_zero: '回收金额必须大于0',
        Please_enter_the_recharge_amount: '请输入充值金额',
        The_UID_you_entered_does_not_exist: '您所输入的UID不存在',
        Please_enter_the_reward_experience_money: '请输入奖励体验金',
        //  2022/7/14
        bind_channel_tip: '是否确定将此渠道绑定至用户名为“{name}”UID为“{uid}”账户下？',
        bindTips: '用户已绑定渠道，不可绑定'
    },

    // 页面所有的 button 文字
    buttons: {
        up: '上',
        IOS: 'IOS',
        WEB: 'WEB',
        down: '下',
        add: '添加',
        all: '全部',
        frame: '架',
        edit: '编辑',
        copy: '复制',
        save: '保存',
        HASH: 'HASH',
        trial: '初审',
        reset: '重置',
        audit: '审核',
        issue: '发放',
        close: '关闭',
        public: '公共',
        toView: '查看',
        remove: '解除',
        search: '搜索',
        cancel: '取消',
        delete: '删除',
        modify: '修改',
        export: '导出',
        refuse: '拒绝',
        recheck: '复审',
        summary: '汇总',
        through: '通过',
        release: '发布',
        confirm: '确认',
        android: '安卓',
        move_up: '上移',
        download: '下载',
        rejected: '驳回',
        move_down: '下移',
        placedTop: '置顶',
        determine: '确定',
        eliminate: '剔除',
        canTop: '取消置顶',
        startUsing: '启用',
        viewAll: '查看全部',
        chargeMoney: '充币',
        have_what: '已到底',
        setLabal: '设置标签',
        updLabal: '修改标签',
        delLabal: '删除标签',
        proAgent: '提升代理',
        addLabel: "添加标签",
        setSmount: '设置金额',
        new_group: '新建组别',
        has_been_to: '已到顶',
        addAccount: '添加账号',
        edit_label: '编辑标签',
        A_key_issue: '一键发放',
        cancelIssue: '取消发放',
        addBANNER: '添加BANNER',
        add_delete: '添加/删除',
        stand_up_down: '上下架',
        await_audit: '等待审核',
        audit_reset: '审核重置',
        audit_refuse: '审核拒绝',
        trial_refuse: '初审拒绝',
        seeYesterday: '查看昨日',
        cancel_watch: '取消观察',
        reset_google: '重置谷歌',
        add_grouping: '添加分组',
        confim_refuse: '确认拒绝',
        trial_through: '初审通过',
        A_key_through: '一键通过',
        A_key_refuse: '一键驳回',
        activitiesSet: '活动设置',
        clickOnUpload: '点击上传',
        change_groups: '修改分组',
        on_the_cross: '上传中...',
        audit_through: '审核通过',
        confim_through: '确认通过',
        recheck_refuse: '复审拒绝',
        reset_passwords: '重置密码',
        proTopAgent: '提升顶级代理',
        recheck_through: '复审通过',
        determineInsert: '确定插入',
        determine_modify: '确定修改',
        remove_startUsing: '解除/开启',
        modifyLeaderboard: '修改排行榜',
        add_suspected_user: '添加嫌疑用户',
        addHomeFloatingLayer: '添加首页浮层',
        risk_control_whitelist: '风控白名单',
        withdraw_remove_rebate: '撤回去除返佣',
        view_IP_user_overview: '查看IP用户概况',
        view_IP_transaction_detailsv: '查看IP交易详情',

        // 2022/02/25 新增
        Edit_new_version: '编辑版本',
        Create_new_version: '创建新版本',
        Shop_address_management: '商店地址管理',
        // 2022/03/13 新增
        Language_description: '语言描述',
        // 2021/12/22 新增
        query: '查询',
        tbxzh: '提币限制',
        qxtbxzh: '取消提币限制',
        // 2022/7/14
        bind: '绑定管理员',
        viewCurves: '查看曲线',

        // 2025/05/03
        approve: '通过',
    },

    // 登录
    login: {
        title: '登录',
        code: '验证码',
        ph_pass: '密码',
        next_step: '下一步',
        newPassWord: '新密码',
        totp: '请输入谷歌验证码',
        errTip_pass: '请输入密码',
        errTip_user: '请输入用户名',
        errTip_code: '验证码不能为空',
        confirm_new_password: '确认新密码',
        Scan_Google_QR_code: '扫描下方谷歌二维码',

        // 2021/11/30 新增
        Log_out: '退出登录',
        To_obtain: '后重新获取',
        Change_password: '修改密码',
        last_password_6: '密码至少6位',
        Length_6_16: '长度必须在6~16位',
        Length_4_16: '长度必须在4~16位',
        Enter_the_old_password: '输入原密码',
        Enter_the_new_password: '输入新密码',
        Obtaining_verification_code: '获取验证码',
        Only_English_Number: '只能输入英文和数字',
        Please_enter_the_old_password: '请填写原密码',
        The_old_password_cannot_be_empty: '原密码不能为空',
        Please_enter_your_password_again: '请再次输入密码',
        The_two_passwords_are_inconsistent: '两次输入密码不一致!',
        Please_confirm_the_new_password_again: '请再次确认新密码',
        Please_enter_the_new_password_6_16: '请填写6~16位数新密码',
    },

    // 其他
    others: {
        piece: '张',
        level: '级',
        USDT: 'USDT',
        email: '邮箱',
        risk: '风险率',
        WeChat: '微信',
        assets: '资产',
        rights: '权益',
        not_have: '无',
        phone: '手机号',
        picture: '图片',
        Alipay: '支付宝',
        bank: '开户银行',
        ongoing: '进行中',
        noData: '暂无数据',
        finished: '已结束',
        operator: '操作人',
        Welcome: 'Welcome',
        Bank_card: '银行卡',
        fileName: '文件名称',
        unwindNum: '平仓笔数',
        freezeNum: '冻结数量',
        paymentCode: '收款码',
        buy_a_price: '买一价',
        unwind_PNL: '平仓PNL',
        debtAssets: '负债资产',
        latest_price: '最新价',
        topAgent: '上级代理ID',
        straightMatter: '正文',
        linkAddress: '链接地址',
        releaseTime: '发布时间',
        throughTime: '通过时间',
        User_Total: '用户_合计',
        Copy_success: '复制成功',
        languageType: '语言类型',
        systemColorMode: '系统色彩模式',
        bannerType: 'BANNER类型',
        articleTitle: '文章标题',
        modifyContent: '修改内容',
        accountRights: '账户权益',
        Bank_card_num: '银行卡号',
        moneyPassword: '资金密码',
        operationTime: '操作时间',
        Sold_for_a_price: '卖一价',
        noteOptional: '备注(选填)',
        frozenMargin: '冻结保证金',
        paymentMethods: '收款方式',
        WeChat_Account: '微信账号:',
        withdrawalNum: '提币中数量',
        chargeMoneyAddr: '充币地址',
        importantNotice: '重要公告',
        othersOperation: '其他操作',
        googleCode: '输入谷歌二维码',
        googleValidation: '谷歌验证',
        availableBalance: '可用余额',
        Alipay_Account: '支付宝账号:',
        openDialog: '是否开启弹框显示',
        min_top_up_num: '最小充值金额',
        lastModifyTime: '最后修改时间',
        Google_validator: '谷歌验证器',
        certificationReset: '认证重置',
        Modify_the_success: '修改成功',
        Net_positions_PNL: '净平仓PNL',
        noDataPlacedTop: '暂无置顶数据',
        identityInformation: '身份信息',
        walletAddress: '{name}钱包地址',
        system_automatically: '系统自动',
        accountOpeningBranch: '开户支行',
        subordinateAgentNum: '下级代理人数',
        authenticationFailed: '身份认证失败',
        plaform_user_total: '平台+用户_合计',
        abnormal_price_alarm: '价格异常报警',
        invitationNum: '邀请人数(含间接邀请)',
        profit_and_loss_interval: '盈亏区间',
        platformAssets_Total: '平台资产_合计',
        platformWalletAccount: '平台钱包账户',
        unrealizedProfitandLoss: '未实现盈亏',
        changeGoogle_p_three: '新的谷歌验证码',
        unwind_number_interval: '平仓笔数区间',
        inIdentityAuthentication: '身份认证中',
        poundageCommissionPro: '手续费返佣比例',
        current_contract_rights: '当前合约权益',
        platformContractAccount: '平台合约账户',
        pictureSize: '图片尺寸要求：640 x 700 px',
        walletAccountAssets: '钱包账户资产(USDT)',
        passIdentityAuthentication: '身份认证通过',
        platformTransactionAccount: '平台合约账户',
        contractAccountAssets: '合约账户资产(USDT)',
        changeGoogle_p_two: '秘钥:TGOXJ3V2B5S7POF3',
        contract_net_gold_interval: '合约净入金区间',
        min_max: '最多输入100个字符，最少输入4个字符；',
        documentaryAccountAssets: '跟单账户资产(USDT)',
        price_net_position_monitoring: '价格及净持仓监控',
        wallet_account_financial_record: '钱包账户财务记录',
        operation_after_no_error: '确认信息无误后请谨慎操作',
        contract_account_financial_record: '合约账户财务记录',
        exponential_stability_observation: '指数稳定观察汇总表',
        documentary_account_financial_record: '跟单账户财务记录',
        changeGoogle_h4_two: '请将您获得的验证码填入下方输入框中,并完成验证',
        changeGoogle_p_one: '请务必妥善保管谷歌验证秘钥，以免更换或丢失手机号导致无法换绑',
        changeGoogle_h4_one: '安装完成后打开Google Authentication,扫描下方二维码或手动输入秘钥，得到六位数验证码',
        // 2021/12/21 new add
        qpcczc: '强平穿仓支出',
        pccczc: '平仓穿仓支出',
        liquidationGet: '强平注入',

        // 2022/07/05
        delUserLabel: '删除用户标签',
        addUserRisk: '添加用户风控组',
        updUserRisk: '更新用户风控组',
        setUserRiskWhite: '设置用户风控白名单',
        delUserRiskWhite: '删除用户风控白名单',
        updUserRiskWhite: '修改用户风控组信息',
        setUserTBAudit: '设置用户提币审核限制',
        RemoveUserTBLimit: '解除用户提币限制',

        // 2022/07/07
        xexzh: '小额限制',
        gluid: '关联UID：',
        shctb: '首次提币',
        qxzxzhyhlx: '请选择限制用户类型',
        qxzqxxzhyhlx: '请选择取消限制用户类型',
        xzhOption1: '此用户',
        xzhOption2: '此代理及所有下级用户',
        // 2025/05/06
        categoryType: '公告分类',
        // 2025/05/12
        noticeTabTitle: '公告标签标题',
    },
    sysdict: {
        dictId: '字典ID',
        dictName: '字典名称',
        dictType: '字典类型',
        status: '状态',
        remark: '备注',
        createTime: '创建时间',
        pleaseEnterDictName: '请输入字典名称',
        pleaseEnterDictType: '请输入字典类型',
        pleaseSelectStatus: '请选择状态',
        pleaseEnterDictLabel: '请输入数据标签',
        pleaseEnterDictSort: '请输入字典排序',
        pleaseEnterDictValue: '请输入数据键值',
        normal: '正常',
        disabled: '停用',
        addDictType: '添加字典类型',
        editDictType: '修改字典类型',
        deleteDictType: '删除字典类型',
        confirmDeleteDictType: '确定删除字典类型？',
        pleaseEnterRemark: '请输入备注',
        dictTypeRequired: '字典类型不能为空',
        dictNameRequired: '字典名称不能为空',
        dictLabelRequired: '字典标签不能为空',
        dictSortRequired: '字典排序不能为空',
        dictValueRequired: '字典值不能为空',
        dictLabel: '数据标签',
        dictValue: '数据键值',
        dictSort: '显示排序',
        dictStatus: '显示状态',
        dictCode: '字典编码',
        pleaseSelectDictName: '请选择字典名称',
        cssClass: '样式属性',
        pleaseEnterCssClass: '请输入样式属性',
        listClass: '回显样式',
        pleaseEnterListClass: '请输入列表样式类名',
        isDefault: '是否默认',
        addDictData: '添加字典数据',
        editDictData: '修改字典数据',
        deleteDictData: '删除字典数据',
        confirmDeleteDictData: '确定删除字典数据？',
        pleaseEnterDictData: '请输入字典数据',
        pleaseEnterDictDataSort: '请输入字典数据排序',
        pleaseSelectListClass: '请选择回显样式',

    },
    sysdictdata: {
        dictName: '字典名称',
        dictType: '字典类型',
        dictValue: '字典值',
        dictLabel: '字典标签',
        dictSort: '显示排序',
        dictStatus: '状态',
        pleaseEnterDictName: '请输入字典名称',
        pleaseEnterDictType: '请输入字典类型',
        pleaseEnterDictLabel: '请输入字典标签',
        pleaseEnterDictSort: '请输入字典排序',
        pleaseEnterDictValue: '请输入字典值',
        pleaseSelectStatus: '请选择状态',
        normal: '正常',
        disabled: '停用',
    },
    common: {
        pleaseEnterDictName: '请输入字典名称',
        pleaseEnterDictType: '请输入字典类型',
        pleaseEnterDictLabel: '请输入字典标签',
        pleaseEnterDictSort: '请输入字典排序',
        pleaseEnterDictValue: '请输入字典值',
        search: '搜索',
        add: '添加',
        edit: '编辑',
        delete: '删除',
        purgeRedis: '清除Redis',
        confirmDelete: '确定删除？',
        cancel: '取消',
        reset: '重置',
        confirm: '确认',
        operation: '操作',
        index: '序号',
        addSuccess: '新增成功',
        updateSuccess: '修改成功',
        tip: '提示',
        deleteSuccess: '删除成功',
        deleteFailed: '删除失败',
        yes: '是',
        no: '否',
        copied: '已复制到剪贴板',
        copyFailed: '复制失败',
        close: '关闭',
    },

    // feature/daniel-academycoursemanagement-0507
    course: {
        commissionTypes: {
            direct: '直推',
            indirect: '间推',
        },
        forms: {
            add: '添加课程',
            edit: '编辑课程',
            detailsLinkRequired: '课程链接是必填项',
            portalLinkRequired: '门户图片URL是必填项',
            validLink: '请输入有效的URL',
            tokenRequired: '令牌名称是必填项',
            langRequired: '至少需要一個語言條目',
            titleRequired: '课程标题是必填项',
            detailsRequired: '课程详情是必填项',
            showHidden: '显示隐藏的标',
            addTab: '添加通知标签',
            addNotice: '添加通知',
            hiddenTab: '隐藏',
            show: '显示',
            referralGraph: '推荐图',
            maxDepth: '最大深度',
        },
        placeholders: {
            title: '请输入课程名称',
            tokenName: '请输入代币名称',
            start: '开始日期',
            end: '结束日期',
            selectLang: '選擇語言',
            rebateId: '返佣 UID',
            sourceId: '来源 UID',
            commissionType: '佣金类型',
            vipLevel: '选择VIP等级',
        },
        tableHeaders: {
            status: '状态',
            title: '课程名称',
            totalSeats: '课程总量',
            totalSold: '已售数量',
            amount: '课程单价',
            coin: '计价单位',
            begin: '购买启动时间',
            end: '购买截止时间',
            operation: '操作',
            details: '详情',
            detailsLink: '课程链接',
            portalImage: '门户图片',
            langContent: '多語言內容',
            language: '语言',
            purchaseTime: '申购时间',
            txId: '购课交易ID',
            rebateId: '返佣 UID',
            sourceId: '来源 UID',
            commissionType: '佣金类型',
            payoutTime: '发放时间',
            purchasedCourses: '已购课程',
            directReferrals: '直接邀请人数',
            teamSize: '团队人数',
            vipLevel: 'VIP等级',
            vipTierName: 'VIP名称',
            directReward: '直推奖励',
            indirectReward: '间推奖励',
        },
    },

    // feature/daniel-launchpadmanagement-0529
    lang: {
        zhCN: '简体中文',
        en: '英语',
        zhTW: '繁體中文',
        ko: '韩语',
        ja: '日语',
    },
    status: {
        all: '所有的',
        aboutToStart: '即将开始',
        inProgress: '进行中',
        normal: '正常',
        registrationClosed: '报名已满',
        ended: '已结束',
        unknown: '未知',
    },
    launchpad: {
        dialogs: {
            redemptionApprove: '赎回审核通过?',
        },
        forms: {
            detailsLinkRequired: '详细链接是一个必填项。',
            portalLinkRequired: '门户图片URL是必填项',
            validLink: '请输入有效的URL',
            tokenRequired: '令牌名称是必填项',
            langRequired: '至少需要一個語言條目',
            titleRequired: '项目标题是一个必填项',
            detailsRequired: '项目详情是必填项',
            view: '查看订阅详情',
            pledgeCoinRequired: '请输入质押资产/币种',
            success: '发放成功',
            loading: '分发中',
            fail: '发放失败',
            noNeed: '不要',
        },
        placeholders: {
            projectTitle: '请输入项目名称',
            tokenName: '请输入代币名称',
            begin: '开始日期',
            end: '结束日期',
            selectLang: '選擇語言',
            pricePerUnit: '价格 (每单位)',
            priceNotEntered: '请输入价格',
            selectToken: '选择币种',
            pledgeStatusNormal: '正常',
	        pledgeStatusReview: '赎回审核中',
            pledgeStatusRedeeming: '赎回中',        
	        pledgeStatusRedeemed: '已赎回',
            pledgeCoin: '请输入质押资产/币种',
            redeemable: '随时赎回',
        },
        tableHeaders: {
            begin: '开始日期',
            end: '结束日期',
            status: '状态',
            title: '项目名称/期数',
            details: '详情',
            coin: '代币名称',
            totalVolume: '申购总量',
            offerPrice: '美金进行申购 (假的)',
            unitPrice: '价格单位',
            totalFundsRaised: '总募集资金',
            fundsRaised: '已募集资金',
            usersFundRaised: '用户参与',
            operation: '操作',
            portal: '项目Banner图',
            language: '语言',
            icon: '项目图标',
            price: '课程单价',
            offered: '购买获得的硬币',
            paymentAmount: '付款金额',
            paymentCoin: '付款币种',
            purchasePrice: '代币价格',
            quantitySubscribed: '申购数量',
            category: '质押类别',
            pledgeCoin: '质押资产/币种',
            baseCoins: '总可质押量',
            realCoins: '总可用流动性',
            basePledges: '掺假质押量',
            realPledges: '真实质押数量',
            startPledge: '开始时间',
            endPledge: '结束时间',
            releasePledge: '释放时间',
            distributionMethod: '分配方式',
            yield: '收益率 (%)',
            network: '所属公链',
            iconUrl: '图标',
            pledgeStatus: '质押状态',
            pledgeTime: '质押时间',
            income: '已经收入多少',
            actualIncome: '实发收益金额',
            redemptionInitiated: '赎回发起时间',
            redemptionRedeemed: '赎回成功时间',
            redemptionState: '赎回状态',
            automaticReviewStatus: '自动审核状态',
        }
    },

    ...zhLocale
}