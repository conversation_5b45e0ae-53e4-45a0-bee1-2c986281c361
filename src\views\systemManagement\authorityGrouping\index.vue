<template>
  <div class="authority-container">
    <div class="filter-container">
      <el-button
        size="mini"
        type="success"
        style="float: right; margin-bottom: 10px;"
        @click="AddClick()"
        v-if="$store.getters.roles.indexOf('moldeladd1')>-1"
        >{{$t('buttons.add_grouping')}}</el-button
      >
    </div>

    <el-table
      v-loading="listLoading"
      :data="authorityList"
      style="width: 100%; margin-top: 30px; margin-left: 15px;"
      border
      fit
      highlight-current-row
      size="mini"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column type="index" align="center"></el-table-column>
      <el-table-column prop="modelname" align="center" :label="$t('tableHeader.role_of_grouping')" > </el-table-column>
      <el-table-column prop="creattime" align="center" :label="$t('tableHeader.creationTime')"></el-table-column>

      <el-table-column :label="$t('tableHeader.operation')" align="center" min-width="130px" v-if="$store.getters.roles.indexOf('moldeldel')>-1 || $store.getters.roles.indexOf('moldeladd3')>-1">
        <template slot-scope="{ row, $index }">
       
          <el-button size="mini"  v-if="$store.getters.roles.indexOf('moldeldel')>-1" @click="handlesc(row,$index)">{{$t('buttons.delete')}}</el-button>
          <el-button size="mini" v-if="$store.getters.roles.indexOf('moldeladd3')>-1" @click="handlexx(row)">{{$t('buttons.modify')}}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagina-tion
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { moldelist, moldeldel } from "@/api/systemManagement";
// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";
export default {
  name: "authorityGrouping",
  data() {
    return {
      listLoading: false,
      listQuery: {
        pageNo: 1,
        pagesize: 20,
      },
      authorityList: null,
      total: 0,
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.getList();
  },

  methods: {
    //添加分组
    AddClick() {
      this.$router.push({
        path: "/systemManagement/addGroup",
        query: { uid: "", username: "", type: "2" },
      });
    },
    handlesc(v){
      this.$confirm(this.$t('dialog.Confirm_deletion'), this.$t('dialog.Prompt'), {
          confirmButtonText: this.$t('buttons.determine'),
          cancelButtonText: this.$t('buttons.cancel'),
          type: 'warning'
        }).then(() => {
          moldeldel({upid: v.model_id}).then((res)=>{
            this.$notify({ title: this.$t('dialog.Successful'),message:res.msg,type: 'success'});
            if(this.authorityList.length-1 === 0){
              this.listQuery.pageNo -= 1;
              this.listQuery.pageNo = this.listQuery.pageNo<1?1:this.listQuery.pageNo;
            }
            this.getList();
          })
        }).catch(() => {         
        });
    },
    handlexx(v){
      this.$router.push({ 
        path:'/systemManagement/addGroup',
        query:{id:v.model_id,name:v.modelname,type:"1"}
      })
    },
    //渲染table数据
    getList() {
      var that = this;
      //开始有加载中效果
      that.listLoading = true;
      moldelist(that.listQuery).then((res) => {
        that.authorityList = res.data.list;
        that.total = res.data.total;
        this.listLoading = false;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
</style>