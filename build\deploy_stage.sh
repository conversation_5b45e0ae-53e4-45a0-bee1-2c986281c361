#!/bin/bash

# === Configuration ===
PEM_PATH="/Users/<USER>/Documents/GitLab/deeplink-2.pem"
PROJECT_PATH="/Users/<USER>/Documents/GitLab/manage_agent-pd-main"
ZIP_NAME="cfmanage-go2-web$(date +%m%d).zip"
REMOTE_USER="ec2-user"
REMOTE_HOST="ec2-54-251-6-50.ap-southeast-1.compute.amazonaws.com"
REMOTE_PATH="/home/<USER>"
DEPLOY_DIR="${REMOTE_PATH}/web/cfmanage-go2"

# === Build Project ===
echo "🚧 Navigating to project folder..."
cd "$PROJECT_PATH" || { echo "❌ Project path not found"; exit 1; }

echo "📦 Installing dependencies (if needed)..."
npm install

echo "⚙️  Building project for staging..."
npm run build:stage || { echo "❌ Build failed"; exit 1; }

# === Zip Build Output ===
echo "📁 Zipping dist folder into $ZIP_NAME..."
cd dist || { echo "❌ dist folder not found"; exit 1; }
zip -r "../$ZIP_NAME" ./*
cd ..

# echo "📂 ZIP file created at: $PROJECT_PATH/$ZIP_NAME"
# ls -lh "$ZIP_NAME"

# # === Local Confirmation ===
# read -p "🔍 Confirm the ZIP file above looks good. Proceed with SCP upload to EC2? (y/n) " confirm
# if [[ $confirm != "y" ]]; then
#   echo "🚫 Deployment aborted."
#   exit 0
# fi

# === Upload to EC2 ===
echo "📤 Uploading $ZIP_NAME to EC2..."
scp -i "$PEM_PATH" "$ZIP_NAME" "$REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH" || { echo "❌ SCP failed"; exit 1; }

# === Deploy on EC2 ===
echo "📦 Connecting to EC2 and deploying..."
ssh -i "$PEM_PATH" "$REMOTE_USER@$REMOTE_HOST" << EOF
  echo "📛 Fixing ownership to ensure deployment works..."
  sudo chown -R ec2-user:ec2-user "$DEPLOY_DIR"
  
  echo "🗄️  Backing up current deployment..."
  BACKUP_DIR="${DEPLOY_DIR}_backup_\$(date +%Y%m%d_%H%M%S)"
  if [ -d "$DEPLOY_DIR" ]; then
    sudo cp -r "$DEPLOY_DIR" "\$BACKUP_DIR"
    echo "✅ Backup created at \$BACKUP_DIR"
  else
    echo "⚠️  Deploy directory does not exist, skipping backup."
  fi

  echo "📦 Deploying..."
  sudo unzip -o "$ZIP_NAME" -d "$DEPLOY_DIR"

  echo "🧹 Cleaning up zip..."
  sudo rm -f "$ZIP_NAME"
  echo "✅ Deployment completed and zip file cleaned up on EC2."
EOF

# === Cleanup ===
echo "🧹 Cleaning up local zip file..."
rm "$ZIP_NAME"

echo "🎉 All done!"
