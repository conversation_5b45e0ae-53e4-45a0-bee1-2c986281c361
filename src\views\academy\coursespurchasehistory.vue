<!-- src/views/academy/coursespurchasehistory.vue -->
<template>
	<div class="academy-container">
		<!-- Filter Bar -->
		<el-form :inline="true" :model="filters" class="filter-form">
			<el-form-item>
				<el-input v-model="filters.course_name" :placeholder="$t('course.placeholders.title')" clearable />
			</el-form-item>
			<el-form-item>
				<el-input v-model="filters.coin" :placeholder="$t('course.placeholders.tokenName')" clearable />
			</el-form-item>
			<el-form-item label="Start Date">
				<el-date-picker v-model="filters.start" type="date" value-format="yyyy-MM-dd" />
			</el-form-item>
			<el-form-item label="End Date">
				<el-date-picker v-model="filters.end" type="date" value-format="yyyy-MM-dd" />
			</el-form-item>
			<el-form-item>
				<el-button type="primary" icon="el-icon-search" @click="handleSearch">
					{{ $t('buttons.search') }}
				</el-button>
			</el-form-item>
		</el-form>

		<!-- Course Purchase History Table -->
		<el-table :data="tableData" border fit size="mini" class="course-table"
			:header-cell-style="{ background: '#F0F8FF', color: '#333', fontWeight: 'bold' }">
			<el-table-column label="UID" align="center">
				<template #default="{ row }">
					<el-tooltip :content="row.user_id.toString()" placement="top">
						<span>{{ shortenMiddle(row.user_id.toString()) }}</span>
					</el-tooltip>
				</template>
			</el-table-column>

			<el-table-column :label="$t(`course.tableHeaders.purchaseTime`)" align="center">
				<template slot-scope="scope">
					{{ formatUnix(scope.row.purchase_at) }}
				</template>
			</el-table-column>

			<el-table-column :label="$t(`course.tableHeaders.status`)" align="center" min-width="80">
				<template slot-scope="scope">
					<el-tag :type="scope.row.tx_id ? 'success' : 'warning'">
						{{ scope.row.tx_id ? 'Success' : 'Pending' }}
					</el-tag>
				</template>
			</el-table-column>

			<el-table-column :label="$t(`course.tableHeaders.title`)" align="center" min-width="180">
				<template #default="{ row }">
					{{ getCourseTitle(row.langs) }}
				</template>
			</el-table-column>

			<el-table-column prop="val" :label="$t(`course.tableHeaders.amount`)" align="center" width="100" />
			
			<el-table-column prop="coin" :label="$t(`course.tableHeaders.coin`)" align="center" width="100" />
			
			<el-table-column :label="$t(`course.tableHeaders.txId`)" align="center">
				<template #default="{ row }">
					<el-tooltip :content="row.tx_id || '-'" placement="top">
						<span>{{ shortenMiddle(row.tx_id || '-', 6) }}</span>
					</el-tooltip>
					<el-button v-if="row.tx_id" type="text" icon="el-icon-document-copy" size="small"
						@click="copyToClipboard(row.tx_id)" style="margin-left: 8px" />
				</template>
			</el-table-column>
		</el-table>

		<!-- 引入封装分页组件 -->
		<pagina-tion v-show="total> 0" :total="total" :page-sizes="[10, 50, 100, 300]" :page.sync="filters.pageNo"
			:limit.sync="filters.pagesize" @pagination="fetchData" />
	</div>
</template>

<script>
import { formatUnix } from '@/utils/time';
import { getCoursePurchaseHistory } from '@/api/academy';
import { getCourseTitle, copyToClipboard } from '@/utils/course';
import { shortenMiddle } from '@/utils/format';

export default {
	data() {
		return {
			filters: {
				uid: 0,
				course_name: '',
				coin: '',
				start: '',
				end: '',
				pageNo: 1,
				pagesize: 50
			},
			tableData: [],
			total: 0
		}
	},
	methods: {
		formatUnix,
		getCourseTitle,
		copyToClipboard,
		shortenMiddle,
		async fetchData() {
			try {
				// console.log(JSON.stringify(this.filters));
				const res = await getCoursePurchaseHistory(this.filters);
				this.tableData = res.data.list || [];
				this.total = res.data.total;
			} catch (error) {
				console.error('Failed to fetch data', error);
			}
		},
		handleSearch() {
			this.filters.pageNo = 1;
			this.fetchData();
		},
	},
	mounted() {
		this.fetchData();
	}
}
</script>

<style>
.academy-container {
	padding: 20px;
}

.filter-form {
	margin-bottom: 20px;
}
</style>
