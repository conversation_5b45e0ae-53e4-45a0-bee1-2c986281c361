<template>
  <div class="asset-container">
    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      style="width: 100%; margin-top: 30px"
      size="mini"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.channel_name')" prop="channel_name" align="center" min-width="78" />
      <el-table-column :label="$t('tableHeader.agent_name')" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.agent_name || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.tatalreg')" prop="tatalreg" align="center" />
      <el-table-column :label="$t('tableHeader.totaltrading')" prop="totaltrading" align="center" />
      <el-table-column :label="$t('dialog.cumulativePoundage')" prop="totalcommiss" align="center" />
      <el-table-column :label="$t('tableHeader.yestdayreg')" prop="yestdayreg" align="center" />
      <el-table-column :label="$t('tableHeader.yestdaytrading')" prop="yestdaytrading" align="center" />
      <el-table-column :label="$t('tableHeader.yestdaycommiss')" prop="yestdaycommiss" align="center" />
      <el-table-column :label="$t('tableHeader.weekreg')" prop="weekreg" align="center" />
      <el-table-column :label="$t('tableHeader.weektrading')" prop="weektrading" align="center" />
      <el-table-column :label="$t('tableHeader.weekcommiss')" prop="weekcommiss" align="center" />
      <el-table-column :label="$t('tableHeader.mounthreg')" prop="mounthreg" align="center" />
      <el-table-column :label="$t('tableHeader.mounthtrading')" prop="mounthtrading" align="center" />
      <el-table-column :label="$t('tableHeader.mounthcommiss')" prop="mounthcommiss" align="center" />
      <el-table-column :label="$t('tableHeader.isopenagent')" prop="isopenagent" align="center" />
      <el-table-column :label="$t('tableHeader.Opening_time')" prop="create_time" align="center" width="75"/>
      <el-table-column :label="$t('tableHeader.operation')" prop="child_reg_num" align="center" width="120">
        <template slot-scope="{ row }">
          <el-button
            type="primary"
            size="mini"
            @click="handleBind(row)"
          >{{ $t('buttons.bind') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10, 20, 50, 100]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />

    
    <!-- 备注 -->
    <el-dialog
      v-dialogDrag
      :visible.sync="searchDialogShow"
      width="60%"
      :title="$t('tableHeader.note')"
    >
      <el-form
        ref="bindChannelForm"
        :model="searchData"
        label-position="left"
        label-width="auto"
        :inline="true"
        :rules="rules"
      >
        <el-form-item :label="$t('filters.find_administrator')" prop="uid">
          <el-input
            :placeholder="$t('filters.agent_uid')"
            v-model="searchData.uid"
            oninput="value=value.replace(/[^\d]/g, '')"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearchUID">{{$t("buttons.search")}}</el-button>
        </el-form-item>
      </el-form>
      <div class="bindDialog_userinfo_wrap">
        <div><span>UID:</span>{{userInfoData && userInfoData.user_id || '--'}}</div>
        <div><span>{{$t('tableHeader.userName')}}:</span>{{userInfoData && userInfoData.user_name || '--'}}</div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">{{$t('buttons.cancel')}}</el-button>
        <el-button type="primary" @click="handleEntry">{{$t('buttons.determine')}}</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
// 封装api
import { getactivitychennal, addchannelagent } from '@/api/channelManage'
import { userinfo } from "@/api/userQuery"

export default {
  name: 'ChannelStatisticsList',
  components: {},
  data() {
    return {
      listLoading: false,
      total: 0, // 总条数
      tableData: null, // 表格数据
      listQuery: {
        pageNo: 1, // 页数
        pagesize: 10 // 条数
      },
      searchDialogShow: false,
      searchData:{
        uid: '',
      },
      curItemData: null,
      userInfoData: null,
      rules: {  // 验证规则
        uid: [{ required: true, message: this.$t('filters.agent_uid'), trigger: "blur" },],
      },
    }
  },

  computed: {
  },

  mounted() {
    this.getList()
  },

  methods: {
    handleSearchUID(){
      this.$refs.bindChannelForm.validate((valid) => {
        if (valid) {
          userinfo({
            user_id :JSON.parse(this.searchData.uid)
          }).then((res) => {
            if (res.data.user_id !== 0) {
              this.userInfoData = res.data
            } else {
              this.userInfoData = null
              this.$notify({
                title: this.$t('dialog.The_UID_you_entered_does_not_exist'),
                type: "warning",
                duration: 2000,
              });
              return false
            }
          })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    handleEntry(){
      if(!this.userInfoData || !this.curItemData) return false
      this.$confirm(`${this.$t('dialog.bind_channel_tip',{name: this.userInfoData.user_name,uid: this.userInfoData.user_id})}?`)
        .then(() => {
          addchannelagent({
            uid: JSON.stringify(this.userInfoData.user_id),
            channelid: this.curItemData.channel_id,  
            channelname: this.curItemData.channel_name
          }).then((res) => {
            this.$notify({
              title: this.$t('dialog.Successful'),
              message: this.$t('dialog.Operation_is_successful'),
              type: "success",
              duration: 2000,
            });
            this.handleCancel()
            this.getList()
          })
        })
        .catch(_ => {
          this.$t('dialog.bindTips')
        })
    },
    handleCancel(){
      this.searchDialogShow = false 
      this.userInfoData = null 
      this.curItemData = null
      setTimeout(()=>{
        this.$refs.bindChannelForm.resetFields()
      },500)
    },
    //  渲染table列表
    getList() {
      // 开始有加载中效果
      // this.listLoading = true
      getactivitychennal(this.listQuery).then((res) => {
        this.tableData = res.data.list
        this.total = res.data.total
        this.listLoading = false
      })
    },
    handleBind(item) {
      this.curItemData = item
      this.searchDialogShow = true
      this.userInfoData = null
    }
  }
}
</script>
<style lang="scss">
.bindDialog_userinfo_wrap{
  background: #fafafa;
  margin: 0 20px;
  padding: 15px;
  &>div{
    line-height: 30px;
  }
}
</style>
<style lang="scss" scoped>
.asset-container {
  .filter-container {
    .highSwitch_wrap {
      margin-top: 15px;
      width: 100px;
      cursor: pointer;
      font-size: 14px;
    }
  }
  .select_wrap {
    // padding: 0 30px 0 30px;
    display: flex;
    align-items: center;
    span {
      width: 100px;
      // padding-right: 20px;
    }
  }
}
</style>
