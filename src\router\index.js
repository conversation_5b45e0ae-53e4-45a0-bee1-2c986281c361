import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'
/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [{
  path: '/login',
  component: () =>
    import('@/views/login/index'),
  hidden: true
},
{
  path: '/404',
  component: () =>
    import('@/views/404'),
  hidden: true
},

{
  path: '/',
  component: Layout,
  redirect: '/home',
  hidden: true,
  children: [{
    path: 'home',
    name: 'welcomeHome',
    component: () =>
      import('@/views/home/<USER>'),
    meta: { title: '平台管理系统', titleKey: 'name', icon: 'dashboard' }
  }]
}
]

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = [
  {
    path: '/notice',
    component: Layout,
    redirect: '/notice/gglb',
    name: 'notice',
    meta: { title: '通知', titleKey: 'notice_management', icon: 'el-icon-s-platform', roles: ['gglb', /*'ggfb',*/ 'getnoticeimportant', 'bannerConfig'] },
    // roles: ['notice']
    children: [{
      path: 'gglb',
      name: 'gglb-noKeep',
      component: () => import('@/views/notice/gglb'),
      meta: { title: '公告列表', titleKey: 'noticeList', icon: 'el-icon-s-platform', roles: ['gglb'] }
    },
    // feature/daniel-noticetab-0512
    // note: moving this as a dialog
    // {
    //   path: 'ggfb',
    //   name: 'ggfb',
    //   component: () => import('@/views/notice/ggfb'),
    //   meta: { title: '公告发布', titleKey: 'noticeRelease', icon: 'el-icon-s-platform', roles: ['ggfb'] }
    // },
    {
      path: 'ggxg',
      name: 'ggxg',
      component: () => import('@/views/notice/ggxg'),
      meta: { title: '公告修改', titleKey: 'noticeUpd', icon: 'el-icon-s-platform', roles: ['ggxg'] },
      hidden: true
    },
    {
      path: 'importantNotice',
      name: 'importantNotice',
      component: () => import('@/views/notice/importantNotice'),
      meta: { title: '首页浮层', titleKey: 'noticeImportant', icon: 'el-icon-s-platform', roles: ['getnoticeimportant'] }
    },
    {
      path: 'bannerConfig',
      name: 'bannerConfig',
      component: () => import('@/views/systemManagement/bannerConfig'),
      meta: { title: 'Banner配置', titleKey: 'home_Banner_configuration', icon: 'el-icon-s-tools', roles: ['bannerConfig'] }
    }
    ]
  },
  {
    path: '/user',
    component: Layout,
    redirect: '/user/list',
    name: 'username',
    meta: { title: '用户管理', titleKey: 'userQuery', icon: 'el-icon-user', roles: ['list', 'verifyhistorylist'] },
    children: [{
      path: 'list',
      name: 'userList',
      component: () => import('@/views/userQuery/userList/index'),
      meta: { title: '用户列表', titleKey: 'userList', icon: 'el-icon-user', roles: ['list'] }
    },
    {
      path: 'detail',
      name: 'userListDetail-noKeep',
      component: () => import('@/views/userQuery/userListDetail'),
      meta: { title: '用户信息详情', titleKey: 'userListDetails', icon: 'el-icon-user', roles: ['list'] },
      hidden: true
    },
    {
      path: 'kyc',
      name: 'Kyc',
      component: () => import('@/views/userQuery/kyc/index'),
      meta: { title: 'KYC审核', titleKey: 'userKYC', icon: 'el-icon-user', roles: ['verifyhistorylist'] }
    }
    ]
  },
  {
    path: '/fundQuery',
    component: Layout,
    redirect: '/fundQuery/spotAssetsQuery',
    name: 'fundQuery',
    meta: { title: '资产管理', titleKey: 'money_query', icon: 'el-icon-tickets', roles: ['spotaccountinfo', 'spotcommisfee', 'spotusermonitor', 'userTotalAssetQueryLook', 'USDAssetsQueryLook', 'getwellinfo', 'getaccinfo', 'getwalletbill', 'getwallerhistory', 'getwithdrawlist', 'userexchangelist', 'legalorderlist', 'getagentcapital', 'getuserpnl', 'getusercaption', 'getcommiss', 'daypnllist'] },
    children: [{
      path: 'userTotalAssetQuery',
      name: 'userTotalAssetQuery',
      component: () => import('@/views/fundQuery/userTotalAssetQuery/index'),
      meta: { title: '用户总资产查询', titleKey: 'user_total_assets_query', icon: 'el-icon-tickets', roles: ['userTotalAssetQueryLook'] }
    }, {
      path: 'userAssetQuery',
      name: 'userAssetQuery',
      component: () => import('@/views/fundQuery/userAssetQuery/index'),
      meta: { title: '钱包账户资产', titleKey: 'user_assets_query', icon: 'el-icon-tickets', roles: ['getwellinfo'] }
    }, {
      path: 'spotAssetsQuery',
      name: 'spotAssetsQuery',
      component: () => import('@/views/fundQuery/spotAssetsQuery/index'),
      meta: { title: '现货账户资产', titleKey: 'spot_assets_query', icon: 'el-icon-tickets', roles: ['spotaccountinfo'] }
    }, {
      path: 'tradeAssetQuery',
      name: 'tradeAssetQuery',
      component: () => import('@/views/fundQuery/tradeAssetQuery/index'),
      meta: { title: 'USDT合约资产', titleKey: 'trading_assets_query', icon: 'el-icon-tickets', roles: ['getaccinfo'] }
    }, {
      path: 'USDAssetsQuery',
      name: 'USDAssetsQuery',
      component: () => import('@/views/fundQuery/USDAssetsQuery/index'),
      meta: { title: '币本位合约资产', titleKey: 'usd_assets_query', icon: 'el-icon-tickets', roles: ['USDAssetsQueryLook'] }
    }, {
      path: 'spotPoundageQuery',
      name: 'spotPoundageQuery',
      component: () => import('@/views/fundQuery/spotPoundageQuery/index'),
      meta: { title: '现货手续费查询', titleKey: 'spot_poundage_query', icon: 'el-icon-tickets', roles: ['spotcommisfee'] }
    }, {
      path: 'spotMonitoring',
      name: 'spotMonitoring',
      component: () => import('@/views/fundQuery/spotMonitoring/index'),
      meta: { title: '现货数据监控', titleKey: 'spot_monitioring', icon: 'el-icon-tickets', roles: ['spotusermonitor'] }
    },  
    {
      path: 'goldquery',
      name: 'goldquery',
      component: () => import('@/views/fundQuery/goldquery/index'),
      meta: { title: '用户出入金查询', titleKey: 'user_in_out_money_query', icon: 'el-icon-tickets', roles: ['getwalletbill'] }
    },
    {
      path: 'financialRecords',
      name: 'financialrecords',
      component: () => import('@/views/fundQuery/financialRecords/index'),
      meta: { title: '用户财务记录', titleKey: 'user_financial_query', icon: 'el-icon-tickets', roles: ['getwallerhistory'] }
    },
    {
      path: 'withdrawalAdminister',
      name: 'withdrawaladminister',
      component: () => import('@/views/fundQuery/withdrawalAdminister/index'),
      meta: { title: '用户提币管理', titleKey: 'user_extract_management', icon: 'el-icon-tickets', roles: ['getwithdrawlist'] }
    },
    {
      path: 'exchangeQuery',
      name: 'exchangeQuery',
      component: () => import('@/views/fundQuery/exchangeQuery/index'),
      meta: { title: '币币兑换管理', titleKey: 'Currency_exchange_management', icon: 'el-icon-tickets', roles: ['userexchangelist'] }
    },
    {
      path: 'agentCapital',
      name: 'agentCapital',
      component: () => import('@/views/fundQuery/agentCapital/index'),
      meta: { title: '用户数据监控', titleKey: 'user_data_monitoring', icon: 'el-icon-tickets', roles: ['getagentcapital'] }
    },
    {
      path: 'legalOrder',
      name: 'legalOrder',
      component: () => import('@/views/fundQuery/legalOrder/index'),
      meta: { title: '法币卖出管理', titleKey: 'fiat_sell_management', icon: 'el-icon-tickets', roles: ['legalorderlist'] }
    },
    {
      path: 'pnlQuery',
      name: 'pnlQuery',
      component: () => import('@/views/fundQuery/pnlQuery/index'),
      meta: { title: 'PNL查询', titleKey: 'PNL_query', icon: 'el-icon-tickets', roles: ['getuserpnl'] }
    },
    {
      path: 'usercaption',
      name: 'usercaption',
      component: () => import('@/views/fundQuery/usercaption/index'),
      meta: { title: '资金费用查询', titleKey: 'capital_cost_query', icon: 'el-icon-tickets', roles: ['getusercaption'] }
    },
    {
      path: 'commiss',
      name: 'commiss',
      component: () => import('@/views/fundQuery/commiss/index'),
      meta: { title: '手续费查询', titleKey: 'poundage_query', icon: 'el-icon-tickets', roles: ['getcommiss'] }
    },
    {
      path: 'daypnllist',
      name: 'daypnllist',
      component: () => import('@/views/fundQuery/daypnllist/index'),
      meta: { title: '每日PNL汇总', titleKey: 'daily_PNL_summary', icon: 'el-icon-tickets', roles: ['daypnllist'] }
    }
    ]
  },
  {
    path: '/spotTrading',
    component: Layout,
    redirect: '/spotTrading/CommissionedHistory',
    name: 'spotTrading',
    meta: { title: '现货交易', titleKey: 'spot_trading', icon: 'el-icon-document-remove', roles: ['spothistorderlist', 'spotorderlist', 'spotplanorder', 'stopoutQuery', 'plancloseorder', 'getplanorder', 'getposstioncap', 'getmanageexpotlist'] },
    children: [{
      path: 'CommissionedHistory',
      name: 'CommissionedHistory',
      component: () => import('@/views/spotTrading/CommissionedHistory/index'),
      meta: { title: '历史委托', titleKey: 'commissioned_history', icon: 'el-icon-document-remove', roles: ['spothistorderlist'] }
    }, {
      path: 'CommissionedCurrent',
      name: 'CommissionedCurrent',
      component: () => import('@/views/spotTrading/CommissionedCurrent/index'),
      meta: { title: '当前委托', titleKey: 'commissioned_current', icon: 'el-icon-document-remove', roles: ['spotorderlist'] }
    }, {
      path: 'PlanEntrust',
      name: 'PlanEntrust',
      component: () => import('@/views/spotTrading/PlanEntrust/index'),
      meta: { title: '计划委托', titleKey: 'plan_entrust', icon: 'el-icon-document-remove', roles: ['spotplanorder'] }
    }
    ]
  },
  {
    path: '/transactionQuery',
    component: Layout,
    redirect: '/transactionQuery/SpotTrading',
    name: 'trade',
    meta: { title: 'USDT合约', titleKey: 'trading_query', icon: 'el-icon-s-order', roles: ['positionlist', 'closetradelist', 'opentradelist', 'tradelist', 'stopoutQuery', 'plancloseorder', 'getplanorder', 'getposstioncap', 'getmanageexpotlist'] },
    children: [{
      path: 'holdquery',
      name: 'holdquery',
      component: () => import('@/views/transactionQuery/holdQuery/index'),
      meta: { title: '持仓查询', titleKey: 'position_query', icon: 'el-icon-s-order', roles: ['positionlist'] }
    },
    {
      path: 'levelquery',
      name: 'levelquery',
      component: () => import('@/views/transactionQuery/levelQuery/index'),
      meta: { title: '平仓查询', titleKey: 'unwind_query', icon: 'el-icon-s-order', roles: ['closetradelist'] }
    },
    {
      path: 'openquery',
      name: 'openquery',
      component: () => import('@/views/transactionQuery/openQuery/index'),
      meta: { title: '开仓查询', titleKey: 'openPositions_query', icon: 'el-icon-s-order', roles: ['opentradelist'] }
    },
    {
      path: 'tradelist',
      name: 'tradelist',
      component: () => import('@/views/transactionQuery/tradeList/index'),
      meta: { title: '开平仓查询', titleKey: 'open_unwind_query', icon: 'el-icon-s-order', roles: ['tradelist'] }
    },
    {
      path: 'stopoutQuery',
      name: 'stopoutQuery',
      component: () => import('@/views/transactionQuery/stopoutQuery/index'),
      meta: { title: '强平单查询', titleKey: 'liquidationListQuery', icon: 'el-icon-s-order', roles: ['stopoutQuery'] }
    },
    {
      path: 'PLQuery',
      name: 'PLQuery',
      component: () => import('@/views/transactionQuery/PLQuery/index'),
      meta: { title: '止盈止损查询', titleKey: 'check_full_stop_query', icon: 'el-icon-s-order', roles: ['plancloseorder'] }
    },
    {
      path: 'planQuery',
      name: 'planQuery',
      component: () => import('@/views/transactionQuery/planQuery/index'),
      meta: { title: '计划委托查询', titleKey: 'order_query', icon: 'el-icon-s-order', roles: ['getplanorder'] }
    },
    {
      path: 'posstioncap',
      name: 'posstioncap',
      component: () => import('@/views/transactionQuery/posstioncap/index'),
      meta: { title: '用户持仓监控', titleKey: 'user_position_monitoring', icon: 'el-icon-s-order', roles: ['getposstioncap'] }
    },
    {
      path: 'exportList',
      name: 'exportList',
      component: () => import('@/views/transactionQuery/exportList/index'),
      meta: { title: '导出下载列表', titleKey: 'exports_download_list', icon: 'el-icon-s-order', roles: ['getmanageexpotlist'] }
    }
    ]
  },
  // {
  //   path: '/multiCurrency',
  //   component: Layout,
  //   redirect: '/multiCurrency',
  //   name: 'multiCurrency',
  //   meta: { title: '币本位合约查询', titleKey: 'Multi_currency_contract_management', icon: 'el-icon-document', roles: ['USDOpenCloseQuery', 'USDPositionQuery', 'USDLiquidationData', 'USDPlanPL', 'USDPlanQuery', 'USDPositionMonitoring'] },
  //   alwaysShow: true,
  //   children: [
  //     {
  //       path: 'openCloseQuery',
  //       name: 'usdOpenCloseQuery',
  //       component: () => import('@/views/multiCurrency/openCloseQuery/index'),
  //       meta: { title: '开平仓查询', titleKey: 'Open_and_close_query', icon: 'el-icon-document', roles: ['USDOpenCloseQuery'] }
  //     },
  //     {
  //       path: 'positionQuery',
  //       name: 'positionQuery',
  //       component: () => import('@/views/multiCurrency/positionQuery/index'),
  //       meta: { title: '持仓查询', titleKey: 'Position_inquiry', icon: 'el-icon-document', roles: ['USDPositionQuery'] }
  //     },
  //     {
  //       path: 'liquidationData',
  //       name: 'liquidationData',
  //       component: () => import('@/views/multiCurrency/liquidationData/index'),
  //       meta: { title: '用户强平数据', titleKey: 'User_liquidation_data', icon: 'el-icon-document', roles: ['USDLiquidationData'] }
  //     },
  //     {
  //       path: 'planPL',
  //       name: 'planPL',
  //       component: () => import('@/views/multiCurrency/planPL/index'),
  //       meta: { title: '止盈止损查询', titleKey: 'check_full_stop_query', icon: 'el-icon-document', roles: ['USDPlanPL'] }
  //     },
  //     {
  //       path: 'planQuery',
  //       name: 'usdPlanQuery',
  //       component: () => import('@/views/multiCurrency/planQuery/index'),
  //       meta: { title: '计划委托查询', titleKey: 'order_query', icon: 'el-icon-document', roles: ['USDPlanQuery'] }
  //     },
  //     {
  //       path: 'positionMonitoring',
  //       name: 'positionMonitoring',
  //       component: () => import('@/views/multiCurrency/positionMonitoring/index'),
  //       meta: { title: '用户持仓监控', titleKey: 'User_position_monitoring', icon: 'el-icon-document', roles: ['USDPositionMonitoring'] }
  //     }
  //   ]
  // },
  // {
  //   path: '/followQuery',
  //   component: Layout,
  //   redirect: '/followQuery/holdquery',
  //   name: 'followQuery',
  //   meta: { title: '跟单查询', titleKey: 'documentary_query', icon: 'el-icon-document-copy', roles: ['docpositionlist', 'docclosetradelist', 'docopentradelist', 'followtradelist', 'docplancloseorder', 'docgetplanorder', 'followaccinfo', 'getflposstioncap', 'getfollowcapital'] },
  //   children: [{
  //     path: 'holdquery',
  //     name: 'followholdquery',
  //     component: () => import('@/views/documentaryQuery/holdQuery/index'),
  //     meta: { title: '持仓查询', titleKey: 'documentary_position_query', icon: 'el-icon-document-copy', roles: ['docpositionlist'] }
  //   },
  //   {
  //     path: 'levelquery',
  //     name: 'followlevelquery',
  //     component: () => import('@/views/documentaryQuery/levelQuery/index'),
  //     meta: { title: '平仓查询', titleKey: 'documentary_unwind_query', icon: 'el-icon-document-copy', roles: ['docclosetradelist'] }
  //   },
  //   {
  //     path: 'openquery',
  //     name: 'followopenquery',
  //     component: () => import('@/views/documentaryQuery/openQuery/index'),
  //     meta: { title: '开仓查询', titleKey: 'documentary_openPositions_query', icon: 'el-icon-document-copy', roles: ['docopentradelist'] }
  //   },
  //   {
  //     path: 'followtradelist',
  //     name: 'followtradelist',
  //     component: () => import('@/views/documentaryQuery/followtradelist/index'),
  //     meta: { title: '开平仓查询', titleKey: 'documentary_open_unwind_query', icon: 'el-icon-document-copy', roles: ['followtradelist'] }
  //   },
  //   {
  //     path: 'PLQuery',
  //     name: 'followPLQuery',
  //     component: () => import('@/views/documentaryQuery/PLQuery/index'),
  //     meta: { title: '止盈止损查询', titleKey: 'documentary_check_full_stop_query', icon: 'el-icon-document-copy', roles: ['docplancloseorder'] }
  //   },
  //   {
  //     path: 'planQuery',
  //     name: 'followplanQuery',
  //     component: () => import('@/views/documentaryQuery/planQuery/index'),
  //     meta: { title: '计划委托查询', titleKey: 'documentary_order_query', icon: 'el-icon-document-copy', roles: ['docgetplanorder'] }
  //   },
  //   {
  //     path: 'followaccinfo',
  //     name: 'followaccinfo',
  //     component: () => import('@/views/documentaryQuery/followaccinfo/index'),
  //     meta: { title: '跟单资产查询', titleKey: 'documentary_assets_query', icon: 'el-icon-document-copy', roles: ['followaccinfo'] }
  //   },
  //   {
  //     path: 'flposstioncap',
  //     name: 'flposstioncap',
  //     component: () => import('@/views/documentaryQuery/flposstioncap/index'),
  //     meta: { title: '跟单持仓监控', titleKey: 'documentary_position_monitoring', icon: 'el-icon-document-copy', roles: ['getflposstioncap'] }
  //   },
  //   {
  //     path: 'followcapital',
  //     name: 'followcapital',
  //     component: () => import('@/views/documentaryQuery/followcapital/index'),
  //     meta: { title: '跟单数据监控', titleKey: 'documentary_data_monitoring', icon: 'el-icon-document-copy', roles: ['getfollowcapital'] }
  //   }
  //   ]
  // },
  
  {
    path: '/rebateQuery',
    component: Layout,
    redirect: '/rebateQuery/rebateList',
    name: 'rebateList',
    meta: { title: '返佣查询', titleKey: 'commission_query', icon: 'el-icon-coin', roles: ['getrebotlist', 'getuserrebot', 'UsdCommissionLook', 'UsdCommissionDetailLooK'] },
    alwaysShow: true,
    children: [{
      path: 'USDTCommission',
      name: 'USDTCommission',
      component: () => import('@/views/rebateQuery/rebateList/index'),
      meta: { title: 'USDT合约返佣', titleKey: 'poundage_commission', icon: 'el-icon-coin', roles: ['getrebotlist'] }
    },
    {
      path: 'USDTCommissionDetail',
      name: 'USDTCommissionDetail-noKeep',
      component: () => import('@/views/rebateQuery/rebateListDetail/index'),
      meta: { title: 'USDT合约返佣详情', titleKey: 'poundageCommissionDetails', icon: 'el-icon-coin', roles: ['getuserrebot'] },
      hidden: true
    },
    {
      path: 'USDCommission',
      name: 'USDCommission',
      component: () => import('@/views/rebateQuery/usdCommission/index'),
      meta: { title: '币本位合约返佣', titleKey: 'usd_contract_commission', icon: 'el-icon-coin', roles: ['UsdCommissionLook'] }
    },
    {
      path: 'USDCommissionDetail',
      name: 'USDCommissionDetail-noKeep',
      component: () => import('@/views/rebateQuery/usdCommissionDetail/index'),
      meta: { title: '币本位合约返佣详情', titleKey: 'usd_contract_commission_detail', icon: 'el-icon-coin', roles: ['UsdCommissionDetailLooK'] },
      hidden: true
    }
    ]
  },
  {
    path: '/platformFinance',
    component: Layout,
    redirect: '/platformFinance',
    name: 'platformfinance',
    meta: { title: '平台财务', titleKey: 'platform_financial', icon: 'el-icon-bank-card', roles: ['spotfinanceasset', 'platwalletlist', 'getplataccount', 'liquidationGetList', 'throughPayList', 'campaignSpending', 'giftoutDetail', 'cointouDetail'] }, // 'getfinacewallet',
    children: [
      // {
      //     path: 'overView',
      //     name: 'overView',
      //     component: () => import('@/views/platformFinance/overView/index'),
      //     meta: { title: '概览', titleKey: '概览', icon: 'el-icon-bank-card', roles: ['getfinacewallet'] },
      // },
      {
        path: 'spotAccount',
        name: 'spotAccount',
        component: () => import('@/views/platformFinance/spotAccount/index'),
        meta: { title: '现货账户', titleKey: 'spot_account', icon: 'el-icon-bank-card', roles: ['spotfinanceasset'] }
      },
      {
        path: 'assetAccount',
        name: 'assetAccount',
        component: () => import('@/views/platformFinance/assetAccount/index'),
        meta: { title: '资产账户', titleKey: 'assetsAccount', icon: 'el-icon-bank-card', roles: ['platwalletlist'] }
      },
      {
        path: 'assetHistoryRecord',
        name: 'assetHistoryRecord',
        component: () => import('@/views/platformFinance/assetAccount/assetHistoryRecord'),
        meta: { title: '历史记录', titleKey: 'historicalRecord', icon: 'el-icon-bank-card', roles: ['platwalletdaillist'] },
        hidden: true
      },
      {
        path: 'transactionAccount',
        name: 'transactionAccount',
        component: () => import('@/views/platformFinance/transactionAccount/index'),
        meta: { title: '交易账户', titleKey: 'transactionAccount', icon: 'el-icon-bank-card', roles: ['getplataccount'] }
      },
      {
        path: 'transactionHistoryRecord',
        name: 'transactionHistoryRecord',
        component: () => import('@/views/platformFinance/transactionAccount/transactionHistoryRecord'),
        meta: { title: '历史记录', titleKey: 'historicalRecord', icon: 'el-icon-bank-card', roles: ['plataccountdail'] },
        hidden: true
      },
      {
        path: 'liquidationGetList',
        name: 'liquidationGetList',
        component: () => import('@/views/platformFinance/liquidationGetList/index'),
        meta: { title: '强平注入资金', titleKey: 'liquidationGetFund', icon: 'el-icon-bank-card', roles: ['liquidationGetList'] }
      },
      {
        path: 'throughPayList',
        name: 'throughPayList',
        component: () => import('@/views/platformFinance/throughPayList/index'),
        meta: { title: '穿仓支出资金', titleKey: 'throughPayFund', icon: 'el-icon-bank-card', roles: ['throughPayList'] }
      },
      {
        path: 'campaignSpending',
        name: 'campaignSpending',
        component: () => import('@/views/platformFinance/campaignSpending/index'),
        meta: { title: '每日活动支出', titleKey: 'Campaign_spending', icon: 'el-icon-bank-card', roles: ['campaignSpending'] }
      },
      {
        path: 'giftoutDetail',
        name: 'giftoutDetail',
        component: () => import('@/views/platformFinance/campaignSpending/giftoutDetail'),
        meta: { title: '赠金支出详情', titleKey: 'giftoutDetail', icon: 'el-icon-bank-card', roles: ['giftoutDetail'] },
        hidden: true
      },
      {
        path: 'cointouDetail',
        name: 'cointouDetail',
        component: () => import('@/views/platformFinance/campaignSpending/cointouDetail'),
        meta: { title: '活动支出详情', titleKey: 'cointouDetail', icon: 'el-icon-bank-card', roles: ['cointouDetail'] },
        hidden: true
      }
    ]
  },
  {
    path: '/activity/',
    component: Layout,
    redirect: '/activity/shareHandlingFee',
    name: 'activity',
    meta: { title: '活动管理', titleKey: 'activities_management', icon: 'el-icon-timer', roles: ['bcactivitylist', 'giftCashManageLook', 'giftCashActiveLook'] },
    alwaysShow: true,
    children: [
      {
        path: 'shareHandlingFee',
        name: 'shareHandlingFee',
        component: () => import('@/views/activity/shareHandlingFee/index'),
        meta: { title: '瓜分手续费', titleKey: 'carve_up_poundage', icon: 'el-icon-timer', roles: ['bcactivitylist'] }
      },
      {
        path: 'feeDetails',
        name: 'feeDetails-noKeep',
        component: () => import('@/views/activity/shareHandlingFee/details'),
        meta: { title: '瓜分手续费详情', titleKey: 'carve_up_poundageDetails', icon: 'el-icon-coin', roles: ['getuseractivitylist'] },
        hidden: true
      },
      {
        path: 'giftCashManage',
        name: 'giftCashManage',
        component: () => import('@/views/activity/giftCash/giftCashManage'),
        meta: { title: '赠金管理', titleKey: 'giftCash_manage', icon: 'el-icon-timer', roles: ['giftCashManageLook'] }

      },
      {
        path: 'giftCashActive',
        name: 'giftCashActive',
        component: () => import('@/views/activity/giftCash/giftCashActive'),
        meta: { title: '赠金活动', titleKey: 'giftCash_active', icon: 'el-icon-timer', roles: ['giftCashActiveLook'] }
      }
    ]
  },
  {
    path: '/channelManage',
    component: Layout,
    redirect: '/channelManage/topAgentList',
    name: 'channelManage',
    meta: { title: '渠道管理', titleKey: 'channel_management', icon: 'el-icon-coin', roles: ['topAgentList', 'agentDirectlist', 'channelStatisticsList'] },
    alwaysShow: true,
    children: [
      {
        path: 'topAgentList',
        name: 'topAgentList',
        component: () => import('@/views/channelManage/topAgentList/index'),
        meta: { title: '顶级代理统计', titleKey: 'top_agent_statistical', icon: 'el-icon-coin', roles: ['topAgentList'] }
      },
      {
        path: 'agentDirectlist',
        name: 'agentDirectlist',
        component: () => import('@/views/channelManage/agentDirectlist/index'),
        meta: { title: '代理直推统计', titleKey: 'agent_direct_drive_statistical', icon: 'el-icon-coin', roles: ['agentDirectlist'] }
      },
      {
        path: 'channelStatisticsList',
        name: 'channelStatisticsList',
        component: () => import('@/views/channelManage/channelStatisticsList/index'),
        meta: { title: '渠道统计', titleKey: 'channel_statistics', icon: 'el-icon-coin', roles: ['channelStatisticsListLook'] }
      }
    ]
  },
  {
    path: '/riskAdminister',
    component: Layout,
    redirect: '/riskAdminister/lebelAdminister',
    name: 'windcontrol',
    meta: { title: '风控管理', titleKey: 'risk_control_management', icon: 'el-icon-chat-dot-square', roles: ['bcprocontractcoinset', 'frequency', 'usdfrequency', 'usdfrequencydetails', 'highfrequency', 'highpnl', 'highwinning', 'usdhighwinning', 'systemmonitoring', 'ipstatisticslist', 'ipuserlist', 'iptradelist', 'watchuserlist', 'getgrouplist', 'getriskuserlist','contractPNLQuery'] },
    alwaysShow: true,
    children: [
      {
        path: 'lebelAdminister',
        name: 'lebelAdminister',
        component: () => import('@/views/riskAdminister/lebelAdminister/index'),
        meta: { title: '标签管理', titleKey: 'label_management', icon: 'el-icon-chat-dot-square', roles: ['bcprocontractcoinset'] }
      },
      {
        path: 'frequency',
        name: 'frequency',
        component: () => import('@/views/riskAdminister/frequency/index'),
        meta: { title: '交易频次', titleKey: 'trading_frequency', icon: 'el-icon-chat-dot-square', roles: ['frequency'] }
      },
      {
        path: 'frequencyDetail',
        name: 'frequencyDetail-noKeep',
        component: () => import('@/views/riskAdminister/frequencyDetail/index'),
        meta: { title: '交易频次--详情', titleKey: 'trading_frequencyDetails', icon: 'el-icon-chat-dot-square', roles: ['bcprocontractcoinset'] },
        hidden: true
      },
      {
        path: 'usdfrequency',
        name: 'usdfrequency',
        component: () => import('@/views/riskAdminister/usdfrequency/index'),
        meta: { title: '币本位合约交易频次', titleKey: 'usdtrading_frequency', icon: 'el-icon-chat-dot-square', roles: ['usdfrequency'] }
      },
      {
        path: 'usdfrequencyDetail',
        name: 'usdfrequencyDetail-noKeep',
        component: () => import('@/views/riskAdminister/usdfrequencyDetail/index'),
        meta: { title: '币本位交合约交易频次--详情', titleKey: 'usdtrading_frequencyDetails', icon: 'el-icon-chat-dot-square', roles: ['bcprocontractcoinset'] },
        hidden: true
      },
      {
        path: 'highfrequency',
        name: 'highfrequency',
        component: () => import('@/views/riskAdminister/highfrequency/index'),
        meta: { title: '高频用户识别', titleKey: 'high_frequency_user_identification', icon: 'el-icon-chat-dot-square', roles: ['highfrequency'] }
      },
      {
        path: 'highpnl',
        name: 'highpnl',
        component: () => import('@/views/riskAdminister/highpnl/index'),
        meta: { title: '高盈利用户识别', titleKey: 'high_monetization_user_recognition', icon: 'el-icon-chat-dot-square', roles: ['highpnl'] }
      },
      {
        path: 'tradepnl',
        name: 'tradepnl',
        component: () => import('@/views/riskAdminister/tradepnl/index'),
        meta: { title: '用户收益数据', titleKey: 'user_trade_pnl', icon: 'el-icon-chat-dot-square', roles: ['tradepnl'] }
      },
      {
        path: 'highwinning',
        name: 'highwinning',
        component: () => import('@/views/riskAdminister/highwinning/index'),
        meta: { title: '高胜率用户识别', titleKey: 'high_win_rate_user_identification', icon: 'el-icon-chat-dot-square', roles: ['highwinning'] }
      },
      {
        path: 'highwinningdetails',
        name: 'highwinningdetails-noKeep',
        component: () => import('@/views/riskAdminister/highwinning/highwinningdetails'),
        meta: { title: '高胜率用户信息详情', titleKey: 'high_win_rate_user_details', icon: 'el-icon-chat-dot-square', roles: ['highwinning'] },
        hidden: true
      },
      {
        path: 'usdhighwinning',
        name: 'usdhighwinning',
        component: () => import('@/views/riskAdminister/usdhighwinning/index'),
        meta: { title: '币本位合约高胜率用户识别', titleKey: 'usd_high_win_rate_user_identification', icon: 'el-icon-chat-dot-square', roles: ['usdhighwinning'] }
      },
      {
        path: 'usdhighwinningdetails',
        name: 'usdhighwinningdetails-noKeep',
        component: () => import('@/views/riskAdminister/usdhighwinning/usdhighwinningdetails'),
        meta: { title: '币本位合约高胜率用户信息详情', titleKey: 'usd_high_win_rate_user_details', icon: 'el-icon-chat-dot-square', roles: ['usdhighwinning'] },
        hidden: true
      },
      {
        path: 'systemmonitoring',
        name: 'systemmonitoring',
        component: () => import('@/views/riskAdminister/systemmonitoring/index'),
        meta: { title: '系统监控', titleKey: 'system_monitoring', icon: 'el-icon-chat-dot-square', roles: ['systemmonitoring'] }
      },
      {
        path: 'ipstatisticslist',
        name: 'ipstatisticslist',
        component: () => import('@/views/riskAdminister/ipstatisticslist/index'),
        meta: { title: '同IP行为分析', titleKey: 'same_IP_behavior_analysis', icon: 'el-icon-chat-dot-square', roles: ['ipstatisticslist'] }
      },
      {
        path: 'ipuserlist',
        name: 'ipuserlist',
        component: () => import('@/views/riskAdminister/ipstatisticslist/ipuserlist'),
        meta: { title: 'IP用户概况', titleKey: 'IP_user_overview', icon: 'el-icon-chat-dot-square', roles: ['ipuserlist'] },
        hidden: true
      },
      {
        path: 'iptradelist',
        name: 'iptradelist',
        component: () => import('@/views/riskAdminister/ipstatisticslist/iptradelist'),
        meta: { title: 'IP交易详情', titleKey: 'IP_transaction_detailsv', icon: 'el-icon-chat-dot-square', roles: ['iptradelist'] },
        hidden: true
      },
      {
        path: 'watchuserlist',
        name: 'watchuserlist',
        component: () => import('@/views/riskAdminister/watchuserlist/index'),
        meta: { title: '观察用户列表', titleKey: 'observing_User_List', icon: 'el-icon-chat-dot-square', roles: ['watchuserlist'] }
      },
      {
        path: 'riskgroup',
        name: 'riskgroup',
        component: () => import('@/views/riskAdminister/riskgroup/index'),
        meta: { title: '风控组别列表', titleKey: 'list_risk_control_groups', icon: 'el-icon-chat-dot-square', roles: ['getgrouplist'] }
      },
      {
        path: 'riskuserlist',
        name: 'riskuserlist',
        component: () => import('@/views/riskAdminister/riskuserlist/index'),
        meta: { title: '风控用户列表', titleKey: 'list_risk_control_users', icon: 'el-icon-chat-dot-square', roles: ['getriskuserlist'] }
      },
      {
        path: 'riskuserwhite',
        name: 'riskuserwhite',
        component: () => import('@/views/riskAdminister/riskuserlist/riskuserwhite'),
        meta: { title: '风控用户白名单', titleKey: 'view_risk_control_whitelist', icon: 'el-icon-chat-dot-square', roles: ['getwhitelist'] },
        hidden: true
      },
      {
        path: 'riskuserhistory',
        name: 'riskuserhistory',
        component: () => import('@/views/riskAdminister/riskuserlist/riskuserhistory'),
        meta: { title: '风控用户历史记录', titleKey: 'viewing_historical_records', icon: 'el-icon-chat-dot-square', roles: ['getristlist'] },
        hidden: true
      },
      {
        path: 'contractPNLQuery',
        name: 'contractPNLQuery',
        component: () => import('@/views/riskAdminister/contractPNLQuery/index'),
        meta: { title: '合约PNL查询', titleKey: 'contract_PNL_query', icon: 'el-icon-chat-dot-square', roles: ['contractPNLQuery'] },
      }
    ]
  },
  {
    path: '/systemManagement',
    component: Layout,
    redirect: '/systemManagement',
    name: 'systemmanage',
    meta: { title: '系统管理', titleKey: 'system_management', icon: 'el-icon-s-tools', roles: ['sysdict','sysdictdata','configurationDownloadList', 'addLink', 'contractSlippageList', 'bannerConfig', 'managelist', 'moldelist', 'getmanagelogs', 'contactUs', 'userlog', 'usererrlog', 'getagentoplog'] },
    children: [
      {
        path: 'sysdict',
        name: 'sysdict',
        component: () => import('@/views/sysdict/sysdict'),
        meta: { title: '系统字典管理', titleKey: 'system_dictionary_management', icon: 'el-icon-s-tools', roles: ['sysdict'] }
      },
      {
        path: 'sysdictdata',
        name: 'sysdictdata',
        hidden: true,
        component: () => import('@/views/sysdict/sysdictdata'),
        meta: { title: '系统字典数据管理', titleKey: 'system_dictionary_data_management', icon: 'el-icon-s-tools', roles: ['sysdictdata'] }
      },
      {
        path: 'configurationDownload',
        name: 'configurationDownload',
        component: () => import('@/views/systemManagement/configurationDownload'),
        meta: { title: '配置下载', titleKey: 'Download_the_configuration', icon: 'el-icon-s-tools', roles: ['configurationDownloadList'] }
      },
      {
        path: 'addLink',
        name: 'addLink',
        component: () => import('@/views/systemManagement/configurationDownload/addLink'),
        meta: { title: '添加链接', titleKey: 'Add_link', icon: 'el-icon-s-tools', roles: ['configurationDownloadOperation'] },
        hidden: true
      },
      {
        path: 'contractSlippageSet',
        name: 'contractSlippageSet',
        component: () => import('@/views/systemManagement/contractSlippageSet'),
        meta: { title: '合约点差设置', titleKey: 'Contract_point_difference_setting', icon: 'el-icon-s-tools', roles: ['contractSlippageList'] }
      },
      // {
      //   path: 'bannerConfig',
      //   name: 'bannerConfig',
      //   component: () => import('@/views/systemManagement/bannerConfig'),
      //   meta: { title: '首页Banner配置', titleKey: 'home_Banner_configuration', icon: 'el-icon-s-tools', roles: ['bannerConfig'] }
      // },
      {
        path: 'roleAdminister',
        name: 'roleAdminister',
        component: () => import('@/views/systemManagement/roleAdminister/index'),
        meta: { title: '角色管理', titleKey: 'role_management', icon: 'el-icon-s-tools', roles: ['managelist'] }
      },
      {
        path: 'authorityGrouping',
        name: 'authorityGrouping',
        component: () => import('@/views/systemManagement/authorityGrouping/index'),
        meta: { title: '权限分组', titleKey: 'rights_group', icon: 'el-icon-s-tools', roles: ['moldelist'] }
      },
      {
        path: 'addGroup',
        name: 'addGroup-noKeep',
        component: () => import('@/views/systemManagement/authorityGrouping/addGroup/index'),
        meta: { title: '添加分组', titleKey: 'add_group', icon: 'el-icon-s-tools', roles: ['moldeladd1'] },
        hidden: true
      },
      {
        path: 'operatingLog',
        name: 'operatingLog',
        component: () => import('@/views/systemManagement/operatingLog/index'),
        meta: { title: '操作日志', titleKey: 'operation_log', icon: 'el-icon-s-tools', roles: ['getmanagelogs'] }
      },
      {
        path: 'getagentoplog',
        name: 'getagentoplog',
        component: () => import('@/views/systemManagement/getagentoplog/index'),
        meta: { title: 'CRM操作日志', titleKey: 'CRM_operation_log', icon: 'el-icon-s-tools', roles: ['getagentoplog'] }
      },
      {
        path: 'contactUs',
        name: 'contactUs',
        component: () => import('@/views/systemManagement/contactUs/index'),
        meta: { title: '联系我们', titleKey: 'contact_us', icon: 'el-icon-s-tools', roles: ['contactUs'] }
      },
      {
        path: 'userlog',
        name: 'userlog',
        component: () => import('@/views/systemManagement/userLog/index'),
        meta: { title: '用户日志', titleKey: 'user_log', icon: 'el-icon-s-tools', roles: ['userlog'] }
      },
      {
        path: 'usererrlog',
        name: 'usererrlog',
        component: () => import('@/views/systemManagement/usererrlog/index'),
        meta: { title: '反馈数据', titleKey: 'feedback_data', icon: 'el-icon-s-tools', roles: ['usererrlog'] }
      }

      // {
      //     path: 'securityCenter',
      //     name: 'securityCenter',
      //     component: () => import('@/views/systemManagement/securityCenter/index'),
      //     meta: { title: '安全中心', titleKey: '安全中心', icon: 'el-icon-s-tools', roles: ['aaaaaaa'] },
      // },
      // {
      //     path: 'changePassword',
      //     name: 'changePassword',
      //     component: () => import('@/views/systemManagement/securityCenter/changePassword/index'),
      //     meta: { title: '修改密码', titleKey: '修改密码', icon: 'el-icon-s-tools', roles: ['aaaaaaa'] },
      //     hidden: true
      // },
      // {
      //     path: 'changeGoogle',
      //     name: 'changeGoogle',
      //     component: () => import('@/views/systemManagement/securityCenter/changeGoogle/index'),
      //     meta: { title: '修改谷歌验证器', titleKey: '修改谷歌验证器', icon: 'el-icon-s-tools', roles: ['aaaaaaa'] },
      //     hidden: true
      // },

    ]
  },
  // feature/daniel-academycoursemanagement-0507
  {
    path: '/academy',
    component: Layout,
    redirect: '/academy/courses',
    name: 'courses',
    meta: {
      title: '课程管理',
      titleKey: 'course_management',
      icon: 'el-icon-reading',
      // roles: ['academy']
    },
    children: [
      {
        path: 'courses',
        name: 'academy-list',
        component: () => import('@/views/academy/index'),
        meta: {
          title: '课程列表',
          titleKey: 'course_list',
          icon: 'el-icon-notebook-2',
          // roles: ['academy']
        }
      },
      {
        path: 'coursespurchasehistory',
        name: 'academy-purchases',
        component: () => import('@/views/academy/coursespurchasehistory'),
        meta: {
          title: '购买记录',
          titleKey: 'course_purchases',
          icon: 'el-icon-s-order',
          // roles: ['academy_purchases']
        }
      },
      {
        path: 'courseusertierlist',
        name: 'course-user-tier',
        component: () => import('@/views/academy/courseusertierlist'),
        meta: {
          title: '客户等级',
          titleKey: 'customer_tier',
          icon: 'el-icon-s-custom',
          // roles: ['academy_tier']
        },
      },
      {
        path: 'rewards',
        name: 'academy-rewards',
        component: () => import('@/views/academy/rewards'),
        meta: {
          title: '返佣列表',
          titleKey: 'reward_list',
          icon: 'el-icon-s-finance',
          // roles: ['academy_rewards']
        },
      }
    ]
  },
  // feature/daniel-launchpadmanagement-0529
  {
    path: '/launchpad',
    component: Layout,
    redirect: '/launchpad/subscriptions',
    name: 'launchpad',
    meta: {
      title: '申购总览',
      titleKey: 'launchpad',
      icon: 'el-icon-s-promotion',
      // roles: ['academy']
    },
    children: [
      {
        path: 'subscriptions',
        name: 'subscriptions',
        component: () => import('@/views/launchpad/index'),
        meta: {
          title: '申购总览',
          titleKey: 'subscriptions',
          icon: 'el-icon-document',
          // roles: ['subscription']
        }
      },
      {
        path: 'subscriptionrecords',
        name: 'subscription-records',
        component: () => import('@/views/launchpad/subscriptionrecords'),
        meta: {
          title: '申购记录查询',
          titleKey: 'subscription_records',
          icon: 'el-icon-s-order',
          // roles: ['subscription_records'],
        }
      },
      {
        path: 'pledgeoverview',
        name: 'pledge-overview',
        component: () => import('@/views/launchpad/pledgeoverview'),
        meta: {
          title: '质押总览',
          titleKey: 'pledges',
          icon: 'el-icon-s-custom',
          // roles: ['pledges'],
        },
      },
      {
        path: 'pledgerecords',
        name: 'pledge-records',
        component: () => import('@/views/launchpad/pledgerecords'),
        meta: {
          title: '质押记录查询',
          titleKey: 'pledge_records',
          icon: 'el-icon-s-order',
          // roles: ['pledge_records'],
        },
      },
      {
        path: 'redemptionrecords',
        name: 'redemption-records',
        component: () => import('@/views/launchpad/redemptionrecords'),
        meta: {
          title: '赎回记录',
          titleKey: 'redemption_records',
          icon: 'el-icon-goods',
          // roles: ['redemption_records'],
        },
      },
    ]
  },
  { path: '*', redirect: '/', hidden: true }
]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
