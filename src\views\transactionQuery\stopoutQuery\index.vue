<template>
  <div class="hold-cantainer">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.contract_code"
        :placeholder="$t('tableHeader.contract')"
        clearable
        style="width: 120px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in contractOptions"
          :key="item.traderpairs"
          :label="item.traderpairs"
          :value="item.traderpairs"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.account_type"
        :placeholder="$t('filters.positionType')"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in accountTypeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.side"
        :placeholder="$t('filters.direction')"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="(val,key,idx) in sizeOptions"
          :key="idx"
          :label="val"
          :value="key"
        />
      </el-select>
      <span style="margin-left: 20px; font-size: 12px">{{$t('tableHeader.clinchDealTime')}}</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change='filterTimeTransform'
      >
      </el-date-picker>
      <el-input
        size="mini"
        v-model="listQuery.tradeid"
        :placeholder="$t('tableHeader.transactionNumber')"
        style="width: 150px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-button
        class="filter-item"
        size="mini"
        style="margin-left: 20px; margin-top: 5px"
        type="primary"
        @click="handleFilter"
      >
        <!-- @click="handleFilter" -->
        {{$t('buttons.search')}}
      </el-button>
      <el-button
        class="filter-item"
        v-if="$store.getters.roles.indexOf('getstopoutexport')>-1"
        :loading="exportLoading"
        style="margin-left: 20px; margin-top: 5px"
        @click="handleExport"
        size="mini"
        type="success"
      >
        {{$t('buttons.export')}}
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="holdList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="userid" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center" min-width="95"> </el-table-column>
      <el-table-column :label="$t('tableHeader.contract')" prop="contractcode" min-width="90px" align="center"></el-table-column>
      <el-table-column :label="$t('filters.positionType')" min-width="80px" align="center">
        <template slot-scope="{ row }">
          <span>
            {{ row.accounttype == 1 ? $t('tableHeader.all_warehouse') : 
              row.accounttype == 2 ? $t('tableHeader.by_warehouse') :
              row.accounttype == 5 ? $t('filters.All_warehouse_Points_storehouse') :
              row.accounttype == 6 ? $t('filters.By_warehouse_Points_storehouse') : '--'
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.direction')" min-width="80px" align="center">
        <template slot-scope="{ row }">
          <span>{{sizeOptions[row.offset+'_'+row.side]}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.leverage')" align="center" min-width="60px">
        <template slot-scope="{ row }">
          <span>&times;{{ row.lever }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.number')" prop="volume" align="center" min-width="110px">
          <template slot-scope="{ row }">
          <span>{{ row.volume }}{{$t('others.piece')}}</span><span>/{{row.conversion}}{{row.contractcode.slice(0,-4)}}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('tableHeader.flatPrice')" prop="init_margin" align="center" min-width="100px">
          <template slot-scope="{ row }">
          <span>{{ row.price }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('tableHeader.averageOpen')" prop="open_avg_price" align="center" min-width="90px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.poundage')" prop="commission" align="center" min-width="90px"> </el-table-column>
      <el-table-column label="PNL" prop="closeprofit" align="center" min-width="90px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.Amount_of_use_of_bonus')" prop="gift_amount" align="center" min-width="90px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.Bonus_handling_fee')" prop="gift_fee" align="center" min-width="90px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.Available_balance_of_bonus')" prop="gift_available" align="center" min-width="90px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.Give_gold_balance')" prop="gift_balance" align="center" min-width="90px"></el-table-column>
      <el-table-column :label="$t('tableHeader.Time_stamp_of_last_open_position')" prop="cur_open_time" align="center" min-width="90px">
        <template slot-scope="{ row }">
          <span>{{ row.cur_open_time || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.positionID')" prop="position_id" align="center" min-width="90px">
        <template slot-scope="{ row }">
          <span>{{ row.position_id || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.net_PNl')" prop="profit" align="center" min-width="90px"></el-table-column>
      <!-- <el-table-column :label="$t('tableHeader.clear_fee')" prop="blowing_fee" align="center" min-width="90px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.through_pay')" prop="blowing_fee" align="center" min-width="90px"> </el-table-column> -->
      <el-table-column :label="$t('tableHeader.clinchDealTime')" align="center" min-width="75px">
        <template slot-scope="{ row }">
          <span>{{ row.trade_time }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.transactionNumber')"
        prop="tradeid"
        align="center"
        min-width="78"
      />
    </el-table>

    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,50,100,200,300]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";
//引入封装接口
import { getstopout, getstopoutexport } from "@/api/transactionQuery";
import { bcprocontractset } from "@/api/user";

export default {
  name: "stopoutQuery",
  data() {
    return {
      listLoading: false,
      total: 0,
      holdList: null,
      contractOptions: [],
      filterTime: [],
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        account_type: null, //账户模式 1：全仓 2：逐仓
        contract_code: "", //合约代码
        side: null, //方向 B买S卖
        pageNo: 1,
        pagesize: 10,
        star: '', //开始
        end: '', //结束
        tradeid: "", //
      },
      accountTypeOptions: [
        { key: 1, name: this.$t('tableHeader.all_warehouse') },
        { key: 2, name: this.$t('tableHeader.by_warehouse') },
        { key: 5, name: this.$t('filters.All_warehouse_Points_storehouse') },
        { key: 6, name: this.$t('filters.By_warehouse_Points_storehouse') },
      ],
      sizeOptions: {
        "O_B": this.$t('tableHeader.open_to_buy_more'),
        "O_S": this.$t('tableHeader.sell_empty'),
        "C_B": this.$t('tableHeader.buy_sides_jersey'),
        "C_S": this.$t('tableHeader.sell_more_flat'),
      },
      exportLoading: false,//导出加载中效果
    };
  },

  components: {},

  computed: {
    // 默认时间
    timeDefault () {
      let date = new Date()
      // 通过时间戳计算
      let defalutStartTime = ((date.getTime() - 1 * 24 * 3600 * 1000)/1000).toDate('yyyy-MM-dd') // 转化为时间戳
      let defalutEndTime = ((date.getTime()/1000)).toDate('yyyy-MM-dd')
      return [defalutStartTime, defalutEndTime]
    }
  },

  mounted() {
    bcprocontractset({}).then((res) => {
      this.contractOptions = res.data.filter(v=>v.isshow == 1)
    })
    this.filterTime = this.timeDefault;
    this.getList();
  },

  methods: {
    //  请求表格数据
    getList() {
      this.listLoading = true;
      let data = {}
      Object.assign(data, this.listQuery)
      data.side = this.listQuery.side && this.listQuery.side.split('_')[1] || undefined
      data.offset = this.listQuery.side && this.listQuery.side.split('_')[0] || undefined
      data.account_type = this.listQuery.account_type || undefined
      data.star = this.filterTime ? this.filterTime[0] + " 00:00:00" : "";;
      data.end = this.filterTime ? this.filterTime[1] + " 23:59:59" : "";
      getstopout(data).then((response) => {
        this.holdList = response.data.list;
        this.total = response.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    filterTimeTransform(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val? val[1]+' 23:59:59':'';
    },
    //表格导出功能
    handleExport() {
      this.exportLoading = true;
      let data = {}
      Object.assign(data,this.listQuery)
      let newDate = parseInt(new Date().getTime()/1000);
      data.end = this.listQuery.end ? this.listQuery.end : (newDate.toDate('yyyy-MM-dd')+ ' 23:59:59')
      newDate = parseInt(newDate-518400)
      data.star = this.listQuery.star ? (this.listQuery.star + ' 00:00:00' ): (newDate.toDate('yyyy-MM-dd')+ ' 00:00:00')
      getstopoutexport(data).then((res) => {
        if(res.ret == 0){
            window.location.href=res.data.download_url;
            this.exportLoading = false
        }
      }).catch(err=>{
        this.exportLoading = false
      })
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) =>
        filterVal.map((j) => {
          if (j === "transaction_time") {
            return parseTime(v[j]);
          } else {
            return v[j];
          }
        })
      );
    },
  },
};
</script>
<style lang="scss" scoped>
</style>