<template>
  <div class="giftCashActive-container">
    <div class="btns-container">
      <el-button @click="handleNewWithGold()" type="primary">{{ $t('dialog.Give_new_a_gold') }}</el-button>
    </div>

    <div class="filter-container">
      <!-- <el-input 
        size="mini"
        v-model="listQuery.activeTitle" 
        placeholder="活动标题"
        style="width: 150px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      /> -->
      <el-select
        size="mini"
        v-model="listQuery.category"
        :placeholder="$t('filters.Give_gold_type')"
        clearable
        style="width: 120px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in withGoldTypeOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.status"
        :placeholder="$t('tableHeader.state')"
        clearable
        style="width: 120px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in statusOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <!-- <span style="margin-left: 20px; font-size: 12px">结束时间</span>
      <el-date-picker
        style="width: 220px; margin-left: 20px; margin-top: 10px"
        v-model="filterTime1"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change="filterTimeTransform1"
      >
      </el-date-picker> -->
      <span style="margin: 0px 20px; font-size: 12px">{{ $t('tableHeader.startTime') }}</span>
      <el-date-picker
        style="width: 220px; margin: 10px 20px 0px 0px;"
        v-model="filterTime2"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change="filterTimeTransform2"
      >
      </el-date-picker>

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin: 10px 0 0 0;"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="tableList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <!-- 赠金名称 -->
      <el-table-column :label="$t('tableHeader.Give_name_of_gold')" align="center" min-width="85">
        <template slot-scope="{ row }">
          <span>{{ row.giftinfo[0].title }}</span>
        </template>
      </el-table-column>
      <!-- 赠金类型 -->
      <el-table-column :label="$t('filters.Give_gold_type')" align="center" min-width="85">
        <template slot-scope="{ row }">
          <span>{{ 
                  row.category == 1 ? $t('tableHeader.Top_up_for_the_first_time') : 
                  row.category == 2 ? $t('tableHeader.For_the_first_time_trade') :
                  row.category == 3 ? $t('tableHeader.Novice_transaction_volume_meets_the_standard') :
                  row.category == 4 ? $t('tableHeader.Active_users') :
                  row.category == 5 ? $t('tableHeader.Top_up_rebate') :
                  row.category == 6 ? $t('tableHeader.Invite_friends') : '--' 
                }}
          </span>
        </template>
      </el-table-column>
      <!-- 注册时限 -->
      <el-table-column :label="$t('tableHeader.Registration_deadline')" align="center" min-width="85">
        <template slot-scope="{ row }">
          <span>{{ row.time_limit || '--' }}</span> <span v-if="row.time_limit">H</span>
        </template>
      </el-table-column>
      <!-- 领取过期时间 -->
      <el-table-column :label="$t('tableHeader.Overdue_time')" align="center" min-width="85">
        <template slot-scope="{ row }">
          <span>{{ row.receive_exceed || '--' }}</span> <span v-if="row.receive_exceed">H</span>
        </template>
      </el-table-column>
      <!-- 激活时限 -->
      <el-table-column :label="$t('tableHeader.The_activation_time_limit')" align="center" min-width="85">
        <template slot-scope="{ row }">
          <span>{{ row.activation_exceed || '--' }}</span> <span v-if="row.activation_exceed">H</span>
        </template>
      </el-table-column>
      <!-- 交易回收时限 -->
      <el-table-column :label="$t('tableHeader.Transaction_recovery_time')" align="center" min-width="85">
        <template slot-scope="{ row }">
          <span>{{ row.trade_exceed || '--' }}</span> <span v-if="row.trade_exceed">H</span>
        </template>
      </el-table-column>
      <!-- 平仓次数 -->
      <el-table-column :label="$t('tableHeader.unwindNum')" align="center" min-width="85">
        <template slot-scope="{ row }">
          <span>{{ row.close_count || '--' }}</span>
        </template>
      </el-table-column>
      <!-- 充值≥ -->
      <el-table-column :label="$t('tableHeader.Top_up')" align="center" min-width="85">
        <template slot-scope="{ row }">
          <span>{{ row.amount_limit || '--' }}</span> <span v-if="row.amount_limit">USDT</span>
        </template>
      </el-table-column>
      <!-- 有效交易天数 -->
      <el-table-column :label="$t('tableHeader.Effective_trading_days')" align="center" min-width="85">
        <template slot-scope="{ row }">
          <span>{{ row.trader_day || '--' }}</span> <span v-if="row.trader_day">{{ $t('forms.day') }}</span>
        </template>
      </el-table-column>
      <!-- 有效金额限制 -->
      <!-- <el-table-column :label="$t('tableHeader.Effective_amount_limit')" align="center" min-width="85">
        <template slot-scope="{ row }">
          <span>{{ row.amount_limit || '--' }}</span> <span v-if="row.amount_limit">USDT</span>
        </template>
      </el-table-column> -->
      <!-- 赠金 -->
      <!-- <el-table-column :label="$t('tableHeader.With_gold')" align="center" min-width="85">
        <template slot-scope="{ row }">
          <span>{{ row.gift_amount || '--' }}</span> <span v-if="row.gift_amount">USDT</span>
        </template>
      </el-table-column> -->
      <!-- 好友充值 -->
      <el-table-column :label="$t('tableHeader.Friend_recharge')" align="center" min-width="85">
        <template slot-scope="{ row }">
          <span>{{ row.friends_recharge || '--' }}</span> <span v-if="row.friends_recharge">USDT</span>
        </template>
      </el-table-column>
      <!-- 奖励体验金 -->
      <el-table-column :label="$t('tableHeader.Bonus_experience_money')" align="center" min-width="85">
        <template slot-scope="{ row }">
          <span>{{ row.gift_amount || '--' }}</span> <span v-if="row.gift_amount">USDT</span>
        </template>
      </el-table-column>
      <!-- 赠金描述 -->
      <el-table-column :label="$t('tableHeader.Give_gold_description')" align="center" min-width="85">
        <template slot-scope="{ row }">
          <span>{{ row.giftinfo[0].content || '--' }}</span>
        </template>
      </el-table-column>
      <!-- 参与者 -->
      <el-table-column :label="$t('tableHeader.Participants')" align="center" min-width="85">
        <template slot-scope="{ row }">
          <span>{{ 
              row.partake == 0 ? $t('tableHeader.All_users') :
              row.partake == 1 ? $t('tableHeader.Non_proxy_user') :
              row.partake == 2 ? $t('tableHeader.Proxy_user') : '--'  
            }}
          </span>
        </template>
      </el-table-column>
      <!-- 创建时间 -->
      <el-table-column :label="$t('tableHeader.creationTime')" align="center" min-width="85">
        <template slot-scope="{ row }">
          <span>{{ row.create_time || '--' }}</span>
        </template>
      </el-table-column>
      <!-- 活动过期时间 -->
      <el-table-column :label="$t('tableHeader.Activity_expiration_time')" align="center" min-width="85">
        <template slot-scope="{ row }">
          <span>{{ row.exceed_time || '--' }}</span>
        </template>
      </el-table-column>
      <!-- 状态 -->
      <el-table-column :label="$t('tableHeader.state')" align="center" min-width="85">
        <template slot-scope="{ row }">
          <el-switch
            v-model="row.state"
            @change="changeSwitch(row)"
            active-color="#13ce66"
            inactive-color="#ccc"
            :active-value="1"
            :inactive-value="0"
            >
          </el-switch>
        </template>
      </el-table-column>
      <!-- 操作 -->
      <el-table-column :label="$t('tableHeader.operation')" align="center" min-width="200">
        <template slot-scope="{ row }">
          <el-button
            type="primary"
            size="mini"
            @click="handleEdit(row)"
          >{{ $t('buttons.edit') }}</el-button>
          <el-button 
            type="primary"
            size="mini"
            @click="handleChangeLanguage(row)"
          >{{ $t('buttons.Language_description') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10, 25, 50]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />

    <el-dialog
      :title="dialogVisibleType == 0 ? $t('dialog.Give_new_a_gold') : dialogVisibleType == 1 ? $t('dialog.Give_change_a_gold') : $t('dialog.Modifying_the_Language_description')"
      :visible.sync="dialogVisible"
      width="650px"
      :before-close="handleClose">

      <el-form ref="form" :model="form" :rules="rulesForm" label-width="130px" :label-position="labelPosition">
        <!-- 选择语言 -->
        <el-form-item :label="$t('forms.Select_the_language')" prop="selectLanguage">
          <el-select v-model="form.selectLanguage" multiple style="width: 100%" v-bind:disabled="isDisabledSelectLanguage">
            <el-option
              v-for="item in languageList"
              :key="item.lang_id"
              :label="item.lang_name"
              :value="item.lang_id">
            </el-option>
          </el-select>
        </el-form-item>
        <!-- 中文赠金名称 -->
        <el-form-item :label="$t('forms.ZHWithGoldName')" prop="ZHWithGoldName" v-if="form.selectLanguage.find(item => item == 0) == 0">
          <el-input v-model="form.ZHWithGoldName" v-bind:disabled="dialogVisibleType !== 0 && ZHLanguageDisable"></el-input>
          <el-button @click="handleChangeEdit(0)" v-if="dialogVisibleType == 2 && ZHLanguageDisable == true" type="primary" icon="el-icon-edit" circle style="margin-left: 10px"></el-button>
          <el-button @click="handleSuccessEdit(0)" v-if="dialogVisibleType == 2 && ZHLanguageDisable == false" type="success" icon="el-icon-check" circle style="margin-left: 10px"></el-button>
        </el-form-item>
        <!-- 英文赠金名称 -->
        <el-form-item :label="$t('forms.ENWithGoldName')" prop="ENWithGoldName" v-if="form.selectLanguage.find(item => item == 1) == 1">
          <el-input v-model="form.ENWithGoldName" v-bind:disabled="dialogVisibleType !== 0 && ENLanguageDisable"></el-input>
          <el-button @click="handleChangeEdit(1)" v-if="dialogVisibleType == 2 && ENLanguageDisable == true" type="primary" icon="el-icon-edit" circle style="margin-left: 10px"></el-button>
          <el-button @click="handleSuccessEdit(1)" v-if="dialogVisibleType == 2 && ENLanguageDisable == false" type="success" icon="el-icon-check" circle style="margin-left: 10px"></el-button>
        </el-form-item>
        <!-- 繁中赠金名称 -->
        <el-form-item :label="$t('forms.TCWithGoldName')" prop="TCWithGoldName" v-if="form.selectLanguage.find(item => item == 2) == 2">
          <el-input v-model="form.TCWithGoldName" v-bind:disabled="dialogVisibleType !== 0 && TCLanguageDisable"></el-input>
          <el-button @click="handleChangeEdit(2)" v-if="dialogVisibleType == 2 && TCLanguageDisable == true" type="primary" icon="el-icon-edit" circle style="margin-left: 10px"></el-button>
          <el-button @click="handleSuccessEdit(2)" v-if="dialogVisibleType == 2 && TCLanguageDisable == false" type="success" icon="el-icon-check" circle style="margin-left: 10px"></el-button>
        </el-form-item>
        <!-- 韩文赠金名称 -->
        <el-form-item :label="$t('forms.KOWithGoldName')" prop="KOWithGoldName" v-if="form.selectLanguage.find(item => item == 3) == 3">
          <el-input v-model="form.KOWithGoldName" v-bind:disabled="dialogVisibleType !== 0 && KOLanguageDisable"></el-input>
          <el-button @click="handleChangeEdit(3)" v-if="dialogVisibleType == 2 && KOLanguageDisable == true" type="primary" icon="el-icon-edit" circle style="margin-left: 10px"></el-button>
          <el-button @click="handleSuccessEdit(3)" v-if="dialogVisibleType == 2 && KOLanguageDisable == false" type="success" icon="el-icon-check" circle style="margin-left: 10px"></el-button>
        </el-form-item>
        <!-- 越南赠金名称 -->
        <el-form-item :label="$t('forms.VNWithGoldName')" prop="VNWithGoldName" v-if="form.selectLanguage.find(item => item == 4) == 4">
          <el-input v-model="form.VNWithGoldName" v-bind:disabled="dialogVisibleType !== 0 && VNLanguageDisable"></el-input>
          <el-button @click="handleChangeEdit(4)" v-if="dialogVisibleType == 2 && VNLanguageDisable == true" type="primary" icon="el-icon-edit" circle style="margin-left: 10px"></el-button>
          <el-button @click="handleSuccessEdit(4)" v-if="dialogVisibleType == 2 && VNLanguageDisable == false" type="success" icon="el-icon-check" circle style="margin-left: 10px"></el-button>
        </el-form-item>
        <!-- 印尼赠金名称 -->
        <el-form-item :label="$t('forms.IDWithGoldName')" prop="IDWithGoldName" v-if="form.selectLanguage.find(item => item == 5) == 5">
          <el-input v-model="form.IDWithGoldName" v-bind:disabled="dialogVisibleType !== 0 && IDLanguageDisable"></el-input>
          <el-button @click="handleChangeEdit(5)" v-if="dialogVisibleType == 2 && IDLanguageDisable == true" type="primary" icon="el-icon-edit" circle style="margin-left: 10px"></el-button>
          <el-button @click="handleSuccessEdit(5)" v-if="dialogVisibleType == 2 && IDLanguageDisable == false" type="success" icon="el-icon-check" circle style="margin-left: 10px"></el-button>
        </el-form-item>
        <!-- 赠金类型 -->
        <el-form-item :label="$t('filters.Give_gold_type')" prop="withGoldType">
          <el-select v-model="form.withGoldType" :placeholder="$t('dialog.pleaseSelect')" style="width: 100%" clearable v-bind:disabled="isDisabledEdit">
            <el-option
              v-for="item in withGoldTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <!-- 活动过期时间 -->
        <el-form-item :label="$t('tableHeader.Activity_expiration_time')" prop="dateTimes">
          <el-date-picker
            v-bind:disabled="isDisabledEdit"
            style="width: 100%"
            v-model="form.dateTimes"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetime"
            :placeholder="$t('dialog.Select_date_and_time')">
          </el-date-picker>
        </el-form-item>

        <div v-if="form.withGoldType !== ''" style="width: 100%; height: 1px; background: #ccc; margin-bottom: 20px;"></div>

        <!-- 注册时限 -->
        <el-form-item :label="$t('tableHeader.Registration_deadline')" v-if="form.withGoldType !== '' && form.withGoldType == 1">
          <el-input v-model="form.timeLimit" v-bind:disabled="isDisabledEdit" :placeholder="$t('dialog.Optional')" oninput="value=value.replace(/[^\d]/g, '')">
            <template slot="suffix">
              <span>H</span>
            </template>
          </el-input>
        </el-form-item>
        <!-- 领取过期时限 -->
        <el-form-item :label="$t('tableHeader.Overdue_time')" v-if="form.withGoldType !== '' && form.withGoldType == 1" prop="overdueTime">
          <el-input v-model="form.overdueTime" v-bind:disabled="isDisabledEdit" oninput="value=value.replace(/[^\d]/g, '')">
            <template slot="suffix">
              <span>H</span>
            </template>
          </el-input>
        </el-form-item>
        <!-- 激活时限 -->
        <el-form-item :label="$t('tableHeader.The_activation_time_limit')" v-if="form.withGoldType !== '' && form.withGoldType == 1" prop="activationTimeLimit">
          <el-input v-model="form.activationTimeLimit" v-bind:disabled="isDisabledEdit" oninput="value=value.replace(/[^\d]/g, '')">
            <template slot="suffix">
              <span>H</span>
            </template>
          </el-input>
        </el-form-item>
        <!-- 回收交易时限 -->
        <el-form-item :label="$t('forms.Time_limit_for_recovery_transaction')" v-if="form.withGoldType !== '' && form.withGoldType == 1" prop="recyclingTradingTimeLimit">
          <el-input v-model="form.recyclingTradingTimeLimit" v-bind:disabled="isDisabledEdit" oninput="value=value.replace(/[^\d]/g, '')">
            <template slot="suffix">
              <span>H</span>
            </template>
          </el-input>
        </el-form-item>
        <!-- 充值≥ -->
        <el-form-item :label="$t('tableHeader.Top_up')" v-if="form.withGoldType !== '' && form.withGoldType == 1" prop="topUp">
          <el-input v-model="form.topUp" v-bind:disabled="isDisabledEdit" oninput="value=value.match(/\d+(\.\d{0,2})?/) ? value.match(/\d+(\.\d{0,8})?/)[0] : ''">
            <template slot="suffix">
              <span>USDT</span>
            </template>
          </el-input>
        </el-form-item>
        <!-- 奖励体验金 -->
        <el-form-item :label="$t('tableHeader.Bonus_experience_money')" v-if="form.withGoldType !== '' && form.withGoldType == 1" prop="bonusExperienceMoney">
          <el-input v-model="form.bonusExperienceMoney" v-bind:disabled="isDisabledEdit" oninput="value=value.match(/\d+(\.\d{0,2})?/) ? value.match(/\d+(\.\d{0,8})?/)[0] : ''">
            <template slot="suffix">
              <span>USDT</span>
            </template>
          </el-input>
        </el-form-item>
        <!-- 平仓次数 -->
        <el-form-item :label="$t('tableHeader.unwindNum')" v-if="form.withGoldType !== '' && form.withGoldType !== 1" prop="positionsNum">
          <el-input v-model="form.positionsNum" v-bind:disabled="isDisabledEdit">
          </el-input>
        </el-form-item>
        <!-- 有效交易天数 5-->
        <el-form-item :label="$t('tableHeader.Effective_trading_days')" v-if="form.withGoldType !== '' && form.withGoldType !== 1" prop="effectiveTradingDays">
          <el-input v-model="form.effectiveTradingDays" v-bind:disabled="isDisabledEdit">
            <template slot="suffix">
              <span>{{ $t('forms.day') }}</span>
            </template>
          </el-input>
        </el-form-item>
        <!-- 有效交易额≥ -->
        <!-- <el-form-item :label="$t('forms.Effective_trading_volume')" v-if="form.withGoldType !== '' && form.withGoldType !== 1" prop="effectiveTurnover">
          <el-input v-model="form.effectiveTurnover" v-bind:disabled="isDisabledEdit">
            <template slot="suffix">
              <span>USDT</span>
            </template>
          </el-input>
        </el-form-item> -->
        <!-- 赠金 -->
        <el-form-item :label="$t('tableHeader.With_gold')" v-if="form.withGoldType !== '' && form.withGoldType !== 1" prop="giftAmount">
          <el-input v-model="form.giftAmount" v-bind:disabled="isDisabledEdit">
            <template slot="suffix">
              <span>USDT</span>
            </template>
          </el-input>
        </el-form-item>
        <!-- 好友充值 -->
        <el-form-item :label="$t('tableHeader.Friend_recharge')" v-if="form.withGoldType !== '' && form.withGoldType !== 1" prop="friendRecharge">
          <el-input v-model="form.friendRecharge" v-bind:disabled="isDisabledEdit">
            <template slot="suffix">
              <span>USDT</span>
            </template>
          </el-input>
        </el-form-item>
        <!-- 参加活动用户类型 -->
         <el-form-item :label="$t('forms.Type_of_the_user_who_participates_in_the_event')" v-if="form.withGoldType !== ''">
          <el-select v-model="form.userTypes" :placeholder="$t('dialog.Please_select_the_active_user_type')" style="width: 100%" v-bind:disabled="isDisabledSelectLanguage">
            <el-option :label="$t('tableHeader.All_users')"   :value="0"></el-option>
            <el-option :label="$t('tableHeader.Non_proxy_user')" :value="1"></el-option>
            <el-option :label="$t('tableHeader.Proxy_user')"   :value="2"></el-option>
          </el-select>
        </el-form-item>
        <!-- 中文说明 -->
        <el-form-item :label="$t('forms.ZHInstructions')" v-if="form.withGoldType !== '' && form.selectLanguage.find(item => item == 0) == 0" prop="ZHInstructions">
          <el-input v-model="form.ZHInstructions" v-bind:disabled="dialogVisibleType !== 0 && ZHLanguageDisable"></el-input>
          <el-button @click="handleChangeEdit(0)" v-if="dialogVisibleType == 2 && ZHLanguageDisable == true" type="primary" icon="el-icon-edit" circle style="margin-left: 10px"></el-button>
          <el-button @click="handleSuccessEdit(0)" v-if="dialogVisibleType == 2 && ZHLanguageDisable == false" type="success" icon="el-icon-check" circle style="margin-left: 10px"></el-button>
        </el-form-item>
        <!-- 英文说明 -->
        <el-form-item :label="$t('forms.ENInstructions')" v-if="form.withGoldType !== '' && form.selectLanguage.find(item => item == 1) == 1" prop="ENInstructions">
          <el-input v-model="form.ENInstructions" v-bind:disabled="dialogVisibleType !== 0 && ENLanguageDisable"></el-input>
          <el-button @click="handleChangeEdit(1)" v-if="dialogVisibleType == 2 && ENLanguageDisable == true" type="primary" icon="el-icon-edit" circle style="margin-left: 10px"></el-button>
          <el-button @click="handleSuccessEdit(1)" v-if="dialogVisibleType == 2 && ENLanguageDisable == false" type="success" icon="el-icon-check" circle style="margin-left: 10px"></el-button>
        </el-form-item>
        <!-- 繁中说明 -->
        <el-form-item :label="$t('forms.TCInstructions')" v-if="form.withGoldType !== '' && form.selectLanguage.find(item => item == 2) == 2" prop="TCInstructions">
          <el-input v-model="form.TCInstructions" v-bind:disabled="dialogVisibleType !== 0 && TCLanguageDisable"></el-input>
          <el-button @click="handleChangeEdit(2)" v-if="dialogVisibleType == 2 && TCLanguageDisable == true" type="primary" icon="el-icon-edit" circle style="margin-left: 10px"></el-button>
          <el-button @click="handleSuccessEdit(2)" v-if="dialogVisibleType == 2 && TCLanguageDisable == false" type="success" icon="el-icon-check" circle style="margin-left: 10px"></el-button>
        </el-form-item>
        <!-- 韩文说明 -->
        <el-form-item :label="$t('forms.KOInstructions')" v-if="form.withGoldType !== '' && form.selectLanguage.find(item => item == 3) == 3" prop="KOInstructions">
          <el-input v-model="form.KOInstructions" v-bind:disabled="dialogVisibleType !== 0 && KOLanguageDisable"></el-input>
          <el-button @click="handleChangeEdit(3)" v-if="dialogVisibleType == 2 && KOLanguageDisable == true" type="primary" icon="el-icon-edit" circle style="margin-left: 10px"></el-button>
          <el-button @click="handleSuccessEdit(3)" v-if="dialogVisibleType == 2 && KOLanguageDisable == false" type="success" icon="el-icon-check" circle style="margin-left: 10px"></el-button>
        </el-form-item>
        <!-- 越南说明 -->
        <el-form-item :label="$t('forms.VNInstructions')" v-if="form.withGoldType !== '' && form.selectLanguage.find(item => item == 4) == 4" prop="VNInstructions">
          <el-input v-model="form.VNInstructions" v-bind:disabled="dialogVisibleType !== 0 && VNLanguageDisable"></el-input>
          <el-button @click="handleChangeEdit(4)" v-if="dialogVisibleType == 2 && VNLanguageDisable == true" type="primary" icon="el-icon-edit" circle style="margin-left: 10px"></el-button>
          <el-button @click="handleSuccessEdit(4)" v-if="dialogVisibleType == 2 && VNLanguageDisable == false" type="success" icon="el-icon-check" circle style="margin-left: 10px"></el-button>
        </el-form-item>
        <!-- 印尼说明 -->
        <el-form-item :label="$t('forms.IDInstructions')" v-if="form.withGoldType !== '' && form.selectLanguage.find(item => item == 5) == 5" prop="IDInstructions">
          <el-input v-model="form.IDInstructions" v-bind:disabled="dialogVisibleType !== 0 && IDLanguageDisable"></el-input>
          <el-button @click="handleChangeEdit(5)" v-if="dialogVisibleType == 2 && IDLanguageDisable == true" type="primary" icon="el-icon-edit" circle style="margin-left: 10px"></el-button>
          <el-button @click="handleSuccessEdit(5)" v-if="dialogVisibleType == 2 && IDLanguageDisable == false" type="success" icon="el-icon-check" circle style="margin-left: 10px"></el-button>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">{{ $t('buttons.cancel') }}</el-button>
        <el-button type="primary" @click="handleSure">{{ $t('buttons.determine') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { 
  giftlist,   // 获取增进活动泪飙
  addgift,    // 新建赠金
  updategiftinfo,     // 更新赠金活动
  updategiftlange,    // 更新赠金活动语言
  updategiftstatus,   // 更新赠金活动状态
  getlangelist,       // 语言列表
} from "@/api/activity";

export default {
  name: "giftCashActive",
  data() {
    // 选择语言
    let selectLanguage_v = (rule, value, callback) => {
      if (value.length == 0) {
        return  callback(new Error(this.$t('dialog.Please_select_a_language')))
      } else if (value.find(item => item == 0) !== 0 && value.find(item => item == 0) !== 1) {
        return callback(new Error(this.$t('dialog.The_name_English_and_Chinese_is_mandatory')))
      } else (
        callback()
      )
    }
    // 中文赠金名称
    let ZHWithGoldName_v = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('dialog.Please_select_ZHWithGoldName')))
      } else {
        callback()
      }
    }
    // 英文赠金名称
    let ENWithGoldName_v = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('dialog.Please_select_ENWithGoldName')))
      } else {
        callback()
      }
    }
    // 繁中赠金名称
    let TCWithGoldName_v = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('dialog.Please_select_TCWithGoldName')))
      } else {
        callback()
      }
    }
    // 韩文赠金名称
    let KOWithGoldName_v = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('dialog.Please_select_KOWithGoldName')))
      } else {
        callback()
      }
    }
    // 越南赠金名称
    let VNWithGoldName_v = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('dialog.Please_select_VNWithGoldName')))
      } else {
        callback()
      }
    }
    // 印尼赠金名称
    let IDWithGoldName_v = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('dialog.Please_select_IDWithGoldName')))
      } else {
        callback()
      }
    }
    // 赠金类型
    let withGoldType_v = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('dialog.Please_select_the_type_of_bonus')))
      } else {
        callback()
      }
    }
    // 注册时限
    // let timeLimit_v = (rule, value, callback) => {
    //   if (!value) {
    //     return callback(new Error(this.$t('dialog.Please_enter_a_time_limit')))
    //   } else {
    //     callback()
    //   }
    // }
    // 活动过期时间
    let dateTimes_v = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('dialog.Please_select_the_expiration_time')))
      } else {
        callback()
      }
    }
    // 领取过期期限
    let overdueTime_v = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('dialog.Please_enter_the_expiration_date')))
      } else {
        callback()
      }
    }
    // 激活时限
    let activationTimeLimit_v = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('dialog.Please_enter_the_activation_time_limit')))
      } else {
        callback()
      }
    }
    // 回收交易时限
    let recyclingTradingTimeLimit_v = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('dialog.Please_enter_the_recycle_transaction_time_limit')))
      } else {
        callback()
      }
    }
    // 平仓次数
    let positionsNum_v = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('dialog.Please_enter_closing_times')))
      } else {
        callback()
      }
    }
    // 有效交易天数
    let effectiveTradingDays_v = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('dialog.Please_enter_valid_trading_days')))
      } else {
        callback()
      }
    }
    // 有效交易额
    // let effectiveTurnover_v = (rule, value, callback) => {
    //   if (!value) {
    //     return callback(new Error(this.$t('dialog.Please_enter_valid_transaction_amount')))
    //   } else {
    //     callback()
    //   }
    // }
    // 赠金
    // let giftAmount_v = (rule, value, callback) => {
    //   if (!value) {
    //     return callback(new Error(this.$t('dialog.Please_enter_the_bonus')))
    //   } else {
    //     callback()
    //   }
    // }
    // 好友充值
    let friendRecharge_v = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('dialog.Please_input_the_friend_recharge_amount')))
      } else {
        callback()
      }
    }
    // 中文说明
    let ZHInstructions_v = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('dialog.ZHInstructions')))
      } else {
        callback()
      }
    }
    // 英文说明
    let ENInstructions_v = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('dialog.ENInstructions')))
      } else {
        callback()
      }
    }
    // 繁中说明
    let TCInstructions_v = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('dialog.TCInstructions')))
      } else {
        callback()
      }
    }
    // 韩文说明
    let KOInstructions_v = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('dialog.KOInstructions')))
      } else {
        callback()
      }
    }
    // 越南说明
    let VNInstructions_v = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('dialog.VNInstructions')))
      } else {
        callback()
      }
    }
    // 印尼说明
    let IDInstructions_v = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('dialog.IDInstructions')))
      } else {
        callback()
      }
    }
    // 充值≥
    let topUp_v = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('dialog.Please_enter_the_recharge_amount')))
      } else {
        callback()
      }
    }
    // 奖励体验金
    let bonusExperienceMoney_v = (rulle, value, callback) => {
      if (!value) {
        return  callback(new Error(this.$t('dialog.Please_enter_the_reward_experience_money')))
      } else {
        callback()
      }
    }
    return {
      listLoading: false, // 懒加载
      total: 0,           // 总条数
      tableList: null,    // 表格数据
      filterTime1: [],    // 时间
      filterTime2: [],    // 时间
      listQuery: {
        category: '',     // 赠金类型
        activeTitle: "",  // 活动标题
        status: "",       // 状态
        star1: "",        // 开始
        end1: "",         // 结束
        star2: "",        // 开始
        end2: "",         // 结束
        pageNo: 1,        // 页树
        pagesize: 10,     // 条数
      },
      languageList: [],   // 语言列表
      statusOptions: [
        { value: 1, label: this.$t('tableHeader.open') },
        { value: 0, label: this.$t('buttons.close') },
      ],                  // 状态

      labelPosition: 'left',            // label考左
      dialogVisible: false,             // 新建赠金弹框
      dialogVisibleType: 0,             // 0添加 1编辑 2编辑语言
      withGoldTypeOptions: [
        { value: 1, label: this.$t('tableHeader.Top_up_for_the_first_time') },
        // { value: 2, label: this.$t('tableHeader.For_the_first_time_trade') },
        // { value: 3, label: this.$t('tableHeader.Novice_transaction_volume_meets_the_standard') },
        // { value: 4, label: this.$t('tableHeader.Active_users') },
        // { value: 5, label: this.$t('tableHeader.Top_up_rebate') },
        // { value: 6, label: this.$t('tableHeader.Invite_friends') },
      ],  // 赠金类型
      form: {
        selectLanguage: [0,1],        // 选择语言
        ZHWithGoldName: '',           // 中文赠金name
        ENWithGoldName: '',           // 英文赠金name
        TCWithGoldName: '',           // 繁中赠金name
        KOWithGoldName: '',           // 韩文赠金name
        VNWithGoldName: '',           // 越南赠金name
        IDWithGoldName: '',           // 印尼赠金name
        withGoldType: '',             // 赠金类型
        timeLimit: '',                // 注册时限
        dateTimes: '',                // 活动过期时间
        overdueTime: '',              // 领取过期时限
        activationTimeLimit: '',      // 激活时限
        recyclingTradingTimeLimit: '',// 回收交易时限
        positionsNum: '',             // 平仓次数
        effectiveTradingDays: '',     // 有效交易天数
        // effectiveTurnover: '',        // 有效交易额
        // giftAmount: '',               // 赠金
        friendRecharge: '',           // 好友充值
        userTypes: 0,                 // 用户类型
        ZHInstructions: '',           // 中文说明
        ENInstructions: '',           // 英文说明
        TCInstructions: '',           // 繁中说明
        KOInstructions: '',           // 韩文说明
        VNInstructions: '',           // 越南说明
        IDInstructions: '',           // 印尼说明
        topUp: '',                    // 充值≥
        bonusExperienceMoney: '',     // 奖励体验金
      },                              // form

      rulesForm: {
        selectLanguage:       [{validator: selectLanguage_v,        trigger: 'blur'}],    // 选择语言
        ZHWithGoldName:       [{validator: ZHWithGoldName_v,        trigger: 'blur'}],    // 中文赠金名称
        ENWithGoldName:       [{validator: ENWithGoldName_v,        trigger: 'blur'}],    // 英文赠金name
        TCWithGoldName:       [{validator: TCWithGoldName_v,        trigger: 'blur'}],    // 繁中赠金name
        KOWithGoldName:       [{validator: KOWithGoldName_v,        trigger: 'blur'}],    // 韩文赠金name
        VNWithGoldName:       [{validator: VNWithGoldName_v,        trigger: 'blur'}],    // 越南赠金name
        IDWithGoldName:       [{validator: IDWithGoldName_v,        trigger: 'blur'}],    // 印尼赠金name
        withGoldType:         [{validator: withGoldType_v,          trigger: 'change'}],  // 赠金类型
        // timeLimit:            [{validator: timeLimit_v,             trigger: 'blur'}],    // 时间限制
        dateTimes:            [{validator: dateTimes_v,             trigger: 'change'}],  // 活动过期时间
        overdueTime:          [{validator: overdueTime_v,           trigger: 'blur'}],    // 领取过期时限
        activationTimeLimit:  [{validator: activationTimeLimit_v,   trigger: 'blur'}],    // 激活时限
        recyclingTradingTimeLimit: [{validator: recyclingTradingTimeLimit_v,        trigger: 'blur'}],// 回收交易时限
        positionsNum:         [{validator: positionsNum_v,          trigger: 'blur'}],    // 平仓次数
        effectiveTradingDays: [{validator: effectiveTradingDays_v,  trigger: 'blur'}],    // 有效交易天数
        // effectiveTurnover:    [{validator: effectiveTurnover_v,     trigger: 'blur'}],    // 有效交易额
        // giftAmount:           [{validator: giftAmount_v,            trigger: 'blur'}],    // 赠金
        friendRecharge:       [{validator: friendRecharge_v,        trigger: 'blur'}],    // 好友充值
        ZHInstructions:       [{validator: ZHInstructions_v,        trigger: 'blur'}],    // 中文说明
        ENInstructions:       [{validator: ENInstructions_v,        trigger: 'blur'}],    // 英文说明
        TCInstructions:       [{validator: TCInstructions_v,        trigger: 'blur'}],    // 繁中说明
        KOInstructions:       [{validator: KOInstructions_v,        trigger: 'blur'}],    // 韩文说明
        VNInstructions:       [{validator: VNInstructions_v,        trigger: 'blur'}],    // 越南说明
        IDInstructions:       [{validator: IDInstructions_v,        trigger: 'blur'}],    // 印尼说明
        topUp:                [{validator: topUp_v,                 trigger: 'blur'}],    // 充值
        bonusExperienceMoney: [{validator: bonusExperienceMoney_v,  trigger: 'blur'}],    // 奖励体验金
      },

      isDisabledEdit: false,            // 编辑 禁用
      isDisabledSelectLanguage: false,  // 选择语言 禁用
      ZHLanguageDisable: true,
      ENLanguageDisable: true,
      TCLanguageDisable: true,
      KOLanguageDisable: true,
      VNLanguageDisable: true,
      IDLanguageDisable: true,
    }
  },

  components: {},

  computed: {},

  mounted() {
    this.getList();
  },

  methods: {
    // 状态初始化
    disables() {
      this.ZHLanguageDisable = true;
      this.ENLanguageDisable = true;
      this.TCLanguageDisable = true;
      this.KOLanguageDisable = true;
      this.VNLanguageDisable = true;
      this.IDLanguageDisable = true;
    },
    // 点击编辑
    handleChangeEdit(type) {
      switch(type) {
        case 0:
          this.ZHLanguageDisable = false;
          this.ENLanguageDisable = true;
          this.TCLanguageDisable = true;
          this.KOLanguageDisable = true;
          this.VNLanguageDisable = true;
          this.IDLanguageDisable = true;
        break
        case 1: 
          this.ZHLanguageDisable = true;
          this.ENLanguageDisable = false;
          this.TCLanguageDisable = true;
          this.KOLanguageDisable = true;
          this.VNLanguageDisable = true;
          this.IDLanguageDisable = true;
        break 
        case 2: 
          this.ZHLanguageDisable = true;
          this.ENLanguageDisable = true;
          this.TCLanguageDisable = false;
          this.KOLanguageDisable = true;
          this.VNLanguageDisable = true;
          this.IDLanguageDisable = true;
        break 
        case 3: 
          this.ZHLanguageDisable = true;
          this.ENLanguageDisable = true;
          this.TCLanguageDisable = true;
          this.KOLanguageDisable = false;
          this.VNLanguageDisable = true;
          this.IDLanguageDisable = true;
        break 
        case 4: 
          this.ZHLanguageDisable = true;
          this.ENLanguageDisable = true;
          this.TCLanguageDisable = true;
          this.KOLanguageDisable = true;
          this.VNLanguageDisable = false;
          this.IDLanguageDisable = true;
        break 
        case 5: 
          this.ZHLanguageDisable = true;
          this.ENLanguageDisable = true;
          this.TCLanguageDisable = true;
          this.KOLanguageDisable = true;
          this.VNLanguageDisable = true;
          this.IDLanguageDisable = false;
        break 
      }
    },
    // 点击完成
    handleSuccessEdit(type) {
      updategiftlange({
        upid:       Number(this.isID), // upid
        lang_type:  Number(type), // 语言类型
        title:      type==0?this.form.ZHWithGoldName : type==1?this.form.ENWithGoldName : type==2?this.form.TCWithGoldName : type==3?this.form.KOWithGoldName : type==4?this.form.VNWithGoldName : this.form.IDWithGoldName,  // 赠金名称
        content:    type==0?this.form.ZHInstructions : type==1?this.form.ENInstructions : type==2?this.form.TCInstructions : type==3?this.form.KOInstructions : type==4?this.form.VNInstructions : this.form.IDInstructions,  // 描述
      }).then(() => {
        this.$notify({
          title: this.$t('others.Modify_the_success'),
          type: "success",
          duration: 2000,
        });
        this.disables()
        this.dialogVisible = false;
        this.getList();
        this.formClear();
      })
    },


    // 清除
    formClear() {
      this.form = {
        selectLanguage: [0,1],        // 选择语言
        ZHWithGoldName: '',           // 中文赠金name
        ENWithGoldName: '',           // 英文赠金name
        TCWithGoldName: '',           // 繁中赠金name
        KOWithGoldName: '',           // 韩文赠金name
        VNWithGoldName: '',           // 越南赠金name
        IDWithGoldName: '',           // 印尼赠金name
        withGoldType: '',             // 赠金类型
        timeLimit: '',                // 注册时限
        dateTimes: '',                // 活动过期时间
        overdueTime: '',              // 领取过期时限
        activationTimeLimit: '',      // 激活时限
        recyclingTradingTimeLimit: '',// 回收交易时限
        positionsNum: '',             // 平仓次数
        effectiveTradingDays: '',     // 有效交易天数
        // effectiveTurnover: '',        // 有效交易额
        // giftAmount: '',               // 赠金
        friendRecharge: '',           // 好友充值
        userTypes: 0,                 // 用户类型
        ZHInstructions: '',           // 中文说明
        ENInstructions: '',           // 英文说明
        TCInstructions: '',           // 繁中说明
        KOInstructions: '',           // 韩文说明
        VNInstructions: '',           // 越南说明
        IDInstructions: '',           // 印尼说明
        topUp: '',                    // 充值大于等于
        bonusExperienceMoney: '',     // 奖励体验金
      };
      this.$refs['form'].resetFields();
    },
    // 新建赠金
    handleNewWithGold() {
      this.dialogVisibleType = 0;           // 添加类型
      this.dialogVisible = true;            // 打开弹框
      this.isDisabledEdit = false;          // 编辑 取消禁用
      this.isDisabledSelectLanguage = false;// 选择语言 取消禁用
    },
    // icon
    handleClose() {
      this.dialogVisible = false;     // 关闭弹框
      this.formClear();               // 清空内容
      this.disables()
    },
    // 取消
    handleCancel() {
      this.dialogVisible = false;     // 关闭弹框
      this.formClear();               // 清空内容
      this.disables()
    },
    // 修改语言
    handleChangeLanguage(v) {
      let arr = []                          // 空数组
      this.dialogVisibleType = 2;           // 编辑类型
      this.isDisabledEdit = true;           // 编辑 取消禁用
      this.isDisabledSelectLanguage = true; // 选择语言 取消禁用
      this.dialogVisible = true;            // 打开弹框
      this.isID = v.id                      // 获取数据id
      v.giftinfo.map(item => {
          arr.push(item.lang_type)
      })                                    // 存入数组
      this.form = {
        selectLanguage: arr,                        // 选择语言
        ZHWithGoldName: arr.find(item => item == 0) == 0 ? v.giftinfo.find(item => item.lang_type == 0).title : null,   // 中文赠金name
        ENWithGoldName: arr.find(item => item == 1) == 1 ? v.giftinfo.find(item => item.lang_type == 1).title : null,   // 英文赠金name
        TCWithGoldName: arr.find(item => item == 2) == 2 ? v.giftinfo.find(item => item.lang_type == 2).title : null,   // 繁中赠金name
        KOWithGoldName: arr.find(item => item == 3) == 3 ? v.giftinfo.find(item => item.lang_type == 3).title : null,   // 韩文赠金name
        VNWithGoldName: arr.find(item => item == 4) == 4 ? v.giftinfo.find(item => item.lang_type == 4).title : null,   // 越南赠金name
        IDWithGoldName: arr.find(item => item == 5) == 5 ? v.giftinfo.find(item => item.lang_type == 5).title : null,   // 印尼赠金name
        withGoldType:         v.category,           // 赠金类型
        timeLimit:            v.time_limit,         // 注册时限
        dateTimes:            v.exceed_time,        // 活动过期时间
        overdueTime:          v.receive_exceed,     // 领取过期时限
        activationTimeLimit:  v.activation_exceed,  // 激活时限
        recyclingTradingTimeLimit: v.trade_exceed,  // 回收交易时限
        positionsNum:         v.close_count,        // 平仓次数
        effectiveTradingDays: v.trader_day,         // 有效交易天数
        topUp:                v.amount_limit,       // 充值≥
        bonusExperienceMoney: v.gift_amount,        // 获取体验金
        friendRecharge:       v.friends_recharge,   // 好友充值
        userTypes:            v.partake,            // 用户类型
        ZHInstructions: arr.find(item => item == 0) == 0 ?  v.giftinfo.find(item => item.lang_type == 0).content : null,  // 中文说明
        ENInstructions: arr.find(item => item == 1) == 1 ?  v.giftinfo.find(item => item.lang_type == 1).content : null,  // 英文说明
        TCInstructions: arr.find(item => item == 2) == 2 ?  v.giftinfo.find(item => item.lang_type == 2).content : null,  // 繁中说明
        KOInstructions: arr.find(item => item == 3) == 3 ?  v.giftinfo.find(item => item.lang_type == 3).content : null,  // 韩文说明
        VNInstructions: arr.find(item => item == 4) == 4 ?  v.giftinfo.find(item => item.lang_type == 4).content : null,  // 越南说明
        IDInstructions: arr.find(item => item == 5) == 5 ?  v.giftinfo.find(item => item.lang_type == 5).content : null,  // 印尼说明
      };
    },
    // 编辑
    handleEdit(v) {
      let arr = []                          // 空数组
      this.dialogVisibleType = 1;           // 编辑类型
      this.isDisabledEdit = false;          // 编辑 取消禁用
      this.isDisabledSelectLanguage = true; // 选择语言 取消禁用
      this.dialogVisible = true;            // 打开弹框
      this.isID = v.id                      // 获取数据id
      v.giftinfo.map(item => {
          arr.push(item.lang_type)
      })                                    // 存入数组
      this.form = {
        selectLanguage: arr,                        // 选择语言
        ZHWithGoldName: arr.find(item => item == 0) == 0 ? v.giftinfo.find(item => item.lang_type == 0).title : null,   // 中文赠金name
        ENWithGoldName: arr.find(item => item == 1) == 1 ? v.giftinfo.find(item => item.lang_type == 1).title : null,   // 英文赠金name
        TCWithGoldName: arr.find(item => item == 2) == 2 ? v.giftinfo.find(item => item.lang_type == 2).title : null,   // 繁中赠金name
        KOWithGoldName: arr.find(item => item == 3) == 3 ? v.giftinfo.find(item => item.lang_type == 3).title : null,   // 韩文赠金name
        VNWithGoldName: arr.find(item => item == 4) == 4 ? v.giftinfo.find(item => item.lang_type == 4).title : null,   // 越南赠金name
        IDWithGoldName: arr.find(item => item == 5) == 5 ? v.giftinfo.find(item => item.lang_type == 5).title : null,   // 印尼赠金name
        withGoldType:         v.category,           // 赠金类型
        timeLimit:            v.time_limit,         // 注册时限
        dateTimes:            v.exceed_time,        // 活动过期时间
        overdueTime:          v.receive_exceed,     // 领取过期时限
        activationTimeLimit:  v.activation_exceed,  // 激活时限
        recyclingTradingTimeLimit: v.trade_exceed,  // 回收交易时限
        positionsNum:         v.close_count,        // 平仓次数
        effectiveTradingDays: v.trader_day,         // 有效交易天数
        topUp:                v.amount_limit,       // 充值
        bonusExperienceMoney: v.gift_amount,        // 获取体验金
        friendRecharge:       v.friends_recharge,   // 好友充值
        userTypes:            v.partake,            // 用户类型
        ZHInstructions: arr.find(item => item == 0) == 0 ?  v.giftinfo.find(item => item.lang_type == 0).content : null,  // 中文说明
        ENInstructions: arr.find(item => item == 1) == 1 ?  v.giftinfo.find(item => item.lang_type == 1).content : null,  // 英文说明
        TCInstructions: arr.find(item => item == 2) == 2 ?  v.giftinfo.find(item => item.lang_type == 2).content : null,  // 繁中说明
        KOInstructions: arr.find(item => item == 3) == 3 ?  v.giftinfo.find(item => item.lang_type == 3).content : null,  // 韩文说明
        VNInstructions: arr.find(item => item == 4) == 4 ?  v.giftinfo.find(item => item.lang_type == 4).content : null,  // 越南说明
        IDInstructions: arr.find(item => item == 5) == 5 ?  v.giftinfo.find(item => item.lang_type == 5).content : null,  // 印尼说明
      };
    },
    // 确定
    handleSure() {
      // 添加
      if (this.dialogVisibleType == 0) {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            this.$confirm(this.$t('dialog.Have_you_confirmed_creation_activity'), {
              distinguishCancelAndClose: true,
              confirmButtonText: this.$t('buttons.determine'),
              cancelButtonText: this.$t('buttons.cancel')
            }).then(() => {
              addgift({
                time_limit:         this.form.timeLimit == '' ? 0                 : Number(this.form.timeLimit),                // 注册时限
                close_count:        this.form.positionsNum == '' ? 0              : Number(this.form.positionsNum),             // 平仓次数
                trader_day:         this.form.effectiveTradingDays == '' ? 0      : Number(this.form.effectiveTradingDays),     // 有效交易天数
                category:           this.form.withGoldType == '' ? 0              : Number(this.form.withGoldType),             // 1首次充值，2首次交易，3新手交易量达标，4活跃用户，5，充值返利，6邀请好友，
                partake:            this.form.userTypes  == '' ? 0                : Number(this.form.userTypes),                // 参与者0 全部用户 1 非代理用户 2代理，
                amount_limit:       this.form.topUp == '' ? 0                     : Number(this.form.topUp),                    // 充值
                gift_amount:        this.form.bonusExperienceMoney == '' ? 0      : Number(this.form.bonusExperienceMoney),     // 获取体验金
                friends_recharge:   this.form.friendRecharge == '' ? 0            : Number(this.form.friendRecharge),           // 好友充值
                exceed_time:        this.form.dateTimes == '' ? ''                : this.form.dateTimes,                        // 活动过期时间
                receive_exceed:     this.form.overdueTime == '' ? 0               : Number(this.form.overdueTime),              // 领取过期时间
                activation_exceed:  this.form.activationTimeLimit == '' ? 0       : Number(this.form.activationTimeLimit),      // 激活过期时间
                trade_exceed:       this.form.recyclingTradingTimeLimit == '' ? 0 : Number(this.form.recyclingTradingTimeLimit),// 交易过期回收时间
                addgiftname: [
                  this.form.selectLanguage.find(item => item==0) == 0 ? {lang_type:0,gift_name:this.form.ZHWithGoldName,content:this.form.ZHInstructions} : null,
                  this.form.selectLanguage.find(item => item==1) == 1 ? {lang_type:1,gift_name:this.form.ENWithGoldName,content:this.form.ENInstructions} : null,
                  this.form.selectLanguage.find(item => item==2) == 2 ? {lang_type:2,gift_name:this.form.TCWithGoldName,content:this.form.TCInstructions} : null,
                  this.form.selectLanguage.find(item => item==3) == 3 ? {lang_type:3,gift_name:this.form.KOWithGoldName,content:this.form.KOInstructions} : null,
                  this.form.selectLanguage.find(item => item==4) == 4 ? {lang_type:4,gift_name:this.form.VNWithGoldName,content:this.form.VNInstructions} : null,
                  this.form.selectLanguage.find(item => item==5) == 5 ? {lang_type:5,gift_name:this.form.IDWithGoldName,content:this.form.IDInstructions} : null,
                ],
              }).then(() => {
                this.$notify({
                  title: this.$t('dialog.Add_a_success'),
                  type: "success",
                  duration: 2000,
                });
                this.getList();
                this.dialogVisible = false;
                this.formClear();
              })
            })
            .catch(action => {})
          } else {
            return false
          }
        })
      } else if (this.dialogVisibleType == 1) {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            this.$confirm(this.$t('dialog.Have_you_confirmed_modification_activity'), {
              distinguishCancelAndClose: true,
              confirmButtonText: this.$t('buttons.determine'),
              cancelButtonText: this.$t('buttons.cancel')
            }).then(() => {
              updategiftinfo({
                upid:               Number(this.isID),                            // upid
                time_limit:         this.form.timeLimit == '' ? 0                 : Number(this.form.timeLimit),                // 时间限制
                close_count:        this.form.positionsNum == '' ? 0              : Number(this.form.positionsNum),             // 平仓次数
                trader_day:         this.form.effectiveTradingDays == '' ? 0      : Number(this.form.effectiveTradingDays),     // 有效交易天数
                amount_limit:       this.form.topUp == '' ? 0                     : Number(this.form.topUp),                    // 充值
                gift_amount:        this.form.bonusExperienceMoney == '' ? 0      : Number(this.form.bonusExperienceMoney),     // 获取体验金
                friends_recharge:   this.form.friendRecharge == '' ? 0            : Number(this.form.friendRecharge),           // 好友充值
                exceed_time:        this.form.dateTimes == '' ? ''                : this.form.dateTimes,                        // 过期时间
                receive_exceed:     this.form.overdueTime == '' ? 0               : Number(this.form.overdueTime),              // 领取过期时间
                activation_exceed:  this.form.activationTimeLimit == '' ? 0       : Number(this.form.activationTimeLimit),      // 激活过期时间
                trade_exceed:       this.form.recyclingTradingTimeLimit == '' ? 0 : Number(this.form.recyclingTradingTimeLimit),// 交易过期回收时间
              }).then(() => {
                this.$notify({
                  title: this.$t('others.Modify_the_success'),
                  type: "success",
                  duration: 2000,
                });
                this.getList();
                this.dialogVisible = false;
                this.formClear();
              })
            })
            .catch(action => {})
          } else {
            return false
          }
        })
      }
    },

    // 状态
    changeSwitch(v) {
      updategiftstatus({
        upid: v.id,
        status: v.state,
      }).then(() => {
        this.$notify({
          title: this.$t('dialog.Modify_the_success'),
          type: "success",
          duration: 2000,
        });
        this.getList()
      })
    },

    getList() {
      this.listLoading = true;
      giftlist({
        category:   this.listQuery.category == '' ? Number(0) : Number(this.listQuery.category),
        state:      this.listQuery.status == '' ? Number(-1) : Number(this.listQuery.status),
        star:       this.listQuery.star2,
        end:        this.listQuery.end2,
        pageNo:     this.listQuery.pageNo,
        pagesize:   this.listQuery.pagesize
      }).then((res) => {
        // console.log(res,"列表")
        this.tableList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      })

      getlangelist().then((res) => {
        // console.log(res, "语言列表")
        this.languageList = res.data
      })
    },

    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },

    // filterTimeTransform1(val) {
    //   this.listQuery.star1 = (val && val[0]) || "";
    //   this.listQuery.end1 = (val && val[1] + " 23:59:59") || "";
    // },
    filterTimeTransform2(val) {
      this.listQuery.star2 = (val && val[0]) || "";
      this.listQuery.end2 = (val && val[1] + " 23:59:59") || "";
    },
  },
}
</script>

<style lang="scss" scoped>
.giftCashActive-container {
  padding: 10px;
  .btns-container {
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }
  ::v-deep .el-form-item__content{
    display: flex;
  }
  ::v-deep .el-input__suffix {
    display: flex;
    align-items: center;
    color: #000;
    padding: 0 3px;

    .el-input__suffix-inner {
      & > div {
        display: flex;
        flex-direction: row;
        align-items: center;
      }
    }
  }
  .title {
    font-weight: 700;
    box-sizing: border-box;
    vertical-align: middle;
    font-size: 14px;
    color: #606266;
    cursor: default;
    word-break: break-all;
  }
  .line {
    text-align: center;
  }
}
</style>