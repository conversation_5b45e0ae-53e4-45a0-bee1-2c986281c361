<!--公告列表-->
<template>
	<div class="notice_wrap">
		<!-- tb_notice_tab Filter Pane -->
		<el-switch v-model="showHiddenTabs" @change="fetchTabs(activeLang)" style="margin-left: 10px;"
			:active-text="$t('course.forms.showHidden')" />

		<el-tabs v-model="activeLang">
			<el-tab-pane v-for="(item, index) in langOptions" :key="index" :label="$t(`filters.${item.name}`)"
				:name="item.value"></el-tab-pane>
		</el-tabs>

		<!-- Main content layout -->
		<div class="main-content" style="display: flex;">

			<!-- tb_notice_tab, Left Pane -->
			<div class="left-pane" style="min-width: 350px; padding: 16px; border-right: 1px solid #eaeaea;">
				<div class="tab-title">{{ $t('others.categoryType') }}
					<el-button type="primary" icon="el-icon-plus" @click="openAddTabDialog"
						style="margin-left: 10px;">{{ $t('course.forms.addTab') }}</el-button>
				</div>

				<div class="tab-container">
					<draggable v-model="visibleTabs" @end="onDragEnd" :filter="'.no-drag'" :preventOnFilter="false"
						style="margin-top: 10px;">
						<el-card v-for="tab in visibleTabs" :key="tab.id"
							:class="{ 'selected-card': selectedTab && selectedTab.id === tab.id }"
							@click.native="selectTab(tab)" style="margin-bottom: 10px;">
							<div class="d-flex justify-between align-center">
								<span>{{ tab.content }}</span>
								<p class="date_p">{{ $t('others.releaseTime') }}：{{ formatDate(tab.create_at) }}</p>
							</div>
							<div class="d-flex justify-between align-center mt-1">
								<el-checkbox v-model="tab.hidden" @change="onHiddenToggle(tab)" @click.native.stop
									style="margin-right: 16px">{{ $t('course.forms.hiddenTab') }}</el-checkbox>
								<el-button size="mini" icon="el-icon-edit" @click.stop="editTabDialog(tab)">
									{{ $t('buttons.modify') }}
								</el-button>
								<el-button size="mini" icon="el-icon-delete" type="danger"
									@click.stop="deleteTabDialog(tab)">
									{{ $t('buttons.delete') }}
								</el-button>
							</div>
						</el-card>
					</draggable>
				</div>
			</div>

			<!-- Announcement list & pagination -->
			<div style="flex: 1; padding: 16px;">
				<el-button type="primary" icon="el-icon-plus" @click="showAddNoticeDialog = true"
					style="margin-left: 10px;">{{ $t('course.forms.addNotice') }}</el-button>
				<div class="w_c">
					<draggable element="div" class="w100" v-model="toplist" v-bind="dragOptions" :move="onMove"
						@change="change" @start="start" @end="end" @add="addCollectionD">
						<transition-group type="transition" class="title_ul" tag="ul" name="flip-list">
							<li v-for="item in toplist" :key="item.id">
								<div class="content_div">
									<p class="title_p">
										<el-tag v-if="item.is_important" size='mini' type="danger" effect="dark">
											{{ $t('others.importantNotice') }}
										</el-tag>
										<span>{{ item.title }}</span>
									</p>
									<p class="date_p">{{ $t('others.releaseTime') }}：{{ item.created_at }}</p>
								</div>
								<div class="handle_div">
									<el-button type="danger" size="mini"
										v-if="$store.getters.roles.indexOf('ggdel') > -1" @click="handleDelete(item)">{{
											$t('buttons.delete') }}</el-button>
									<el-button type="warning" size="mini"
										v-if="$store.getters.roles.indexOf('ggxg') > -1" @click="handleEdit(item)">{{
											$t('buttons.modify') }}</el-button>
									<el-button type="primary" size="mini" @click="handleToTopOrNo('0', item)">{{
										$t('buttons.canTop') }}</el-button>
								</div>
							</li>
						</transition-group>
					</draggable>
					<div class="tishi_div" v-if="toplist.length <= 0">{{ $t('others.noDataPlacedTop') }}</div>
					<div class="online_div"></div>
					<draggable v-if="tableData.length > 0" class="title_ul" tag="ul" v-model="tableData"
						v-bind="dragOptions" :move="onMove" @change="change" @start="start" @end="end"
						@add="addCollection">
						<transition-group type="transition" name="flip-list">
							<li v-for="item in tableData" :key="item.id">
								<div class="content_div">
									<p class="title_p">
										<el-tag v-if="item.is_important" size='mini' type="danger" effect="dark">
											{{ $t('others.importantNotice') }}
										</el-tag>
										<span>{{ item.title }}</span>
									</p>
									<p class="date_p">{{ $t('others.releaseTime') }}：{{ item.created_at }}</p>
								</div>
								<div class="handle_div">
									<el-button type="danger" size="mini"
										v-if="$store.getters.roles.indexOf('ggdel') > -1" @click="handleDelete(item)">{{
											$t('buttons.delete') }}</el-button>
									<el-button type="warning" size="mini"
										v-if="$store.getters.roles.indexOf('ggxg') > -1" @click="handleEdit(item)">{{
											$t('buttons.modify') }}</el-button>
									<el-button type="primary" size="mini" @click="handleToTopOrNo('1', item)">{{
										$t('buttons.placedTop') }}</el-button>
								</div>
							</li>
						</transition-group>
					</draggable>
					<div class="tishi_div" v-else>{{ $t('others.noData') }}</div>
				</div>
				<div class="w_page">
					<el-pagination background v-show="datatotal > 0" @size-change="handleSizeChange"
						@current-change="handleCurrentChange" :current-page="currentPage" :page-sizes="[10, 20, 30, 50]"
						:page-size="10" layout="total, sizes, prev, pager, next, jumper"
						:total="datatotal"></el-pagination>
					<!-- 引入封装分页组件 -->
					<!-- <pagina-tion
                            v-show="datatotal > 0"
                            :total="datatotal"
                            :page.sync="currentPage"
                            :limit.sync="currentPageNum"
                            @pagination="getData"
                            /> -->
				</div>
			</div>
		</div>

		<!-- Add/Edit Tab Dialog -->
		<el-dialog :title="dialogMode === 'add' ? 'Add Notice Tab' : 'Edit Notice Tab'" :visible.sync="showTabDialog"
			width="400px">
			<el-form :model="tabForm" label-width="100px">
				<el-form-item label="Tab Name">
					<el-input v-model="tabForm.content" placeholder="Enter tab name" />
				</el-form-item>
				<el-form-item label="Hidden?">
					<el-switch v-model="tabForm.hidden" />
				</el-form-item>
			</el-form>

			<template #footer>
				<span class="dialog-footer">
					<el-button @click="showTabDialog = false">{{ $t('buttons.cancel') }}</el-button>
					<el-button type="primary" @click="submitTabForm">{{ $t('buttons.confirm') }}</el-button>
				</span>
			</template>
		</el-dialog>

		<!-- Add/Edit Notice Dialog -->
		<el-dialog :visible.sync="showAddNoticeDialog" width="70%" title="公告发布">
			<ggfb @success="onNoticeAddSuccess" @cancel="showAddNoticeDialog = false" :tab="selectedTab" />
		</el-dialog>
	</div>
</template>

<script>
// import { GETGGLB, SCGG, ZDORNO } from "@/constant";
// import {getgg} from '@/api.js'
import ggfb from '@/views/notice/ggfb.vue';
import draggable from "vuedraggable"; //引入拖拽
import dayjs from 'dayjs';
//getNoticeMsg公告列表  deleteNotice删除公告
import { getNoticeMsg, deleteNotice, zdOrqxNotice, fetchNoticeTabs, addNoticeTab, updateNoticeTab, deleteNoticeTab, reorderNoticeTabs } from "@/api/notice";
// import { getlangelist, } from "@/api/activity";
export default {
	name: 'gglb',
	data() {
		return {
			datatotal: 0, //总数据条数
			currentPageNum: 10, //每页默认10条
			currentPage: 1, //当前页
			isDragging: false,
			toplist: [], //置顶数组
			tableData: [],
			activeLang: "0",
			langOptions: [
				{ name: 'ChineseSimplifiedNotice', value: "0", },
				{ name: 'EnglishNotice', value: "1", },
				{ name: 'ChineseTraditionalNotice', value: "2", },
				{ name: 'KoreanNotice', value: "3", },
				{ name: 'VietnameseNotice', value: "4", },
				{ name: 'IndonesianNotice', value: "5", },
				{ name: 'RussianNotice', value: "6", },
				{ name: 'GermanNotice', value: "7", },
				{ name: 'JapaneseNotice', value: "8", },
			], // 语言tab
			showHiddenTabs: false,
			noticeTabs: [],
			visibleTabs: [],
			selectedTab: null,
			showTabDialog: false,
			dialogMode: 'add', // or 'edit'
			tabForm: {
				id: null,
				content: '',
				hidden: false,
			},
			showAddNoticeDialog: false,
		};
	},
	mounted() {
		// this.$register(this);
		// this.msg = [GETGGLB, SCGG,ZDORNO];
		// getlangelist().then((res) => {
		//   console.log(res, "语言列表")
		//   // this.languageList = res.data
		// })
		this.fetchTabs(this.activeLang);
	},
	components: {
		draggable,
		// feature/daniel-noticetab-0512
		ggfb,
	},
	computed: {
		dragOptions() {
			return {
				animation: 0,
				group: "description",
				// disabled: !this.editable,
				ghostClass: "ghost",
			};
		},
	},
	methods: {
		whichArr(id) {
			let aa = false;
			for (let i = 0; i < this.toplist.length; i++) {
				if (this.toplist[i].id == id) {
					aa = true;
					return true;
				} else {
					aa = false;
				}
			}
			return aa;
		},
		change: function (evt) {
			// console.log(evt);
			// console.log("!!!",evt.added);
			// console.log("!!!",evt.removed);
			// console.log("!!!",evt.moved);
			if (evt.moved) {
				//判断拖拽的是否是置顶数组
				if (this.whichArr(evt.moved.element.id)) {
					//是
					//判断是否拖拽到第 0 个
					if (evt.moved.newIndex == 0) {
						var data = {
							id: evt.moved.element.id,
							weight: Number(this.toplist[evt.moved.newIndex + 1].weight) + 1,
							is_top: 1,
							notice_tab_id: evt.moved.element.notice_tab_id
						};
						zdOrqxNotice(data).then(async (res) => {
							// console.log(res)
							this.$message({ message: this.$t('dialog.Successful'), type: "success" });
							if (
								this.tableData.length == 0 ||
								this.tableData.length - 1 == 0
							) {
								//删除后已经不再有数据 页数减1
								this.currentPage -= 1;
								this.currentPage = this.currentPage < 1 ? 1 : this.currentPage;
							}
							//刷新
							await this.getData();
						});
						// this.$api.zdOrqxNotice({
						//   tag: {
						//     dataType: ZDORNO,
						//     id: evt.moved.element.id,
						//     weight: Number(this.toplist[evt.moved.newIndex+1].weight) + 1,
						//     type: 1,
						//   }
						// });
						//判断是否拖拽到最后
					} else if (evt.moved.newIndex == this.toplist.length - 1) {
						var data = {
							id: evt.moved.element.id,
							weight: Number(this.toplist[evt.moved.newIndex - 1].weight) - 1,
							is_top: 1,
							notice_tab_id: evt.moved.element.notice_tab_id
						};
						zdOrqxNotice(data).then(async (res) => {
							// console.log(res)
							this.$message({ message: this.$t('dialog.Successful'), type: "success" });
							if (
								this.tableData.length == 0 ||
								this.tableData.length - 1 == 0
							) {
								//删除后已经不再有数据 页数减1
								this.currentPage -= 1;
								this.currentPage = this.currentPage < 1 ? 1 : this.currentPage;
							}
							//刷新
							await this.getData();
						});
						// this.$api.zdOrqxNotice({
						//   tag: {
						//     dataType: ZDORNO,
						//     id: evt.moved.element.id,
						//     weight: Number(this.toplist[evt.moved.newIndex-1].weight) - 1,
						//     type: 1,
						//   }
						// });
					} else {
						var data = {
							id: evt.moved.element.id,
							weight:
								(Number(this.toplist[evt.moved.newIndex + 1].weight) +
									Number(this.toplist[evt.moved.newIndex - 1].weight)) /
								2,
							is_top: 1,
							notice_tab_id: evt.moved.element.notice_tab_id
						};
						zdOrqxNotice(data).then(async (res) => {
							this.$message({ message: this.$t('dialog.Successful'), type: "success" });
							if (
								this.tableData.length == 0 ||
								this.tableData.length - 1 == 0
							) {
								//删除后已经不再有数据 页数减1
								this.currentPage -= 1;
								this.currentPage = this.currentPage < 1 ? 1 : this.currentPage;
							}
							//刷新
							await this.getData();
						});
						// this.$api.zdOrqxNotice({
						//   tag: {
						//     dataType: ZDORNO,
						//     id: evt.moved.element.id,
						//     weight: (Number(this.toplist[evt.moved.newIndex+1].weight)+Number(this.toplist[evt.moved.newIndex-1].weight))/2,
						//     type: 1,
						//   }
						// });
					}
				} else {
					//否
					//判断是否拖拽到第 0 个
					// if(evt.moved.newIndex == 0){
					//   this.$api.zdOrqxNotice({
					//     tag: {
					//       dataType: ZDORNO,
					//       id: evt.moved.element.id,
					//       weight: Number(this.tableData[evt.moved.newIndex+1].weight) + 1,
					//       type: 0,
					//     }
					//   });
					//   //判断是否拖拽到最后
					// }else if(evt.moved.newIndex == this.tableData.length-1){
					//   this.$api.zdOrqxNotice({
					//     tag: {
					//       dataType: ZDORNO,
					//       id: evt.moved.element.id,
					//       weight: Number(this.tableData[evt.moved.newIndex-1].weight) - 1,
					//       type: 0,
					//     }
					//   });
					// }else{
					//   this.$api.zdOrqxNotice({
					//     tag: {
					//       dataType: ZDORNO,
					//       id: evt.moved.element.id,
					//       weight: (Number(this.tableData[evt.moved.newIndex+1].weight)+Number(this.tableData[evt.moved.newIndex-1].weight))/2,
					//       type: 0,
					//     }
					//   });
					// }
				}
			} else {
				// 判断是拖到置顶还是拖出置顶
				// console.log(evt,this.toplist,evt.added.element.id)
				// let aa = false
				// let addedID = evt.added.element.id
				// for (let i = 0; i < this.toplist.length; i++) {
				//   if(this.toplist[i].id == addedID){
				//     aa = true
				//     return true
				//   }else{
				//     aa = false
				//   }
				// }
				// if(aa){//拖到置顶
				//   // //判断是否拖拽到第 0 个
				//   if(evt.moved.newIndex == 0){
				//     this.$api.zdOrqxNotice({
				//       tag: {
				//         dataType: ZDORNO,
				//         id: evt.moved.element.id,
				//         weight: Number(this.toplist[evt.moved.newIndex+1].weight) + 1,
				//         type: 1,
				//       }
				//     });
				//     //判断是否拖拽到最后
				//   }else if(evt.moved.newIndex == this.toplist.length-1){
				//     this.$api.zdOrqxNotice({
				//       tag: {
				//         dataType: ZDORNO,
				//         id: evt.moved.element.id,
				//         weight: Number(this.toplist[evt.moved.newIndex-1].weight) - 1,
				//         type: 1,
				//       }
				//     });
				//   }else{
				//     this.$api.zdOrqxNotice({
				//       tag: {
				//         dataType: ZDORNO,
				//         id: evt.moved.element.id,
				//         weight: (Number(this.toplist[evt.moved.newIndex+1].weight)+Number(this.toplist[evt.moved.newIndex-1].weight))/2,
				//         type: 1,
				//       }
				//     });
				//   }
				// }
			}
		},
		start: function (evt) {
			// console.log(evt)
		},
		onMove({ relatedContext, draggedContext }) {
			const relatedElement = relatedContext.element;
			const draggedElement = draggedContext.element;
			// return (
			//   (!relatedElement || !relatedElement.fixed) && !draggedElement.fixed
			// );
		},
		end() { },
		addCollectionD(evt) {
			//拖拽置顶
			//判断是否拖拽到第 0 个
			if (evt.newIndex == 0) {
				if (this.toplist.length > 1) {
					var data = {
						id: this.toplist[evt.newIndex].id,
						weight: Number(this.toplist[evt.newIndex + 1].weight) + 1,
						is_top: 1,
						notice_tab_id: this.toplist[evt.newIndex].notice_tab_id
					};
					zdOrqxNotice(data).then(async (res) => {
						this.$message({ message: this.$t('dialog.Successful'), type: "success" });
						if (this.tableData.length == 0 || this.tableData.length - 1 == 0) {
							//删除后已经不再有数据 页数减1
							this.currentPage -= 1;
							this.currentPage = this.currentPage < 1 ? 1 : this.currentPage;
						}
						//刷新
						await this.getData();
					});
					// this.$api.zdOrqxNotice({
					//   tag: {
					//     dataType: ZDORNO,
					//     id: this.toplist[evt.newIndex].id,
					//     weight: Number(this.toplist[evt.newIndex+1].weight) + 1,
					//     type: 1,
					//   }
					// });
				} else {
					var data = {
						id: this.toplist[evt.newIndex].id,
						weight: 1,
						is_top: 1,
						notice_tab_id: this.toplist[evt.newIndex].notice_tab_id
					};
					zdOrqxNotice(data).then(async (res) => {
						this.$message({ message: this.$t('dialog.Successful'), type: "success" });
						if (this.tableData.length == 0 || this.tableData.length - 1 == 0) {
							//删除后已经不再有数据 页数减1
							this.currentPage -= 1;
							this.currentPage = this.currentPage < 1 ? 1 : this.currentPage;
						}
						//刷新
						await this.getData();
					});
					// this.$api.zdOrqxNotice({
					//   tag: {
					//     dataType: ZDORNO,
					//     id: this.toplist[evt.newIndex].id,
					//     weight: 1,
					//     type: 1,
					//   }
					// });
				}

				//判断是否拖拽到最后
			} else if (evt.newIndex == this.toplist.length - 1) {
				var data = {
					id: this.toplist[evt.newIndex].id,
					weight: Number(this.toplist[evt.newIndex - 1].weight) - 1,
					is_top: 1,
					notice_tab_id: this.toplist[evt.newIndex].notice_tab_id
				};
				zdOrqxNotice(data).then(async (res) => {
					this.$message({ message: this.$t('dialog.Successful'), type: "success" });
					if (this.tableData.length == 0 || this.tableData.length - 1 == 0) {
						//删除后已经不再有数据 页数减1
						this.currentPage -= 1;
						this.currentPage = this.currentPage < 1 ? 1 : this.currentPage;
					}
					//刷新
					await this.getData();
				});
				// this.$api.zdOrqxNotice({
				//   tag: {
				//     dataType: ZDORNO,
				//     id: this.toplist[evt.newIndex].id,
				//     weight: Number(this.toplist[evt.newIndex-1].weight) - 1,
				//     type: 1,
				//   }
				// });
			} else {
				var data = {
					id: this.toplist[evt.newIndex].id,
					weight:
						(Number(this.toplist[evt.newIndex + 1].weight) +
							Number(this.toplist[evt.newIndex - 1].weight)) /
						2,
					is_top: 1,
					notice_tab_id: this.toplist[evt.newIndex].notice_tab_id
				};
				zdOrqxNotice(data).then(async (res) => {
					this.$message({ message: this.$t('dialog.Successful'), type: "success" });
					if (this.tableData.length == 0 || this.tableData.length - 1 == 0) {
						//删除后已经不再有数据 页数减1
						this.currentPage -= 1;
						this.currentPage = this.currentPage < 1 ? 1 : this.currentPage;
					}
					//刷新
					await this.getData();
				});
				// this.$api.zdOrqxNotice({
				//   tag: {
				//     dataType: ZDORNO,
				//     id: this.toplist[evt.newIndex].id,
				//     weight: (Number(this.toplist[evt.newIndex+1].weight)+Number(this.toplist[evt.newIndex-1].weight))/2,
				//     type: 1,
				//   }
				// });
			}
		},
		addCollection(evt) {
			//拖拽取消置顶
			// evt.item //可以知道拖动的本身
			// evt.to    // 可以知道拖动的目标列表
			// evt.from  // 可以知道之前的列表
			// evt.oldIndex  // 可以知道拖动前的位置
			// evt.newIndex  // 可以知道拖动后的位置

			//判断是否拖拽到第 0 个
			if (evt.newIndex == 0) {
				var data = {
					id: this.tableData[evt.newIndex].id,
					weight: Number(this.tableData[evt.newIndex + 1].weight) + 1,
					is_top: 0,
					notice_tab_id: this.toplist[evt.newIndex].notice_tab_id
				};
				zdOrqxNotice(data).then(async (res) => {
					this.$message({ message: this.$t('dialog.Successful'), type: "success" });
					if (this.tableData.length == 0 || this.tableData.length - 1 == 0) {
						//删除后已经不再有数据 页数减1
						this.currentPage -= 1;
						this.currentPage = this.currentPage < 1 ? 1 : this.currentPage;
					}
					//刷新
					await this.getData();
				});
				// this.$api.zdOrqxNotice({
				//   tag: {
				//     dataType: ZDORNO,
				//     id: this.tableData[evt.newIndex].id,
				//     weight: Number(this.tableData[evt.newIndex+1].weight) + 1,
				//     type: 0,
				//   }
				// });
				//判断是否拖拽到最后
			} else if (evt.newIndex == this.tableData.length - 1) {
				var data = {
					id: this.tableData[evt.newIndex].id,
					weight: Number(this.tableData[evt.newIndex - 1].weight) - 1,
					is_top: 0,
					notice_tab_id: this.toplist[evt.newIndex].notice_tab_id
				};
				zdOrqxNotice(data).then(async (res) => {
					this.$message({ type: "success" });
					if (this.tableData.length == 0 || this.tableData.length - 1 == 0) {
						//删除后已经不再有数据 页数减1
						this.currentPage -= 1;
						this.currentPage = this.currentPage < 1 ? 1 : this.currentPage;
					}
					//刷新
					await this.getData();
				});
				// this.$api.zdOrqxNotice({
				//   tag: {
				//     dataType: ZDORNO,
				//     id: this.tableData[evt.newIndex].id,
				//     weight: Number(this.tableData[evt.newIndex-1].weight) - 1,
				//     type: 0,
				//   }
				// });
			} else {
				var data = {
					id: this.tableData[evt.newIndex].id,
					weight:
						(Number(this.tableData[evt.newIndex + 1].weight) +
							Number(this.tableData[evt.newIndex - 1].weight)) /
						2,
					is_top: 0,
					notice_tab_id: this.toplist[evt.newIndex].notice_tab_id
				};
				zdOrqxNotice(data).then(async (res) => {
					this.$message({ type: "success", title: this.$t('dialog.Successful') });
					if (this.tableData.length == 0 || this.tableData.length - 1 == 0) {
						//删除后已经不再有数据 页数减1
						this.currentPage -= 1;
						this.currentPage = this.currentPage < 1 ? 1 : this.currentPage;
					}
					//刷新
					await this.getData();
				});
				// this.$api.zdOrqxNotice({
				//   tag: {
				//     dataType: ZDORNO,
				//     id: this.tableData[evt.newIndex].id,
				//     weight: (Number(this.tableData[evt.newIndex+1].weight)+Number(this.tableData[evt.newIndex-1].weight))/2,
				//     type: 0,
				//   }
				// });
			}
		},
		handleDelete(item) {
			this.$confirm(this.$t('dialog.Whether_to_delete_the_bulletin'), this.$t('dialog.Prompt'), {
				confirmButtonText: this.$t('buttons.determine'),
				cancelButtonText: this.$t('buttons.cancel'),
				type: "warning",
			}).then(() => {
				//  36.删除公告
				//   地址：/customer/customer_owner/del_notice_msg
				//  方式：post
				//   参数id
				//  返回
				// {"res":200,"msg":""}
				// console.log('======',item.userid)
				// console.log(item)
				var data = {
					id: JSON.parse(item.id),
					notice_tab_id: JSON.parse(item.notice_tab_id)
				};
				// console.log(data)
				deleteNotice(data).then(async (res) => {
					// console.log(res)
					if (this.tableData.length - 1 == 0) {
						//删除后已经不再有数据 页数减1
						this.currentPage -= 1;
						this.currentPage = this.currentPage < 1 ? 1 : this.currentPage;
					}
					//刷新
					await this.getData();
					this.$message({
						type: "error",
						message: this.$t('dialog.Delete_the_success'),
						title: this.$t('dialog.Prompt'),
					});
				});

				// this.$api.deleteNotice({
				//   tag: {
				//     dataType: SCGG,
				//     id: item.id
				//   }
				// });
			}).catch(() => {
				this.$message({
					type: "info",
					message: this.$t('dialog.Cancelled_delete'),
					title: this.$t('dialog.Prompt'),
				});
			});
		},
		handleEdit(item) {
			this.$router.push({
				path: "/notice/ggxg",
				query: {
					data: JSON.stringify(item),
				},
			});
		},
		handleToTopOrNo(zdType, item) {
			let setWeight = null;
			if (this.toplist.length > 0) {
				setWeight = Number(this.toplist[0].weight) + 1;
			}
			this.$confirm(
				`${this.$t('dialog.Whether')} ${zdType == "0" ? this.$t('buttons.canTop') : this.$t('buttons.placedTop')} ${this.$t('dialog.The_announcement')}?`,
				this.$t('dialog.Prompt'),
				{
					confirmButtonText: this.$t('buttons.determine'),
					cancelButtonText: this.$t('buttons.cancel'),
					type: "warning",
				}
			).then(() => {
				//  49.修改公告置顶信息
				//  地址：/Customer_owner/up_top_notice_msg
				//  参数：id公告id，type （0取消置顶，1置顶）， weight置顶权重值
				//  返回{"res":200,"msg":""}
				var data = {
					id: item.id,
					weight: setWeight,
					is_top: parseInt(zdType),
					notice_tab_id: parseInt(item.notice_tab_id),
				};
				// console.log(data)
				zdOrqxNotice(data).then((res) => {
					this.$message({ message: this.$t('dialog.Successful'), type: "success" });
					if (this.tableData.length == 0 || this.tableData.length - 1 == 0) {
						//删除后已经不再有数据 页数减1
						this.currentPage -= 1;
						this.currentPage = this.currentPage < 1 ? 1 : this.currentPage;
					}
					//刷新
					this.getData();
				});
				// this.$api.zdOrqxNotice({
				//   tag: {
				//     dataType: ZDORNO,
				//     id: item.id,
				//     weight: setWeight,
				//     type: zdType,
				//   }
				// });
			})
				.catch(() => {
					this.$message({
						type: "info",
						message: this.$t('filters.Has_been_cancelled'),
						title: this.$t('dialog.Prompt'),
					});
				});
		},
		async handleSizeChange(val) {
			this.currentPageNum = val;
			await this.getData();
		},
		async handleCurrentChange(val) {
			this.currentPage = val;
			await this.getData();
		},
		async getData() {
			if (!this.selectedTab || !this.noticeTabs.some(tab => tab.id === this.selectedTab.id)) {
				// console.warn('[getData] Skipped: selectedTab is null or invalid');
				this.clearState();
				return;
			}

			const noticeTabId = this.selectedTab ? this.selectedTab.id : 0;
			// console.log('[getData] Fetching notices with:', {
			// 	pageNo: this.currentPage,
			// 	pagesize: this.currentPageNum,
			// 	lang_type: Number(this.activeLang),
			// 	notice_tab_id: noticeTabId,
			// });

			const res = await getNoticeMsg({
				pageNo: this.currentPage,
				pagesize: this.currentPageNum,
				lang_type: Number(this.activeLang),
				notice_tab_id: noticeTabId,
			});

			if (res.ret === 0 && res.data.list) {
				this.tableData = res.data.list;
				this.tableData.sort((a, b) => b.weight - a.weight); //分数从高到低 排序
				this.toplist = this.tableData.filter((v) => v.is_top === 1); //res.data.toplist;  //置顶数组
				if (this.toplist.length > 0) {
					this.toplist.map((v) => {
						let idx = this.tableData.findIndex((t) => t.id === v.id);
						if (idx >= 0) {
							this.tableData.splice(idx, 1);
						}
					});
				}
				this.datatotal = Number(res.data.total) - this.toplist.length;

				// console.log('[getData] Updated tableData:', this.tableData);
				// console.log('[getData] Updated toplist:', this.toplist);
				// console.log('[getData] Updated datatotal:', this.datatotal);
			} else {
				this.tableData = [];
				this.toplist = [];
				this.datatotal = 0;
			}
			//     .获取公告列表
			// 地址：/customer/Customer_owner/get_notice_msg
			// 方式：POST
			// 参数：pageNo，pagesize， star，end
			// 返回：title标题，abstract中文摘要，content中文正文，content_en英文正文，abstract_en英文摘要，title_en英文标题
			// {"res":200,"msg":"","data":{"rowCount":"1","list":[{"id":"1","title":"\u4e0a\u7ebf","abstract":"\u4e0a\u7ebf\u4ecb\u7ecd","content":"\u968f\u4fbf\u8bf4\u8bf4","content_en":"say yes","abstract_en":"online haha","title_en":"online","created_at":"2019-08-08 23:16:11"}]}}
			// this.$api.getNoticeMsg({
			//   tag: {
			//     dataType: GETGGLB,
			//     pageNo: this.currentPage,
			//     pagesize: this.currentPageNum,
			//     star: "",
			//     end: ""
			//   }
			// });
		},

		// message(tag,data) {
		// switch (tag.dataType) {
		//   case GETGGLB:
		//     if(data.ret === 0 && data.data.list){
		//       this.tableData = data.data.list;
		//       this.tableData.sort((a, b)=> b.weight-a.weight); //分数从高到低 排序
		//       this.toplist = this.tableData.filter(v=>v.is_top===1);//data.data.toplist;  //置顶数组
		//       if(this.toplist.length>0){
		//         this.toplist.map(v=>{
		//           let idx = this.tableData.findIndex(t=>t.id === v.id);
		//           if(idx>=0){
		//             this.tableData.splice(idx,1)
		//           }
		//         });
		//       }
		//       this.datatotal = Number(data.data.total)-this.toplist.length;
		//     }else{
		//       this.tableData = [];
		//       this.toplist = [];
		//       this.datatotal = 0;
		//     }
		//     break;
		//   case SCGG:
		//     if(data.ret === 0){
		//       this.$msg({ message: data.msg, type: "success", title: "成功" });
		//       if(this.tableData.length-1 == 0){
		//         //删除后已经不再有数据 页数减1
		//         this.currentPage -= 1;
		//         this.currentPage = this.currentPage<1?1:this.currentPage;
		//       }
		//       //刷新
		//       this.getData();
		//     }
		//     break;
		//   case ZDORNO:
		//     if(data.ret === 0){
		//       this.$msg({ message: data.msg, type: "success", title: "成功" });
		//       if(this.tableData.length == 0 || this.tableData.length-1 == 0){
		//         //删除后已经不再有数据 页数减1
		//         this.currentPage -= 1;
		//         this.currentPage = this.currentPage<1?1:this.currentPage;
		//       }
		//       //刷新
		//       this.getData();
		//     }
		//     break;
		// }
		// }

		// feature/daniel-noticetab-0512
		formatDate(dateStr) {
			return dateStr ? dayjs(dateStr).format('YYYY-MM-DD HH:mm') : '';
		},
		async fetchTabs(lang) {
			try {
				const res = await fetchNoticeTabs({ lang_type: Number(lang) });
				if (res.ret === 0 && res.data) {
					this.noticeTabs = res.data.list || [];
					this.filterTabs();

					if (this.noticeTabs.length === 0) {
						this.clearState();
						return;
					}

					if (!this.selectedTab || !this.visibleTabs.some(tab => tab.id === this.selectedTab.id)) {
						this.selectedTab = this.visibleTabs[0];
						// console.log('[fetchTabs]: ' + JSON.stringify(this.selectedTab));
					}

					await this.getData();
				} else {
					this.clearState();
				}
			} catch (err) {
				console.error('fetchTabs error:', err);
				this.clearState();
			}
		},
		filterTabs() {
			this.visibleTabs = this.showHiddenTabs
				? [...this.noticeTabs]
				: this.noticeTabs.filter(tab => !tab.hidden);
		},
		async selectTab(tab) {
			if (!tab || !this.noticeTabs.some(t => t.id === tab.id)) {
				// console.warn('[selectTab] Skipped: tab is invalid or not in noticeTabs');
				return;
			}
			console.log('[selectTab] Selecting tab:', JSON.stringify(tab));
			this.selectedTab = tab;
			await this.getData();
		},
		openAddTabDialog() {
			this.dialogMode = 'add';
			this.tabForm = {
				id: null,
				content: '',
				hidden: false
			};
			this.showTabDialog = true;
		},
		editTabDialog(tab) {
			this.dialogMode = 'edit';
			this.tabForm = {
				id: tab.id,
				content: tab.content,
				hidden: tab.hidden
			};
			this.showTabDialog = true;
		},
		submitTabForm() {
			const payload = {
				id: this.tabForm.id,
				content: this.tabForm.content,
				hidden: this.tabForm.hidden,
				lang_type: Number(this.activeLang),
				type: this.dialogMode === 'add' ? 1 : 2
			};

			const handler = this.dialogMode === 'add' ? addNoticeTab : updateNoticeTab;

			handler(payload).then(res => {
				if (res.ret === 0) {
					this.$message.success('Tab saved');
					this.showTabDialog = false;
					this.fetchTabs(this.activeLang);
				} else {
					this.$message.error('Operation failed');
				}
			});
		},
		deleteTabDialog(tab) {
			this.$confirm('Are you sure you want to delete this tab?', 'Warning', {
				confirmButtonText: this.$t('buttons.determine'),
				cancelButtonText: this.$t('buttons.cancel'),
				type: 'warning'
			}).then(() => {
				deleteNoticeTab({
					id: tab.id,
					lang_type: tab.lang_type
				}).then((res) => {
					if (res.ret === 0) {
						this.fetchTabs(this.activeLang);
					}
				});
			});
		},
		onHiddenToggle(tab) {
			updateNoticeTab({
				id: tab.id,
				content: tab.content,
				hidden: tab.hidden,
				lang_type: Number(this.activeLang),
				type: 2
			}).then((res) => {
				if (res.ret === 0) {
					this.filterTabs();
				}
			});
		},
		onDragEnd() {
			const sorted = this.visibleTabs.map((tab, index) => ({
				id: tab.id,
				lang_type: tab.lang_type,
				order_num: index + 1
			}));

			reorderNoticeTabs({ list: sorted }).then((res) => {
				if (res.ret === 0) {
					this.fetchTabs(this.activeLang);
				}
			});
		},
		async changeLanguage(lang) {
			this.clearState();
			this.activeLang = lang;
			await this.fetchTabs(lang);
		},
		clearState() {
			this.noticeTabs = [];
			this.visibleTabs = [];
			this.selectedTab = null;
			this.tableData = [];
			this.toplist = [];
			this.datatotal = 0;
		},
		onNoticeAddSuccess() {
			this.$message.success('公告发布成功');
			this.showAddNoticeDialog = false;
			this.getData();
		},
	},
	watch: {
		isDragging(newValue) {
			if (newValue) {
				this.delayedDragging = true;
				return;
			}
			this.$nextTick(() => {
				this.delayedDragging = false;
			});
		},
		// feature/daniel-noticetab-0512
		activeLang(newValue) {
			this.changeLanguage(newValue);
		},
	},
	// beforeDestroy() {
	//   this.$unRegister(this);
	// }
};
</script>

<style lang="scss" scoped>
.notice_wrap {
	width: 100%;
	display: flex;
	flex-direction: column;
	padding: 20px;

	.w_c {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: flex-start;
		padding: 20px;

		.online_div {
			height: 1px;
			width: 100%;
			border-top: 1px solid #ccc;
			margin-top: 15px;
			margin-bottom: 25px;
		}

		.title_ul {
			width: 100%;
			min-height: 10px;
			padding: 0;

			li {
				&:hover {
					cursor: move;
				}

				border: 1px solid #ccc;
				display: flex;
				// justify-content: space-between;
				align-items: center;
				justify-content: space-between;
				padding: 12px;
				margin-bottom: 10px;

				.content_div {
					width: 80%;
					white-space: nowrap;
					overflow: hidden;

					p {
						text-align: left;
					}

					.title_p {
						display: flex;
						flex-wrap: wrap;
						align-items: center;
						word-break: break-word;
						white-space: normal;
						line-height: 1.5;

						span {
							font-size: 18px;
							font-weight: 700;
						}
					}

					.date_p {
						line-height: 18px;
						font-weight: 500;
						font-size: 14px;
					}
				}

				.handle_div {
					width: 20%;
					justify-content: flex-end;
					display: flex;
					flex-shrink: 0;
					align-items: center;
				}
			}
		}

		.tishi_div {
			line-height: 60px;
			color: #909399;
			font-size: 14px;
		}
	}

	.w_page {
		flex-shrink: 0;
		display: flex;
		align-items: center;
		justify-content: flex-end;
		padding-top: 10px;
		padding-right: 30px;
	}
}

.w100 {
	width: 100%;
}

.flip-list-move {
	transition: transform 0.375s;
}

.no-move {
	transition: transform 0s;
}

.ghost {
	opacity: 0.5;
	background: #c8ebfb;
}

// feature/daniel-noticetab-0512
.tab-container {
	padding-top: 16px;
	min-height: 1000px;
	overflow-y: auto;
}

.selected-card {
	border: 2px solid #409EFF;
	background-color: #f0f9ff;
}

.no-drag {
	cursor: pointer;
}
</style>
