<!-- src/views/launchpad/subscriptionrecords.vue -->
<template>
	<div class="launchpad-container">
		<!-- Filter Bar -->
		<el-form :inline="true" :model="filters" class="filter-form">
			<el-form-item>
				<el-input v-model="filters.title" :placeholder="$t(`launchpad.placeholders.projectTitle`)" clearable />
			</el-form-item>
			<el-form-item>
				<el-select v-model="filters.coin" :placeholder="$t(`launchpad.placeholders.tokenName`)" clearable >
					<el-option v-for="coin in coinOptions" :key="coin.value" :label="coin.label" :value="coin.value" />
				</el-select>
			</el-form-item>
			<el-form-item :label="$t(`filters.startTime`)">
				<el-date-picker v-model="filters.begin" type="date" :placeholder="$t(`launchpad.placeholders.begin`)" />
			</el-form-item>
			<el-form-item :label="$t(`filters.endTime`)">
				<el-date-picker v-model="filters.end" type="date" :placeholder="$t(`launchpad.placeholders.end`)" />
			</el-form-item>
			<el-form-item>
				<el-button type="primary" icon="el-icon-search" @click="fetchData">
					{{ $t('buttons.search') }}
				</el-button>
			</el-form-item>
		</el-form>

		<!-- Data Table -->
		<el-table :data="tableData" border fit stripe highlight-current-row size="mini" class="subscription-table"
			:header-cell-style="{ background: '#F0F8FF', color: '#333', fontWeight: 'bold' }">
			<el-table-column label="User ID" align="center" min-width="120">
				<template #default="{ row }">
					<el-tooltip :content="row.user_id.toString()" placement="top">
						<span>{{ shortenMiddle(row.user_id.toString()) }}</span>
					</el-tooltip>
				</template>
			</el-table-column>
			<el-table-column prop="title" :label="$t('launchpad.tableHeaders.title')" align="center" min-width="180" />
			<el-table-column prop="offer.coin" :label="$t(`launchpad.tableHeaders.coin`)" width="120" align="center" />

			<el-table-column prop="coin_info.price" :label="$t('launchpad.tableHeaders.purchasePrice')" width="120" align="center" />
			<el-table-column prop="coin_info.coin" :label="$t('launchpad.tableHeaders.coin')" width="120" align="center" />
			
			<el-table-column :label="$t('launchpad.tableHeaders.paymentAmount')" width="120" align="center">
				<template #default="{ row }">
					{{ formatNumber(row.price) }} U
				</template>	
			</el-table-column>
			<el-table-column prop="coin" :label="$t('launchpad.tableHeaders.paymentCoin')" align="center" width="100" />
			
			<el-table-column prop="offered" :label="$t('launchpad.tableHeaders.quantitySubscribed')"  width="120" align="center" />
			<el-table-column :label="$t('tableHeader.creationTime')" align="center" width="180">
				<template #default="{ row }">
					{{ formatIsoDatetime(row.created_at) }}
				</template>
			</el-table-column>
		</el-table>

		<!-- 引入封装分页组件 -->
		<pagina-tion v-show="total > 0" :total="total" :page-sizes="[10, 50, 100, 300]" :page.sync="filters.pageNo"
			:limit.sync="filters.pagesize" @pagination="fetchData" />
	</div>
</template>

<script>
import { getSubscriptionRecordsList } from '@/api/launchpad';
import { getBrowserLangCode, getSubscriptionTitle } from '@/utils/launchpad';
import { formatNumber, shortenMiddle } from '@/utils/format';
import { formatIsoDatetime } from '@/utils/time';

export default {
	name: 'SubscriptionPurchaseRecords',
	data() {
		return {
			tableData: [],
			total: 0,
			filters: {
				title: '',
				coin: '',
				begin: null,
				end: null,
				pageNo: 1,
				pagesize: 10,
				langCode: 0, // default lang
			},
			coinOptions: [
				{ label: 'USDT', value: 'USDT' },
				{ label: 'USDC', value: 'USDC' },
			],
		}
	},
	mounted() {
		this.filters.langCode = this.getBrowserLangCode()
		this.fetchData()
	},
	methods: {
		formatIsoDatetime,
		formatNumber,
		getBrowserLangCode,
		getSubscriptionTitle,
		shortenMiddle,
		async fetchData() {
			// Reset pageNo if any filter is applied
			const hasFilter = Object.entries(this.filters).some(
				([key, value]) =>
					!['pageNo', 'pagesize'].includes(key) &&
					value !== '' &&
					value !== null &&
					value !== undefined
			);
			if (hasFilter) {
				this.filters.pageNo = 1;
			}
			getSubscriptionRecordsList(this.filters).then(({ data }) => {
				// console.log(JSON.stringify(data))
				this.tableData = data.list;
				this.total = data.total;
			});
		},
	},
}
</script>

<style>
.launchpad-container {
	padding: 20px;
}

.filter-form {
	margin-bottom: 20px;
}

.subscription-table .el-table__row:hover>td {
	background-color: inherit !important;
	transition: none;
}

.subscription-table th .cell {
	white-space: normal !important;
	word-wrap: break-word !important;
	word-break: break-word !important;
}
</style>
