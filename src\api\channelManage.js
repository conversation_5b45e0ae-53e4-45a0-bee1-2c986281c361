import request from '@/utils/request'

// 顶级代理统计
export function topagentlist(data) {
  return request({
    url: '/managers/v1/channel/topagentlist',
    method: 'post',
    data: { data }
  })
}
// 代理直推统计
export function agenttotallist(data) {
  return request({
    url: '/managers/v1/channel/agenttotallist',
    method: 'post',
    data: { data }
  })
}

// usd顶级代理统计
export function usdopagentlist(data) {
  return request({
    url: '/managers/v1/channel/usdopagentlist',
    method: 'post',
    data: { data }
  })
}
// usd代理直推统计
export function usdagenttotallist(data) {
  return request({
    url: '/managers/v1/channel/usdagenttotallist',
    method: 'post',
    data: { data }
  })
}

// 获取渠道列表信息
export function getactivitychennal(data) {
  return request({
    url: '/managers/v1/channel/getactivitychennal',
    method: 'post',
    data: { data }
  })
}
// 绑定管理员
export function addchannelagent(data) {
  return request({
    url: '/managers/v1/channel/addchannelagent',
    method: 'post',
    data: { data }
  })
}

