<template>
  <div class="gold-container">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        :placeholder="$t('filters.topIDNick')"
        style="width: 130px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        :placeholder="$t('filters.agent')"
        style="width: 150px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.coinid"
        :placeholder="$t('filters.currency')"
        clearable
        style="width: 120px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in coinOptions"
          :key="item.currencyid"
          :label="item.currencyname"
          :value="item.currencyid"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.stype"
        :placeholder="$t('filters.orderType')"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in stypeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <span style="margin-left: 20px; font-size: 12px">{{$t('tableHeader.clinchDealTime')}}</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change='filterTimeTransform'
      >
      </el-date-picker>

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin: 10px 0 0 20px;"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="goldList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="userid" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center" min-width="95"> </el-table-column>
      <el-table-column :label="$t('filters.topID')" prop="top_agent_id" align="center" min-width="78"> 
         <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topNick')" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorID')" prop="pareid" align="center" min-width="78">
           <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.currency')" prop="currencyname" align="center" min-width="80px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.amount')" prop="amount" min-width="90px" align="center"></el-table-column>
      <el-table-column :label="$t('tableHeader.walletAccountBalance')" prop="balance" min-width="120px" align="center"></el-table-column>
      <el-table-column :label="$t('filters.orderType')" prop="netcash" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{row.type && stypeOptions.find((v)=>(v.key == row.type)) && stypeOptions.find((v)=>(v.key == row.type)).name || '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.clinchDealTime')" prop="createdtime" width="75" align="center"></el-table-column>
     </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,50,100,200,300]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getwalletbill } from "@/api/fundQuery";
import { getprocoinList } from "@/api/user";

// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";

export default {
  name: "goldquery",
  data() {
    return {
      listLoading: false,
      total: 0,
      goldList: null,
      filterTime: [],
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        coinid: '', //币种id -1全部
        star: "", //开始
        end: "", //结束
        stype: undefined, //1充值 2 提币 64 法币购买
        pageNo: 1,
        pagesize: 10,
      },
      coinOptions: [],
      stypeOptions: [
        { key: 1, name:this.$t('filters.Top_up') },
        { key: 2, name:this.$t('filters.Mention_money') },
        { key: 64, name:this.$t('filters.Fiat_deal') },
        { key: 4096, name:this.$t('filters.Fiat_sold') },
      ],
      downloadLoading: false,
    };
  },

  components: {},

  computed: {},

  mounted() {
    getprocoinList({}).then((res)=>{
      this.coinOptions = res.data.filter(v=>v.status)
    })
    this.getList();
  },

  methods: {
    //  渲染table列表
    getList() {
      this.listLoading = true;
      let data = {}
      Object.assign(data,this.listQuery)
      data.coinid = data.coinid || -1
      // status: this.listQuery.status === 0 ? 0 : this.listQuery.status || -1,
      data.stype = data.stype || undefined
      getwalletbill(data).then((res) => {
        this.goldList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    filterTimeTransform(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val && val[1]+' 23:59:59' || '';
    },
  },
};
</script>
<style lang="scss" scoped>
</style>