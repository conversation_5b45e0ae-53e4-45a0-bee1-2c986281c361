<!-- src/views/launchpad/pledgerecords.vue -->
<template>
	<div class="pledgerecords-container">
		<!-- Filter Bar -->
		<el-form :inline="true" :model="filters" class="filter-form">
			<el-form-item>
				<el-input v-model="filters.title" :placeholder="$t(`launchpad.placeholders.pledgeCoin`)" clearable />
			</el-form-item>
			<el-form-item>
				<el-input v-model="filters.coin" :placeholder="$t(`launchpad.placeholders.tokenName`)" clearable />
			</el-form-item>
			<el-form-item label="Status">
				<el-select v-model="filters.status" placeholder="All" clearable>
					<el-option :label="$t(`launchpad.placeholders.pledgeStatusNormal`)" value="normal" />
					<el-option :label="$t(`launchpad.placeholders.pledgeStatusReview`)" value="redemption_review" />
					<el-option :label="$t(`launchpad.placeholders.pledgeStatusRedeemed`)" value="redeemed" />
				</el-select>
			</el-form-item>
			<el-form-item label="Start">
				<el-date-picker v-model="filters.start" type="date" :placeholder="$t(`launchpad.placeholders.begin`)" />
			</el-form-item>
			<el-form-item label="End">
				<el-date-picker v-model="filters.end" type="date" :placeholder="$t(`launchpad.placeholders.end`)" />
			</el-form-item>
			<el-form-item>
				<el-button type="primary" icon="el-icon-search" @click="fetchData">
					{{ $t('buttons.search') }}
				</el-button>
				<!-- <el-button type="success" icon="el-icon-plus" @click="openDialog('add')">{{ $t('buttons.add') }}</el-button> -->
			</el-form-item>
		</el-form>

		<!-- Table -->
		<el-table :data="tableData" border fit size="mini" class="pledge-table" :row-class-name="tableRowClassName"
			:span-method="tableSpanMethod"
			:header-cell-style="{ background: '#F0F8FF', color: '#333', fontWeight: 'bold' }">
			<el-table-column label="User ID" align="center" min-width="120">
				<template #default="{ row }">
					{{ shortenMiddle(row.user_id) }}
				</template>
			</el-table-column>

			<el-table-column :label="$t(`launchpad.tableHeaders.pledgeStatus`)" align="center" min-width="100">
				<template #default="{ row }">
					<el-tag :type="getPledgeStateTagType(row.pledge.status)">
						{{ getPledgeStatusText(row.pledge.status) }}
					</el-tag>
				</template>
			</el-table-column>
			<el-table-column :label="$t(`launchpad.tableHeaders.category`)" align="center">
				<template #default="{ row }">
					{{ row.pledge.category || '-' }}
				</template>
			</el-table-column>
			<el-table-column :label="$t(`launchpad.tableHeaders.distributionMethod`)" align="center">
				<template #default="{ row }">
					{{ row.pledge.distribution_method || '-' }}
				</template>
			</el-table-column>
			<el-table-column prop="pledge.coin" :label="$t(`launchpad.tableHeaders.pledgeCoin`)" align="center"
				min-width="100" />

			<el-table-column prop="value" :label="$t(`launchpad.tableHeaders.price`)" align="center" min-width="110" />

			<el-table-column :label="$t(`launchpad.tableHeaders.coin`)" align="center">
				<template #default="{ row }">
					{{ row.priceItem.coin }}
				</template>
			</el-table-column>
			<el-table-column :label="$t(`launchpad.tableHeaders.yield`)" align="center">
				<template #default="{ row }">
					{{ row.priceItem.yield_rate }}
				</template>
			</el-table-column>

			<el-table-column prop="income" :label="$t(`launchpad.tableHeaders.income`)" align="center" min-width="90" />
			<el-table-column prop="actual_income" :label="$t(`launchpad.tableHeaders.actualIncome`)" align="center"
				min-width="90" />

			<el-table-column :label="$t(`launchpad.tableHeaders.status`)" align="center" min-width="120">
				<template #default="{ row }">
					<el-tag :type="getPledgeRecordStatusTag(row.status)">
						{{ getPledgeRecordStatusText(row.status) }}
					</el-tag>
				</template>
			</el-table-column>

			<el-table-column :label="$t(`tableHeader.creationTime`)" align="center" min-width="150">
				<template #default="{ row }">
					{{ formatUnix(row.created_at) }}
				</template>
			</el-table-column>
			<el-table-column :label="$t(`launchpad.tableHeaders.releasePledge`)" align="center" min-width="150">
				<template #default="{ row }">
					<span v-if="row.redeemed_at">{{ formatUnix(row.redeemed_at) }}</span>
					<span v-else>-</span>
				</template>
			</el-table-column>
		</el-table>

		<!-- 引入封装分页组件 -->
		<pagina-tion v-show="total > 0" :total="total" :page-sizes="[10, 50, 100, 300]" :page.sync="filters.pageNo"
			:limit.sync="filters.pagesize" @pagination="fetchData" />
	</div>
</template>

<script>
import { getPledgeRecordsList } from '@/api/launchpad';
import { getBrowserLangCode } from '@/utils/launchpad';
import { getPledgeStateTagType, getPledgeStatusText, getPledgeRecordStatusTag, getPledgeRecordStatusText, getRedemptionStatusText, shortenMiddle } from '@/utils/format';
import { formatUnix } from '@/utils/time';

export default {
	name: 'PledgeRecords',
	data() {
		return {
			filters: {
				title: '',
				coin: '',
				status: '',
				start: null,
				end: null,
				pageNo: 1,
				pagesize: 10,
			},
			tableData: [],
			total: 0,
		};
	},
	methods: {
		formatUnix,
		getBrowserLangCode,
		getPledgeStateTagType,
		getPledgeStatusText,
		getPledgeRecordStatusTag,
		getPledgeRecordStatusText,
		getRedemptionStatusText,
		shortenMiddle,
		async fetchData() {
			try {
				// Reset pageNo if any filter is applied
				const hasFilter = Object.entries(this.filters).some(
					([key, value]) =>
						!['pageNo', 'pagesize'].includes(key) &&
						value !== '' &&
						value !== null &&
						value !== undefined
				);
				if (hasFilter) {
					this.filters.pageNo = 1;
				}
				await getPledgeRecordsList(this.filters).then(({ data }) => {
					let groupIndex = 0;
					const flattened = [];
					const list = data.list || [];
					list.forEach((record) => {
						// if (!Array.isArray(record.coins)) return;

						// If coins is a non-empty array, flatten as usual
						if (Array.isArray(record.coins) && record.coins.length > 0) {
							record._groupColorIndex = groupIndex;
							record.coins.forEach((priceItem, index) => {
								flattened.push({
									...record,
									_isFirstPrice: index === 0,
									_priceRowSpan: record.coins.length,
									priceItem,
								});
							});
							groupIndex++;
						} else {
							// Note: To remove these (`record_coins` table not implemented as of June 3, 2025)
							// If coins is empty or not an array, push a dummy row
							flattened.push({
								...record,
								_isFirstPrice: true,
								_priceRowSpan: 1,
								priceItem: {
									coin: record.pledge.coin || '-',
									yield_rate: record.pledge.yield_rate || '-',
								},
							});
							groupIndex++;
						}
					});

					// console.log(JSON.stringify(flattened))
					this.tableData = flattened;
					this.total = data.total;
				});
			} catch (err) {
				console.error('Error fetching pledge records:', err);
			}
		},
		tableSpanMethod({ row, column, rowIndex, columnIndex }) {
			// Define column indices that require rowspan
			const mergeCols = [0, 1, 2, 3, 4, 5, 10, 11, 12]; // Adjust as per actual column order

			if (!mergeCols.includes(columnIndex)) {
				return [1, 1]; // Normal cell
			}

			if (row._isFirstPrice) {
				return [row._priceRowSpan, 1]; // rowspan, colspan
			} else {
				return [0, 0]; // merged cell, don't render
			}
		},
		tableRowClassName({ row, rowIndex }) {
			if (row._isFirstPrice) {
				this._currentStripeIndex = (this._currentStripeIndex || 0) + 1;
			}
			const groupIndex = this._currentStripeIndex || 1;
			return groupIndex % 2 === 1 ? 'group-row-odd' : 'group-row-even';
		},
	},
	mounted() {
		this.fetchData();
	},
	computed: {
	},
};
</script>

<style>
.pledgerecords-container {
	padding: 20px;
}

.filter-form {
	margin-bottom: 20px;
}

.pledge-table .el-table__row:hover>td {
	background-color: inherit !important;
	transition: none;
}

.pledge-table th .cell {
	white-space: normal !important;
	word-wrap: break-word !important;
	word-break: break-word !important;
	line-height: 1.2;
}
</style>
