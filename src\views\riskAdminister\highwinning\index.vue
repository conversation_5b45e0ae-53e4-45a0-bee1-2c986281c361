<template>
  <div class="highwinning-container">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.user_id"
        :placeholder="$t('filters.name')"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.top_agent_id"
        size="mini"
        :placeholder="$t('filters.topID')"
        style="width: 130px; margin-left: 20px; margin-top: 5px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.petname"
        size="mini"
        :placeholder="$t('filters.topNick')"
        style="width: 130px; margin-left: 20px; margin-top: 5px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.pareid"
        size="mini"
        :placeholder="$t('filters.agent')"
        style="width: 150px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.parename"
        size="mini"
        :placeholder="$t('filters.topAgentNick')"
        style="width: 150px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-left: 20px; font-size: 12px">{{ $t('tableHeader.net_PNl') }}≥</span>
       <el-input
        v-model="listQuery.pure_pnl"
        size="mini"
        :placeholder="$t('filters.pleaseInput')"
        style="width: 150px; margin-left: 5px"
        class="filter-item"
        clearable
        oninput="value=value.replace(/^\D*(\d*(?:\.\d{0,6})?).*$/g, '$1')"
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-left: 20px; font-size: 12px">{{ $t('tableHeader.PNL') }}≥</span>
       <el-input
        v-model="listQuery.pnl"
        size="mini"
        :placeholder="$t('filters.pleaseInput')"
        style="width: 150px; margin-left: 5px"
        class="filter-item"
        clearable
        oninput="value=value.replace(/^\D*(\d*(?:\.\d{0,6})?).*$/g, '$1')"
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-left: 20px; font-size: 12px">{{ $t('tableHeader.float_PNL') }}≥</span>
       <el-input
        v-model="listQuery.float_pnl"
        size="mini"
        :placeholder="$t('filters.pleaseInput')"
        style="width: 150px; margin-left: 5px"
        class="filter-item"
        clearable
        oninput="value=value.replace(/^\D*(\d*(?:\.\d{0,6})?).*$/g, '$1')"
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-left: 20px; font-size: 12px;margin-right:5px;">{{ $t('tableHeader.time') }}</span>
      <el-date-picker
        v-model="listQuery.date"
        size="mini"
        value-format="yyyy-MM-dd"
        type="date"
        :placeholder="$t('filters.optionDate')">
      </el-date-picker>
      <!-- <el-date-picker
        style="width: 220px; margin-top: 10px; margin-left: 5px"
        class="picker"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        start-placeholder="起始日期"
        end-placeholder="结束日期"
        @change='dataTimeChange'
      > 
      </el-date-picker>-->
      <el-select
        size="mini"
        v-model="listQuery.exclude_condition"
        :placeholder="$t('filters.noEliminateUser')"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 5px"
        class="filter-item"
      >
        <el-option
          v-for="item in dayOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-button
        style="margin-left: 20px; margin-top: 5px"
        class="filter-item"
        size="mini"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >
        {{ $t('buttons.search') }}
      </el-button>
      <el-button
        class="filter-item"
        :loading="exportLoading"
        v-if="$store.getters.roles.indexOf('highwinningexport')>-1"
        @click="handleExport"
        size="mini"
        type="success"
      >
        {{ $t('buttons.export') }}
      </el-button>
      
    </div>
    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="user_id" align="center" min-width="78"> </el-table-column>
      <!-- <el-table-column label="用户名" prop="user_name" align="center" min-width="95"> </el-table-column>
      <el-table-column label="顶级代理ID" prop="top_agent_id" align="center" min-width="78">
          <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column label="顶级代理昵称" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上级ID" prop="pareid" align="center" min-width="78">
          <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column label="上级用户名" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column> -->
      <el-table-column :label="$t('tableHeader.transaction_number')" prop="trader_num" align="center" min-width="60"> </el-table-column>
      <!-- <el-table-column label="开仓次数" prop="open_num" align="center" min-width="60"> </el-table-column>
      <el-table-column label="平仓次数" prop="win_trading_close" align="center" min-width="60"> </el-table-column> -->
      <el-table-column :label="$t('tableHeader.profitnum')" prop="win_close" align="center" min-width="60"> </el-table-column>
      <el-table-column :label="$t('tableHeader.historyOdds')" prop="win_rate" align="center" min-width="60">
        <template slot-scope="{row}">
          <span>{{row.win_rate + '%'}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.net_PNl')" prop="pure_pnl" align="center" min-width="90"> </el-table-column>
      <el-table-column :label="$t('tableHeader.PNL')" prop="win_pnl" align="center" min-width="90"> </el-table-column>
      <el-table-column :label="$t('tableHeader.ershisi_h_tradingNum')" prop="within24_tradenum" align="center" min-width="60"> </el-table-column>
      <el-table-column :label="$t('tableHeader.ershisi_h_net_pnl')" prop="within24_pnl" align="center" min-width="60"> </el-table-column>
      <el-table-column :label="$t('tableHeader.averageTradingCycle')" prop="average_tradeperiod" align="center" min-width="60"> </el-table-column>
      <el-table-column :label="$t('tableHeader.maxSingleLoss')" prop="max_loss_per" align="center" min-width="90"> </el-table-column>
      <el-table-column :label="$t('tableHeader.maxSingleProfit')" prop="max_profit_per" align="center" min-width="90"> </el-table-column>
      <el-table-column :label="$t('tableHeader.currentPositionValue')" prop="position_value" align="center" min-width="90"> </el-table-column>
      <el-table-column :label="$t('tableHeader.float_PNL')" prop="float_profit" align="center" min-width="90"> </el-table-column>
      <!-- <el-table-column label="占用保证金" prop="margin_sum" align="center" min-width="90"> </el-table-column> -->
      <el-table-column :label="$t('tableHeader.riskDegree')" prop="risk" align="center" min-width="90"> </el-table-column>
      <!-- <el-table-column label="可用余额" prop="available" align="center" min-width="90"> </el-table-column>
      <el-table-column label="资产账户余额" prop="balance" align="center" min-width="90"> </el-table-column>
      <el-table-column label="净入金" prop="pure_deposit" align="center" min-width="90"> </el-table-column> -->
      <el-table-column :label="$t('tableHeader.lastLoginTime')" prop="last_login_time" align="center" width="75"> </el-table-column>
      <el-table-column :label="$t('tableHeader.operation')" align="center" width="100">
        <template slot-scope="{ row }">
          <el-button
            size="mini"
            type="primary"
            @click="detailClick(row)"
            >{{ $t('buttons.toView') }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[200,100,50,20]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
//引入封装接口
import {
  highwinning, highwinningexport,
} from "@/api/riskAdminister";
export default {
  name: "highwinning",
  data() {
    
    return {
      total: 0,
      tableData: [],
      listLoading: true,
      filterTime: [],
      listQuery: {
        user_id: "",
        top_agent_id: "",
        petname: "",
        pareid: "",
        parename: "",
        pure_pnl: "", // 净pnl
        pnl: "", // PNL
        float_pnl: "",// 
        exclude_condition: "", 
        date: "",
        pageNo: 1,
        pagesize: 20,
      },
      dayOptions: [
        { key: 3, name: this.$t('filters.There_was_no_trading_on_the_3_day') },
        { key: 5, name: this.$t('filters.There_was_no_trading_on_the_5_day') },
        { key: 7, name: this.$t('filters.There_was_no_trading_on_the_7_day') },
        { key: 15, name: this.$t('filters.There_was_no_trading_on_the_15_day') },
      ], 
      exportLoading: false, //导出加载中效果
    };
  },
  components: {},
  computed: {},
  mounted() {
    this.getList();
  },
  methods: {
    //点击查看跳转详情页
    detailClick(row){
      // var user_id = JSON.parse(row.user_id)
      this.$router.push({
        path: "/riskAdminister/highwinningdetails",
        query: { 
          id:JSON.parse(row.user_id),
          details: JSON.stringify(row)
        },
      });
    },
    // 回车搜索事件
    handleFilter(){
      this.listQuery.pageNo = 1
      this.getList();
    },
    //  table列表数据
    getList() {
      //开始有加载中效果
      this.listLoading = true;
      let data = {}
      Object.assign(data, this.listQuery)
      data.pure_pnl = this.listQuery.pure_pnl?Number(this.listQuery.pure_pnl):0
      data.pnl = this.listQuery.pnl?Number(this.listQuery.pnl):0
      data.float_pnl = this.listQuery.float_pnl?Number(this.listQuery.float_pnl):0
      data.pareid = this.listQuery.pareid?JSON.parse(this.listQuery.pareid):0
      data.top_agent_id = this.listQuery.top_agent_id?JSON.parse(this.listQuery.top_agent_id):0
      data.exclude_condition = this.listQuery.exclude_condition?parseInt(this.listQuery.exclude_condition):0
      highwinning(data).then((res) => {
        this.tableData = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    dataTimeChange(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val && val[1] || '';
    },
    //表格导出功能
    handleExport() {
      this.exportLoading = true;
      let data = {}
      Object.assign(data, this.listQuery)
      data.pure_pnl = this.listQuery.pure_pnl?Number(this.listQuery.pure_pnl):0
      data.pnl = this.listQuery.pnl?Number(this.listQuery.pnl):0
      data.float_pnl = this.listQuery.float_pnl?Number(this.listQuery.float_pnl):0
      data.pareid = this.listQuery.pareid?JSON.parse(this.listQuery.pareid):0
      data.top_agent_id = this.listQuery.top_agent_id?JSON.parse(this.listQuery.top_agent_id):0
      data.exclude_condition = this.listQuery.exclude_condition?parseInt(this.listQuery.exclude_condition):0
      highwinningexport(data).then((res) => {
        if(res.ret == 0){
          this.$notify.success({title:this.$t('dialog.Operation_is_successful'),message:this.$t('dialog.Please_jiaoyi_daochu_download')})
          this.exportLoading = false
        }
      }).catch(err=>{
        this.exportLoading = false
      })
    },
  },
};
</script>
<style lang="scss" scoped>
.wc_1 {
  width: 100%;
  height: 30px;
  display: flex;
  align-items: center;
  margin: 0 10px;
 
  // justify-content: space-around;
  .wc_1-one {
    width: 45%;
  }
  .idPhotoImg_wrap ::v-deep .el-image__inner{
    width: auto;
   
  }
  .idPhotoImg_wrap ::v-deep .el-image__error{
    min-width: 100px;
  }
}
</style>