<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme
import resize from "./mixins/resize";
import { debounce } from '@/utils'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "440px",
    },
    height: {
      type: String,
      default: "300px",
    },
    autoResize: {
      type: Boolean,
      default: true,
    },
    chartData: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val);
      },
    },
  },
  mounted() {
    this.$nextTick(() => {
      // console.log(this.$el)
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    chartResize(){
      debounce(() => {
        if (this.chart) {
          this.chart.resize()
        }
      }, 100)
    },
    initChart() {
      this.chart = echarts.init(this.$el, "macarons");
      this.setOptions(this.chartData);
    },
    setOptions(val) {
      this.chart.clear();
      let seriesArr = [];
      val &&
        val.legend &&
        val.legend.length &&
        val.legend.forEach((element) => {
          seriesArr.push({
            name: element,
            smooth: false,
            type: "line",
            data: val[element + "List"],
            animationDuration: 1000,
            animationEasing: "cubicInOut",
          });
        });
      this.chart.setOption({
        xAxis: {
          data: val.dataTime,
          boundaryGap: false,
          axisTick: {
            show: false,
          },
        },
        grid: {
          left: 10,
          right: 10,
          bottom: 20,
          top: 30,
          containLabel: true,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
          },
          padding: [5, 10],
        },
        yAxis: {
          axisTick: {
            show: false,
          },
        },
        legend: { data: val.legend },
        series: seriesArr,
      });
    },
  },
};
</script>
