<template>
  <div class="frequency-cantainer">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        :placeholder="$t('filters.topIDNick')"
        style="width: 130px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        :placeholder="$t('filters.agent')"
        style="width: 150px; margin-right: 20px; margin-top: 5px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.tradetount"
        size="mini"
        :placeholder="$t('tableHeader.transaction_number')"
        style="width: 150px; margin-right: 20px; margin-top: 5px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.opencount"
        size="mini"
        :placeholder="$t('tableHeader.openPositions_number')"
        style="width: 150px; margin-right: 20px; margin-top: 5px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.closecount"
        size="mini"
        :placeholder="$t('tableHeader.unwindNum')"
        style="width: 150px; margin-right: 20px; margin-top: 5px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <!-- <el-select
        size="mini"
        v-model="listQuery.currency_name"
        :placeholder="$t('filters.Margin_currency')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in marginCurrencyOptions"
          :key="item.currencyid"
          :label="item.currencyname"
          :value="item.currencyname"
        />
      </el-select> -->
      <!-- <el-select
        size="mini"
        v-model="listQuery.contract_code"
        :placeholder="$t('tableHeader.contract')"
        clearable
        style="width: 120px; margin-right: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in contractOptions"
          :key="item.traderpairs"
          :label="item.traderpairs"
          :value="item.traderpairs"
        />
      </el-select> -->

      <span style="margin-right: 20px; font-size: 12px">{{$t('tableHeader.clinchDealTime')}}</span>
      <el-date-picker
        style="width: 220px; margin-top: 5px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change="filterTimeTransform"
      />
      <!-- <span style="margin-right: 20px; font-size: 12px">开仓张数</span>
      <el-input
        v-model="listQuery.volume_min"
        size="mini"
        style="width: 80px; margin-right: 5px; margin-top: 5px"
        class="filter-item"
        @input="
          () => {
            listQuery.volume_min = listQuery.volume_min.replace(/[^0-9]/g, '');
          }
        "
        @keyup.enter.native="handleFilter"
      >
        <span slot="suffix"> 张 </span>
      </el-input>
      <span style="margin-right: 5px; font-size: 12px">-</span>
      <el-input
        v-model="listQuery.volume_max"
        size="mini"
        style="width: 80px; margin-right: 5px; margin-top: 5px"
        class="filter-item"
        @input="
          () => {
            listQuery.volume_max = listQuery.volume_max.replace(/[^0-9]/g, '');
          }
        "
        @keyup.enter.native="handleFilter"
      >
        <span slot="suffix"> 张 </span>
      </el-input> -->

      <!-- <span style="margin-right: 20px; font-size: 12px"> 交易次数 </span>
      <el-input
        v-model="listQuery.jy"
        style="width: 150px; margin-top: 5px"
        size="mini"
      >
        <i slot="suffix" style="font-style: normal; line-height: 30px">笔</i>
      </el-input> -->

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-right: 20px; margin-top: 5px"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
      <!-- <el-button
        class="filter-item"
        v-if="$store.getters.roles.indexOf('usdtradefrequencyexport')>-1"
        :loading="exportLoading"
        size="mini"
        type="success"
        style="margin-right: 20px; margin-top: 5px"
        @click="handleExport"
      >
        {{$t('buttons.export')}}
      </el-button> -->
    </div>

    <el-tabs v-model="activeName"  @tab-click="handleClick()">
      <el-tab-pane
        :key="item.currencyid"
        v-for="item in tabArr"
        :label="item.currencyname"
        :name="item.currencyname"
      >
      </el-tab-pane>
    </el-tabs>

    <el-table
      v-loading="listLoading"
      :data="openList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="user_id" align="center" min-width="78">
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.userName')"
        prop="user_name"
        align="center"
        min-width="95"
      >
      </el-table-column>
      <el-table-column
        :label="$t('filters.topID')"
        prop="top_agent_id"
        align="center"
        min-width="78"
      >
        <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topNick')" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.superiorID')"
        prop="pareid"
        align="center"
        min-width="78"
      >
        <template slot-scope="{ row }">
          <span>{{ row.pareid || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('filters.Margin_currency')"
        prop="currency_name"
        min-width="90px"
        align="center"
      ></el-table-column>
      <el-table-column :label="$t('tableHeader.transaction_number')" min-width="80px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.trader_num || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.openPositions_number')" align="center" min-width="80px">
        <template slot-scope="{ row }">
          <span>{{ row.open_num }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.unwindNum')" align="center" min-width="80px">
        <template slot-scope="{ row }">
          <span>{{ row.close_num }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.openPositions_pieceNum')" align="center" min-width="80px">
        <template slot-scope="{ row }">
          <span>{{ row.trade_volume }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        :label="$t('tableHeader.IP_addr')"
        prop="ipaddress"
        align="center"
        min-width="120px"
      >
      </el-table-column> -->
      <el-table-column :label="$t('tableHeader.operation')" align="center" min-width="210px">
        <template slot-scope="{ row }">
          <!-- <el-dropdown @command="handleQCFY">
            <el-button size="mini" type="success">
              {{row.isignorere?'撤回去除返佣':'去除返佣'}}<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="beforeHandleCommand(row,'yesterday')">去除昨日</el-dropdown-item>
              <el-dropdown-item :command="beforeHandleCommand(row,'today')">去除当日</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown> -->
          <el-button
            v-if="$store.getters.roles.indexOf('usdsetignorerebate') > -1"
            type="success"
            size="mini"
            @click="handleQCFY(row)"
            >{{ row.isignorere ? $t('buttons.withdraw_remove_rebate') : $t('tableHeader.getRidOfCommission') }}</el-button
          >
          <el-button type="primary" size="mini" @click="handleCK(row)"
            >{{$t('buttons.toView')}}</el-button
          >
          <!--  style="margin-left:10px;" -->
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10, 20, 30, 50]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
//封装的api
import { usdhighfrequency, setignorerebate, tradefrequencyexport } from "@/api/riskAdminister";
import { bcprocontractset, getprocoinList } from "@/api/user";
// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";
export default {
  name: "usdfrequency",
  data() {
    return {
      listLoading: false,
      total: 0,
      openList: null,
      filterTime: [],
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        // account_type: undefined, //账户模式 1：全仓 2：逐仓
        tradetount: "", // 交易次数
        opencount: "", // 开仓次数
        closecount: "", // 平仓次数
        contract_code: "", //合约代码
        // side: undefined, //方向 B买S卖
        // volume_min: "", // 开仓张数
        // volume_max: "", // 开仓张数
        pageNo: 1,
        pagesize: 10,
        star: "", //开始
        end: "", //结束
        jg: "",
        jy: "",
      },

      tabArr: [],
      activeName: '',

      contractOptions: [],
      exportLoading: false, //导出加载中效果
    };
  },

  components: {},

  computed: {
    // 默认时间
    timeDefault() {
      let date = new Date();
      // 通过时间戳计算
      let defalutStartTime = (
        (date.getTime() - 3 * 24 * 3600 * 1000) /
        1000
      ).toDate("yyyy-MM-dd"); // 转化为时间戳
      let defalutEndTime = (date.getTime() / 1000).toDate("yyyy-MM-dd");
      return [defalutStartTime, defalutEndTime];
    },
  },
  mounted() {
    this.filterTime = this.timeDefault;
    // 获取保证金币种
    getprocoinList({}).then((res) => {
      for(var i=0; i<res.data.length; i++) {
        if(res.data[i].currencyname == "USDT") {
          res.data.splice(i,1)
        }
      }
      this.tabArr = res.data.filter(v => v.status)
      if(this.tabArr.length>0){
        this.activeName = this.tabArr[0].currencyname
      }
      this.getList();
    })
    bcprocontractset({}).then((res) => {
      this.contractOptions = res.data.filter((v) => v.isshow == 1);
    });
  },

  methods: {
    //返回新的command对象
    beforeHandleCommand(row, command) {
      //index我这里是遍历的角标，即你需要传递的额外参数
      return Object.assign(row, { dateStr: command });
    },
    //表格导出功能
    handleExport() {
      this.exportLoading = true;
      let data = {};
      this.listQuery.tradetount =
        Number(this.listQuery.tradetount) || undefined;
      this.listQuery.opencount = Number(this.listQuery.opencount) || undefined;
      this.listQuery.closecount =
        Number(this.listQuery.closecount) || undefined;
      this.listQuery.star = (this.filterTime && this.filterTime[0]) || "";
      this.listQuery.end = this.filterTime
        ? this.filterTime[1] + " 23:59:59"
        : "";
      Object.assign(data, this.listQuery, {
        // volume_min: Number(this.listQuery.volume_min) || 0,
        // volume_max: Number(this.listQuery.volume_max) || 0,
      });
      tradefrequencyexport(data).then((res) => {
        if(res.ret == 0){
          this.$notify.success({title:this.$t('dialog.Operation_is_successful'),message:this.$t('dialog.Please_jiaoyi_daochu_download')})
          this.exportLoading = false
        }
      }).catch(err=>{
        this.exportLoading = false
      })
    },
    getList() {
      this.listLoading = true;
      let data = {};
      this.listQuery.tradetount =
        Number(this.listQuery.tradetount) || undefined;
      this.listQuery.opencount = Number(this.listQuery.opencount) || undefined;
      this.listQuery.closecount =
        Number(this.listQuery.closecount) || undefined;
      this.listQuery.star = (this.filterTime && this.filterTime[0]) || "";
      this.listQuery.end = this.filterTime
        ? this.filterTime[1] + " 23:59:59"
        : "";
      Object.assign(data, this.listQuery, {
        currency_name: this.activeName,
        volume_min: Number(this.listQuery.volume_min) || 0,
        volume_max: Number(this.listQuery.volume_max) || 0,
      });
      usdhighfrequency(data).then((res) => {
        this.openList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    //点击查看进入详情页
    handleCK(row) {
      console.log(row)
      this.$router.push({
        path: "/riskAdminister/usdfrequencyDetail",
        query: {
          id: JSON.parse(row.user_id),
          star: this.listQuery.star,
          end: this.listQuery.end,
          contract_code: this.listQuery.contract_code,
        },
      });
    },
    //去除返佣
    handleQCFY(row) {
      // let date = new Date()
      // let today = ((date.getTime()/1000)).toDate('yyyy-MM-dd')
      // let yesterday = ((date.getTime() - 1 * 24 * 3600 * 1000)/1000).toDate('yyyy-MM-dd')
      // ${row.dateStr == 'today'?'当日':'昨日'}
      this.$confirm(
        `${this.$t('dialog.Whether_to_confirm')}${row.isignorere ? this.$t('dialog.To_withdraw') : ""} ${this.$t('dialog.Go_to_this_user_rebate')} `,
        this.$t('dialog.Prompt'),
        {
          confirmButtonText: this.$t('buttons.determine'),
          cancelButtonText: this.$t('buttons.cancel'),
          type: "warning",
        }
      )
        .then(() => {
          setignorerebate({
            uid: row.userid,
            stype: row.isignorere ? 1 : 2,
            // tdate: row.dateStr == 'today'?today:yesterday
          }).then((res) => {
            this.$notify.success({ title: this.$t('dialog.Successful'), message: this.$t('dialog.Operation_is_successful') });
            this.getList();
          });
        })
        .catch(() => {});
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    filterTimeTransform(val) {
      this.listQuery.star = (val && val[0]) || "";
      this.listQuery.end = val ? val[1] + " 23:59:59" : "";
    },
    // tab切换
    handleClick() {
      this.getList()
    }
  },
};
</script>
<style lang="scss">
.frequency-cantainer {
  .el-input__prefix,
  .el-input__suffix {
    display: flex;
    align-items: center;
  }
  .el-input--suffix .el-input__inner {
    padding-left: 8px;
    padding-right: 20px;
  }
}
</style>