<template>
  <div class="asset-container">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        :placeholder="$t('filters.topIDtopNick')"
        style="width: 180px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        size="mini"
        v-model="listQuery.agent"
        :placeholder="$t('filters.agent')"
        style="width: 150px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <!-- <el-select
        size="mini"
        v-model="listQuery.currency_name"
        :placeholder="$t('filters.Margin_currency')"
        clearable
        style="width: 150px; margin-left: 10px;"
        class="filter-item"
      >
        <el-option
          v-for="item in marginCurrencyOptions"
          :key="item.currencyid"
          :label="item.currencyname"
          :value="item.currencyname"
        />
      </el-select> -->
      <el-button
        style="margin-top: 10px; margin-left: 20px"
        class="filter-item"
        size="mini"
        type="primary"
        @click="handleFilter"
      >
        {{ $t("buttons.search") }}
      </el-button>
    </div>

    <div class="box-se">
        <div class="box-content" v-for="(item,index) in usdbanlce" :key="index">
          <div>{{ item.currency_name }}</div>
          <div><span>{{ $t('tableHeader.available') }}</span>:<span>{{ item.available }}</span></div>
          <div><span>{{ $t('tableHeader.freeze') }}</span>:<span>{{ item.lock_amount }}</span></div>
          <div><span>{{ $t('others.rights') }}</span>:<span>{{ item.balance }}</span></div>
        </div>
    </div>

    <el-table
      v-loading="listLoading"
      :data="totalAssetList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')"       prop="user_id" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.user_id || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')"  prop="user_name" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.user_name || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topID')"         prop="top_agent_id" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topNick')"       prop="petname" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorID')"        prop="pareid" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')"  prop="parename" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('others.rights')"         prop="usdassetquey" align="center" min-width="120">
        <template slot-scope="{ row }">
          <div v-for="(item,index) in row.usdassetquey" :key="index">{{ item.currency_name }}&nbsp;{{ item.balance }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.available')" prop="usdassetquey" align="center" min-width="120">
        <template slot-scope="{ row }">
          <div v-for="(item,index) in row.usdassetquey" :key="index">{{ item.currency_name }}&nbsp;{{ item.available }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.freeze')"    prop="usdassetquey" align="center" min-width="120">
        <template slot-scope="{ row }">
          <div v-for="(item,index) in row.usdassetquey" :key="index">{{ item.currency_name }}&nbsp;{{ item.lock_amount }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.net_transfer')" prop="usdassetquey" align="center" min-width="120">
        <template slot-scope="{ row }">
          <div v-for="(item,index) in row.usdassetquey" :key="index">{{ item.currency_name }}&nbsp;{{ item.outcap }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.net_into')"     prop="usdassetquey" align="center" min-width="120">
        <template slot-scope="{ row }">
          <div v-for="(item,index) in row.usdassetquey" :key="index">{{ item.currency_name }}&nbsp;{{ item.intcap }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.net_PNl')"       prop="usdassetquey" align="center" min-width="120">
        <template slot-scope="{ row }">
          <div v-for="(item,index) in row.usdassetquey" :key="index">{{ item.currency_name }}&nbsp;{{ item.netpnl }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.PNL')"           prop="usdassetquey" align="center" min-width="120">
        <template slot-scope="{ row }">
          <div v-for="(item,index) in row.usdassetquey" :key="index">{{ item.currency_name }}&nbsp;{{ item.closepnl }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.poundage')"       prop="usdassetquey" align="center" min-width="120">
        <template slot-scope="{ row }">
          <div v-for="(item,index) in row.usdassetquey" :key="index">{{ item.currency_name }}&nbsp;{{ item.commission }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.moneyCost')"     prop="usdassetquey" align="center" min-width="120">
        <template slot-scope="{ row }">
          <div v-for="(item,index) in row.usdassetquey" :key="index">{{ item.currency_name }}&nbsp;{{ item.capital }}</div>
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10, 20, 30, 50]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { reverseusdassetquery } from "@/api/fundQuery";
import { getprocoinList } from "@/api/user";
export default {
  name: "USDAssetsQuery",
  data() {
    return {
      listLoading: false,
      total: 0,
      totalAssetList: null,
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        sectop: "",  // 顶级代理ID/顶级昵称
        agent: "",  // 上级代理ID/用户名
        // currency_name: "", // 保证金币种
        pageNo: 1,
        pagesize: 10,
      },
      marginCurrencyOptions: [],    // 保证金币种
      usdbanlce: null, // 汇总数据
    };
  },
  components: {},
  computed: {},
  mounted() {
    getprocoinList({reverse_enable:1}).then((res) => {
      this.marginCurrencyOptions = res.data.filter(v => v.status)
    })
    this.getList();
  },
  methods: {
    getList() {
      this.listLoading = true;
      let data =  {};
      Object.assign(data, this.listQuery)
      reverseusdassetquery(data).then((res) => {
        console.log(res)
        this.usdbanlce = res.data.usdbanlce;
        this.totalAssetList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      })
    },
    // 搜索
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
  },
};
</script>

<style lang="scss" scoped>
.box-se {
  border: 1px solid #c9c9c9;
  padding: 10px 0;
  margin: 15px 5%;
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: flex-start;
  .box-content {
    display: flex;
    flex-direction: row;
    div {
      margin: 5px 10px;
      width: 30%;
      display: flex;
      align-items: center;
    }
    div:nth-child(1) {
      width: 10%;
    }
  }
}
</style>