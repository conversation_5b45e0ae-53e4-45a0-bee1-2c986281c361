import request from '@/utils/request'

// 字典类型管理

/**
 * 获取字典类型列表
 * @param {Object} query 查询参数
 * @returns {Promise}
 */
export function getDictTypeList(query) {
  return request({
    url: '/managers/v1/dict/getdicttypelist',
    method: 'post',
    data: query
  })
}

/**
 * 获取字典类型详情
 * @param {Object} data 参数
 * @returns {Promise}
 */
export function getDictTypeInfo(data) {
  return request({
    url: '/managers/v1/dict/getdicttypeinfo',
    method: 'post',
    data: { data }
  })
}

/**
 * 新增字典类型
 * @param {Object} data 字典类型数据
 * @returns {Promise}
 */
export function addDictTypeInfo(data) {
  return request({
    url: '/managers/v1/dict/adddicttypeinfo',
    method: 'post',
    data: { data }
  })
}

/**
 * 修改字典类型
 * @param {Object} data 字典类型数据
 * @returns {Promise}
 */
export function upDictTypeInfo(data) {
  return request({
    url: '/managers/v1/dict/updicttypeinfo',
    method: 'post',
    data: { data }
  })
}

/**
 * 删除字典类型
 * @param {Object} data 参数
 * @returns {Promise}
 */
export function delDictTypeInfo(data) {
  return request({
    url: '/managers/v1/dict/deldicttypeinfo',
    method: 'post',
    data: { data }
  })
}

// 字典数据管理

/**
 * 获取字典数据列表
 * @param {Object} query 查询参数
 * @returns {Promise}
 */
export function getDictDataList(query) {
  return request({
    url: '/managers/v1/dict/getdictdatalist',
    method: 'post',
    data: query
  })
}

/**
 * 获取字典数据详情
 * @param {Object} data 参数
 * @returns {Promise}
 */
export function getDictDataInfo(data) {
  return request({
    url: '/managers/v1/dict/getdictdatainfo',
    method: 'post',
    data: { data }
  })
}

/**
 * 新增字典数据
 * @param {Object} data 字典数据
 * @returns {Promise}
 */
export function addDictDataInfo(data) {
  return request({
    url: '/managers/v1/dict/adddictdatainfo',
    method: 'post',
    data: { data }
  })
}

/**
 * 修改字典数据
 * @param {Object} data 字典数据
 * @returns {Promise}
 */
export function upDictDataInfo(data) {
  return request({
    url: '/managers/v1/dict/updictdatainfo',
    method: 'post',
    data: { data }
  })
}

/**
 * 删除字典数据
 * @param {Object} data 参数
 * @returns {Promise}
 */
export function delDictDataInfo(data) {
  return request({
    url: '/managers/v1/dict/deldictdatainfo',
    method: 'post',
    data: { data }
  })
}

// 删除指定字典类型的所有 Redis 缓存
export function delDictDataRedis(data) {
  return request({
    url: '/managers/v1/dict/deldictdataredis',
    method: 'post',
    data: { data }
  })
}
