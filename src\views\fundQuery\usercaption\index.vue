<template>
  <div class="caption-cantainer">
    <div class="filter-container">
      <el-select
        size="mini"
        v-model="listQuery.contcode"
        :placeholder="$t('tableHeader.contract')"
        clearable
        style="width: 120px;"
        class="filter-item"
      >
        <el-option
          v-for="item in contractOptions"
          :key="item.traderpairs"
          :label="item.traderpairs"
          :value="item.traderpairs"
        />
      </el-select>
      <span style="margin-left: 20px; font-size: 12px">{{$t('tableHeader.clinchDealTime')}}</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change='filterTimeTransform'
      >
      </el-date-picker>

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-left: 20px;"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
      <el-button
        class="filter-item"
        :loading="exportLoading"
        v-if="$store.getters.roles.indexOf('getusercaptionexport')>-1"
        @click="handleExport"
        size="mini"
        type="success"
      >
        {{$t('buttons.export')}}
      </el-button>
    </div>

    <el-tabs v-model="activeName"  @tab-click="handleClick()">
      <el-tab-pane
        :key="item.currencyid"
        v-for="item in tabArr"
        :label="item.currencyname"
        :name="item.currencyname"
      >
      </el-tab-pane>
    </el-tabs>

    <el-table
      v-loading="listLoading"
      :data="openList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.contract')" prop="contractcode" min-width="90px" align="center"></el-table-column>
      <el-table-column :label="$t('filters.Margin_currency')" prop="currency_name" min-width="90px" align="center">USDT</el-table-column>
      <el-table-column :label="$t('tableHeader.moneyReat')" prop="funding_rate" min-width="90px" align="center"></el-table-column>
      <el-table-column :label="$t('tableHeader.difference')" prop="diff" align="center" min-width="100px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.settlementTime')" prop="create_time" align="center" min-width="100px"> </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,50,100,200,300]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
//封装的api
import { getusercaption, usercaptionexport } from "@/api/fundQuery";
import { getprocoinList } from "@/api/user";
import { bcusdprocontractset } from "@/api/multiCurrency"
// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";
export default {
  name: "usercaption",
  data() {
    return {
      listLoading: false,
      total: 0,
      openList: null,
      filterTime: [],
      listQuery: {
        contcode: "", //合约代码
        pageNo: 1,
        pagesize: 10,
        star: '', //开始
        end: '', //结束
      },
      contractOptions: [],
      accountTypeOptions: [
        { key: 1, name: this.$t('tableHeader.all_warehouse') },
        { key: 2, name: this.$t('tableHeader.by_warehouse') },
      ],
      sizeOptions: [
        { key: "B", name: this.$t('tableHeader.open_to_buy_more') },
        { key: "S", name: this.$t('tableHeader.sell_empty') },
      ],
      orderTypeObj: {
        0: this.$t('filters.Market_orders'),
        1: this.$t('tableHeader.order'), 
        2: this.$t('tableHeader.check_single'), 
        4: this.$t('tableHeader.stop_loss_orders'), 
        5: this.$t('filters.Strong_flat_sheet')
      },
      exportLoading: false, //导出加载中效果

      tabArr: [],
      activeName: ''
    };
  },

  components: {},

  computed: {
    // 默认时间
    timeDefault () {
      let date = new Date()
      // 通过时间戳计算
      let defalutStartTime = ((date.getTime() - 1 * 24 * 3600 * 1000)/1000).toDate('yyyy-MM-dd') // 转化为时间戳
      let defalutEndTime = ((date.getTime()/1000)).toDate('yyyy-MM-dd')
      return [defalutStartTime, defalutEndTime]
    }
  },

  mounted() {
    // getprocoinList({}).then((res) => {
    //   res.data.map(v => {
    //     v['currencyid'] = v['currencyid'].toString()
    //   })
    //   for(var i=0; i<res.data.length; i++) {
    //     if(res.data[i].currencyname == "XRP") {
    //       res.data.splice(i,1)
    //     }
    //   }
    //   this.tabArr = res.data.filter(v => v.status)
    //   // console.log(this.tabArr)
    // })
    getprocoinList({}).then((res) => {
      this.tabArr = res.data.filter(v => v.status)
      if(this.tabArr.length>0){
        this.activeName = this.tabArr[0].currencyname
      }
      this.getList();
    })
    bcusdprocontractset({}).then((res) => {
      this.contractOptions = res.data.filter(v=>v.isshow == 1)
    })
    this.filterTime = this.timeDefault;
  },

  methods: {
    getList() {
      this.listLoading = true;
      let data = {}
      this.listQuery.side = this.listQuery.side || undefined
      this.listQuery.account_type = this.listQuery.account_type || undefined
      this.listQuery.star = (this.filterTime && this.filterTime[0]) || "";
      this.listQuery.end = this.filterTime ? this.filterTime[1] + " 23:59:59" : "";
      Object.assign(data, this.listQuery, {
        // currency_name: this.activeName,
      })
      getusercaption(data).then((res) => {
        this.openList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    //表格导出功能
    handleExport() {
      this.exportLoading = true;
      let data = {}
      Object.assign(data,this.listQuery)
      let newDate = parseInt(new Date().getTime()/1000);
      data.end = this.listQuery.end ? this.listQuery.end : (newDate.toDate('yyyy-MM-dd')+ ' 23:59:59')
      newDate = parseInt(newDate-518400)
      data.star = this.listQuery.star ? (this.listQuery.star + ' 00:00:00' ): (newDate.toDate('yyyy-MM-dd')+ ' 00:00:00')
      usercaptionexport(data).then((res) => {
        if(res.ret == 0){
          this.$notify.success({title:this.$t('dialog.Operation_is_successful'),message:this.$t('dialog.Please_jiaoyi_daochu_download')})
          this.exportLoading = false
        }
      }).catch(err=>{
        this.exportLoading = false
      })
    },
    filterTimeTransform(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val? val[1]+' 23:59:59':'';
    },

    // tab切换
    handleClick() {
      this.getList()
    }
  },
};
</script>
<style lang="scss" scoped>
</style>