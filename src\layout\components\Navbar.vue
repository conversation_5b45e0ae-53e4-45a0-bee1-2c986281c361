<template>
  <div class="navbar">
    <hamburger
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    />

    <breadcrumb class="breadcrumb-container" />

    <div class="right-menu">
      <el-dropdown class="avatar-container" trigger="click">
        <span>{{username}}</span>
        <i class="el-icon-caret-bottom" />

        <el-dropdown-menu
          slot="dropdown"
          style="width: 240px; position: absolute; left: 1656px"
        >
          <router-link to="/">
            <el-dropdown-item> {{ $t('menus.home') }} </el-dropdown-item>
          </router-link>
          <el-dropdown-item divided @click.native="modifyPass">
            <span style="display: block">{{ $t('login.Change_password') }}</span>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="logout">
            <span style="display: block">{{ $t('login.Log_out') }}</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-dropdown class="avatar-container">
        <div class="avatar-wrapper">
          <span>{{ language[$i18n.locale] }}</span>
          <i class="el-icon-arrow-down" />
        </div>
        <el-dropdown-menu slot="dropdown" class="user-dropdown">
          <el-dropdown-item v-for="(value, key, index) in language" :key="index" @click.native="checkoutLan(key)"
            >{{value}}</el-dropdown-item
          >
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <el-dialog
      title=" "
      :visible.sync="modifyPassDialog"
      @close="closexgmm"
      :close-on-click-modal="false"
      width="50%"
    >
      <el-form  :rules="rules" ref="ruleForm" :model="ruleForm">
        <el-form-item :label="$t('login.Enter_the_old_password')" prop="oldPass" label-width="83px">
          <el-input
            type="password"
            :placeholder="$t('login.Please_enter_the_old_password')"
            v-model="ruleForm.oldPass"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item :label="$t('login.Enter_the_new_password')" prop="newPass" label-width="83px">
          <el-input
            type="password"
            :placeholder="$t('login.Please_enter_the_new_password_6_16')"
            v-model="ruleForm.newPass"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item :label="$t('login.confirm_new_password')" prop="checkPass" label-width="83px">
          <el-input
            type="password"
            :placeholder="$t('login.Please_confirm_the_new_password_again')"
            v-model="ruleForm.checkPass"
            autocomplete="off"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closexgmm()">{{ $t('buttons.cancel') }}</el-button>
        <el-button size="mini" type="primary" @click="entry()">{{ $t('buttons.determine') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Breadcrumb from "@/components/Breadcrumb";
import Hamburger from "@/components/Hamburger";
import { upmanagepass } from "@/api/user";
import Cookies from 'js-cookie'
export default {
  data(){
    var checkAge = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('login.The_old_password_cannot_be_empty')));
      } else {
        callback();
      }
    };
    var validatePass = (rule, value, callback) => {
      if (value === "") {
        callback(new Error(this.$t('login.errTip_pass')));
      } else if (!/^[0-9a-zA-Z]+$/.test(value)) {
        callback(new Error(this.$t('login.Only_English_Number')));
      } else if (!/^\w{4,16}$/.test(value)) {
        callback(new Error(this.$t('login.Length_6_16')));
      } else {
        if (this.ruleForm.checkPass !== "") {
          this.$refs.ruleForm.validateField("checkPass");
        }
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error(this.$t('login.Please_enter_your_password_again')));
      } else if (!/^[0-9a-zA-Z]+$/.test(value)) {
        callback(new Error(this.$t('login.Only_English_Number')));
      } else if (value !== this.ruleForm.newPass) {
        callback(new Error(this.$t('login.The_two_passwords_are_inconsistent')));
      } else {
        callback();
      }
    };
    return {
      language: {
        zh: "简体中文",
        en: "English",
        // ko:"한국어",
        // ja:'日本語',
        // zhTW:'繁體中文',
      },
      modifyPassDialog: false,
      ruleForm: {
        oldPass: "",
        newPass: "",
        checkPass: ""
      },
      rules: {
        newPass: [{ validator: validatePass, trigger: "blur" }],
        checkPass: [{ validator: validatePass2, trigger: "blur" }],
        oldPass: [{ validator: checkAge, trigger: "blur" }]
      },
      username:''
    }
  },
  components: {
    Breadcrumb,
    Hamburger,
  },
  computed: {
    ...mapGetters([
      "sidebar",
      "avatar",
      //从vuex传过来用户名
      "name",
    ]),
  },
  created() {
    // console.log(this.$store.state.name)
    this.username = Cookies.get('username')
  },
  methods: {
    checkoutLan(type) {
      this.$store.commit('app/setLanguage',type)
      this.$i18n.locale = type;
      // location.reload();
    },
    copy() {
      document.execCommand("Copy"); // 执行浏览器复制命令
      this.$message({
        message: this.$t('others.Copy_success'),
        type: "success",
      });
    },
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },
    closexgmm() {
      this.$refs["ruleForm"].resetFields();
      this.modifyPassDialog = false;
      this.ruleForm.oldPass = "";
      this.ruleForm.newPass = "";
      this.ruleForm.checkPass = "";
    },
    entry() {
      this.$refs["ruleForm"].validate(valid => {
        if (valid) {
          upmanagepass({
            pwd: this.ruleForm.oldPass,
            pwd1: this.ruleForm.newPass,
            pwd2: this.ruleForm.checkPass,
          }).then((res)=>{
            // console.log(res)
            this.$notify({
              title: this.$t('buttons.modify'),
              message: this.$t('others.Modify_the_success'),
              type: "success",
              duration: 2000,
            })
            setTimeout(()=>{
              this.closexgmm()
              this.logout()
            },500)
          })
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    modifyPass(){
      this.modifyPassDialog = true
    },
    async logout() {
      await this.$store.dispatch("user/logout");
      this.$router.push(`/login?redirect=${this.$route.fullPath}`);
    },
  },
};
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .right-menu {
    float: right;
    height: 100%;
    // line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 30px;
      cursor: pointer;
      .avatar-wrapper {
        display: flex;
        align-items: center;
        justify-content: space-around;
        height: 40px;
        margin-top: 10px;
        // position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }
        .el-dropdown-menu {
          width: 200px;
          height: 500px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
