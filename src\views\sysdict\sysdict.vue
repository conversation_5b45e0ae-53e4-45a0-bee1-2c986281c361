<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item :label="$t('sysdict.dictName')" prop="dictName">
        <el-input v-model="queryParams.data.dictName" :placeholder="$t('sysdict.pleaseEnterDictName')" clearable
          style="width: 240px" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('sysdict.dictType')" prop="dictType">
        <el-input v-model="queryParams.data.dictType" :placeholder="$t('sysdict.pleaseEnterDictType')" clearable
          style="width: 240px" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('sysdict.status')" prop="status">
        <el-select v-model="queryParams.data.status" :placeholder="$t('sysdict.pleaseSelectStatus')" clearable
          style="width: 240px">
          <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" @click="handleAdd">{{ $t('common.add') }}</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="typeList" border style="width: 100%">
      <el-table-column type="index" :label="$t('common.index')" width="50" align="center" />
      <el-table-column prop="id" :label="$t('sysdict.dictId')" width="100" align="center" />
      <el-table-column prop="dictName" :label="$t('sysdict.dictName')" min-width="160" align="center"
        :show-overflow-tooltip="true" />
      <el-table-column prop="dictType" :label="$t('sysdict.dictType')" min-width="100" align="center"
        :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <el-link type="primary" @click="handleDictData(scope.row)">{{ scope.row.dictType }}</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="status" :label="$t('sysdict.status')" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">{{ scope.row.status === '0' ?
            $t('sysdict.normal') : $t('sysdict.disabled') }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="remark" :label="$t('sysdict.remark')" align="center" min-width="100" :show-overflow-tooltip="true" />
      <el-table-column prop="createTime" :label="$t('sysdict.createTime')" min-width="90" align="center" />
      <el-table-column :label="$t('common.operation')" min-width="150" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">{{ $t('common.edit') }}</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">{{ $t('common.delete') }}</el-button>
          <el-button size="mini" type="warning" plain icon="el-icon-refresh" @click="handlePurge(scope.row)">{{ $t('common.purgeRedis') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagina-tion v-show="total > 0" :total="total" :page.sync="queryParams.data.pageNo"
      :limit.sync="queryParams.data.pagesize" @pagination="getList" />

    <!-- 添加或修改字典类型对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item :label="$t('sysdict.dictName')" prop="dictName">
          <el-input v-model="form.dictName" :placeholder="$t('sysdict.pleaseEnterDictName')" />
        </el-form-item>
        <el-form-item :label="$t('sysdict.dictType')" prop="dictType">
          <el-input v-model="form.dictType" :placeholder="$t('sysdict.pleaseEnterDictType')" />
        </el-form-item>
        <el-form-item :label="$t('sysdict.status')" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in statusOptions" :key="dict.value" :label="dict.value">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('sysdict.remark')" prop="remark">
          <el-input v-model="form.remark" type="textarea" :placeholder="$t('sysdict.pleaseEnterRemark')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDictTypeList, getDictTypeInfo, addDictTypeInfo, upDictTypeInfo, delDictTypeInfo, delDictDataRedis } from '@/api/sysdict'

export default {
  name: 'SysDict',
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 字典类型表格数据
      typeList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 状态数据字典
      statusOptions: [
        {
          value: '0',
          label: this.$t('sysdict.normal')
        },
        {
          value: '1',
          label: this.$t('sysdict.disabled')
        }
      ],
      // 查询参数
      queryParams: {
        data: {
          pageNo: 1,
          pagesize: 10,
          dictName: undefined,
          dictType: undefined,
          status: undefined,
        }
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        dictName: [
          { required: true, message: this.$t('sysdict.dictNameRequired'), trigger: 'blur' }
        ],
        dictType: [
          { required: true, message: this.$t('sysdict.dictTypeRequired'), trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询字典类型列表 */
    getList() {
      this.loading = true
      getDictTypeList(this.queryParams).then(response => {
        this.typeList = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        dictName: undefined,
        dictType: undefined,
        status: '0',
        remark: undefined
      }
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
    },
    /** 重置表单 */
    resetForm(refName) {
      if (this.$refs[refName]) {
        this.$refs[refName].resetFields()
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.data.page = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.queryParams.data = {
        pageNo: 1,
        pagesize: 10,
        dictName: undefined,
        dictType: undefined,
        status: undefined,
      }
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = this.$t('sysdict.addDictType')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getDictTypeInfo({ id: id }).then(response => {
        this.form = response.data
        this.open = true
        this.title = this.$t('sysdict.editDictType')
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id !== undefined) {
            upDictTypeInfo(this.form).then(response => {
              this.$message.success(this.$t('common.updateSuccess'))
              this.open = false
              this.getList()
            })
          } else {
            addDictTypeInfo(this.form).then(response => {
              this.$message.success(this.$t('common.addSuccess'))
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id
      this.$confirm(this.$t('common.confirmDelete'), this.$t('common.tip'), {
        confirmButtonText: this.$t('common.confirm'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        return delDictTypeInfo({ id: id })
      }).then(() => {
        this.getList()
        this.$message.success(this.$t('common.deleteSuccess'))
      }).catch(() => { })
    },
    /** 跳转到字典数据页面 */
    handleDictData(row) {
      const dictType = row.dictType
      const dictName = row.dictName
      this.$router.push({
        path: '/systemManagement/sysdictdata',
        query: {
          dictType: dictType,
          dictName: dictName,
        }
      })
    },
    handlePurge(row) {
      const id = row.id
      const dictType = row.dictType
      this.$confirm(this.$t('common.confirmDelete'), this.$t('common.tip'), {
        confirmButtonText: this.$t('common.confirm'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        return delDictDataRedis({ dictType: dictType })
      }).then(() => {
        this.getList()
        this.$message.success(this.$t('common.deleteSuccess'))
      }).catch(() => { })
    }
  }
}
</script>
