<template>
  <div class="PNLQueryList">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.userid"
        :placeholder="$t('filters.name')"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.top_agent_id"
        size="mini"
        :placeholder="$t('filters.topIDtopNick')"
        style="width: 180px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.pareid"
        size="mini"
        :placeholder="$t('filters.agent')"
        style="width: 180px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-left: 20px; font-size: 12px">{{
        $t("tableHeader.time")
      }}</span>
      <el-date-picker
        style="width: 220px; margin-top: 5px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change="filterTimeTransform"
        :picker-options="pickerOptions"
      />
      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-left: 20px"
        @click="handleFilter"
      >
        {{ $t("buttons.search") }}
      </el-button>
    </div>
    <el-tabs
      v-model="activeName"
      @tab-click="handleClick"
      style="margin: 15px 15px"
    >
      <el-tab-pane :label="$t('forms.USDT_contract')" name="USDT">
        <el-table
          v-loading="listLoading"
          :data="tableData"
          border
          fit
          highlight-current-row
          style="width: 100%; margin-top: 30px"
          size="mini"
          :header-cell-style="{ background: '#F0F8FF' }"
        >
          <el-table-column
            :label="$t('tableHeader.contractName')"
            prop="contract_code"
            align="left"
            min-width="80"
          >
          </el-table-column>

          <el-table-column
            :label="
              $t('tableHeader.todayCloseTotalProfit') +
              $t('tableHeader.net_PNl')
            "
            prop="netpnl"
            align="left"
            min-width="110"
          >
            <template slot-scope="{ row }">
              <span class="net_PNl">{{ row.netpnl || "--" }} USDT</span>
              <el-button
                @click="lookChart('netpnl',row)"
                type="text"
                size="mini"
                >{{ $t("buttons.viewCurves") }}</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            :label="
              $t('tableHeader.todayCloseTotalProfit') +
              $t('tableHeader.total_PNl')
            "
            prop="pnl"
            align="left"
            min-width="110"
          >
            <template slot-scope="{ row }">
              <span class="total_PNl">{{ row.pnl || "--" }} USDT</span>
              <el-button
                @click="lookChart('pnl',row)"
                type="text"
                size="mini"
                >{{ $t("buttons.viewCurves") }}</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>

      <el-tab-pane :label="$t('forms.USD_contract')" name="USD">
        <el-table
          v-loading="listLoading"
          :data="tableData"
          border
          fit
          highlight-current-row
          style="width: 100%; margin-top: 30px"
          size="mini"
          :header-cell-style="{ background: '#F0F8FF' }"
        >
          <el-table-column
            :label="$t('tableHeader.contractName')"
            prop="contract_code"
            align="left"
            min-width="80"
          >
          </el-table-column>

          <el-table-column
            :label="
              $t('tableHeader.todayCloseTotalProfit') +
              $t('tableHeader.net_PNl')
            "
            prop="netpnl"
            align="left"
            min-width="110"
          >
            <template slot-scope="{ row }">
              <div class="usd_main">
                <ul>
                  <li v-for="item,index in row.list" :key="index"><span class="net_PNl">{{ item.netpnl || "--" }} {{item.coinname}}</span></li>
                </ul>
                <el-button
                  @click="lookChart('netpnl',row)"
                  type="text"
                  size="mini"
                  >{{ $t("buttons.viewCurves") }}</el-button
                >
              </div>
            </template>
          </el-table-column>
          <el-table-column
            :label="
              $t('tableHeader.todayCloseTotalProfit') +
              $t('tableHeader.total_PNl')
            "
            prop="pnl"
            align="left"
            min-width="110"
          >
            <template slot-scope="{ row }">
              <div class="usd_main">
                <ul>
                  <li v-for="item,index in row.list" :key="index"><span class="total_PNl">{{ item.pnl || "--" }} {{item.coinname}}</span></li>
                </ul>
                <el-button
                  @click="lookChart('pnl',row)"
                  type="text"
                  size="mini"
                  >{{ $t("buttons.viewCurves") }}</el-button
                >
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <!--查看曲线弹框-->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible_curve"
      width="480px"
      :close-on-click-modal="false"
      @open="open()"
      @close="
        () => {
          dialogVisible_curve = false;
        }
      "
    >
      <div class="chart-wrapper" style="width:440px">
        <line-chart v-if="activeName == 'USDT'" :chart-data="usdtChartData" :width="'440px'" />
        <line-chart3 v-else :chart-data="usdChartData" :width="'440px'" />
      </div>
      <el-select
        size="mini"
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        @change="timeChange"
        v-model="selectTime"
        :placeholder="$t('dialog.pleaseSelect')"
      >
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-dialog>
  </div>
</template>

<script>
//封装的api
import {
  getriskusdtpnl,
  getriskusdpnl,
  getdayusdtpnl,
  getdayusdpnl,
} from "@/api/riskAdminister";

import LineChart from "@/views/home/<USER>/LineChart.vue";
import LineChart3 from "@/views/home/<USER>/LineChart3.vue";

export default {
  name: "contractPNLQuery",
  data() {
    return {
      pickerOptions: {
        disabledDate: (time) => {
          let defalutStartTime = new Date().getTime() - 30 * 24 * 3600 * 1000; // 转化为时间戳
          return (
            time.getTime() >= Date.now() || time.getTime() <= defalutStartTime
          );
        },
      },
      listLoading: false,
      total: 0,
      filterTime: [],
      tableData: null,
      activeName: "USDT",
      dialogVisible_curve: false,
      listQuery: {
        userid: "", //用户id,手机号，邮箱
        top_agent_id: "", //顶级代理id或昵称
        pareid: "", //上级代理
        contract_code: "", //合约代码
      },
      usdtChartData: {
        dataList: [],
        dataTime: [],
        unit: this.$t('tableHeader.net_PNl'),
        legend: undefined,
      },
      usdChartData: {
        dataTime: [], // X轴时间
        legend: [], // 悬浮说明
        /**
         * 例如：
         * legend: ['BTC','ETH']
         * BTCList: [],
         * ETHList: [],
         * 
         *  */ 
      },
      options: [
        {
          value: "7",
          label: this.$t("filters.In_recent_7_days"),
        },
        {
          value: "30",
          label: this.$t("filters.In_recent_30_days"),
        },
      ],
      selectTime: "7",
      chartQueryData: {},
      dialogTitle: '',
      time7Arr: [],
      time30Arr: [],
    };
  },
  components: {
    LineChart,
    LineChart3,
  },

  computed: {
    // 默认时间
    timeDefault() {
      let date = new Date();
      // 通过时间戳计算
      // let defalutStartTime = (
      //   (date.getTime() - 1 * 24 * 3600 * 1000) /
      //   1000
      // ).toDate("yyyy-MM-dd"); // 转化为时间戳
      let defalutEndTime = (date.getTime() / 1000).toDate("yyyy-MM-dd");
      return [defalutEndTime, defalutEndTime];
    },
  },
  created() {},
  mounted() {
    this.filterTime = this.timeDefault;
    this.getList();
    for (let index = 0; index < 30; index++) {
      this.time30Arr.unshift((parseInt(new Date().getTime()/1000)-(86400*(index))).toDate('yyyy-MM-dd'))
    }
    this.time7Arr = this.time30Arr.slice(-7)
  },

  methods: {
    //  渲染table列表
    getList() {
      this.listLoading = true;
      let data = {};
      Object.assign(data, this.listQuery);
      data.star = (this.filterTime && this.filterTime[0]) || "";
      data.end = this.filterTime ? this.filterTime[1] + " 23:59:59" : "";
      if (this.activeName == "USDT") {
        getriskusdtpnl(data).then((res) => {
          this.tableData = res.data.map(v=>{
            v.netpnl = v.netpnl<=0?'≤0':v.netpnl
            v.pnl = v.pnl<=0?'≤0':v.pnl
            return v
          });
          this.listLoading = false;
        });
      } else if (this.activeName == "USD") {
        getriskusdpnl(data).then((res) => {
          let usdList = []
          res.data.forEach(item => {
            item.netpnl = item.netpnl<=0?'≤0':item.netpnl
            item.pnl = item.pnl<=0?'≤0':item.pnl
            let newName = usdList.find((i) => i.contract_code == item.contract_code) 
            if(!newName){
              usdList.push({ contract_code: item.contract_code, list: [item] }) 
            }else{
              newName.list.push(item)
            }
          });
          this.tableData = usdList
          this.listLoading = false;
        });
      }
    },
    // 搜索事件
    handleFilter() {
      this.getList();
    },
    // tab切换
    handleClick() {
      this.getList();
    },
    // 查看曲线 type pnl|netpnl 判断点击的是哪一列
    lookChart(type, row) {
      this.dialogTitle = row.contract_code+' '+(type == 'netpnl'?this.$t('tableHeader.net_PNl'):this.$t('tableHeader.total_PNl'))
      let end = new Date().dateHandle('yyyy-MM-dd')+ " 23:59:59"
      let star = (parseInt(new Date().getTime()/1000)-(86400*(this.selectTime-1))).toDate('yyyy-MM-dd')
      this.chartQueryData = {
        contract_code: row.contract_code,
        star,
        end,
        type,
      }
      this.getChart()
      this.dialogVisible_curve = true;
    },
    getChart(){
      this.usdtChartData.dataList = []
      this.usdtChartData.dataTime = []
      this.usdChartData = {
        dataTime: [], // X轴时间
        legend: [], // 悬浮说明
      }
      // 判断当前tab
      if(this.activeName == "USDT"){
        getdayusdtpnl({
          top_agent_id: this.listQuery.top_agent_id,
          pareid: this.listQuery.pareid,
          userid: this.listQuery.userid,
          contract_code: this.chartQueryData.contract_code,
          star: this.chartQueryData.star,
          end: this.chartQueryData.end,
        }).then((res) => {
          let list = res.data
          list.forEach(element => {
            element.netpnl = element.netpnl<0?0:element.netpnl
            element.pnl = element.pnl<0?0:element.pnl
            element.time = new Date(element.daytime).dateHandle('yyyy-MM-dd')
            // this.usdtChartData.dataList.push(element[this.chartQueryData.type])
            // this.usdtChartData.dataTime.push(element.time)
          });
          let dataTime = this.selectTime == '7'?this.time7Arr:this.time30Arr
          dataTime.forEach(i => {
            let item = list.find(o=>o.time == i)
            this.usdtChartData.dataList.push(item?item[this.chartQueryData.type]:0)
          });
          this.usdtChartData.dataTime = dataTime
          this.usdtChartData.unit = this.chartQueryData.type == 'netpnl'?this.$t('tableHeader.net_PNl'):this.$t('tableHeader.total_PNl')
        });
      }else{
        getdayusdpnl({
          top_agent_id: this.listQuery.top_agent_id,
          pareid: this.listQuery.pareid,
          userid: this.listQuery.userid,
          contract_code: this.chartQueryData.contract_code,
          star: this.chartQueryData.star,
          end: this.chartQueryData.end,
        }).then((res) => {
          let timeArr = []
          let usdChartList = []
          let legend = []
          res.data.forEach(item => {
            item.time = new Date(item.daytime).dateHandle('yyyy-MM-dd')
            timeArr.push(item.time)
            item.netpnl = item.netpnl<=0?0:item.netpnl
            item.pnl = item.pnl<=0?0:item.pnl
            let newName = usdChartList.find((i) => i.coinname == item.coinname) 
            if(!newName){
              usdChartList.push({ coinname: item.coinname, list: [item] }) 
              legend.push(item.coinname)
            }else{
              newName.list.push(item)
            }
          });
          // let dataTime = timeArr.filter((item, index) => timeArr.indexOf(item) === index)
          let dataTime = this.selectTime == '7'?this.time7Arr:this.time30Arr
          usdChartList.forEach(v => {
            this.usdChartData[v.coinname+'List'] = []
            dataTime.forEach(i => {
              let item = v.list.find(o=>o.time == i)
              this.usdChartData[v.coinname+'List'].push(item?item[this.chartQueryData.type]:0)
            });
          });
          this.usdChartData.dataTime = dataTime
          this.usdChartData.legend = legend
        });
      }
    },
    open() {},
    timeChange(val) {
      let end = new Date().dateHandle('yyyy-MM-dd')
      let star = (parseInt(new Date().getTime()/1000)-(86400*(this.selectTime-1))).toDate('yyyy-MM-dd')
      Object.assign(this.chartQueryData,{
        star,
        end,
      })
      this.getChart()
    },
    filterTimeTransform(val) {
      this.listQuery.star = (val && val[0]) || "";
      this.listQuery.end = val ? val[1] : "";
    },
  },
};
</script>
<style lang="scss" scoped>
.PNLQueryList {
  .net_PNl,
  .total_PNl {
    margin-right: 10px;
  }
  .usd_main{
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    margin: 0;
    ul{
      list-style:none;
      margin: 0;
      padding: 0;
      li{
        text-align: left;
      }
    }
    .el-button--mini, .el-button--mini.is-round{
      padding: 0;
    }
  }
}
</style>