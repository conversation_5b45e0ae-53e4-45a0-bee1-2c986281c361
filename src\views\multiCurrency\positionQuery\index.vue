<template>
  <div class="position-query">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px;margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        :placeholder="$t('filters.topIDNick')"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        :placeholder="$t('filters.agent')"
        style="width: 150px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.contract_code"
        :placeholder="$t('tableHeader.contract')"
        clearable
        style="width: 120px; margin-right: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in contractOptions"
          :key="item.traderpairs"
          :label="item.traderpairs"
          :value="item.traderpairs"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.marginCurrency"
        :placeholder="$t('filters.Margin_currency')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in marginCurrencyOptions"
          :key="item.currencyid"
          :label="item.currencyname"
          :value="item.currencyname"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.account_type"
        :placeholder="$t('filters.positionType')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in accountTypeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.side"
        :placeholder="$t('filters.direction')"
        clearable
        style="width: 120px; margin: 10px 20px 0 0px"
        class="filter-item"
      >
        <el-option
          v-for="item in sizeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <!-- <span style="margin: 0 10px; font-size: 12px">{{
        $t("tableHeader.clinchDealTime")
      }}</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px; margin-right: 10px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change="filterTimeTransform"
      >
      </el-date-picker> -->
      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-top: 10px"
        @click="handleFilter"
      >
        {{ $t("buttons.search") }}
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="levelList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')"             prop="userid" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')"        prop="user_name" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('filters.topID')"               prop="top_agent_id" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topNick')"             prop="petname" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorID')"      prop="pareid" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" prop="parename" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.contract')"        prop="contractcode" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('filters.Margin_currency')"     prop="currency_name" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('filters.positionType')"        prop="accounttype" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{row.accounttype==1?$t('tableHeader.all_warehouse'):$t('tableHeader.by_warehouse')}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.direction')"           prop="side" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{row.side=='S'?$t('tableHeader.sell_empty'):$t('tableHeader.open_to_buy_more')}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.leverage')"        prop="lever" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>&times;{{ row.lever }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.number')"          prop="volume" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.volume }}</span>&nbsp;<span>{{row.currency_name}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('others.frozenMargin')"         prop="init_margin" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ Number(row.initmargin + row.commission).toFixed(6) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.averageOpen')"     prop="price" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('tableHeader.poundage')"        prop="commission" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('tableHeader.flatPrice')"       prop="forceprice" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('tableHeader.float_PNL')"       prop="floatprofit" align="center" min-width="78"> </el-table-column>
    </el-table>

    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,20,30,50]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { positionlist, bcusdprocontractset } from "@/api/multiCurrency"
import { getprocoinList } from "@/api/user";
export default {
  name: "positionQuery",
  data() {
    return {
      listLoading: false,
      total: 0,
      levelList: [],
      // filterTime: [],
      listQuery: {
        sname: "",          // 用户id,手机号，邮箱
        sectop: "",         // 顶级代理id或昵称
        sagent: "",         // 代理id或者名字
        account_type: null, // 仓位类型 1：全仓 2：逐仓
        contract_code: "",  // 合约代码
        side: null,         // 方向 B买S卖
        marginCurrency: "", // 保证金币种
        pageNo: 1,
        pagesize: 10,
        // star: "", // 开始
        // end: "", // 结束
      },
      contractOptions: [],        // 合约
      marginCurrencyOptions: [],  // 保证金币种
      accountTypeOptions: [
        { key: 1, name: this.$t('tableHeader.all_warehouse') },
        { key: 2, name: this.$t('tableHeader.by_warehouse') },
      ], // 仓位类型
      sizeOptions: [
        { key: "B", name: this.$t('tableHeader.open_to_buy_more') },
        { key: "S", name: this.$t('tableHeader.sell_empty') },
      ], // 方向
    };
  },
  components: {},

  computed: {
    // 默认时间
    // timeDefault() {
    //   let date = new Date();
    //   // 通过时间戳计算
    //   let defalutStartTime = ((date.getTime() - 1 * 24 * 3600 * 1000) / 1000 ).toDate("yyyy-MM-dd"); // 转化为时间戳
    //   let defalutEndTime = ((date.getTime() / 1000)).toDate("yyyy-MM-dd");
    //   return [defalutStartTime, defalutEndTime];
    // },
  },

  mounted() {
    // 获取保证金币种
    getprocoinList({}).then((res) => {
      for(var i=0; i<res.data.length; i++) {
        if(res.data[i].currencyname == "USDT") {
          res.data.splice(i,1)
        }
      }
      this.marginCurrencyOptions = res.data.filter(v => v.status)
    })
    // 获取USD合约
    bcusdprocontractset({}).then((res) => {
      this.contractOptions = res.data.filter(v=>v.isshow == 1)
    })
    // this.filterTime = this.timeDefault;
    this.getList()
  },

  methods: {
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList()
    },
    // 时间
    // filterTimeTransform(val) {
    //   this.listQuery.star = (val && val[0]) || "";
    //   this.listQuery.end = val ? val[1] + " 23:59:59" : "";
    // },
    getList() {
      this.listLoading = true
      let data = {};
      // this.listQuery.star = (this.filterTime && this.filterTime[0]) || "";
      // this.listQuery.end = this.filterTime
      //   ? this.filterTime[1] + " 23:59:59"
      //   : "";
      Object.assign(data, {
        sname: this.listQuery.sname,          // string 账号
        sectop: this.listQuery.sectop,        // string 顶级id或昵称
        sagent: this.listQuery.sagent,        // string 代理id和昵称
        account_type: Number(this.listQuery.account_type) || null,  // int 仓位类型
        side: this.listQuery.side || null,    // 方向
        contract_code: this.listQuery.contract_code,                // string
        tradeid: this.listQuery.tradeid,      // string
        currency_name: this.listQuery.marginCurrency,               //  保证金币种 name
        // star: this.listQuery.start, // string 开始时间
        // end: this.listQuery.end,  // string 结束时间
        pageNo: this.listQuery.pageNo,        // int 页数
        pagesize: this.listQuery.pagesize,    // int 分页数量
      })
      positionlist(data).then((res) => {
        // console.log(res)
        this.levelList = res.data.list
        this.total = res.data.total
        this.listLoading = false
      })
    },
  },
};
</script>

<style>
</style>