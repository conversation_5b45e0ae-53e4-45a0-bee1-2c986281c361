import request from '@/utils/request'

// 课程列表
export function getCourseList(data) {
    return request({
        url: '/managers/v1/academy/courses',
        method: 'post',
        data: { data }
    })
}

// 添加课程
export function addCourse(data) {
    return request({
        url: '/managers/v1/academy/addcourse',
        method: 'post',
        data: { data }
    })
}

// 编辑课程
export function updateCourse(data) {
    return request({
        url: '/managers/v1/academy/updatecourse',
        method: 'post', // should have been a 'HTTP PUT' but Go backend defined everything as 'HTTP POST'
        data: { data }
    })
}

// 查询购课记录
export function getCoursePurchaseHistory(data) {
    return request({
        url: '/managers/v1/academy/coursespurchasehistory',
        method: 'post',
        data: { data }
    })
}

// 查询返佣记录
export function getRewards(data) {
    return request({
        url: '/managers/v1/academy/rewards',
        method: 'post',
        data: { data }
    })
}

// 查询用户等级（VIP）列表
export function getCourseUserTierList(data) {
    return request({
        url: '/managers/v1/academy/courseusertierlist',
        method: 'post',
        data: { data }
    })
}

// 查询用户等级（VIP）图表
export function getCourseUserTierGraph(data) {
    return request({
        url: '/managers/v1/academy/courseusertiergraph',
        method: 'post',
        data: { data }
    })
}
