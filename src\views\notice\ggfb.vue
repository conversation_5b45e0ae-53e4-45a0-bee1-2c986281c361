<!--公告发布-->
<template>
	<div class="withdraw">
		<div class="w_c">
			<!-- <div class="article_title">
				<span>{{ $t('others.articleTitle') }}：</span>
				<el-input style="width:280px;" v-model="articleTitle"
					:placeholder="$t('filters.pleaseEnterTheContent')"></el-input>
				<span class="zhuyi_span">{{ $t('others.min_max') }}</span>
				<span style="padding-left: 20px;">{{ $t('others.languageType') }}：</span>
				<el-select v-model="articleLang" :placeholder="$t('filters.pleaseLanguageType')">
					<el-option v-for="item in langOptions" :key="item.value" :label="$t(`filters.${item.name}`)"
						:value="item.value">
					</el-option>
				</el-select>
				<el-checkbox style="margin-left: 20px" v-model="is_important" :true-label="1"
					:false-label="0">{{ $t('others.openDialog') }}</el-checkbox>
			</div> -->

			<div class="article_content">
				<el-form label-position="top" :model="{ articleTitle, detailContent, is_important }">
					<div>
						<label>{{ $t('others.noticeTabTitle') }}: </label>
						<span>{{ tab ? tab.content : '-' }}</span>
					</div>
					<div>
						<label>{{ $t('others.languageType') }}: </label>
						<span>{{ languageLabel }}</span>
					</div>

					<el-form-item :label="$t('others.articleTitle')">
						<el-input v-model="articleTitle" :placeholder="$t('filters.pleaseEnterTheContent')" clearable />
						<span class="note-text">{{ $t('others.min_max') }}</span>
					</el-form-item>

					<!-- 图片上传组件辅助-->
					<el-form-item :label="$t('others.uploadPictures')" v-show="false">
						<el-upload id="avatar-uploader" :action="actionUrl" name="file" ref="uploaden"
							:show-file-list="false" :on-success="uploadSuccess" :on-error="uploadError"
							:before-upload="beforeUpload" enctype="multipart/form-data" :data="uploadData">
							<el-button size="small" type="primary">{{ $t('buttons.uploadImage') }}</el-button>
							<div class="el-upload__tip">{{ $t('others.pictureSize') }}</div>
						</el-upload>
					</el-form-item>

					<!--富文本编辑器组件-->
					<el-form-item :label="$t('others.straightMatter')">
						<quill-editor v-model="detailContent" ref="myQuillEditor" :options="editorOption" />
					</el-form-item>

					<el-form-item>
						<el-checkbox v-model="is_important" :true-label="1" :false-label="0">
							{{ $t('others.openDialog') }}
						</el-checkbox>
					</el-form-item>
				</el-form>
			</div>

			<!-- Buttons -->
			<div class="handle_div">
				<el-button @click="$emit('cancel')">{{ $t('buttons.cancel') }}</el-button>
				<el-button type="primary" @click="entry()">{{ $t('buttons.release') }}</el-button>
			</div>
		</div>
	</div>
</template>

<script>
import { quillEditor } from "vue-quill-editor"; //调用编辑器
import "quill/dist/quill.core.css";
import "quill/dist/quill.snow.css";
import "quill/dist/quill.bubble.css";
// 工具栏配置
const toolbarOptions = [
	["bold", "italic", "underline", "strike"], // toggled buttons
	["blockquote", "code-block"],

	[{ header: 1 }, { header: 2 }], // custom button values
	[{ list: "ordered" }, { list: "bullet" }],
	[{ script: "sub" }, { script: "super" }], // superscript/subscript
	[{ indent: "-1" }, { indent: "+1" }], // outdent/indent
	[{ direction: "rtl" }], // text direction

	[{ size: ["small", false, "large", "huge"] }], // custom dropdown
	[{ header: [1, 2, 3, 4, 5, 6, false] }],

	[{ color: [] }, { background: [] }], // dropdown with defaults from theme
	[{ font: [] }],
	[{ align: [] }],
	["link", "image", "video"],
	["clean"] // remove formatting button
];
import { getAddupNotice } from "@/api/notice";
export default {
	props: {
		tab: {
			type: Object,
			default: () => null
		}
	},
	data() {
		return {
			quillUpdateImg: false,
			detailContent: "", // 富文本内容
			editorOption: {
				placeholder: "",
				theme: "snow", // or 'bubble'
				modules: {
					toolbar: {
						container: toolbarOptions, // 工具栏
						handlers: {
							image: function (value) {
								if (value) {
									document.querySelector("#avatar-uploader input").click();
								} else {
									this.quill.format("image", false);
								}
							}
						}
					}
				}
			}, // 富文本编辑器配置

			datatotal: 0, //总数据条数
			currentPageNum: 10, //每页默认10条
			currentPage: 1, //当前页

			detailContent: "", // 富文本内容
			articleTitle: "", //文章标题
			actionUrl: '',
			uploadData: {},
			disabledFB: false,
			is_important: 0, // 是否开启弹框显示
			langOptions: [
				{ name: 'ChineseSimplifiedNotice', value: 0, },
				{ name: 'EnglishNotice', value: 1, },
				{ name: 'ChineseTraditionalNotice', value: 2, },
				{ name: 'KoreanNotice', value: 3, },
				{ name: 'VietnameseNotice', value: 4, },
				{ name: 'IndonesianNotice', value: 5, },
				{ name: 'RussianNotice', value: 6, },
				{ name: 'GermanNotice', value: 7, },
				{ name: 'JapaneseNotice', value: 8, },
			], // 语言tab
		};
	},
	mounted() {
		// this.$register(this);
		// this.msg = [GETXGZJGG];
		this.actionUrl =
			(process.env.VUE_APP_API == '/' ? window.location.protocol + '//' + window.location.host : process.env.VUE_APP_API) + "/managers/v1/banner/bcprobannerupload";
	},

	components: {
		quillEditor
	},
	computed: {
		languageLabel() {
			if (!this.tab) return '-';
			const lang = this.langOptions.find(item => item.value === this.tab.lang_type);
			return lang ? this.$t('filters.' + lang.name) : '-';
		}
	},
	methods: {
		cancel() { },
		entry() {
			if (!this.articleTitle) {
				this.$notify.warning({ title: this.$t('dialog.Prompt'), message: this.$t('dialog.Please_fill_in_the_title') })
				return false;
			}
			// Note: removed because lang_type 0 is a valid value, automatically binded when tab info is sent to this page
			// if (!this.tab.lang_type) {
			// 	this.$notify.warning({ title: this.$t('dialog.Prompt'), message: this.$t('dialog.Select_a_language_for_the_bulletin') })
			// 	return false;
			// }
			// let res = /^[\u4e00-\u9fff]{4,28}$/   !res.test(this.articleTitle)
			if (this.articleTitle.length > 100 || this.articleTitle.length < 4) {
				this.$notify.warning({ title: this.$t('dialog.Prompt'), message: this.$t('dialog.Please_enter_the_article_title') })
				return false;
			}
			if (!this.detailContent) {
				this.$notify.warning({ title: this.$t('dialog.Prompt'), message: this.$t('dialog.Please_fill_in_the_text') })
				return false;
			}
			this.$confirm(this.$t('dialog.Whether_to_add_the_bulletin'), this.$t('dialog.Prompt'), {
				confirmButtonText: this.$t('buttons.determine'),
				cancelButtonText: this.$t('buttons.cancel'),
				type: "warning"
			})
				.then(() => {
					this.getData();
				})
				.catch(() => {
					this.$message.error(this.$t('dialog.Publication_cancelled'))
				});
		},
		getData() {
			var data = {
				type: 1,
				title: this.articleTitle,
				abstract: "",
				content: this.detailContent,
				content_en: "",
				abstract_en: "",
				title_en: "",
				id: 0,
				lang_type: Number(this.tab.lang_type),
				is_important: this.is_important,
				notice_tab_id: this.tab.id,
			}
			// console.log(data)
			getAddupNotice(data).then(data => {
				// console.log(data)
				if (data.ret == 0) {
					this.$notify({
						title: this.$t('dialog.Successful'),
						message: this.$t('dialog.Release_success'),
						type: "success",
						duration: 2000,
					});
					// feature/daniel-noticetab-0512
					// note: wrapping ggfb as a dialog, had to remove this
					// setTimeout(() => {
					// 	this.$router.push("/notice/gglb");
					// }, 500)
					this.$emit('success');
				} else {
					this.$notify({
						title: this.$t('dialog.Failure'),
						message: this.$t('dialog.Post_failure'),
						type: "warning",
						duration: 2000,
					});
				}
			})
			// this.$api.getAddupNotice({
			//   tag: {
			//     dataType: GETXGZJGG,
			//     type: 1,
			//     title: this.articleTitle,
			//     abstract: "",
			//     content: this.detailContent,
			//     content_en: "",
			//     abstract_en: "",
			//     title_en: "",
			//     id: 0
			//   }
			// });
		},

		// message(tag, data) {
		//   switch (tag.dataType) {
		//     case GETXGZJGG:
		//       if (data.ret == 0) {
		//         this.$msg({
		//           title: "成功",
		//           message: "发布成功",
		//           type: "success"
		//         });
		//         this.$router.push("/notice/gglb");
		//       }
		//       break;
		//   }
		// },

		// 上传图片前
		beforeUpload(res, file) {
			let time = parseInt(new Date().getTime() / 1000) + "";
			let sin = md5(
				// md5("#$"+process.env.VUE_APP_APIKEY) + time
				md5(process.env.VUE_APP_APIKEY) + time
			);
			// console.log(sin)
			this.uploadData["sign"] = sin;
			this.uploadData["ts"] = time;
			// if (this.$refs.uploadzh.uploadFiles.length > 0) {
			// if (this.beforeAvatarUpload(this.$refs.uploadzh.uploadFiles[0])) {
			//     if (this.$refs.uploaden.uploadFiles.length > 0) {
			//       if (
			//         this.beforeAvatarUpload(this.$refs.uploaden.uploadFiles[0])
			//       ) {
			//         this.uploading = true;
			//         this.$refs.uploadzh.submit();
			//       } else {
			//         this.$refs.uploaden.clearFiles();
			//       }
			//     } else {
			//       this.uploading = true;
			//       this.$refs.uploadzh.submit();
			//     }
			//   } else {
			//     this.$refs.uploadzh.clearFiles();
			//   }
			// } else if (this.$refs.uploaden.uploadFiles.length > 0) {
			//   if (this.beforeAvatarUpload(this.$refs.uploaden.uploadFiles[0])) {
			//     this.uploading = true;
			//     this.$refs.uploaden.submit();
			//   }
			// } else {
			//   this.$message.error('请选择需要上传的图片')
			// }
			console.log("上传图片前");
			// 显示loading动画
			this.quillUpdateImg = true;
		},
		// 上传图片成功
		uploadSuccess(res, file) {
			console.log("上传图片成功");
			var self = this;
			// res为图片服务器返回的数据
			// 获取富文本组件实例
			let quill = self.$refs.myQuillEditor.quill;
			// console.log(quill);
			// console.log(res)
			// 如果上传成功
			if (res.ret === 0) {
				// 获取光标所在位置
				let length = quill.getSelection().index;
				// console.log(length)
				// 插入图片  res.info为服务器返回的图片地址
				// quill.insertEmbed(length, "image", self.$Api.imgUrlBase + res.result); self.actionUrl+'/'+res.data
				// quill.insertEmbed(length, "image", self.actionUrl+res.data);
				quill.insertEmbed(length, "image", res.data);
				console.log(res.data, quill)

				// 调整光标到最后
				quill.setSelection(length + 1);
			} else {
				this.$notify({
					title: this.$t('dialog.Error'),
					message: this.$t('dialog.Image_insertion_failed'),
					type: "warning",
					duration: 2000,
				});
			}
			// loading动画消失
			this.quillUpdateImg = false;
		},
		// 上传图片失败
		uploadError(res, file) {
			console.log("上传图片失败");
			console.log(res);
			// loading动画消失
			this.quillUpdateImg = false;
			this.$notify({
				title: this.$t('dialog.Error'),
				message: this.$t('dialog.Image_insertion_failed'),
				type: "warning",
				duration: 2000,
			});
		}
	},
	watch: {
		tab: {
			immediate: true,
			handler(val) {
				if (val && val.id) {
					console.log('[ggfb] Received tab:', JSON.stringify(val));
					// do anything needed once tab is ready
				}
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.buttons {
	margin-left: 30px;
}

.withdraw {
	width: 100%;
	display: flex;
	flex-direction: column;
	box-sizing: border-box;

	.w_c {
		margin-top: 20px;
		width: 95%;
		flex-grow: 1;
		box-sizing: border-box;
		display: flex;
		align-self: center;
		align-items: flex-start;
		flex-direction: column;
		justify-content: flex-start;

		// .article_title {
		// 	display: flex;
		// 	justify-content: flex-start;
		// 	align-items: center;
		// 	white-space: nowrap;
		// 	width: 100%;

		// 	.zhuyi_span {
		// 		font-size: 14px;
		// 		padding-left: 10px;
		// 	}
		// }

		.article_content {
			display: flex;
			justify-content: flex-start;
			flex-direction: column;
			align-items: flex-start;
			width: 100%;

			p {
				margin: 10px 0 5px;
			}

			::v-deep .quill-editor .ql-container {
				min-height: 350px !important;

				::v-deep .ql-editor {
					min-height: 350px !important;
					white-space: pre-wrap !important;
					word-wrap: break-word !important;
					overflow-wrap: break-word !important;
					overflow-x: hidden !important;
				}
			}
		}

		.handle_div {
			width: 100%;
			padding-top: 10px;
			text-align: right;

			.el-button {
				margin: 0;
			}
		}
	}

	.w_page {
		flex-shrink: 0;
		display: flex;
		align-items: center;
		justify-content: flex-end;
		padding-top: 10px;
		padding-right: 30px;
	}
}

.dialogc {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.dialogstip {
	color: red;
	padding: 20px 10px;
}
</style>
