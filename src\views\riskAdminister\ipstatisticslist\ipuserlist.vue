 <template>
  <div class="rebatelistdetail_wrap">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.topid"
        size="mini"
        :placeholder="$t('filters.topID')"
        style="width: 130px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        :placeholder="$t('filters.agent')"
        style="width: 160px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-left: 20px; font-size: 12px">{{$t('menus.num_transactions_IP')}}≥</span>
       <el-input
        v-model.number="listQuery.trade_times"
        size="mini"
        :placeholder="$t('filters.pleaseInput')"
        style="width: 150px; margin-left: 5px"
        class="filter-item"
        clearable
        oninput="value=value.replace(/[^\d]/g,'')"
        @keyup.enter.native="handleFilter"
      />

      <el-button
        class="filter-item"
        style="margin-left: 10px"
        size="mini"
        type="primary"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="rebateDetailList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="user_id" align="center" min-width="78">
        <template slot-scope="{row}">
          <span>{{ row.user_id || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.userType')" min-width="105px" align="center">
        <template slot-scope="{ row }">
          <span>{{row.user_type && userTypeOptions[row.user_type] || '--'}}</span>
        </template> 
      </el-table-column>
      <el-table-column :label="$t('filters.topID')" prop="top_agent_id" align="center" min-width="78">
       <template slot-scope="{row}">
          <span>{{row.top_agent_id || '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('others.topAgent')" prop="parent_agent_id" align="center" min-width="78">
       <template slot-scope="{row}">
          <span>{{row.parent_agent_id || '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.regWay')" prop="register_method" align="center" min-width="95"/>
      <el-table-column :label="$t('filters.regTime')" prop="created_time" align="center" min-width="75" />
      <el-table-column :label="$t('tableHeader.KYCstate')" prop="verify" align="center" min-width="90px">
        <template slot-scope="{row}">
          <span>{{categoryStatus[row.verify]}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.num_login_IP')" prop="login_times_this" align="center" min-width="95px">
        <template slot-scope="{row}">
          <span class="canclick" @click="viewClick(row, 'two')">{{ row.login_times_this }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.num_login_total')" prop="login_times_total" align="center" min-width="90px">
        <template slot-scope="{row}">
          <span class="canclick" @click="viewClick(row, 'one')">{{ row.login_times_total }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.number_logged_devices')" prop="device_amount" align="center" min-width="90px" >
        <template slot-scope="{row}">
          <span class="canclick" @click="viewClick(row, 'one')">{{ row.device_amount }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.lastLoginTime')" prop="last_login_time" align="center" min-width="75"/>
      <el-table-column :label="$t('menus.num_transactions_IP')" prop="trade_times_this" align="center" min-width="90px"/>
      <el-table-column :label="$t('tableHeader.num_transactions_total')" prop="trade_times_total" align="center" min-width="90px"/>
      <el-table-column :label="$t('tableHeader.lastTransactionTime')" prop="last_trade_time" align="center" min-width="75">
        <template slot-scope="{row}">
          <span>{{ row.last_trade_time || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.operation')" align="center" min-width="80px">
        <template slot-scope="{ row }">
          <el-button type="primary" size="mini" @click="detailsClick(row)">{{$t('buttons.toView')}}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagina-tion
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="rebatelistDetail"
    />

    <el-dialog v-dialogDrag :title="$t('dialog.detail')" :visible.sync="viewDialogVisible" width="80%">
      <el-table
        v-loading="viewlistLoading"
        :data="viewList"
        border
        fit
        highlight-current-row
      size="mini"
        style="width: 100%;"
        :header-cell-style="{ background: '#F0F8FF' }"
      >
        <el-table-column :label="$t('tableHeader.IP')" prop="ip_address" align="center" min-width="120">
          <template slot-scope="{row}">
            <span>{{row.ip_address || '--'}}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('tableHeader.equipment')" prop="device" align="center" min-width="90px"/>
        <el-table-column :label="$t('tableHeader.equipmentID')" prop="device_id" align="center" min-width="90px"/>
        <el-table-column :label="$t('tableHeader.operationSystem')" prop="os_type" align="center" min-width="90px">
          <template slot-scope="{row}">
            <span>{{os_typeObj[row.os_type]}}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('dialog.language')" prop="lang_type" align="center" min-width="90px">
          <template slot-scope="{row}">
            <span>{{ langObj[row.lang_type] }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('dialog.app_version')" prop="version" align="center" min-width="90px">
          <template slot-scope="{row}">
            <span>{{row.version || '--'}}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="网络运营商" prop="contractcode" align="center" min-width="90px"/> -->
        <el-table-column :label="$t('others.operationTime')" prop="created_time" align="center" min-width="90px"/>
      </el-table>
      <pagina-tion
         v-show="viewtotal > 0"
        :total="viewtotal"
        :page.sync="viewQuery.pageNo"
        :limit.sync="viewQuery.pagesize"
        @pagination="viewClicks"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="viewDialogVisible = false">{{$t('buttons.close')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { ipuserlist, ipdetaillist } from "@/api/riskAdminister";

export default {
  name: "ipuserlist",
  data() {
    return {
      listLoading: false,

      rebateDetailList: null, //接受详情数据

      filterTime: [],
      listQuery: {
        ip_address: "",
        sname: "",
        topid: "",
        sagent: "",
        trade_times: undefined,
        pageNo: 1,
        pagesize: 10,
      },
      id: "",
      total: 0,
      //控制查看对话框
      viewDialogVisible: false,
      viewList: [],
      viewtotal: 0,
      viewlistLoading: false,
      viewQuery: {
        user_id: "", //用户id
        ip_address: "",
        pageNo: 1,
        pagesize: 10,
      },
      orderTypeObj: {
        0: this.$t('filters.Market_orders'),
        1: this.$t('tableHeader.order'), 
        2: this.$t('tableHeader.check_single'), 
        4: this.$t('tableHeader.stop_loss_orders'), 
        5: this.$t('filters.Strong_flat_sheet')
      },
      status: {
        1: this.$t('filters.Stay_out'),
        2: this.$t('filters.Issued'),
        3: this.$t('filters.Has_been_cancelled'),
      },
      categoryStatus:{
        0: this.$t('filters.Unauthorized'),
        1: this.$t('filters.Authentication_of_identity_information'),
        2: this.$t('filters.Failed_to_authenticate_the_identity_information_Procedure'),
        3: this.$t('filters.The_identity_information_is_authenticated_Procedure'),
        4: this.$t('filters.Face_information_authentication'),
        5: this.$t('filters.Face_authentication_failed_Procedure'),
        6: this.$t('filters.The_face_information_is_authenticated'),
        7: this.$t('buttons.reset'),
      },
      userTypeOptions: {
        1: this.$t('filters.top_agent'),
        2: this.$t('filters.The_agent'),
        3: this.$t('filters.The_average_user'),
        4: this.$t('filters.The_proxy_directly_pushes_users'),
      },
      langObj:{
        0: this.$t('filters.Chinese_simplified'),
        1: this.$t('filters.English'),
        2: this.$t('filters.Chinese_traditional'),
        3: this.$t('filters.Korean'),
        4: this.$t('filters.Japanese'),
      },
      os_typeObj:{
        1: "android",
        2: "iOS",
        3: "WEB",
        4: "H5",
        5: "open_api",
        6: this.$t('others.system_automatically'),
      },
    };
  },

  components: {},

  activated(){
    this.rebatelistDetail();
  },

  computed: {},

  mounted() {
    this.rebatelistDetail();
  },

  methods: {
    rebatelistDetail() {
      this.listLoading = true;
      let data = {};
      Object.assign(data, this.listQuery, {
        ip_address: this.$route.query.ip,
        trade_times: this.listQuery.trade_times?Number(this.listQuery.trade_times):undefined
      });
      ipuserlist(data).then((res) => {
        this.rebateDetailList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.rebatelistDetail();
    },
    detailsClick(row){
      this.$router.push({
        path: "/user/detail",
        query: {
          id: row.user_id,
        },
      });
    },
    //查看对话框
    viewClick(row, type) {
      this.viewQuery.user_id = JSON.stringify(row.user_id);
      this.viewQuery.ip_address = type == 'one'? undefined : this.$route.query.ip;
      this.viewDialogVisible = true;
      this.viewClicks();
    },
    viewClicks() {
      this.viewlistLoading = true;
      let data = {}
      Object.assign(data, this.viewQuery);
      ipdetaillist(data).then((res) => {
        this.viewList = res.data.list;
        this.viewtotal = res.data.total;
        this.viewlistLoading = false;
      });
    },
    filterTimeTransform(val) {
      this.listQuery.star = (val && val[0]) || "";
      this.listQuery.end = (val && val[1] + " 23:59:59") || "";
    },
  },
};
</script>
<style lang="scss" scoped>
// @import url('~@/styles/variables.scss');
.rebatelistdetail_wrap {
  width: 100%;
  .canclick{
    color: #409EFF;
    cursor: pointer;
  }
}
</style>