<template>
  <div class="lebeladminister-container">
    <div class="filter-container">
      <el-button
        v-if="$store.getters.roles.indexOf('bcprocontractcoinadd') > -1"
        type="primary"
        @click="AddClick()"
        >{{$t('buttons.addLabel')}}</el-button
      >
    </div>

    <el-table
      v-loading="listLoading"
      :data="lebelList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column
        :label="$t('tableHeader.labalName')"
        prop="label_name"
        align="center"
        min-width="100px"
      />
      <el-table-column
        :label="$t('tableHeader.contract')"
        prop="contract_code"
        align="center"
        min-width="95px"
      />
      <el-table-column
        :label="$t('tableHeader.maxLeverage')"
        prop="max_lever"
        align="center"
        min-width="90px"
      />
      <el-table-column
        :label="$t('tableHeader.maxOrderQuantity')"
        prop="max_order_volume"
        align="center"
        min-width="120"
      />
      <el-table-column
        :label="$t('tableHeader.minOrderQuantity')"
        prop="min_order_volume"
        align="center"
        min-width="120"
      />
      <el-table-column
        :label="$t('tableHeader.maxPos')"
        prop="max_posi_volume"
        align="center"
        min-width="100px"
      />
      <el-table-column
        :label="$t('tableHeader.minSomeBad')"
        prop="min_slippage"
        min-width="90px"
        align="center"
      />
      <el-table-column
        :label="$t('tableHeader.maxSomeBad')"
        prop="slippage"
        min-width="90px"
        align="center"
      />
      <el-table-column
        :label="$t('tableHeader.poundageReat')"
        prop="fee"
        min-width="90px"
        align="center"
      />
      <el-table-column
        :label="$t('tableHeader.moneyReat')"
        prop="funding"
        align="center"
        min-width="90"
      />
      <el-table-column
        :label="$t('tableHeader.minRisk')"
        prop="risk_rate"
        align="center"
        min-width="120"
      />
      <el-table-column :label="$t('tableHeader.state')" min-width="80px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.status_stype ? $t('tableHeader.open') : $t('buttons.close') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.creationTime')"
        prop="creat_time"
        align="center"
        width="160px"
      />
      <el-table-column
        v-if="$store.getters.roles.indexOf('bcprocontractcoinadd') > -1"
        :label="$t('tableHeader.operation')"
        min-width="220"
        align="center"
      >
        <template slot-scope="{ row }">
          <el-button size="mini" @click="handleEdit(row)">{{$t('buttons.edit')}}</el-button>
          <el-button
            size="mini"
            :type="row.status_stype ? 'info' : 'success'"
            @click="handleModifyStatus(row)"
            >{{ row.status_stype ? $t('buttons.close') : $t('tableHeader.open') }}
          </el-button>
          <el-button type="danger" size="mini" @click="handleDel(row)"
            >{{$t('buttons.delete')}}</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
    <el-dialog
      @close="closeConfigLabelDialog"
      :title="this.handleDialogType == 'add' ? $t('buttons.addLabel') : $t('buttons.edit_label')"
      :visible.sync="addAndConfigDialogVisible"
      width="75%"
      v-dialogDrag
    >
      <el-form
        ref="selectLabelForm"
        :rules="rules"
        :model="selectLabelForm"
        label-width="auto"
        label-position="left"
      >
        <el-form-item :label="$t('forms.selectLabel')" label-position="left" prop="lablname">
          <div style="display: flex; align-items: center">
            <el-select
              v-model="selectLabelForm.lablname"
              :placeholder="$t('forms.pleaseSelectLabel')"
              style="width: 100%"
              :disabled="handleDialogType == 'edit'"
            >
              <el-option
                v-for="item in labelOptions"
                :key="item.labelid"
                :label="item.label_name"
                :value="item.labelid"
              ></el-option>
            </el-select>
            <i
              v-show="handleDialogType == 'add'"
              @click="
                () => {
                  addLableDialog = true;
                }
              "
              class="el-icon-circle-plus-outline"
              style="font-size: 24px; padding-left: 10px"
            ></i>
          </div>
        </el-form-item>
        <!-- <el-divider></el-divider>
        <el-button style="width: 90%; margin-left: 5%">设置其他合约</el-button> -->
      </el-form>
      <el-form
        v-show="selectLabelForm.lablname"
        ref="setLabelInfoForm"
        :rules="rules"
        :model="setLabelInfoForm"
        label-width="auto"
        label-position="left"
      >
        <el-divider></el-divider>
        <el-form-item :label="$t('forms.selectContract')" prop="contract_code">
          <el-select
            v-model="setLabelInfoForm.contract_code"
            :placeholder="$t('forms.pleaseSelectContract')"
            style="width: 100%"
            @change="contractChange"
            :disabled="handleDialogType == 'edit'"
          >
            <el-option
              v-for="item in contractOptions"
              :key="item.traderpairs"
              :label="item.traderpairs"
              :value="item.traderpairs"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('tableHeader.maxLeverage')" prop="max_lever">
          <el-input
            v-model="setLabelInfoForm.max_lever"
            onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))"
            onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,15})?).*$/g, '$1')"
          ></el-input>
        </el-form-item>
        <el-form-item :label="$t('tableHeader.maxOrderQuantity')" prop="max_order_volume">
          <el-input
            v-model="setLabelInfoForm.max_order_volume"
            onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))"
            onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,15})?).*$/g, '$1')"
          >
            <i slot="suffix" style="font-style: normal">{{$t('others.piece')}}</i>
          </el-input>
          <!-- <span style="color:#FF9900;font-size:13px;">最低风险率最大为0.25</span> -->
        </el-form-item>
        <el-form-item :label="$t('tableHeader.minOrderQuantity')" prop="min_order_volume">
          <el-input
            v-model="setLabelInfoForm.min_order_volume"
            onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))"
            onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,15})?).*$/g, '$1')"
          >
            <i slot="suffix" style="font-style: normal">{{$t('others.piece')}}</i>
          </el-input>
        </el-form-item>
        <el-form-item :label="$t('tableHeader.maxPosition')" prop="max_posi_volume">
          <el-input
            v-model="setLabelInfoForm.max_posi_volume"
            onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))"
            onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,15})?).*$/g, '$1')"
          >
            <i slot="suffix" style="font-style: normal">{{$t('others.piece')}}</i>
          </el-input>
        </el-form-item>
        <el-form-item :label="$t('forms.someBad')">
          <el-col :span="11">
            <el-form-item prop="min_slippage">
              <el-input v-model="setLabelInfoForm.min_slippage" oninput="value=value.replace(/[^\d^\.]/g,'')"></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align:center;" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="slippage">
              <el-input v-model="setLabelInfoForm.slippage" oninput="value=value.replace(/[^\d^\.]/g,'')"></el-input>
            </el-form-item>
          </el-col>
          <!-- <span style="color:#FF9900;font-size:13px;">点差最大可输入{{this.forms}}倍最小交易单位</span> -->
        </el-form-item>
        <el-form-item :label="$t('tableHeader.poundageReat')" prop="fee">
          <el-input v-model="setLabelInfoForm.fee" @input="feeInput"></el-input>
          <!--onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" 中间有个d{0,2}是输入限制-->
          <!-- <span style="color:#FF9900;font-size:13px;">手续费率最大可输入0.0005</span> -->
        </el-form-item>
        <el-form-item :label="$t('tableHeader.moneyReat')" prop="funding">
          <el-input v-model="setLabelInfoForm.funding"></el-input>
          <!-- <span style="color:#FF9900;font-size:13px;">资金费率最大可输入0.001</span> -->
        </el-form-item>
        <el-form-item :label="$t('tableHeader.minRisk')" prop="risk_rate">
          <el-input
            v-model="setLabelInfoForm.risk_rate"
            onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
          ></el-input>
          <!-- <span style="color:#FF9900;font-size:13px;">最低风险率最大为0.25</span> -->
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" size="mini" @click="configLabelEntry()"
          >{{$t('buttons.determine')}}</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      @close="addLabelResetFields"
      class
      :title="$t('buttons.addLabel')"
      width="50%"
      :visible.sync="addLableDialog"
      :close-on-click-modal="false"
    >
      <el-form
        :rules="rules"
        label-position="left"
        ref="addLabelForm"
        :model="addLabelForm"
        label-width="auto"
      >
        <el-form-item :label="$t('filters.labal')" prop="addLabelVal">
          <el-input v-model="addLabelForm.addLabelVal"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="addLableDialog = false">{{$t('buttons.cancel')}}</el-button>
        <el-button size="mini" type="primary" @click="entryAddLabel()"
          >{{$t('buttons.determine')}}</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      @close="closexgmm"
      class
      :title="$t('others.googleCode')"
      width="350px"
      :visible.sync="checkvkeyDialog"
      :close-on-click-modal="false"
      @submit.native.prevent
    >
      <el-form
        :rules="rules"
        label-position="left"
        ref="ruleForm"
        :model="ruleForm"
        label-width="auto"
      >
        <el-form-item :label="$t('login.code')" prop="yzmVal">
          <el-input v-model="ruleForm.yzmVal" maxlength="6"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="checkvkeyDialog = false"
          >{{$t('buttons.cancel')}}</el-button
        >
        <el-button size="mini" type="primary" @click="entrySendyzm()"
          >{{$t('buttons.determine')}}</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getlablinfo,
  addlabel,
  addlablinfo,
  updatelablinfo,
  dellablinfo,
} from "@/api/riskAdminister";
import { bcprocontractset, getlabel, commckeckvkey } from "@/api/user";
import permission from '@/store/modules/permission';

export default {
  name: "lebelAdminister",
  data() {
    // 最大杠杆
    var max_lever_V = (rules, value, callback) => {
      if (this.selectedContract.label_lever && Number(value) > Number(this.selectedContract.label_lever)) {
        return callback(new Error(this.$t('dialog.The_maximum_leverage_of_the_contract_is')+this.selectedContract.label_lever));
      } else {
        callback();
      }
    };
    // 标签最大下单量
    var max_order_volume_V = (rules, value, callback) => {
      if (this.selectedContract.label_max_order_volume && Number(value) > Number(this.selectedContract.label_max_order_volume)) {
        return callback(new Error(this.$t('dialog.The_maximum_order_quantity_of_the_contract_is')+this.selectedContract.label_max_order_volume+this.$t('others.piece')));
      } else {
        callback();
      }
    };
    //单笔最小下单量
    var min_order_volume_V = (rules, value, callback) => {
      if (
        this.setLabelInfoForm.max_order_volume &&
        value &&
        Number(value) > Number(this.setLabelInfoForm.max_order_volume)
      ) {
        return callback(new Error(this.$t('dialog.Min_less_than_Max')));
      } else {
        callback();
      }
    };
    //最大持仓量
    var max_posi_volume_V = (rules, value, callback) => {
      if (
        this.setLabelInfoForm.max_order_volume &&
        value &&
        value < Number(this.setLabelInfoForm.max_order_volume)
      ) {
        return callback(new Error(this.$t('dialog.Position_greater_than_order_quantity')));
      } else if (this.selectedContract.label_max_posi_volume && Number(value) > Number(this.selectedContract.label_max_posi_volume)) {
        return callback(new Error(this.$t('dialog.The_maximum_open_position_of_the_contract_is')+this.selectedContract.label_max_posi_volume+this.$t('others.piece')));
      } else  {
        callback();
      }
    };
    //  最小点差
    var min_slippage_V = (rules, value, callback) => {
      let sliMin = this.selectedContract.label_slippage &&  this.selectedContract.label_slippage.split(',')[0] || ''
      let sliMax = this.selectedContract.label_slippage &&  this.selectedContract.label_slippage.split(',')[1] || ''
      let precision = sliMin.split('.')[1] && sliMin.split('.')[1].length || 2
      value = value+''
      let valPre = value && value.split('.')[1] && value.split('.')[1].length || 0
      if ((sliMin && Number(value) && Number(value) < Number(sliMin)) || (sliMax && value && Number(value) > Number(sliMax))) {
        return callback(new Error(this.$t('dialog.The_contract_point_spread_range_is')+sliMin+'-'+sliMax));
      }else if(valPre && precision && valPre>precision){
        return callback(new Error(this.$t('dialog.The_maximum_point_difference_digit_of_the_contract_is')+Math.pow(10,-precision)));
      }else {
        callback();
      }
    };
    //  点差
    var slippage_V = (rules, value, callback) => {
      let sliMin = this.selectedContract.label_slippage &&  this.selectedContract.label_slippage.split(',')[0] || ''
      let sliMax = this.selectedContract.label_slippage &&  this.selectedContract.label_slippage.split(',')[1] || ''
      let precision = sliMin.split('.')[1] && sliMin.split('.')[1].length || 2
      value = value+''
      let valPre = value && value.split('.')[1] && value.split('.')[1].length || 0
      if ((sliMin && Number(value) && Number(value) < Number(sliMin)) || (sliMax && value && Number(value) > Number(sliMax))) {
        return callback(new Error(this.$t('dialog.The_contract_point_spread_range_is')+sliMin+'-'+sliMax));
      }else if(valPre && precision && valPre>precision){
        return callback(new Error(this.$t('dialog.The_maximum_point_difference_digit_of_the_contract_is')+Math.pow(10,-precision)));
      }else if(this.setLabelInfoForm.min_slippage && Number(this.setLabelInfoForm.min_slippage)>Number(this.setLabelInfoForm.slippage)){
        return callback(new Error(this.$t('dialog.Maximum_point_difference_is_greater_than_minimum_point_difference')));
      }  else {
        callback();
      }
    };
    //手续费率
    var fee_V = (rules, value, callback) => {
      let feeMin = this.selectedContract.label_fee &&  this.selectedContract.label_fee.split(',')[0] || ''
      let feeMax = this.selectedContract.label_fee &&  this.selectedContract.label_fee.split(',')[1] || ''
      let precision = feeMin.split('.')[1] && feeMin.split('.')[1].length || 2
      value = value+''
      let valPre = value && value.split('.')[1] && value.split('.')[1].length || 0
      if ((feeMin && Number(value) && Number(value) < Number(feeMin)) || (feeMax && value && Number(value) > Number(feeMax))) {
        return callback(new Error(this.$t('dialog.The_contract_procedures_rate_range_is')+feeMin+'-'+feeMax));
      }else if(valPre && precision && valPre>precision){
        return callback(new Error(this.$t('dialog.The_maximum_digit_of_the_processing_rate_is')+(Number(10).pow(-precision))));
      } else {
        callback();
      }
    };
    //  资金费率
    var funding_V = (rules, value, callback) => {
      if (this.selectedContract.label_funding && Number(value) > Number(this.selectedContract.label_funding)) {
        return callback(new Error(this.$t('dialog.The_maximum_fund_rate_for_the_contract_is')+this.selectedContract.label_funding));
      } else {
        callback();
      }
    };
    // 最低风险率增减
    var risk_rate_V = (rules, value, callback) => {
      if (value && this.selectedContract.label_min_risk_rate && Number(value) > Number(this.selectedContract.label_min_risk_rate)) {
        return callback(new Error(this.$t('dialog.The_minimum_risk_of_the_contract_is')+this.selectedContract.label_min_risk_rate));
      } else {
        callback();
      }
    };
    return {
      listLoading: false,
      total: 0,
      lebelList: null,
      listQuery: {
        pageNo: 1,
        pagesize: 10,
      },
      selectLabelForm: {
        lablname: "",
      },
      addLabelForm: {
        addLabelVal: "",
      },
      setLabelInfoForm: {
        label_id: null, //主标签 id
        contract_code: null, //合约代码
        max_lever: null, // 最大杠杆
        max_order_volume: null, // 最大下单
        min_order_volume: null, // 最小下单
        max_posi_volume: null, //  最大持仓
        fee: null, // 手续费加点
        funding: null, // 资金费用加点
        min_slippage: null,
        slippage: null, //滑点
        risk_rate: null, //风险率
      },
      ruleForm: {
        yzmVal: "", // 谷歌验证码
      },
      addLableDialog: false, // 控制google验证弹框显示
      checkvkeyDialog: false, // 控制google验证弹框显示
      addAndConfigDialogVisible: false, //控制添加标签对话框的显示和隐藏
      handleDialogType: "", //add: 添加 edit:编辑
      labelOptions: [],
      contractOptions: [],
      rules: {
        yzmVal: [
          { required: true, message: this.$t('dialog.The_input_box_cannot_be_empty'), trigger: "blur" },
        ],
        addLabelVal: [
          { required: true, message: this.$t('dialog.The_input_box_cannot_be_empty'), trigger: "blur" },
        ],
        // lablname: [
        //   {  message: "请选择标签", trigger: "change" },
        // ],
        contract_code: [
          { required: true, message: this.$t('forms.pleaseSelectContract'), trigger: "change" },
        ],
        max_lever: [{ validator: max_lever_V, trigger: "blur" }],
        max_order_volume: [{ validator: max_order_volume_V, trigger: "blur" }],
        min_order_volume: [{ validator: min_order_volume_V, trigger: "blur" }],
        max_posi_volume: [{ validator: max_posi_volume_V, trigger: "blur" }],
        min_slippage: [{ validator: min_slippage_V, trigger: "blur" }],
        slippage: [{ validator: slippage_V, trigger: "blur" }],
        fee: [{ validator: fee_V, trigger: "blur" }],
        funding: [{ validator: funding_V, trigger: "blur" }],
        risk_rate: [{ validator: risk_rate_V, trigger: "blur" }],
      },
      forms: null,
      selectedContract: {
        // 添加标签-选中合约的相关配置
        label_fee: "", //标签手续费 区间逗号隔开
        label_funding: 0, //标签最大资金费率
        label_lever: 0, //最大杠杆
        label_max_order_volume: 0, // 标签最大下单量
        label_max_posi_volume: 0, //  标签最大持仓
        label_min_risk_rate: 0, //  最低风险率
        label_slippage: "", // 标签滑点区间逗号隔开
      },
    };
  },

  components: {},

  computed: {},

  mounted() {
    bcprocontractset({}).then((res) => {
      this.contractOptions = res.data.filter((v) => v.isshow == 1);
    });
    getlabel({}).then((res) => {
      this.labelOptions = res.data;
    });
    this.getList();
  },

  methods: {
    feeInput(val){
      this.setLabelInfoForm.fee = this.clearNoNumOfAlert(val)
    },
    //只能输入数字只能有一个小数点，小数点不能在开头，不能在结尾，第一位允许添加负号
    clearNoNumOfAlert(value){
      //得到第一个字符是否为负号
      var t = value.charAt(0);  
        //先把非数字的都替换掉，除了数字和.   
        value = value.replace(/[^\d.]/g,"");   
        //必须保证第一个为数字而不是.   
        value = value.replace(/^\./g,"");   
        //保证只有出现一个.而没有多个.   
        value = value.replace(/\.{2,}/g,".");   
        //保证.只出现一次，而不能出现两次以上   
        value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
        //如果第一位是负号，则允许添加
        if(t == '-'){
          value = '-'+value;
        }
        return value
    },
    contractChange(val) {
      this.selectedContract = this.contractOptions.find(
        (v) => v.traderpairs == val
      );
    },
    //点击添加标签对话框里面确定按钮
    configLabelEntry() {
      if (this.selectLabelForm.lablname) {
        this.$refs["selectLabelForm"].validate((valid) => {
          if (valid) {
            this.$refs["setLabelInfoForm"].validate((valid) => {
              if (valid) {
                this.checkvkeyDialog = true;
              }
            });
          }
        });
      } else {
        this.$refs["selectLabelForm"].validate((valid) => {
          if (valid) {
          }
        });
      }
    },
    //点击添加标签出现弹框
    AddClick() {
      this.handleDialogType = "add";
      this.addAndConfigDialogVisible = true;
      this.$nextTick(() => {
        this.$refs["setLabelInfoForm"].clearValidate();
      });
    },
    // 列表删除按钮
    handleDel(row) {
      this.$confirm(this.$t('dialog.Whether_you_want_to_delete_this_label'), this.$t('dialog.Prompt'), {
        confirmButtonText: this.$t('buttons.determine'),
        cancelButtonText: this.$t('buttons.cancel'),
        type: "warning",
      })
        .then(() => {
          dellablinfo({ id: row.id }).then((res) => {
            this.$notify({
              title: this.$t('tableHeader.operation'),
              message: this.$t('dialog.Delete_the_success'),
              type: "success",
              duration: 2000,
            });
            this.getList();
          });
        })
        .catch(() => {});
    },
    // 列表编辑按钮
    handleEdit(row) {
      this.selectLabelForm.lablname = row.label_id;
      Object.assign(this.setLabelInfoForm, row);
      this.addAndConfigDialogVisible = true;
      this.handleDialogType = "edit";
      this.contractChange(row.contract_code);
    },
    //状态开启和关闭
    handleModifyStatus(row) {
      this.$confirm(`${this.$t('buttons.confirm')}${row.status_stype ? this.$t('buttons.close') : this.$t('tableHeader.open')}？`)
        .then((_) => {
          let data = JSON.parse(JSON.stringify(row));
          // for (const key in data) {
          //   if (data.hasOwnProperty(key)) {
          //     const element = data[key];
          //     if(/^[0-9]+.?[0-9]*$/.test(element)){
          //       data[key] = Number(element)
          //       return false
          //     }
          //   }
          // }
          Object.assign(data, { status_stype: data.status_stype ? 0 : 1 });
          updatelablinfo(data).then((res) => {
            this.$notify({
              title: this.$t('tableHeader.operation'),
              message: this.$t('dialog.Operation_is_successful'),
              type: "success",
              duration: 2000,
            });
            this.getList();
          });
        })
        .catch((_) => {});
    },
    //  渲染table列表
    getList() {
      this.listLoading = true;
      let data = {}
      Object.assign(data, this.listQuery, {
        contracttype: this.activeName
      })
      getlablinfo(data).then((response) => {
        this.lebelList = response.data.list;
        this.total = response.data.total;
        this.listLoading = false;
      });
    },
    closeConfigLabelDialog() {
      this.addLabelForm.addLabelVal = "";
      (this.setLabelInfoForm = {
        label_id: null, //主标签 id
        contract_code: null, //合约代码
        max_lever: null, // 最大杠杆
        max_order_volume: null, // 最大下单
        min_order_volume: null, // 最小下单
        max_posi_volume: null, //  最大持仓
        fee: null, // 手续费加点
        funding: null, // 资金费用加点
        min_slippage: null,
        slippage: null, //滑点
        risk_rate: null,
      }),
        this.$refs["selectLabelForm"].resetFields();
      this.$refs["setLabelInfoForm"].resetFields();
    },
    addLabelResetFields() {
      this.$refs["addLabelForm"].resetFields();
    },
    entryAddLabel() {
      this.$refs["addLabelForm"].validate((valid) => {
        if (valid) {
          addlabel({
            lablname: this.addLabelForm.addLabelVal,
          }).then((res) => {
            this.addLableDialog = false;
            this.addLabelForm.addLabelVal = "";
            this.$notify({
              title: this.$t('dialog.Successful'),
              message: this.$t('dialog.Operation_is_successful'),
              type: "success",
              duration: 2000,
            });
            getlabel({}).then((res) => {
              this.labelOptions = res.data;
            });
          });
        } else {
          return false;
        }
      });
    },
    closexgmm() {
      this.$refs["ruleForm"].resetFields();
    },
    entrySendyzm() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          commckeckvkey({ code: this.ruleForm.yzmVal }).then((res) => {
            let data = this.setLabelInfoForm;
            // 对象内字段value转换成Number
            for (const key in data) {
              if (data.hasOwnProperty(key)) {
                const element = data[key];
                // 排除非转换Number的
                if (['contract_code','creat_time','label_name',].indexOf(key) == -1) {
                  data[key] = Number(element) || 0;
                } else {
                  if (!element) {
                    data[key] = 0;
                  }
                }
              }
            }
            Object.assign(data, { label_id: this.selectLabelForm.lablname });
            if (this.handleDialogType == "add") {
              addlablinfo(data).then((res) => {
                this.ruleForm.yzmVal = "";
                this.closeConfigLabelDialog();
                this.checkvkeyDialog = false;
                this.addAndConfigDialogVisible = false;
                this.$notify({
                  title: this.$t('buttons.add'),
                  message: this.$t('dialog.Operation_is_successful'),
                  type: "success",
                  duration: 2000,
                });
                this.getList();
              });
            } else {
              updatelablinfo(data).then((res) => {
                this.ruleForm.yzmVal = "";
                this.closeConfigLabelDialog();
                this.checkvkeyDialog = false;
                this.addAndConfigDialogVisible = false;
                this.$notify({
                  title: this.$t('buttons.modify'),
                  message: this.$t('dialog.Operation_is_successful'),
                  type: "success",
                  duration: 2000,
                });
                this.getList();
              });
            }
          });
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.filter-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
::v-deep .el-tabs__nav-wrap::after {
  background: none;
}
</style>