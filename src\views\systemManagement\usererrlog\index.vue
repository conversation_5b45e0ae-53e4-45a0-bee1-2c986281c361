<template>
  <div class="open-cantainer">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px;margin-right: 20px;"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.ip"
        size="mini"
        :placeholder="$t('tableHeader.IP')"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <span style="font-size: 12px">{{$t('others.operationTime')}}</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px; margin-right: 20px;"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change='filterTimeTransform'
      >
      </el-date-picker>

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-right: 20px;"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
    </div>


    <el-table
      v-loading="listLoading"
      :data="openList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="userid" align="center" min-width="78">
        <template slot-scope="{row}">
          <span>{{Number(row.userid)?row.userid:'--'}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center" min-width="110px">
        <template slot-scope="{row}">
          <span>{{row.user_name || '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.operationType')" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{typeObj[row.op_type]}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.error_code')" prop="err_code" align="center" min-width="130"> </el-table-column>
      <el-table-column :label="$t('tableHeader.error_content')" prop="err_msg" align="center" min-width="120"> </el-table-column>
      <!-- <el-table-column label="设备ID" prop="device_id" align="center" min-width="120"> </el-table-column>
      <el-table-column label="操作系统" prop="price" align="center" min-width="90px"> </el-table-column>
      <el-table-column label="语言" prop="price" align="center" min-width="90px"> </el-table-column>
      <el-table-column label="APP版本" prop="price" align="center" min-width="90px"> </el-table-column> -->
      <el-table-column :label="$t('tableHeader.IP_addr')" prop="ip" align="center" min-width="130"> </el-table-column>
      <el-table-column :label="$t('others.operationTime')" prop="created_time" align="center" min-width="75"> </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[300,100,50,10]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
//封装的api
import { usererrlog } from "@/api/systemManagement";
export default {
  name: "usererrlog",
  data() {
    return {
      listLoading: false,
      total: 0,
      openList: null,
      filterTime: [],
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        ip:"",
        pageNo: 1,
        pagesize: 300,
        star: '', //开始
        end: '', //结束
      },
      typeOptions: [
        {key: 0, name: this.$t('login.title')},
        {key: 1, name: this.$t('filters.Registered')},
        {key: 2, name: this.$t('filters.Retrieve_login_password')},
        {key: 3, name: this.$t('filters.Login_Password_Setting')},
        {key: 4, name: this.$t('filters.Fund_password_setting')},
        {key: 5, name: this.$t('filters.User_name_Settings')},
        {key: 6, name: this.$t('filters.Withdrawal_application')},
        {key: 7, name: this.$t('filters.Google_Captcha_Settings')},
        {key: 8, name: this.$t('filters.Market_price_is_open')},
        {key: 9, name: this.$t('filters.Market_positions')},
        {key: 10, name: this.$t('filters.A_key_positions')},
        {key: 11, name: this.$t('filters.Check_full_stop')},
        {key: 12, name: this.$t('filters.Plan_to_entrust')},
        {key: 13, name: this.$t('filters.Cancellation_of_plan')},
        {key: 14, name: this.$t('filters.Fiat_deal')},
        {key: 15, name: this.$t('filters.Binding_payment_method')},
        {key: 16, name: this.$t('login.Obtaining_verification_code')},
        {key: 17, name: this.$t('filters.KYC1_application')},
        {key: 18, name: this.$t('filters.KYC2_application')},
        {key: 19, name: this.$t('filters.Manual_KYC_application')},
      ],
      typeObj: {
        0:this.$t('login.title'),
        1:this.$t('filters.Registered'),
        2:this.$t('filters.Retrieve_login_password'),
        3:this.$t('filters.Login_Password_Setting'),
        4:this.$t('filters.Fund_password_setting'),
        5:this.$t('filters.User_name_Settings'),
        6:this.$t('filters.Withdrawal_application'),
        7:this.$t('filters.Google_Captcha_Settings'),
        8:this.$t('filters.Market_price_is_open'),
        9:this.$t('filters.Market_positions'),
        10:this.$t('filters.A_key_positions'),
        11:this.$t('filters.Check_full_stop'),
        12:this.$t('filters.Plan_to_entrust'),
        13:this.$t('filters.Cancellation_of_plan'),
        14:this.$t('filters.Fiat_deal'),
        15:this.$t('filters.Binding_payment_method'),
        16:this.$t('login.Obtaining_verification_code'),
        17:this.$t('filters.KYC1_application'),
        18:this.$t('filters.KYC2_application'),
        19:this.$t('filters.Manual_KYC_application'),
      },
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.getList();
  },

  methods: {
    getList() {
      this.listLoading = true;
      // let data = {};
      // Object.assign(data, this.listQuery, {
      //   stype: this.listQuery.stype === 0 ? 0 : this.listQuery.stype || undefined,
      // });
      usererrlog(this.listQuery).then((res) => {
        this.openList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    filterTimeTransform(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val? val[1]+' 23:59:59':'';
    },
  },
};
</script>
<style lang="scss" scoped>
</style>