<template>
  <div class="operating-container">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.account"
        :placeholder="$t('filters.name')"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-right: 5px; font-size: 12px">{{$t('others.operationTime')}}</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px; margin-right: 20px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="timestamp"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        :clearable="false"
        @change="filterTimeTransform"
      >
      </el-date-picker>
      <el-select
        size="mini"
        v-model="listQuery.op_type"
        :placeholder="$t('filters.actionType')"
        clearable
        style="width: 150px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="(value, key, idx) in typeOptions"
          :key="idx"
          :label="value"
          :value="key"
        />
      </el-select>
      <el-input
        size="mini"
        v-model="listQuery.ip_address"
        :placeholder="$t('tableHeader.IP')"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="operatingList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column
        :label="$t('others.operationTime')"
        prop="create_time"
        align="center"
        min-width="110px"
      >
        <template slot-scope="{ row }">
          <span>{{ row.create_time.toDate('yyyy-MM-dd HH:mm:ss') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.handlersID')"
        prop="agent_id"
        align="center"
        min-width="90px"
      >
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.actionObjectID')"
        prop="user_id"
        align="center"
        min-width="100px"
      >
        <template slot-scope="{ row }">
          <span>{{ row.user_id || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.actionType')" align="center" min-width="130px">
        <template slot-scope="{ row }">
          <span>{{ typeOptions[row.op_type] }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.executionResult')" align="center" min-width="130px">
        <template slot-scope="{ row }">
          <span>{{ row.op_info || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.IP')" align="center" min-width="130px">
        <template slot-scope="{ row }">
          <span>{{ row.ip_address || "--" }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10, 50, 100, 200]"
      :page.sync="listQuery.page_no"
      :limit.sync="listQuery.page_size"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getagentoplog } from "@/api/systemManagement";

export default {
  name: "getagentoplog",
  data() {
    return {
      listLoading: false,
      operatingList: null,
      total: 0,
      filterTime: [],
      listQuery: {
        account: "",
        op_type: "0",
        ip_address: "",
        start_time: "",
        end_time: "",
        page_no: 1,
        page_size: 10,
      },
      typeOptions: {
        0: this.$t('buttons.all'),
        1: this.$t('buttons.proAgent'),
        2: this.$t('filters.Remove_the_agent'),
        3: this.$t('filters.Re_enable_the_agent'),
        4: this.$t('login.Change_password'),
        5: this.$t('login.title'),
      },
    };
  },

  components: {},

  computed: {
    // 默认时间
    timeDefault() {
      let defalutStartTime =
        new Date(new Date().toLocaleDateString()).getTime() -
        3 * 24 * 3600 * 1000;
      let defalutEndTime = new Date().getTime();
      return [defalutStartTime, defalutEndTime];
    },
  },

  mounted() {
    this.filterTime = this.timeDefault;
    this.listQuery.end_time = parseInt(this.filterTime[1] / 1000);
    this.getList();
  },

  methods: {
    //渲染table数据
    getList() {
      this.listLoading = true;
      let data = {};
      Object.assign(data, this.listQuery);
      data.op_type = parseInt(this.listQuery.op_type);
      data.start_time = parseInt(this.filterTime[0] / 1000);
      data.page_no = parseInt(this.listQuery.page_no) - 1;
      getagentoplog(data).then((response) => {
        this.operatingList = response.data.list;
        this.total = response.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.getList();
    },
    filterTimeTransform(val) {
      this.listQuery.end_time = parseInt((val[1] + (1 * 24 * 3600 * 1000))/1000)-1;
    },
  },
};
</script>
<style lang="scss" scoped>
</style>