import request from '@/utils/request'

//活动列表
export function bcactivitylist(data) {
  return request({
    url: '/managers/v1/activity/bcactivitylist',
    method: 'post',
    data: { data }
  })
}
//设置哈希
export function setactivityhash(data) {
  return request({
    url: '/managers/v1/activity/setactivityhash ',
    method: 'post',
    data: { data }
  })
}
//设置奖池金额
export function setactivitylssued(data) {
  return request({
    url: '/managers/v1/activity/setactivitylssued ',
    method: 'post',
    data: { data }
  })
}
//获取活动排行
export function getuserwardlist(data) {
  return request({
    url: '/managers/v1/activity/getuserwardlist',
    method: 'post',
    data: { data }
  })
}
//插入排行
export function inuserwardlist(data) {
  return request({
    url: '/managers/v1/activity/inuserwardlist',
    method: 'post',
    data: { data }
  })
}
//修改排行
export function upuserward(data) {
  return request({
    url: '/managers/v1/activity/upuserward',
    method: 'post',
    data: { data }
  })
}
//获取用户获奖列表
export function getuseractivitylist(data) {
  return request({
    url: '/managers/v1/activity/getuseractivitylist',
    method: 'post',
    data: { data }
  })
}
//设置活动
export function setactivity(data) {
  return request({
    url: '/managers/v1/activity/setactivity',
    method: 'post',
    data: { data }
  })
}
//获取当前活动设置
export function getnowactivity(data) {
  return request({
    url: '/managers/v1/activity/getnowactivity',
    method: 'post',
    data: { data }
  })
}
//奖励审核
export function useractivitycheck(data) {
  return request({
    url: '/managers/v1/activity/useractivitycheck',
    method: 'post',
    data: { data }
  })
}

//  -------- 赠金活动相关 ---------
// 获取体验金汇总
 export function gifttotal(data) {
  return request({
    url: '/managers/v1/gift/gifttotal',
    method: 'post',
    data: { data }
  })
}
// 新建赠金
 export function addgift(data) {
  return request({
    url: '/managers/v1/gift/addgift',
    method: 'post',
    data: { data }
  })
}
// 获取赠金活动列表
export function giftlist(data) {
  return request({
    url: '/managers/v1/gift/giftlist',
    method: 'post',
    data: { data }
  })
}
// 更新赠金活动
export function updategiftinfo(data) {
  return request({
    url: '/managers/v1/gift/updategiftinfo',
    method: 'post',
    data: { data }
  })
}
// 更新赠金活动语言
export function updategiftlange(data) {
  return request({
    url: '/managers/v1/gift/updategiftlange',
    method: 'post',
    data: { data }
  })
}
// 更新赠金活动状态
export function updategiftstatus(data) {
  return request({
    url: '/managers/v1/gift/updategiftstatus',
    method: 'post',
    data: { data }
  })
}

// 获取用户赠金领取列表
export function getgiftrecord(data) {
  return request({
    url: '/managers/v1/gift/getgiftrecord',
    method: 'post',
    data: { data }
  })
}
// 获取用户赠金使用
export function usegiftrecord(data) {
  return request({
    url: '/managers/v1/gift/usegiftrecord',
    method: 'post',
    data: { data }
  })
}
// 给用户发送赠金
 export function sentgiftrecord(data) {
  return request({
    url: '/managers/v1/gift/sentgiftrecord',
    method: 'post',
    data: { data }
  })
}
// 获取用户赠金列表
 export function getsentgift(data) {
  return request({
    url: '/managers/v1/gift/getsentgift',
    method: 'post',
    data: { data }
  })
}
// 收回用户赠金
export function recoverygift(data) {
  return request({
    url: '/managers/v1/gift/recoverygift',
    method: 'post',
    data: { data }
  })
}
// 获取用户取消赠金列表
 export function getrecoverygift(data) {
  return request({
    url: '/managers/v1/gift/getrecoverygift',
    method: 'post',
    data: { data }
  })
}
// 获取语言列表
export function getlangelist(data) {
  return request({
    url: '/managers/v1/gift/getlangelist',
    method: 'post',
    data: { data }
  })
}

/**
 * 1.获取体验金汇总
 地址：/v1/gift/gifttotal
 传值
 返回：{"ret": 0,"msg": "","data": { []} }
 解释： receive_total 总领取 useractotal 总可用，user_lock 总锁定，user_loss 总亏损，user_excced 总过期，user_recovery 总收回 

2.新建赠金
 地址：/v1/gift/addgift
 传值：time_limit 时间限制，close_count 平仓次数，trader_day有效交易天数，category 1首次充值，2首次交易，3新手交易量达标，4活跃用户，5，充值返利，6邀请好友，partake 参与者0 全部用户 1 非代理用户 2代理，amount_limit 有效金额限制，gift_amount 赠金，friends_recharge 好友充值，exceed_time 过期时间，receive_exceed 领取过期时间，activation_exceed 激活过期时间，trade_exceed 交易过期回收时间，addgiftname 语言集合 {lang_type 语言类型,gift_name 赠金名字,content 赠金描述} 
 返回：{"ret": 0,"msg": "","data": {  }


3.获取赠金活动列表
地址：/v1/gift/giftlist
传值：category 赠金类型，state 赠金状态，star 开始时间，end，pageNo，pagesize

返回：{"ret": 0,"msg": "","data":[] }

解释：time_limit 时间限制，close_count 平仓次数，trader_day有效交易天数，category 1首次充值，2首次交易，3新手交易量达标，4活跃用户，5，充值返利，6邀请好友，partake 参与者0 全部用户 1 非代理用户 2代理，amount_limit 有效金额限制，gift_amount 赠金，friends_recharge 好友充值，exceed_time 过期时间，receive_exceed 领取过期时间，activation_exceed 激活过期时间，trade_exceed 交易过期回收时间，addgiftname 语言集合 {lang_type 语言类型,gift_name 赠金名字,content 赠金描述} create_time 创建时间 state 状态 1有效 0无效


4.更新赠金活动
地址：/v1/gift/updategiftinfo
传值：time_limit 时间限制，close_count 平仓次数，trader_day有效交易天数，amount_limit 有效金额限制，gift_amount 赠金，friends_recharge 好友充值，exceed_time 过期时间，receive_exceed 领取过期时间，activation_exceed 激活过期时间，trade_exceed 交易过期回收时 ，upid 活动id 
返回：{"ret": 0,"msg": "","data":[] }

5.更新赠金活动语言
地址：/v1/gift/updategiftlange
传值：lang_type 语言类型，title 赠金名字，content 描述 ，upid 活动ID
返回：{"ret": 0,"msg": "","data":[] }

6.更新赠金活动状态
地址：/v1/gift/updategiftstatus
传值：upid 活动ID，status 状态 状态 1有效 0无效
返回：{"ret": 0,"msg": "","data":[] }

7.获取用户赠金领取列表
地址：/v1/gift/getgiftrecord
传值：sname uid或用户名字，state 状态 1-未开始 2-进行中 3-待领取 4-已领取 5-已过期,6收回 star 开始时间，end，pageNo，pagesize
返回：{"ret": 0,"msg": "","data":[] }

解释：user_id 用户ID，gift_id 赠金ID，amount 赠金金额，state 状态，create_time 创建时间，expire_time 过期时间，receive_time 领取时间，active_time 激活时间，recovery_time 回收时间，update_time 最后更新时间，imei ，ip，after_amount


8.获取用户赠金使用
地址：/v1/gift/usegiftrecord
 传值：sname uid或用户名字，state  类型。 star 开始时间，end，pageNo，pagesize
 返回：{"ret": 0,"msg": "","data": { "total": 0,"list": []} }
解释：id，user_id 用户ID，amount 金额，type 类型，gift_amount 赠金金额，gift_available 赠金可用，gift_balance 赠金 余额，created_time创建时间，user_name 用户名字


9. 给用户发送赠金
 地址：/v1/gift/sentgiftrecord
 传值：sends 发送uids集合,gfitname 赠金名，amount 数量
返回：{"ret": 0,"msg": "","data": { "total": 0,"list": []} }

10.获取用户赠金列表
 地址：/v1/gift/getsentgift
传值：sname，sectop，sagent，star，end，pageNo，pagesize
 返回：{"ret": 0,"msg": "","data": { "total": 0,"list": []} }
解释：id，user_id用户ID，amount金额，stype 类型 ，after_amount 赠金余额，manage 管理员，created_time 床时间时间，user_name


11.收回用户赠金
地址：/v1/gift/recoverygift
传值：recoverys 取消集合，amount 金额
返回：{"ret": 0,"msg": "","data": { } }

12.获取用户取消赠金列表
 地址：/v1/gift/getrecoverygift
传值：sname，sectop，sagent，star，end，pageNo，pagesize
 返回：{"ret": 0,"msg": "","data": { "total": 0,"list": []} }
解释：id，user_id用户ID，amount金额，stype 类型 ，after_amount 赠金余额，manage 管理员，created_time 床时间时间，user_name

 */