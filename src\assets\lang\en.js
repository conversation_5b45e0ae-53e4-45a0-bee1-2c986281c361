//en.js
import enLocale from 'element-ui/lib/locale/lang/en'
export default {
    // 菜单、页面tab、面包屑等相关
    menus: {
        name: 'Platform Management System',
        home: 'Home',
        home_title: 'Welcome to Platform Management System',
        notice_management: 'Announcement Ｍanagement',
        noticeList: 'Announcement List',
        noticeRelease: 'Announcement Release',
        noticeUpd: 'Announcement Modification',
        noticeImportant: 'Home Floating Layer',
        userQuery: 'User Management',
        userList: 'User List',
        userListDetails: 'User information details',
        userKYC: 'KYC Review',
        num_login_accounts: 'Number of Login Accounts',
        num_logged_devices: 'Number of Login Devices',
        num_transactions_IP: 'Transaction Times of This IP ',
        current_whitelisted_user: 'Current Whitelist User',
        historical_whitelisted_users: 'Historical Whitelist User',
        welcome_homepage: 'Welcome Homepage',
        homepage: 'Homepage',
        announcement_management: 'Announcement Management',
        overseas_KYC_audit: 'Overseas KYC Review',
        trading_query: 'USDT contract Inquiry',
        position_query: 'Position Inquiry',
        unwind_query: 'Close Position Inquiry',
        openPositions_query: 'Open Position Inquiry',
        open_unwind_query: 'Open and Close Position Inquiry',
        check_full_stop_query: 'Full Stop Inquiry',
        order_query: 'Plan Documentary Inquiry',
        user_position_monitoring: 'User Position Monitoring',
        exports_download_list: 'Export download list',
        documentary_query: 'Documentary Inquiry',
        documentary_position_query: 'Documentary-Position Inquiry',
        documentary_unwind_query: 'Documentary-Close Position Inquiry',
        documentary_openPositions_query: 'Documentary-Open Position Inquiry',
        documentary_open_unwind_query: 'Documentary-Open and Close Position Inquiry',
        documentary_check_full_stop_query: 'Documentary-Full Stop Inquiry',
        documentary_order_query: 'Documentary Plan Documentary Inquiry',
        documentary_assets_query: 'Documentary-Asset Inquiry',
        documentary_position_monitoring: 'Documentary-Position Monitoring',
        documentary_data_monitoring: 'Documentary-Data Monitoring',
        money_query: 'Documentary-Data Monitoring',
        user_assets_query: 'User Asset Inquiry',
        user_in_out_money_query: 'User Deposit and Withdrawal Inquiry',
        user_financial_query: 'User Financial Records',
        user_extract_management: 'User Withdrawal Management',
        fiat_sell_management: 'Currency Selling Management',
        user_data_monitoring: 'User Data Monitoring',
        PNL_query: 'Fund Fee Inquiry',
        capital_cost_query: 'Fund Fee Inquiry',
        poundage_query: 'Handling Fee Inquiry',
        daily_PNL_summary: 'Daily PNL Summary',
        commission_query: 'Rebate Inquiry',
        issue_cancelIssue: 'Issuance/Cancel Issuance',
        freeze_restore: 'Freeze/Restore',
        trading_record: 'Transaction Record',
        platform_financial: 'Platform Finance',
        assetsAccount: 'Asset Account',
        historicalRecord: 'History Record',
        transactionAccount: 'Transaction Account',
        activities_management: 'Activity Management',
        carve_up_poundage: 'Divide the Handling Fee ',
        carve_up_poundageDetails: 'Partition fee details ',
        set_activity: 'Set Activity',
        set_bonus: 'Set Bonus',
        view_award_winning_users: 'View Award-winning Users',
        review_award_winning_users: 'Award-winning User Audit',
        channel_management: 'Channel Management',
        top_agent_statistical: 'Top Agent Statistics',
        agent_direct_drive_statistical: 'Agent Direct Push Statistics',
        channel_statistics: 'Channel Statistics',
        risk_control_management: 'Risk Control Management',
        label_management: 'Label Management',
        trading_frequency: 'Transaction frequency',
        trading_frequencyDetails: 'Transaction frequency details',
        high_frequency_user_identification: 'High-frequency User Identification',
        high_monetization_user_recognition: 'High-profit User Identification',
        high_win_rate_user_identification: 'High-win Rate User Identification',
        high_win_rate_user_details: 'High win rate user information details',
        system_monitoring: 'System Monitoring',
        same_IP_behavior_analysis: 'Same IP Behavior Analysis',
        IP_user_overview: 'IP User Profile',
        IP_transaction_detailsv: 'IP Transaction Details',
        observing_User_List: 'Watch User List',
        list_risk_control_groups: 'Risk Control Group List',
        list_risk_control_users: 'Risk Control User List',
        viewing_historical_records: 'View History',
        contract_PNL_query: 'Contract PNL query',
        view_risk_control_whitelist: 'View Risk Control Whitelist',
        risk_control_whitelist_operation: 'Operate Risk Control Whitelist',
        system_management: 'System Management',
        system_dictionary_management: 'System Dictionary Management',
        system_dictionary_data_management: 'Dictionary Data Management',
        home_Banner_configuration: 'System Management',
        add_Banner: 'Add Banner',
        delete_Banner: 'Delete Banner',
        role_management: 'Role Management',
        rights_group: 'Permission Grouping',
        add_group: 'Add Group',
        operation_log: 'Permission Grouping',
        CRM_operation_log: 'CRM Operation Log',
        contact_us: 'Contact Us',
        user_log: 'User Log',
        feedback_data: 'Feedback Data',

        // 2021/12/1 新增
        Net_position_loss: 'Net Position Loss',
        Abnormal_account_loss: 'Account Abnormal Loss',
        Continuous_shock_alarm: 'Continued Impact Alarm',
        Abnormal_alarm_of_net_holding: 'Net Position Abnormal',
        Quick_single_side_market_alarm: 'Rapid One-side Market Alarm',
        Latest_price_protection_alarm: 'Last Price Protection Alarm',
        Line_K_could_not_get_the_alarm: 'K-line Disable Obtained Alarm',
        K_line_transaction_data_abnormal: 'K-line Execution Data Abnormal',
        Spot_index_get_abnormal_alarm: 'Spot Index Acquisition Abnormal Alarm',
        Spot_index_could_not_get_alarm: 'Spot Index Unable Acquisition Alarm',
        Shop_single_benchmark_price_to_follow: 'Trading Benchmark Price Follow',
        Spot_index_calculation_anomaly_log: 'Spot Index calculation Abnormal Log',
        BTCUSDT_contract_spot_index_collection_alarm: 'BTCUSDT Futures Spot Index Collection Alarm',
        Shop_single_reference_system_disk_mouth_abnormal_alarm: 'Trading Reference Position Abnormal Alarm',
        NO_BTCUSDT_contract_spot_index_collection_alarm: 'Non BTCUSDT Futures Spot Index Collection Alarm',
        Shop_single_reference_system_price_acquisition_failure_alarm: 'Trading Reference Price Acquisition Fail Alarm',
        Welcome_to_the_admin_background: 'Welcome to Admin Background',
        Currency_exchange_management: 'Currency exchange management',
        Contract_point_difference_setting: 'Contract spread setting',
        // 2021/12/21 new add
        liquidationGetFund: 'Liquidation injection',
        throughPayFund: 'Bankruptcy loss',
        liquidationListQuery: 'Liquidation order query',

        // 2022/02/22 新增
        Download_the_configuration: 'Download settings',
        // 2022/02/25 新增
        Add_link: 'Add link',
        // 2022/03/13 新增
        To_receive_a_gift_of_gold: 'Get bonus',
        Give_use_a_gold: 'Used bonus',
        Issue_experience_gold: 'Give experience',
        Recycle_experience_gold: 'Recycle experience',
        Total_claim_by_user: 'User total get',
        Total_user_availability: 'User total available',
        Freezing_total_Users: 'User total frozen',
        Total_loss_of_users: 'User total loss',
        Total_User_expiration: 'User total expired',
        Always_accept: 'Total recycling',

        // 2022/03/14 新增
        giftCash_manage: 'Bonus management',
        giftCash_active: 'Bonus activity',

        // 2022/04/01 修改
        user_trade_pnl: 'User revenue data',
        // 2021/12/22 新增
        Position_inquiry: 'Position Inquiry',
        Open_and_close_query: 'Open and Close Position Inquiry',
        User_liquidation_data: 'User liquidation data',
        User_position_monitoring: 'User Position Monitoring',
        Multi_currency_contract_management: 'Coin-margined contract query',
        Plan_entrust_stop_profit_and_stop_loss: 'Plan Documentary/Full Stop Inquiry',

        // 2022/02/08 新增
        user_total_assets_query: "User's total assets query",
        usd_assets_query: 'Coin-margined contract asset query',
        usd_contract_commission: 'Coin-margined contract rebate',
        usd_contract_commission_detail: 'Coin-margined contract rebate details',

        trading_assets_query: 'USDT asset query',   // 2022/02/08 修改
        poundage_commission: 'USDT contract rebate',  // 2022/02/08 修改
        poundageCommissionDetails: 'USDT contract rebate details',  // 2022/02/08 修改

        // 2022、05、11 新增
        Check_full_stop: 'Full Stop Inquiry',
        Plan_to_entrust: 'Plan Documentary',
        // 2022/04/22 新增
        spot_trading: 'Spot Trading',
        commissioned_history: 'Historical commission',
        commissioned_current: 'Current mandate',
        plan_entrust: 'Plan commission',
        spot_assets_query: 'User spot asset query',
        spot_poundage_query: 'Spot handling fee inquiry',
        spot_monitioring: 'Spot data monitoring',
        spot_account: 'Spot account',
        // 2022/05/23 新增
        Spot_financial_records: 'Spot financial records',
        USDT_contract_financial_records: 'USDT contract financial records',
        USD_contract_financial_records: 'USD contract financial records',

        // 2022/06/01 新增
        usdtrading_frequency: 'Coin-margined contract transaction frequency',
        usdtrading_frequencyDetails: 'Coin-margined contract transaction frequency details',
        usd_high_win_rate_user_identification: 'Coin-based contract user identification with high winning rate',
        usd_high_win_rate_user_details: 'Coin-based contract user identification with high winning rate details',

        // 2022/06/20 新增
        giftoutDetail: 'Bonus payout details',
        cointouDetail: 'Activity payout details',
        Campaign_spending: 'Daily activity payout',

        // feature/daniel-academycoursemanagement-0507
        course_management: 'Course Management',
        course_list: 'Course List',
        course_purchases: 'Purchase History',
        customer_tier: 'Customer Tier',
        reward_list: 'Reward List',

        // feature/daniel-launchpadmanagement-0529
        launchpad: 'Launchpad Management',
        subscriptions: 'Subscription Overview',
        subscription_records: 'Subscription Purchase History',
        pledges: 'Pledge Overview',
        pledge_records: 'Pledge Record Inquiry',
        redemption_records: 'Redemption Record',
    },

    // 项目中所有的 筛选条件 
    filters: {
        name: 'UID/Mobile Phone/Email',
        userType: 'User Type',
        top_agent: 'Top Agent',
        topID: 'Top Agent ID',
        nick: 'Top Nickname',
        topNick: 'Top Agent Nickname',
        topIDNick: 'Top Agent ID/Nickname',
        topIDtopNick: 'Top Agent ID/Top Nickname',
        topAgentNick: 'Superior Agent Nickname',
        agentNick: 'Agent Nickname',
        agent: 'Superior Agent ID/User Name',
        regTime: 'Registration Time',
        startTime: 'Start Date',
        endTime: 'End Date',
        labal: 'Label',
        inviteCode: 'Invitation Code',
        backRate: 'Rebate Ratio',
        pleaseEnterTheContent: 'Please enter content',
        pleaseLanguageType: 'Please enter content',
        pleaseBannerType: 'Please select the banner type',
        pleaseSystemColorMode: 'Please select the color mode',
        pleaseLinkAddr: 'Please enter the link address',
        optionDate: 'Select Date',
        optionTime: 'Select Time',
        ChineseSimplifiedNotice: 'Chinese Simplified Announcement',
        EnglishNotice: 'English Announcement',
        ChineseTraditionalNotice: 'Chinese Traditional Announcement',
        KoreanNotice: 'Korean Announcement',
        VietnameseNotice: 'Vietnamese Announcement',
        IndonesianNotice: 'Indonesian Announcement',
        RussianNotice: 'Russian Announcement',
        GermanNotice: 'German Announcement',
        JapaneseNotice: 'Japanese Announcement',
        positionType: 'Position Type',
        direction: 'Direction',
        transactionType: 'Transaction Type',
        delegateType: 'Delegation Type',
        type: 'Type',
        advancedScreening: 'Advanced Filter',
        currency: 'Currency',
        orderType: 'Order Type',
        auditTime: 'Review Time',
        eventDate: 'Activity Date',
        topAgentUID: 'Superior Agent UID',
        pleaseInput: 'Please Enter',
        noEliminateUser: 'No Users Excluded',
        riskControlGroup: 'Risk Control Group',
        actionType: 'Action Type',
        selectClient: 'Please select the client',
        support_English_digital: 'Only English and numbers are supported',
        LightMode: 'Light Mode',
        DarkMode: 'Dark Mode',
        BannerImage: 'Banner Image',
        InvitePoster: 'Invite Poster',
        select_group: 'Select Group',
        please_enter_group_name: 'Please enter the group name',
        comprehensive_data: 'Comprehensive Data',
        new_users: 'New Users',
        user_equipment: 'User Equipment',
        app_download: 'App Download',
        no_commission_restore: 'No Commission/Restore',
        tbxzhqx: 'Withdrawal restriction/cancellation',
        label_deletelabel: 'Tag/Delete Tag',
        advanced_filter: 'Tag/Delete Tag',
        delete_label: 'Delete tag',
        role_account: 'Role Account',
        please_new_pass: 'Role Account',
        no_commission: 'No Commission Rebate',
        restore_commission: 'Restore Commission Rebate',

        Normal: 'Normal',
        Issued: 'Issued',
        Stop_loss: 'Stop loss',
        limited_price: 'Limit',
        Has_triggered: 'Triggered',
        Stay_out: 'To be released',
        To_audit: 'Pending review',
        Mention_money: 'Withdrawal',
        Market_price: 'Market Price',
        Not_trigger: 'Not triggered',
        Market_orders: 'Market order',
        Unauthorized: 'No certification',
        Strong_flat: 'Forced liquidation',
        Check_surplus: 'Take profit and close position',
        With_a_single_check: 'Profit Stop with Documentary',
        Conditions_unwind: 'Conditional Close Position',
        In_recent_30_days: 'Nearly 30 days',
        All_clinch_a_deal: 'All transactions',
        With_a_single_stop: 'Loss Stop with Documentary',
        Some_clinch_a_deal: 'Partial transaction',
        Adjustment_margin: 'Adjust Margin',
        Liquidation_cost: 'Liquidation Fee for Position Explosion',
        Activities_to_reward: 'Activity Award',
        With_single_warehouse: 'Closing a position with a single',
        Agency_commission_Award: 'Agent Commission Reward',
        No_deal_has_been_withdrawn: 'Unsold cancellation',
        Unwinding_of_profit_and_loss: 'Close Position Profit and Loss',
        Check_surplus: 'Take profit and close position',
        Export_failure: 'Export failed',
        Ban_on_trading: 'Prohibit transaction',
        To_clinch_a_deal: 'Waiting for deal',
        Stop_positions: 'Stop loss closing',
        In_recent_7_days: 'Nearly 7 days',
        Strong_flat_sheet: 'Liquidation order',
        Trigger_failure: 'Trigger failed',
        Check_full_stop: 'Full Stop Inquiry',
        Plan_to_entrust: 'Plan Documentary',
        Users_to_unwind: 'User liquidation/',
        Allowed_to_trade: 'Allow transaction',
        Unwind_to_cancel: 'Liquidation cancellation',
        Opening_charge: 'Handling Fee of Opening Position',
        System_to_unwind: 'System position closing',
        Closing_charge: 'Close Position Fee',
        Has_been_cancelled: 'Cancelled',
        // 2021/11/30 新增
        User: 'Users',
        Drop: 'Airdrop',
        System: 'System',
        Korean: 'Korean',
        Top_up: 'Deposit',
        RollOut: 'Withdraw',
        English: 'English',
        Shift_to: 'Transfer-in',
        Japanese: 'Japanese',
        Expired: 'Expired',
        The_agent: 'Agency',
        Registered: 'Sign Up',
        First_prize: 'First Prize',
        Third_prize: 'Third Prize',
        Add_notes: 'Add Notes',
        Fiat_deal: 'Fiat Trading',
        Fiat_sold: 'Fiat Sold',
        To_put_money: 'Waiting For Crypto',
        Ban_unwind: 'Ban Close',
        Second_prize: 'Second Prize',
        In_the_export: 'Exporting',
        Reward_drop: 'Airdrop Reward',
        Into: 'Transfer In (From Funding Account)',
        API_to_unwind: 'API Close',
        Modify_email: 'Change Email',
        Has_refused_to: 'Refused',
        Add_the_agent: 'Add Agency',
        For_the_payment: 'Outstanding',
        Have_to_account: 'Received',
        C2C_settlement: 'C2C Settlement',
        Export_success: 'Export Succeed',
        Banned_logging: 'Disable Login',
        Have_been_frozen: 'Frozen',
        Has_put_the_coin: 'Issued',
        Korean_Banner: 'Korean Banner',
        Paid_commission: 'Offer Referral',
        Open_the_export: 'Open Export',
        A_key_positions: 'One-click Close',
        Google_Captcha: 'Google Authentication',
        Englist_Banner: 'Englist Banner',
        Remove_the_agent: 'Remove Agency',
        Allowed_to_login: 'Allowed Login',
        Strong_flat_back: 'Forced Close Retreat',
        The_average_user: 'Regular User',
        Roll_out: 'Transfer Out (to Funding Account)',
        System_refuse_to: 'System Refused',
        KYC1_application: 'KYC1 Application',
        KYC2_application: 'KYC2 Application',
        Market_positions: 'Market Close',
        Commission_income: 'Commission Income',
        C2C_Other_uses: 'C2C Other Uses',
        Vietnamese_Banner: 'Vietnamese Banner',
        Indonesian_Banner: 'Indonesian Banner',
        Japanese_Banner: 'Japanese Banner',
        Unwind_the_export: 'Close Export',
        Campaign_spending: 'Campaign Expenditure',
        Set_up_your_email: 'Set Email',
        Commission_refund: 'Commission Refund',
        API_open_positions: 'API Open',
        In_recent_14_days: 'Last 14 Days',
        Trades_export: 'Trade Close Export',
        Poundage_export: 'Transaction Fee Export',
        Platform_to_refuse: 'Platform Refuse',
        Chinese_simplified: 'Chinese Simplified',
        Chinese_traditional: 'Chinese Traditional',
        Channel_new_users: 'Channel New Users',
        Review_mention_money: 'Review Withdrawal Crypto',
        The_agent_commission: 'Agency Commission',
        Market_price_is_open: 'Market Open',
        Withhold_commissions: 'Withhold Commission',
        User_name_Settings: 'User name Settings',
        Invite_the_commission: 'Invite Commission',
        Identity_verification: 'Identity Verification',
        Face_authentication: 'Face ID',
        Ban_on_open_positions: 'Disable Open',
        Set_fund_password: 'Set Funding Password',
        Documentary_commission: 'Copy Commission',
        Prohibit_mention_money: 'Disable Withdraw',
        Cancellation_of_plan: 'Open Order Cancellation',
        Withdrawal_application: 'Withdrawal Application',
        Individual_new_users: 'Individual New Users',
        IOS_Download_speed: 'IOS - Fast Download',
        IOS_Local_download: 'IOS - Local Download',
        User_assets_export: 'User Fund Export',
        Withdrawal_commission: 'Withdrawal Fee',
        Images_to_be_uploaded: 'Upload Image',
        Re_enable_the_agent: 'Re-enable Agency',
        Fiat_order_received: 'Fiat Order Received',
        Administrator_release: 'Administrator Release',
        Transaction_commission: 'Transaction Fee',
        Capital_cost_export: 'Fund Expense Export',
        Change_fund_Password: 'Change Funding Password',
        Allowed_to_mention_money: 'Allowed Withdraw',
        Fund_password_setting: 'Funding Password Setting',
        Profit_and_loss_of_income: 'Profit&Loss Income',
        Manual_KYC_application: 'Staff KYC Application',
        Set_mobile_phone_number: 'Set Phone Number',
        Login_Password_Setting: 'Login Password Setting',
        Binding_payment_method: 'Add Payment Method',
        Trading_assets_export: 'Transaction Fund Export',
        Retrieve_login_password: 'Forget Login Password',
        Android_Download_speed: 'Android - Fast Download',
        Android_Local_download: 'Android - Local Download',
        Setting_a_Login_Password: 'Setting Login Password',
        Adding_Label_Information: 'Add Label Information',
        With_a_single_open_positions: 'Leading Open',
        Trading_frequency_export: 'Transaction Frequency Export',
        Google_Captcha_Settings: 'Google Authentication Setting',
        Modifying_User_Information: 'Change User Information',
        Documentary_assets_export: 'Copy Fund Export',
        Face_authentication_passed: 'Face ID Pass',
        Changing_Mobile_phone_Number: 'Change Phone Number',
        Passed_the_platform_review: 'Platform Review Pass',
        On_chain_withdrawal_refused: 'On-chain Withdrawal Refused',
        Chinese_Simplified_Banner: 'Chinese Simplified Banner',
        Modifying_label_Information: 'Change Label Information',
        Platform_account_withdrawal: 'Platform Account Withdrawal',
        Face_authentication_failure: 'Face ID Failed',
        Changing_the_Login_Password: 'Changing Login Password',
        Invitation_commission_Award: 'Invitation Commission Reward',
        Chinese_traditional_Banner: 'Chinese Traditional Banner',
        Transfer_to_assets_account: 'Transfer to Funding Accoun',
        Analog_disk_reduces_assets: 'Simulator Reduce Fund',
        Trade_open_positions_export: 'Transaction Open Export',
        Set_up_the_Google_validator: 'Set Google Authentication',
        With_single_warehouse_export: 'Copy Close Export',
        Modify_the_Google_validator: 'Change Google Authentication',
        There_was_no_trading_on_the_3_day: 'No Trading on 3 Days',
        There_was_no_trading_on_the_5_day: 'No Trading on 5 Days',
        There_was_no_trading_on_the_7_day: 'No Trading on 7 Days',
        Platform_account_charging_coins: 'Platform Account Deposit',
        Transfer_to_a_trading_account: 'Transfer to Trading Account',
        Transfer_to_the_asset_account: 'Transfer to Funding Account',
        Analog_disk_reclaiming_assets: 'Simulator Replacement Fund',
        Analog_disk_supplement_assets: 'Simulator Supplement Fund',
        The_proxy_directly_pushes_users: 'Agency Recommend User',
        There_was_no_trading_on_the_15_day: 'No Trading on 15 Days',
        Has_been_open_unauthorized: 'Enabled <br/>(Not Authenticated)',
        Face_information_authentication: 'Face ID Authenticating',
        Documentary_open_positions_export: 'Copy Open Export',
        Transfer_out_to_the_asset_account: 'Transfer to Funding Account',
        Part_of_the_deal_has_been_withdrawn: 'Part Orders Canceled',
        Platform_cash_withdrawal_commission: 'Platform Withdrawal Fee',
        Follow_up_open_derivation_export: 'Copy Open&Close Export Export',
        Closed_authentication_success: 'Closed <br/>(Authentication succeed)',
        Transfer_it_out_to_the_trading_account: 'Transfer to Trading Account',
        On_chain_withdrawal_has_been_submitted: 'On-chain Withdrawal Submitted',
        Face_authentication_failed_Procedure: 'Face Authentication Failed',
        Authentication_of_identity_information: 'Identity Information Authenticating',
        The_face_information_is_authenticated: 'Face ID Authentication Passed',
        Has_been_open_in_the_authentication: 'Enabled <br/>(Authenticating)',
        Has_been_open_authentication_failed: 'Enabled <br/>(Authentication Failed)',
        Trade_opening_and_closing_export_export: 'Transaction Open&Close Export Export',
        High_frequency_User_Identification_export: 'High Frequency User Identification Export',
        High_win_rate_user_identification_export: 'High Win Rate User Identification Export',
        High_monetization_user_recognition_export: 'High Revenue User Identification Export',
        The_identity_information_is_authenticated_Procedure: 'Identity Information Authentication Passed',
        Transfer_from_asset_account_to_documentary_account: 'Transfer asset account to documentary account',
        Transfer_from_documentary_account_to_asset_account: 'Transfer from Copy Account to Funding Account',
        Failed_to_authenticate_the_identity_information_Procedure: 'Identity Information Authentication Failed',
        Transfer_from_trading_account_to_documentary_account: 'Transfer from Trading Account to Copy Account',
        Transfer_from_documentary_account_to_trading_account: 'Transfer from Copy Account to Trading Account',
        Source_of_currency: 'Source currency',
        The_target_currency: 'Target currency',
        Airdrop_rewards: "Airdrop rewards",
        Fee_activity: "Fee activity",
        system_error: "system error",
        Marketing_activities: "Marketing activities",
        Rebate_rewards: "Rebate rewards",
        Liquidation_fee: "Liquidation fee",
        Full_warehouse_wear: "Full warehouse wear",
        Abnormal_asset_deduction_asset: "Abnormal asset deduction (user asset account)",
        Abnormal_asset_deduction_trading: "Abnormal asset deduction (user trading account)",
        Abnormal_asset_deduction_documentary: "Abnormal asset deduction (user documentary account)",
        Currency_exchange_fee: "Currency exchange fee",
        Cash_out: "Cash out",
        Cash_in: "Cash in",
        Transfer_out_to_deposit_and_withdrawal_account: "Transfer out to deposit and withdrawal account",
        Transfer_from_deposit_and_withdrawal_account: "Transfer from deposit and withdrawal account",
        Transfer_from_currency_exchange_account: "Transfer from currency exchange account",
        Transfer_from_asset_account: "Transfer from asset account",

        // 2022/03/13 新增
        Give_gold_type: 'Bonus type',
        Get_the_time: 'Receive time',
        Not_at_the: 'Not start',
        To_receive: 'Pending',
        Have_to_receive: 'Received',
        Take_back: 'Recycled',

        // 2022/03/14 新增
        Margin_reduction: 'Reduce margin',
        Liquidation_commission: 'Liquidation fee',
        Deduction_of_abnormal_assets: 'Abnormal assets deduction',
        Margin_increase: 'Add margin',
        Give_gold_to_receive: 'Bonus received',
        Give_gold_failure: 'Bonus expired',
        Give_gold_recovery: 'Bonus recycled',

        Opening_and_closing_time: 'Opening and closing time＜',

        // 2022/03/28 新增
        All_warehouse_Points_storehouse: 'Cross - Sub-p',
        By_warehouse_Points_storehouse: 'Isolated - Sub-p',
        // 2021/12/22 新增
        Margin_currency: 'Margin currency',

        // 2022/01/27 新增
        Types_of_profit_and_loss: 'Profit and loss type',

        // 2022/05/23 新增
        Trading: 'Trading pair',
        Delegate_status: 'Delegate status',
        Entrust: 'Entrust',
        Buy: 'Buy',
        Sell: 'Sell',
        Temporary_order: 'Temporary order',
        Not_traded: 'Not traded',
        Conditional_order_status: 'Conditional order status',
        Transaction_fee: 'Transaction fee',
        Transfer_in_from_asset_account: 'Transfer in (from wallet account)',
        Transfer_out_to_the_asset_account: 'Transfer out (to the wallet account)',
        Increase_margin: 'Increase margin',
        Margin_reduction: 'Margin reduction',
        Withdraw: 'Withdraw',
        Transfer_to_trading_account: 'Transfer to trading account',
        Fiat_order_receipt: 'Fiat order receipt',
        Currency_exchange_exchange_out: 'Currency exchange(cash out)',
        Currency_exchange_exchange: 'Currency exchange(cash in)',
        Currency_exchange_exchange_and_refund: 'Currency exchange (exchange and refund)',
        The_asset_account_is_transferred_to_the_USD_contract_account: 'The asset account is transferred to the USD contract account',
        Transfer_USD_contract_account_to_asset_account: 'Transfer USD contract account to asset account',
        The_asset_account_is_transferred_to_the_spot_trading_account: 'The asset account is transferred to the spot trading account',
        Transfer_from_spot_trading_account_to_asset_account: 'Transfer from spot trading account to asset account',
        Transaction_Fees_Taker: 'Transaction fee (taker)',
        Transaction_Fees_Maker: 'Transaction fee (maker)',
        Transaction_transfer_out: 'Transaction transfer out',
        Transaction_transfer_in: 'Transaction transfer in',

        // 2022/06/01 新增
        Coin_margined_contracts_open_cloes_export: 'Open and close positions in currency-margined contracts Export',
        Coin_margined_contracts_user_forced_balance_export: 'Coin-margined contract user forced liquidation data export',

        // 2022/06/21 新增
        Bonus_expired: 'Bonus expired',
        // 2022/7/14
        find_administrator: 'find administrator',
        agent_uid: 'Please enter the administrator UID',
        // 2025/05/23
        Simplified: 'Simplified Chinese',
        Traditional: 'Traditional Chinese',
        Vietnamese: 'Vietnamese',
        Indonesian: 'Indonesian',
        Russian: 'Russian',
        German: 'German',
    },

    // 项目所有的 表头
    tableHeader: {
        uid: 'UID',
        userName: 'User Name',
        KYCstate: 'KYC Status',
        withdrawalState: 'Withdrawal Status',
        title: 'Title',
        content: 'Content',
        link: 'Link',
        time: 'Time',
        releaseTime: 'Release Time',
        downShelvesTime: 'Off Shelf Time',
        clinchDealTime: 'Off Shelf Time',
        operation: 'Operation',
        operationType: 'Operation Type',
        actionObject: 'Operation Object',
        superiorID: 'Superior ID',
        superiorUsername: 'Superior User Name',
        withSingle: 'With Documentary Identity',
        backTime: 'Rebate Period',
        lastLoginTime: 'Last Login Time',
        lastLoginIP: 'Last Login IP',
        note: 'Remark',
        contract: 'Contract',
        maxLeverage: 'Maximum Leverage',
        maxOrderQuantity: 'Maximum Single Order Quantity',
        minOrderQuantity: 'Minimum Single Order Quantity',
        maxPos: 'Maximum Position',
        maxPosition: 'Maximum Positions',
        minSomeBad: 'Minimum Spread',
        maxSomeBad: 'Maximum Spread',
        poundageReat: 'Handling Fee Rate',
        moneyReat: 'Funding Rate',
        risk: 'Risk Rate Increase or Decrease',
        state: 'State',
        toApplyForTime: 'Application Time',
        orderSerialNumber: 'Order Number',
        countriesRegions: 'Country Region',
        processingTime: 'Processing Time',
        leverage: 'Lever',
        number: 'Quantity',
        averageOpen: 'Average Opening Price',
        poundage: 'Handling Fee',
        flatPrice: 'Margin Closeout Price',
        float_PNL: 'Floating PNL',
        checkFullPrice: 'Profit Stop Price',
        StopLossPrice: 'Loss Stop Price',
        clinchDealPrice: 'Deal price',
        IP_addr: 'IP Address',
        transactionNumber: 'Transaction Number',
        unwindPri: 'Close price',
        unwindPrice: 'Close price',
        net_PNl: 'Net PNL',
        PNL: 'PNL',
        openPositionsTime: 'Opening Time',
        orderSource: 'Order Source',
        openPositionsPrice: 'Opening Price',
        accountType: 'Account Type',
        entrustPrice: 'Entrusted Price',
        entrustNum: 'Entrusted Quantity',
        clinchDealNum: 'Deal Quantity',
        clinchDealAveragePrice: 'Average Deal Price',
        entrustTime: 'Entrusted Time',
        updateTime: 'Updated Date',
        clinchDealState: 'Deal Status',
        IP: 'IP',
        submitTime: 'Submission Time',
        triggerType: 'Submission Time',
        triggerState: 'Trigger Status',
        triggerTime: 'Trigger Time',
        triggerPrice: 'Trigger Price',
        positionID: 'Position ID',
        checkSurplusConditions: 'Profit sTOP Conditions',
        stopLossConditions: 'Profit sTOP Conditions',
        cancelTime: 'Cancel Time',
        orderID: 'Plan Documentary ID',
        entrustedClientIP: 'Entrusted Client IP',
        equipmentIdentificationCode: 'Device ID',
        contractName: 'Contract Name',
        moreFlat: 'Close Position',
        moreFlatNum: 'Close Position Quantity',
        moreFlatPNL: 'Equality PNL',
        longPositionsPoundage: 'Equality PNL',
        sidesJersey: 'Close Position',
        sidesJerseyNum: 'Close Position Quantity',
        sidesJerseyPNL: 'Close Position PNL',
        sidesJerseyPoundage: 'Close Position Handling Fee',
        buyMore: 'Pay the Orders (Long Opening)',
        positionAverage: 'Average Position Price',
        sale: 'Sell Order (Open Short)',
        currentPrice: 'Current Price',
        buyingSellingSingleDifferential: 'Sale Order Difference',
        combinedPNL: 'Sale Order Difference',
        combinedPNLPoundage: 'Total Handling Fee',
        creationTime: 'Created Date',
        startTime: 'Start Date',
        endTime: 'End Date',
        documentaryAccountRights: 'Documentary Account Equity',
        documentaryAvailable: 'Documentation Available',
        documentaryFrozenMargin: 'Documentary Frozen Margin',
        moneyCost: 'Funding Expenses',
        centCommission: 'Divided Commission',
        customer_total_gold: 'Customer Total Deposit',
        customer_total_out: 'Customer Total Withdrawal',
        customer_net_gold: 'Customer Net Deposit',
        customer_contract_account_rights: 'Customer Net Deposit',
        last_week_pnl: "Last Week's PNL",
        last_week_poundage: "Last Week's Handling Fee",
        history_pnl: 'History PNL (as of Last Sunday)',
        today_pnl: 'PNL of The Day',
        this_week_pnl: 'PNL of This Week (Starting This Monday)',
        this_week_poundage: "This Week's Handling Fee (Starting This Monday)",
        total_gold: 'Total Deposit',
        total_out: 'Total Withdrawal',
        net_gold: 'Net Deposit',
        moneyAccountRights: 'Funding Account Equity',
        moneyAccountAvailable: 'Funding Account Available',
        moneyAccountFreeze: 'Funding Account Frozen',
        unwindNum: 'Close Position Times',
        moneyAccount: 'Account Assets',
        available: 'Available',
        amount: 'Amount',
        walletAccountBalance: 'Wallet Account Balance',
        clinchDealRecord: 'Deal Record',
        clinchDealSerial: 'Deal Number',
        withdrawalAmount: 'Withdrawal Quantity',
        rollOutAddr: 'Transfer out Address',
        by_the_time: 'Start Time',
        equipment: 'Device',
        equipmentID: 'Device ID',
        operationSystem: 'Operating System',
        transactionAmo: 'Transaction Fund',
        transactionAmount: 'Transaction Amount',
        transactionNum: 'Transaction Quantity',
        merchantsExchangeRate: 'Merchant Exchange Rate',
        difference: 'Difference',
        settlementTime: 'Settlement Time',
        Maker_poundageReat: 'Maker Handling Fee Rate',
        Taker_poundageReat: 'Taker Handling Fee Rate',
        aggregate: 'Total',
        commissionTime: 'Rebate Time',
        userTransactionAmount: 'User Transaction Amount',
        userpoundage: 'User Handling Fee',
        commissionMoney: 'Rebate Amount',
        getRidOfCommission: 'Remove the Rebate',
        statisticalTime: 'Statistics Time',
        issueTime: 'Issuance Time',
        tradersID: 'Trader ID',
        topAgentUserName: 'Superior Agent User Name',
        pieceNum: 'Number of Sheets',
        availableNum: 'Quantity Available',
        platformLiabilities: 'Platform Liabilities',
        transferUID: 'Transfer UID',
        changeNum: 'Change Quantity',
        remainingAvailableBalance: 'Remaining Available Balance',
        implementedProfitandLoss: 'Realized Profit and Loss',
        activityName: 'Activity Name',
        activityDescribe: 'Activity Description',
        jackpotHighestCeiling: 'Maximum Upper Limit of Prize Pool',
        injectionPoundageProportion: 'Injection Handling Fee Ratio',
        startDate: 'Start date',
        eachIssueStartTime: 'Start Time of Each Period',
        eachIssueLotteryTime: 'Draw Time of Each Period',
        eachIssueEndTime: 'Draw Time of Each Period',
        activityState: 'Active Status',
        alreadySentReward: 'Active Status',
        transactionTime: 'Transaction Time',
        lastTransactionTime: 'Last Transaction Time',
        rewardType: 'Reward Type',
        uploadPhotos: 'Upload Photos',
        agentPNl: 'Contract PNL',
        documentaryPoundage: 'Documentary Handling Fees',
        documentaryPNL: 'Documentary PNL',
        P1: 'P1',
        P2: 'P2',
        regNum: 'Number of Registrants',
        profitNum: 'Number/Proportion of Profitable People',
        transactionsPerCapita: 'Per Capita Transactions',
        dayLive: 'Daily Active Number',
        floatProfitLoss: 'Floating Profit and Loss',
        labalName: 'Label Name',
        minRisk: 'Minimum Risk Rate Increase or Decrease',
        transaction_number: 'Transaction Times',
        openPositions_number: 'Open Position Times',
        openPositions_pieceNum: 'Number of open positions', // 2022/02/08 修改
        singleAverageTime: 'Average Time Per Sheet',
        oddsUnwindNum: 'Winning Rate and Closing Times',
        profitnum: 'Number of Profits',
        historyOdds: 'Historical Winning Rate',
        ershisi_h_tradingNum: 'Number of Transactions in the Last 24H',
        ershisi_h_net_pnl: 'Last 24H Net PNL',
        averageTradingCycle: 'Average Transaction Cycle',
        maxSingleLoss: 'Maximum Single Loss',
        maxSingleProfit: 'Maximum Single Profit',
        currentPositionValue: 'Current Position Value',
        riskDegree: 'Risk Degree',
        lastLoginTime: 'Last Login Time',
        occupancyDeposit: 'Occupy Margin',
        ID: 'ID',
        level: 'Level',
        num_login_accounts: 'Number of Login Accounts',
        num_logged_devices: 'Number of Login Devices',
        regWay: 'Registration Method',
        num_login_IP: 'Login Times of This IP',
        num_login_total: 'Total Login Times',
        number_logged_devices: 'Number of Login Devices',
        num_transactions_total: 'Total Transaction Times',
        timeKYC: 'KYC Time',
        num_labeled_observations: 'Mark Observation Times',
        addTime: 'Add time',
        handlers: 'Operator',
        serialNumber: 'Number',
        categoryName: 'Group Name',
        agentUID: 'Agent UID',
        total_golden_interval: 'Total Deposit Range',
        total_gold_interval: 'Total Withdrawal Range',
        net_gold_interval: 'Net Deposit Range',
        PNL_interval: 'PNL Range',
        poundage_interval: 'Handling Fee Range',
        capital_cost_interval: 'Funding Fee Range',
        net_PNL_interval: 'Net PNL Range',
        rate_of_return: 'Profitability',
        rate_of_return_interval: 'Profitability Range',
        odds: 'Winning Rate',
        odds_interval: 'Winning Rate Range',
        poundage_net_gold: 'Ratio of Handling Fee to Net Deposit ',
        poundage_net_gold_interval: 'Ratio of Handling Fee to Net Deposit ',
        occupancy_deposit_interval: 'Occupy Margin Range',
        priority: 'Priority',
        whitelist_Status: 'Whitelist Status',
        addpeople: 'Add people',
        deleteTime: 'Delete Time',
        deletepeople: 'Delete Person',
        deletenote: 'Delete Notes',
        role_of_grouping: 'Role Grouping',
        whether_desensitization: 'Desensitization',
        handlersID: 'Operator ID',
        actionObjectID: 'Operation Object ID',
        executionResult: 'Execution Results',
        the_name: 'Name',
        APP_version: 'APP Version',
        error_code: 'Error Code',
        error_content: 'Error Content',
        on_the_user: 'Daily Active User',
        weeklyActiveUsers: 'Weekly Active Users',
        monthlyActiveUsers: 'Monthly Active Users',
        NumberOfTransactions: 'Number of transactions',
        trading_volume: 'Transaction volume',
        user_equipment: 'Users Device',
        ranking: 'Ranking',
        positions: 'Open Position',
        unwind: 'Close Position',
        buy: 'Buy',
        sell: 'Sell',
        documentary: 'Documentary',
        withSin: 'With Documentary',
        fivefile: '5 gears',
        sell_empty: 'Sell Open Short',
        open_to_buy_more: 'Buy Open Short',
        sell_more_flat: 'Sell Close Position',
        buy_sides_jersey: 'Buy Close Position',
        order: 'Buy Close Position',
        check_single: 'Profit Stop Documentary',
        stop_loss_orders: 'Loss Stop Documentary',
        contract_account_balance: 'Contract Account Balance',
        balance_documentary_account: 'Documentary Account Balance',
        freeze: 'Frozen',
        restore: 'Restore',
        purchase: 'Buy',
        sells: 'Sell',
        open: 'Open',
        unlimited: 'Unlimited',
        effect_of: 'Effective',
        has_failure: 'Expired',
        group: 'Group',
        all_warehouse: 'Full Warehouse',
        by_warehouse: 'Warehouse by Warehouse',

        // 2021/12/16 新增
        The_price: 'Price',
        turnover: 'Transaction amount',
        Their_own_PNL: 'Own PNL',
        Independent_PNL: 'Autonomous PNL',
        PNL_directly_under: 'Directly under PNL',
        Self_handling_fee: 'Own handling fee',
        Direct_commission: 'Direct handling fee',
        Keep_pushing_the_num: 'Direct push',
        transactionTime_S: 'Transaction time (seconds)',
        Independent_commission: 'Autonomous handling fee',
        Num_of_direct_transactions: 'Direct transactions',
        Max_spread_over_trading_time: 'Maximum spread during trading hours',
        Min_point_spread_over_trading_time: 'Minimum spread during trading hours',
        // 2021/12/21 new add
        clear_fee: 'Liquidation fee',
        through_pay: 'Bankruptcy loss',

        // 2022/02/24 新增
        Version_number: 'Version',
        Update_way: 'Update mode',
        Prompt_update: 'Update by prompt',
        Forced_update: 'Forced updating',
        Prompt_content: 'Prompt content',
        Dont_tip: "Don't tip",

        // 2022/02/25 新增
        Edit_version: 'Edit version',
        Current_link: 'Current link',
        Download_the_way: 'Download method',
        Display_version_number: 'Show version number',
        Updated_version_number: 'Update version number',
        Chinese: 'Chinese',
        English: 'English',
        Traditional: 'Chinese-TC',
        Korean: 'Korean',
        Vietnam: 'Vietnam',
        Indonesia: 'Indonesia',
        Download_speed: 'Fast download',
        Alternate_download: 'Alternate download',
        Local_download: 'Local download',
        Download_speed_two: 'Fast download 2',
        // 2022/03/13 新增
        Give_name_of_gold: 'Bonus name',
        Top_up_for_the_first_time: 'First deposit',
        For_the_first_time_trade: 'First transcation',
        Novice_transaction_volume_meets_the_standard: 'New user trading volume to',
        Active_users: 'Active user',
        Top_up_rebate: 'Deposit rebate',
        Invite_friends: 'Invite friends',
        The_time_limit: 'Time limit',
        Effective_trading_days: 'Valid transaction days',
        Participants: 'Participator',
        All_users: 'All user',
        Non_proxy_user: 'Non-agent',
        Proxy_user: 'Agent',
        Effective_amount_limit: 'Valid Amount Limit',
        With_gold: 'Bonus',
        Friend_recharge: 'Friend deposit',
        Overdue_time: 'Receipt expired time',
        The_activation_time_limit: 'Activation time limit',
        Transaction_recovery_time: 'Recovery time limit',
        Give_gold_description: 'Bonus description',
        Expiration_time: 'Expiration time',
        Balance_of_users_bonus: 'User bonus balance',
        The_activation_time: 'Activation time',
        Recovery_time: 'Recycling time',
        Last_Updated: 'Last update time',
        Equipment_information: 'Device',
        Use_the_type: 'Use type',
        Recycling_amount: 'Recycle amount',
        To_book_value: 'Received amount',

        // 2022/03/14 新增
        Amount_of_money_donated_by_users: 'User bonus amount',
        The_amount: 'Amount issued',
        Get_the_total_amount_of_bonus: 'Total amount of bonus',
        Total_use_of_bonus: 'Total used of bonus',
        Bonus_handling_fee: 'Fees of bonus',
        Accumulated_credit_bonus: 'Accumulated bonus',
        Available_balance_of_bonus: 'Bonus available balance',
        Give_gold_balance: 'Bonus balance',
        The_bonus_has_been_used: 'Bonus used',
        With_gold_available: 'Bonus available',
        Bonus_settlement_fee: 'Bonus liquidation Fee',
        Free_use_of_gold_warehouse: ' Bonus used of close position',
        Amount_of_use_of_bonus: 'Bonus used amount',
        Time_stamp_of_last_open_position: 'Last open time stamp',

        // 2022/03/16 新增
        Top_up: 'Recharge≥',
        Bonus_experience_money: 'Reward experience fee',
        Registration_deadline: 'Registration time limit',
        Activity_expiration_time: 'Activity expiration time',
        Administrator_issue: 'Admin issued',
        Administrator_recycling: 'Admin recycling',

        Open_and_close_interval: 'Open and close interval',

        // 2022/04/02 新增
        Nick_name: 'Nick name',
        // 2021/12/29 新增
        Strong_parity: 'strong parity',
        Position_number: 'Position Number',
        Trigger_price: 'Trigger Price',
        Executive_price: 'Execution Price',

        // 2022/01/27 新增
        Balance: 'balance',
        Profit_and_loss: 'Profit and loss',
        Transfer: 'transfer',

        // 2022/04/29 新增
        net_transfer: 'net transfer out',
        net_into: 'net transfer',

        // 2022/05/12 新增
        Total_assets: 'Total assets',
        Total_available: 'Total available',
        Total_freeze: 'Total freeze',
        // 2022/05/19 新增
        Total_spot_purchase: 'Total spot purchase',
        Total_spot_sale: 'Total spot sold',
        Rights_and_interests_spot: 'Spot Equity',

        // 2022/05/23 新增
        Commission_amount: 'Commission amount',
        Lock_up_assets: 'Lock upassets',
        Volume: 'Volume',
        Estimated_transaction_price: 'Estimated transaction price',
        Third_party_order_ID: 'Third party order ID',
        Transaction_details: 'Transaction details',
        Turnover_USDT: 'Turnover',
        Order_ID: 'Order id',
        Entrust_ID: 'Delegate ID',
        Total_transfer: 'Total transfer',
        Total_outgoing: 'Total outgoing',
        Buying_fee: 'Buying fee',
        Selling_fee: 'Selling fee',
        Purchase_quantity: 'Purchase quantity',
        Purchasing_price: 'Purchasing price',
        Purchase_fee: 'Purchase fee',
        Average_purchase_price: 'Average purchase price',
        Purchase_amount: 'Purchase amount',
        Sales: 'Sales',
        Trading_area: 'Trading area',
        Turnover: 'Turnover',
        Deal_done: 'Deal done',
        Not_traded: 'Not traded',
        Stock_available: 'Stock available',
        Spot_Freeze: 'Spot Freeze',
        Buy_amount: 'Buy volume',
        Buy_balnce: 'Purchase amount',
        Buy_fee: 'Buying fee',
        Buy_arg: 'Average buying price',
        Sell_amount: 'Sell volume',
        Sell_balnce: 'Sales',
        Sell_fee: 'Selling fee',
        Sell_arg: 'Average selling price',
        Spread: 'Spread',
        Spot_account_balance: 'Spot account balance',
        Spot_account_freeze: 'Spot account freeze',
        Number_Subordinate_Registrations: 'Number of Subordinate Registrations',
        Subordinate_rebate_data: 'Subordinate rebate data',

        // 2022/06/02 新增
        Transaction_value: 'Transaction value',

        // 2022/06/20 新增
        serial_number: 'NO',
        Serial_number: 'SN',
        Time_of_occurrence: 'Occur time',
        Number_of_activities: 'Activity quantity',
        The_statistical_date: 'Statistics date',
        Payout_coin_reward: 'Reward payout token',
        Expenditure_bonus: 'Reward payout Bonus ',
        The_total_amount_of_spending: 'Total payout',
        Remaining_account_equity: 'Balance',
        Remaining_frozen_quantity: 'Remain frozen',
        channel_name: 'Channel name',
        agent_name: 'Administrator username',
        tatalreg: 'cumulative registrations',
        totaltrading: 'cumulative trading volume',
        yestdayreg: 'The number of registered yesterday',
        yestdaytrading: "Yesterday's trading volume",
        yestdaycommiss: "Yesterday's commission",
        weekreg: 'Number of registrants this week',
        weektrading: "This week's trading volume",
        weekcommiss: "This week's commission",
        mounthreg: 'Number of registrations this month',
        mounthtrading: "This month's trading volume",
        mounthcommiss: "This month's fee",
        isopenagent: 'Whether the agent can be promoted',
        Opening_time: 'Opening time',
        //2025.5.6
        evmaddress: 'EVM address',
        pseudoUID: 'Pseudo UID'
    },

    // 项目所有的表格
    forms: {
        rakeBackTime: 'Issuance Period',
        skipLinks: 'Jump Link',
        superiorInviteCode: 'Supervisor Invitation Code',
        commissionPercentage: 'Handling Fee Divided Commission Ratio',
        day: 'Days',
        yes: 'Yes',
        no: 'No',
        toViewProfitLoss: 'Whether you can view the profit and loss',
        catchPlate: 'Whether to open the analog disk',
        poundageMonitoring: 'Whether you can view the handling fee monitoring',
        canProAgent: 'Promotional agent',
        webBackRate: 'Display rebate ratio at the front end',
        positionMonitoring: 'Can view position monitoring',
        othersAPIpermissions: 'User API Management Permissions',
        allow: 'Allow',
        notAllow: 'Not allowed',
        needcode: 'Whether to need a verification code',
        traders: 'Can apply for a trader',
        buyFiat: 'Can purchase currency',
        sellFiat: 'Can sell currency',
        tradingToOneself: 'Rebate commission for your own transactions',
        desensitization: 'Non-direct push user desensitization',
        proStraightPush: 'Promote non-direct push',
        allUser: 'Show All Users',
        seeHomeData: 'Display homepage data',
        assetQuery: 'Can View Asset Inquiry',
        transactionStatus: 'Transaction Status',
        loginStatus: 'Login Status',
        mentionMoneyStatus: 'Withdrawal Status',
        mentionMoneyConfirm: 'Second Confirmation of Withdrawal',
        APIpermissions: 'API Management Permissions',
        resetCRMPass: 'Reset CRM Password',
        refuseReason: 'Reject Reason',
        FirstPrize: 'Proportion of First Prize',
        SecondPrize: 'Proportion of Second Prize',
        ThirdPrize: 'Proportion of Third Prize',
        selectLabel: 'Select Label',
        pleaseSelectLabel: 'Please select a label',
        selectContract: 'Select Contract',
        pleaseSelectContract: 'Please select a contract',
        someBad: 'Point Difference',
        pleaseUID: 'Please enter UID',
        inputContent: 'You can enter multiple agent IDs, separated by ","',
        client: 'Client',
        uploadPictures: 'Upload Image',
        point_to_link: 'BANNER points to the link',
        account: 'Account',
        grouping_name: 'Group Name',
        // 2021/12/29 new add
        safety_verify: 'Safety Verification',
        safety_verify_email: 'Email for security verification',
        input_format_error: 'Input format error',
        input_re_code: 'Please enter the verification code received by {email}',
        reGetcode: 'Reacquire',

        // 2022/02/24 新增
        Optional: 'optional',
        Mandatory: 'required',
        Digital_version_number: 'version of digital',
        Text_version_number: 'version of text',
        Download_address: 'Download link',
        Standby_address: 'Alternate link',
        Prompt_conten_In_Chinese: 'Tips content(Chinese)',
        Prompt_conten_In_English: 'Tips content(English)',
        Chinese_speed_address: 'Fast download link(zh)',
        Chinese_standby_address: 'Alternate link(zh)',
        English_speed_address: 'Fast download link(en)',
        English_standby_address: 'Alternate link(en)',
        Korean_speed_address: 'Fast download link(ko)',
        Korean_standby_address: 'Alternate link(ko)',
        Indonesia_speed_address: 'Fast download link(id)',
        Indonesia_standby_address: 'Alternate link(id)',
        Vietnam_speed_address: 'Fast download link(vi)',
        Vietnam_standby_address: 'Alternate link(vi)',

        // 2022/02/25 新增
        To_enable: 'Enable',
        Disable: 'Disable',
        Select_client: 'Select client',
        Select_language: 'Select language',
        Select_download_way: 'Select download format',
        // 2022/03/13 新增
        Select_the_language: 'Select language',
        ZHWithGoldName: 'Bonus name(zh)',
        ENWithGoldName: 'Bonus name(en)',
        TCWithGoldName: 'Bonus name(zh-TC)',
        KOWithGoldName: 'Bonus name(ko)',
        VNWithGoldName: 'Bonus name(vi)',
        IDWithGoldName: 'Bonus name(id)',
        ZHInstructions: 'Description(zh)',
        ENInstructions: 'Description(en)',
        TCInstructions: 'Description(zh-TC)',
        KOInstructions: 'Description(ko)',
        VNInstructions: 'Description(vi)',
        IDInstructions: 'Description(id)',
        Time_limit_for_recovery_transaction: 'Recycling transaction time limit',
        Effective_trading_volume: 'Valid transaction volume≥',
        Type_of_the_user_who_participates_in_the_event: 'Participating user type',
        Input_the_UID: 'Enter UID',
        Amount_of_grant_issued: 'Amount of bonus issued',
        Recovery_of_amount_of_the_bonus: 'Recovery Bonus Amount',

        // 2022/02/10 新增
        select_contract_Type: 'Select contract type',
        USDT_contract: 'USDT Contract',
        USD_contract: 'Coin-margined contracts',
    },

    // 项目中所有的弹框
    dialog: {
        language: 'Group Name',
        enableQualification: 'Whether to confirm to enable agent qualification',
        removeQualification: 'Whether to confirm to disqualify the agent qualification?',
        longContent: 'After the disqualification, the agency relationship below will not be disqualified, and the rebate of the agency below will still be normal',
        pleaseSelect: 'Please select',
        name: 'Name',
        surname: 'Last Name',
        IDnumber: 'Certificate Number',
        submitIDPhoto: 'Submit Certificate photo',
        dateOfBirth: 'Date of Birth',
        currentAge: 'Current Age',
        certificationStatus: 'Review Status',
        reviewResults: 'Review Results',
        reviewer: 'Reviewer',
        confirmInformation: 'Confirm Information',
        confirmWhetherResetCertification: 'Please confirm whether to reset the current certification',
        toApplyForNum: 'Application Quantity',
        accountNum: 'Receipt Quantity',
        cumulativeChargeMoney: 'Cumulative Currency Deposit',
        cumulativeFiatBuy: 'Cumulative Currency Buy',
        cumulativeReward: 'Cumulative Reward',
        cumulativeDrop: 'Cumulative Airdrop',
        cumulativeUnwindPNL: 'Cumulative Close Position PNL',
        cumulativePoundage: 'Cumulative Handling Fee',
        cumulativeCapitalCost: 'Cumulative Funding Expense',
        cumulativeWithdrawal: 'Cumulative Withdrawal Currency',
        cumulativeFiatSell: 'Cumulative Currency Sold',
        expectCanWithdrawal: 'Expected Withdrawable Currency',
        poundageCommissionDetail: 'Details of Handling Fee Commission Rebate',
        setHASH: 'Set HASH',
        tradingHASH: 'Transaction HASH',
        setJackpotAmount: 'Set Prize Pool Amount',
        jackpotAmount: 'Prize Pool Amount',
        startLessThanendTime: 'Start time must be less than end time',
        tooltip_content: 'The draw time must be within the start-end time interval',
        endGreaterThanstart: 'End time must be greater than start time',
        cumulativeWinningAmount: 'Cumulative winning amount',
        detail: 'Details',
        app_version: 'App Version',
        el_upload__text: 'Drag the file here, or',
        Image_format: 'Picture format must be in png format',
        max_upload: 'Up to 700kb can be uploaded',
        Image_size: 'Picture size: 1029*383',

        Set: 'setting',
        Error: 'Mistake',
        Prompt: 'Hint',
        Warning: 'Warn',
        Successful: 'Success',
        Add_a_success: 'Added successfully',
        Reset_the_success: 'Reset successfully',
        Operation_is_successful: 'Successful operation',
        Please_check_permissions: 'Please check permissions',
        The_login_timeout: 'Login timed out, please log in again',
        The_account_cannot_be_empty: 'Account cannot be empty',
        Please_select_a_group_name: 'Please select a group name',
        Please_fill_in_the_group_name: 'Please fill in the group name',
        The_input_box_cannot_be_empty: 'The input box cannot be empty',
        // 2021/11/30 新增
        Whether: 'Yes or No',
        Failure: 'Failed',
        Shelves: 'Launch',
        Level_1: '【Level 1】',
        The_user: 'Users',
        To_withdraw: 'Recall',
        Mandatory: 'Required',
        The_shelves: 'Remove',
        The_headline: 'Title ',
        Post_failure: 'Post Failed',
        The_announcement: 'The Announcement',
        Sure_to_delete: 'Confirm Delete',
        Whether_or_not_to: 'Yes or Not',
        Release_success: 'Release Succeed',
        Confirm_to_add: 'Are You Sure to Add?',
        Confirm_audit: 'Confirm to Authentication?',
        Whether_to_confirm: 'Confirm',
        Cancelled_delete: 'Canceled Delete',
        Delete_the_success: 'Delete Succeed',
        Confirm_deletion: 'Are You Sure to Delete?',
        Length_10: 'Maximum of 10 Characters',
        Uploaded_successfully: 'Uploaded Succeed',
        Please_enter_a_title: 'Please Enter The Title',
        Please_select_a_date: 'Please Select The Date',
        Publication_cancelled: 'Publication Canceled',
        Please_fill_in_the_UID: 'Please Fill In UID',
        Please_upload_pictures: 'Please Upload Image',
        Please_fill_in_remarks: 'Please Fill In Remarks',
        Please_select_the_time: 'Please Select The Time',
        Modification_cancelled: 'Canceled Edit',
        Image_insertion_failed: 'Image Insertion Failed',
        Important_announcement: 'Important Announcement?',
        Confirm_one_click_pass: 'Confirm One-click Pass?',
        Confirm_one_click_refuse: 'Confirm will reject all selected users?',
        Please_fill_in_the_text: 'Please Fill In The Text',
        Confirm_to_send_this_user: 'Confirm The User',
        Please_fill_in_the_title: 'Please Fill In The Title',
        Confirm_one_click_release: 'Confirm One-click Release?',
        Go_to_this_user_rebate: 'Canceled This User Referral?',
        Please_enter_delete_remarks: 'Please Enter Delete Remarks',
        Whether_to_add_the_bulletin: 'Do You Want to Add This Announcement?',
        You_want_to_delete_a_label: 'Are You Sure to Delete A Label?',
        Abnormal_changes: 'Abnormal Changes, Frequency Higher Than A Commission Sell Price',
        Whether_to_delete_the_bulletin: 'Do You Want to Delete This Announcement?',
        One_price_below_the_commission: 'Or Less Than A Commission Sell Price',
        The_minimum_validity_period_is_1: 'The minimum validity period is 1',
        Confirm_that_the_UID_is_set_to: 'Do You Confirm Add UID:',
        The_link_is_not_formatted_correctly: 'Link Formatted Incorrect',
        Are_you_sure_to_delete_the_UID: 'Do You Confirm Delete UID:',
        BANNER_Please_try_again: 'BANNER Upload Failed, Please Try Again',
        Please_confirm_whether_to_delete_it: 'Please Confirm Whether to Delete"',
        The_contract_point_spread_range_is: 'This Futures Point Difference Range Is',
        Do_you_want_to_delete_the_heading: 'Do You Want to Delete The Title:',
        Confirm_modification_of_the_bulletin: 'Are You Sure to Edit This Announcement?',
        The_minimum_risk_of_the_contract_is: 'The Minimum Risk of The Futures Is',
        Abnormal_conditions_exist: 'Exist Abnormal Situation, Please Find The Cause Immediately',
        Confirm_resetting_the_current_users: "Confirm Resetting The Current User's",
        Please_select_the_image_to_upload: 'Please Select The Image to Upload',
        Confirm_to_cancel_observing_the_user: 'Confirm to Cancel Observing The User',
        Please_enter_the_article_title: 'Please Enter 4-50 Characters for The Article Title',
        Contract_current_page_latest_price: 'Future Current Page Latest Price',
        Upload_images_in_PNG_format_only: 'Upload Images in PNG Format Only!',
        Min_less_than_Max: 'The Minimum Order Quantity of A Single Transaction Cannot Be Greater Than The Maximum Order Quantity of A Single Transaction!!!',
        Whether_you_want_to_delete_this_label: 'Do You Confirm to Delete The Label?',
        The_contract_procedures_rate_range_is: 'The Futures Transaction Fee Rate Range Is',
        Select_a_language_for_the_bulletin: 'Select A Language for The Announcement',
        Please_enter_the_correct_takedown_time: 'Please Enter The Correct Remove Time',
        Your_browser_does_not_support_sockets: 'Your Browser Does Not Support sockets',
        The_maximum_leverage_of_the_contract_is: 'The Maximum Leverage of The Futures Is',
        The_ratio_cannot_be_greater_than_95: 'The Percentage of Referral Should Not Exceed 95%',
        The_maximum_fund_rate_for_the_contract_is: 'The Maximum Funding Rate for The Futures Is',
        Are_you_sure_to_reset_the_account_password: 'Are You Sure to Reset Account Password?',
        Chinese_BANNER_Please_try_again: 'Chinese BANNER Upload Failed, Please Try Again',
        Are_you_sure_to_reset_your_Google_password: 'Are You Sure to Reset Google Password?',
        The_maximum_open_position_of_the_contract_is: 'The Maximum Position Quantity of The Futures Is',
        The_maximum_order_quantity_of_the_contract_is: 'The Maximum Open Order Quantity of The Futures Is',
        Please_jiaoyi_daochu_download: "Please Go To 'Transaction Inquiry/Export Download List' To Download",
        The_maximum_digit_of_the_processing_rate_is: 'The Maximum Digit of The Futures Transaction Fee Rate Is',
        The_maximum_point_difference_digit_of_the_contract_is: 'The Maximum Digit of The Futures Transaction Fee Rate Is',
        Position_greater_than_order_quantity: 'The Maximum Position Quantity Must Be Greater or Equal Than The Maximum Single Open Order Quantity!!!',
        Total_proportion_of_rewards_cannot_be_greater_than_100: 'The Total Proportion of Rewards Cannot be Greater Than 100%',
        Maximum_point_difference_is_greater_than_minimum_point_difference: 'The Maximum Point Difference Should Be Greater Than The Minimum Point Difference',
        The_size_of_uploaded_image_cannot_exceed_700K: 'The size of uploaded image cannot exceed 700K!',
        The_picture_is_not_up_to_standard: 'The image size of the uploaded file is not acceptable. It should be 640px wide and 700px high. Currently, the width and height of uploaded images are as follows:',

        // 2022/02/24 新增
        Edit_Android_new_Version: 'Edit Android new version',
        Edit_IOS_new_Version: 'Edit IOS new version',

        Please_enter_number_version: 'Please enter version number of digital',
        Please_enter_text_version: 'Please enter version number of text',
        Please_enter_download_address: 'Please enter download link',
        Please_enter_prompt_content: 'Please enter tips content',
        Please_enter_Chinese_speed_address: 'Please enter fast download link(zh)',
        Please_enter_Chinese_standby_address: 'Please enter Alternate link(zh)',
        Please_enter_English_speed_address: 'Please enter fast download link(en)',
        Please_enter_English_standby_address: 'Please enter Alternate link(en)',

        // 2022/02/25 新增
        To_view_links: 'View link',
        Edit_links: 'Edit link',
        Please_select_client: 'Please select a client',
        Please_select_language: 'Please select language',
        Please_select_download_way: 'Please select download format',
        Please_enter_appStore_link: "Please enter the App store's link",
        Please_enter_testFlight_link: "Please enter the TestFlight's link",
        Please_enter_googlePlay_link: "Please enter the Google Play's link",
        Edit_successful: 'Edit succeed',
        Whether_or_not_to_enable_this_version: 'Confirm this version number is enabled?',
        Whether_or_not_disable_this_version: 'Confirm this version number is disabled?',
        // 2022/03/13 新增
        Give_change_a_gold: 'Modify bonus',
        Give_new_a_gold: 'New bonus',
        Modifying_the_Language_description: 'Modify language description',
        Select_date_and_time: 'Select date',
        Please_select_the_active_user_type: 'Select user type',
        Please_select_a_language: 'Select language',
        The_name_English_and_Chinese_is_mandatory: 'The name of the bonus in Chinese and English is required',
        Please_select_ZHWithGoldName: 'Please enter bonus name in Chinese',
        Please_select_ENWithGoldName: 'Please enter bonus name in English',
        Please_select_TCWithGoldName: 'Please enter bonus name in traditional Chinese',
        Please_select_KOWithGoldName: 'Please enter bonus name in Korean',
        Please_select_VNWithGoldName: 'Please enter bonus name in Vietnamese',
        Please_select_IDWithGoldName: 'Please enter bonus name in Indonesian',
        Please_select_the_type_of_bonus: 'Select bonus type',
        Please_enter_a_time_limit: 'Please enter time limit',
        Please_select_the_expiration_time: 'Please select expiration time',
        Please_enter_the_expiration_date: 'Please enter receipt expired time',
        Please_enter_the_activation_time_limit: 'Please enter  activation time limit',
        Please_enter_the_recycle_transaction_time_limit: 'Please enter recycling time limit',
        Please_enter_closing_times: 'Please enter the number of close position',
        Please_enter_valid_trading_days: 'Please enter valid transaction days',
        Please_enter_valid_transaction_amount: 'Please enter a valid transaction volume',
        Please_enter_the_bonus: 'Please enter bonus',
        Please_input_the_friend_recharge_amount: "Please enter the friend's deposit amount",
        ZHInstructions: 'Please enter description in Chinese',
        ENInstructions: 'Please enter description in English',
        TCInstructions: 'Please enter description in Traditional Chinese',
        KOInstructions: 'Please enter description in Korean',
        VNInstructions: 'Please enter description in Vietnamese',
        IDInstructions: 'Please enter description in Indonesian',
        Have_you_confirmed_creation_activity: 'Do you confirm to create this activity?',
        Have_you_confirmed_modification_activity: 'Do you confirm to modify this activity?',
        Confirm_the_payment_of_experience_money: 'Do you confirm to hand out experience?',
        Distribution_of_success: 'Issued successfully',
        Whether_to_confirm_recovery_of_experience_money: 'Do you confirm to recycle experience?',
        Recycling_success: 'Recycling successfully',
        Please_enter_the_name_of_the_donation: 'Please enter bonus name',
        Please_enter_the_amount_of_the_grant: 'Please enter the distribution bonus amount',
        Please_enter_the_amount_of_refund: 'Please enter the recycling bonus amount',

        // 2022/03/14 新增
        Please_enter_the_uid: 'Please enter the UID',

        // 2022/03/16 新增
        Optional: 'Optional',
        Please_enter_the_recharge_amount: 'Please enter the recharge amount',
        Please_enter_the_reward_experience_money: 'Please enter reward trial fee',
        The_amount_than_zero: 'The payment amount must be greater than 0',
        The_amount_than_fifty: 'The payment amount cannot be greater than 50',
        Recycling_amount_than_zero: 'Recovery amount must be greater than 0',
        The_UID_you_entered_does_not_exist: 'The UID you entered does not exist',
        bind_channel_tip: 'Are you sure to bind this channel to the account whose username is "{name}" and whose UID is "{uid}"? ',
        bindTips: 'The user has bound the channel and cannot be bound'
    },

    // 页面所有的 button 文字
    buttons: {
        search: 'Search',
        cancel: 'Cancel',
        confirm: 'Confirm',
        confim_through: 'Confirm Pass',
        confim_refuse: 'Confirm Rejection',
        determine: 'OK',
        setLabal: 'Set Label',
        updLabal: 'Modify Label',
        delLabal: 'Delete Label',
        delete: 'Delete',
        modify: 'Modify',
        canTop: 'Cancel Topping',
        placedTop: 'Top',
        release: 'Release',
        addHomeFloatingLayer: 'Add Homepage Floating Layer',
        frame: 'Shelf',
        edit: 'Edit',
        remove: 'Remove',
        startUsing: 'Enable',
        remove_startUsing: 'Remove/Open',
        proTopAgent: 'Promote Top Agent',
        proAgent: 'Promote Agent',
        toView: 'View',
        reset: 'Reset',
        audit: 'Review',
        export: 'Export',
        download: 'Download',
        rejected: 'Rejected ',
        through: 'Pass',
        refuse: 'Reject',
        save: 'Save',
        summary: 'Summary',
        trial: 'Preliminary Review',
        recheck: 'Review',
        trial_through: 'Passed the Preliminary Review',
        trial_refuse: 'Preliminary Review Refused',
        recheck_through: 'Review Passed',
        recheck_refuse: 'Review Rejected',
        A_key_issue: 'One-click release',
        A_key_through: 'One Click Pass',
        A_key_refuse: 'One-click rejection',
        cancelIssue: 'Cancel Issuance',
        issue: 'Issuance',
        close: 'Close',
        chargeMoney: 'Recharge',
        copy: 'Copy',
        modifyLeaderboard: 'Modify the Leaderboard',
        activitiesSet: 'Activity Settings',
        HASH: 'HASH',
        setSmount: 'Set the Amount',
        determineInsert: 'Confirm to Insert',
        addLabel: 'Add Labei',
        viewAll: 'View All',
        seeYesterday: 'View Yesterday',
        view_IP_user_overview: 'View IP User Profile',
        view_IP_transaction_detailsv: 'View IP Transaction Fetails',
        add_suspected_user: 'Add Suspect User',
        cancel_watch: 'Cancel Observation',
        new_group: 'New Group',
        risk_control_whitelist: 'Risk Control Whitelist',
        add: 'Add',
        addBANNER: 'Add BANNER',
        all: 'All',
        public: 'Public',
        android: 'Android',
        IOS: 'IOS',
        WEB: 'WEB',
        clickOnUpload: 'Click to Upload',
        addAccount: 'Add Account',
        reset_passwords: 'Reset Password',
        reset_google: 'Reset Google',
        add_grouping: 'Add Group',
        add_delete: 'Add/Delete',
        edit_label: 'Edit Label',
        stand_up_down: 'On and off shelves',
        determine_modify: 'Confirm to modify',
        down: 'Off',
        up: 'On',
        withdraw_remove_rebate: 'Withdrawal removal of commission',
        eliminate: 'Delete',
        change_groups: 'Modify Group',
        has_been_to: 'Reach the top',
        have_what: 'Reach the end',
        move_up: 'Move up',
        move_down: 'Move down',
        on_the_cross: 'Uploading...',
        audit_through: 'Review Passed',
        audit_refuse: 'Review Rejected',
        audit_reset: 'Review Reset',
        await_audit: 'Waiting for Review',

        // 2022/02/25 新增
        Shop_address_management: 'Store address management',
        Create_new_version: 'Create new version',
        Edit_new_version: 'Edit version',
        // 2022/03/13 新增
        Language_description: 'Language description',
        // 2021/12/22 新增
        query: 'Inquire',
        tbxzh: 'Withdrawal limit',
        qxtbxzh: 'Cancel the withdrawal limit',
        bind: 'Bind administrator',
        
        // 2025/05/03
        approve: 'Approve',
    },

    // 登录
    login: {
        title: 'Login',
        ph_pass: 'Password',
        errTip_user: 'Please enter user name',
        errTip_pass: 'Please enter password',
        totp: 'Please enter Google verification code',
        code: 'Verification code',
        errTip_code: 'Verification code must be filled',
        newPassWord: 'New password',
        confirm_new_password: 'Confirm the new password',
        Scan_Google_QR_code: 'Scan the Google QR code below',
        next_step: 'Next step',

        // 2021/11/30 新增
        Log_out: 'Sign out',
        To_obtain: 'Re-acquire after',
        Change_password: 'Change Password',
        last_password_6: 'Password at least 6 digits',
        Length_6_16: 'The length must be 6~16 bits',
        Length_4_16: 'The length must be 4~16 bits',
        Enter_the_old_password: 'Enter the original password',
        Enter_the_new_password: 'Enter a new password',
        Obtaining_verification_code: 'Get verification code',
        Only_English_Number: 'Only English and numbers can be entered',
        Please_enter_the_old_password: 'Please fill in the original password',
        The_old_password_cannot_be_empty: 'The original password cannot be empty',
        Please_enter_your_password_again: 'Please enter the password again',
        The_two_passwords_are_inconsistent: 'The two passwords are inconsistent!',
        Please_confirm_the_new_password_again: 'Please confirm the new password again',
        Please_enter_the_new_password_6_16: 'Please fill in the new password with 6~16 digits',
    },

    // 其他
    others: {
        not_have: 'None',
        USDT: 'USDT',
        importantNotice: 'Important Announcement',
        ongoing: 'In progress',
        finished: 'Finished',
        releaseTime: 'Release Time',
        noData: 'No Data Yet',
        noDataPlacedTop: 'No Top Data Yet',
        articleTitle: 'Article Title',
        min_max: 'Enter 100 characters at most and 4 characters at least;',
        languageType: 'Language Type',
        systemColorMode: 'System Color Mode',
        bannerType: 'Banner Type',
        openDialog: 'Whether to open the pop-up display',
        straightMatter: 'Text',
        picture: 'Picture',
        pictureSize: 'Picture Size Requirement: 640 x 700 px',
        googleCode: 'Enter Google QR code',
        phone: 'Phone Number',
        googleValidation: 'Google Verification',
        email: 'Mail',
        moneyPassword: 'Fund Password',
        subordinateAgentNum: 'Number of Subordinate Agents',
        poundageCommissionPro: 'Commission Rebate Ratio',
        modifyContent: 'Modify Content',
        topAgent: 'Supervisor Agent ID',
        invitationNum: 'Number of invitations (including indirect invitations)',
        lastModifyTime: 'Last Modification Time',
        operator: 'Operator',
        identityInformation: 'Identity Information',
        walletAddress: '{name} Wallet Address',
        throughTime: 'Passing Time',
        paymentMethods: 'Payment Method',
        bank: 'Bank of Deposit',
        accountOpeningBranch: 'Sub-branch of Bank',
        paymentCode: 'Receipt Code',
        assets: 'Assets',
        walletAccountAssets: 'Wallet Account Assets (USDT)',
        availableBalance: 'Available Balance',
        freezeNum: 'Frozen Quantity',
        contractAccountAssets: 'Contract Account Assets (USDT)',
        accountRights: 'Account Equity',
        frozenMargin: 'Frozen Margin',
        unrealizedProfitandLoss: 'Unrealized Profit and Loss',
        documentaryAccountAssets: 'Documentary Account Assets (USDT)',
        othersOperation: 'Other Operations',
        operationTime: 'Operation Time',
        inIdentityAuthentication: 'Identity Authentication in progress',
        authenticationFailed: 'Identity Authentication Failed',
        passIdentityAuthentication: 'Identity Authentication Passed',
        certificationReset: 'Authentication Reset',
        piece: 'Sheet',
        Net_positions_PNL: 'Net Close PNL',
        unwind_PNL: 'Close PNL',
        contract_net_gold_interval: 'Contract Net Deposit Range',
        unwindNum: 'Number of Close Positions',
        unwind_number_interval: 'Range of the Number of Close Positions',
        profit_and_loss_interval: 'Profit and Loss Range',
        current_contract_rights: 'Current Contract Equity',
        risk: 'Risk Rate',
        wallet_account_financial_record: 'Wallet Account Financial Records',
        contract_account_financial_record: 'Contract Account Financial Records',
        documentary_account_financial_record: 'Documentary Account Financial Records',
        operation_after_no_error: 'Please operate carefully after confirming that the information is correct',
        noteOptional: 'Remarks (Optional)',
        chargeMoneyAddr: 'Currency Deposit Address',
        min_top_up_num: 'Minimum Recharge Amount',
        withdrawalNum: 'Amount in Withdrawal',
        User_Total: 'User_Total',
        platformWalletAccount: 'Platform Wallet Account',
        debtAssets: 'Liability Assets',
        platformContractAccount: 'Platform Contract Account',
        platformTransactionAccount: 'Platform Contract Account',
        platformAssets_Total: 'Platform Assets_Total',
        plaform_user_total: 'Platform + User_Total',
        level: 'Class',
        price_net_position_monitoring: 'Price and Net Position Monitoring',
        buy_a_price: 'Buy for One Price',
        latest_price: 'Latest Price',
        Sold_for_a_price: 'Sell for One Price',
        abnormal_price_alarm: 'Price Abnormal Alarm',
        exponential_stability_observation: 'Index Stability Observation Summary Table',
        fileName: 'File Name',
        linkAddress: 'Link Address',
        Google_validator: 'Google Authenticator',
        changeGoogle_h4_one: 'Open Google Authentication after installation, scan the QR code below or manually enter the secret key to get a six-digit verification code',
        changeGoogle_p_one: 'Please be sure to keep the Google verification key properly, so as not to change or lose your mobile phone number, which may make it impossible to change the binding',
        changeGoogle_p_two: 'Secret Key: TGOXJ3V2B5S7POF3',
        changeGoogle_h4_two: 'Please fill in the verification code you got into the input box below and complete the verification',
        changeGoogle_p_three: 'New Google Verification Code',
        Welcome: 'Welcome',
        system_automatically: 'System Automatic',
        Bank_card: 'Bank Card',
        Bank_card_num: 'Bank Card Number',
        Alipay: 'Alipay',
        Alipay_Account: 'Alipay Account:',
        WeChat: 'WeChat',
        WeChat_Account: 'WeChat Account:',
        Copy_success: 'Copy successfully',
        Modify_the_success: 'Successfully modified',
        rights: 'Rights',
        // 2021/12/21 new add
        qpcczc: '(Liquidation) Bankruptcy loss',
        pccczc: '(Close) Bankruptcy loss',
        liquidationGet: 'Liquidation injection',
        // 2022/07/05
        delUserLabel: 'Delete user label',
        addUserRisk: 'Add user risk control group',
        updUserRisk: 'Update user risk control group',
        setUserRiskWhite: 'Set user risk control whitelist',
        delUserRiskWhite: 'Delete user risk control whitelist',
        updUserRiskWhite: 'Modify user risk control group information',
        setUserTBAudit: 'Set user withdrawal audit limit',
        RemoveUserTBLimit: "Remove the user's withdrawal limit",
        // 2022/07/07
        xexzh: 'Small limit',
        gluid: 'Associated UID:',
        shctb: 'First withdrawal',
        qxzxzhyhlx: 'Please select restricted user type',
        qxzqxxzhyhlx: 'Please select unrestricted user type',
        xzhOption1: 'this user',
        xzhOption2: 'This agent and all subordinate users',
        // 2025/05/06
        categoryType: 'Category',
        // 2025/05/12
        noticeTabTitle: 'Notice Tab Title',
    },
    sysdict: {
        dictId: 'Dictionary ID',
        dictName: 'Dictionary Name',
        dictType: 'Dictionary Type',
        status: 'Status',
        remark: 'Remark',
        createTime: 'Creation Time',
        pleaseEnterDictName: 'Please enter the dictionary name',
        pleaseEnterDictType: 'Please enter the dictionary type',
        pleaseSelectStatus: 'Please select a status',
        pleaseEnterDictLabel: 'Please enter the data label',
        pleaseEnterDictSort: 'Please enter the dictionary sort order',
        pleaseEnterDictValue: 'Please enter the data value',
        normal: 'Normal',
        disabled: 'Disabled',
        addDictType: 'Add Dictionary Type',
        editDictType: 'Edit Dictionary Type',
        deleteDictType: 'Delete Dictionary Type',
        confirmDeleteDictType: 'Are you sure you want to delete the dictionary type?',
        pleaseEnterRemark: 'Please enter a remark',
        dictTypeRequired: 'Dictionary type cannot be empty',
        dictNameRequired: 'Dictionary name cannot be empty',
        dictLabelRequired: 'Dictionary label cannot be empty',
        dictSortRequired: 'Dictionary sort cannot be empty',
        dictValueRequired: 'Dictionary value cannot be empty',
        dictLabel: 'Data Label',
        dictValue: 'Data Value',
        dictSort: 'Display Order',
        dictStatus: 'Display Status',
        dictCode: 'Dictionary Code',
        pleaseSelectDictName: 'Please select a dictionary name',
        cssClass: 'CSS Class',
        pleaseEnterCssClass: 'Please enter CSS class',
        listClass: 'List Display Class',
        pleaseEnterListClass: 'Please enter list class name',
        isDefault: 'Is Default',
        addDictData: 'Add Dictionary Data',
        editDictData: 'Edit Dictionary Data',
        deleteDictData: 'Delete Dictionary Data',
        confirmDeleteDictData: 'Are you sure you want to delete the dictionary data?',
        pleaseEnterDictData: 'Please enter dictionary data',
        pleaseEnterDictDataSort: 'Please enter dictionary data sort order',
        pleaseSelectListClass: 'Please select a list display class',
    },
    sysdictdata: {
        dictName: 'Dictionary Name',
        dictType: 'Dictionary Type',
        dictValue: 'Dictionary Value',
        dictLabel: 'Dictionary Label',
        dictSort: 'Display Order',
        dictStatus: 'Status',
        pleaseEnterDictName: 'Please enter the dictionary name',
        pleaseEnterDictType: 'Please enter the dictionary type',
        pleaseEnterDictLabel: 'Please enter the dictionary label',
        pleaseEnterDictSort: 'Please enter the dictionary sort order',
        pleaseEnterDictValue: 'Please enter the dictionary value',
        pleaseSelectStatus: 'Please select a status',
        normal: 'Normal',
        disabled: 'Disabled',
    },
    common: {
        pleaseEnterDictName: 'Please enter the dictionary name',
        pleaseEnterDictType: 'Please enter the dictionary type',
        pleaseEnterDictLabel: 'Please enter the dictionary label',
        pleaseEnterDictSort: 'Please enter the dictionary sort order',
        pleaseEnterDictValue: 'Please enter the dictionary value',
        search: 'Search',
        add: 'Add',
        edit: 'Edit',
        delete: 'Delete',
        purgeRedis: 'Purge Redis',
        confirmDelete: 'Are you sure to delete?',
        cancel: 'Cancel',
        reset: 'Reset',
        confirm: 'Confirm',
        operation: 'Operation',
        index: 'No.',
        addSuccess: 'Added successfully',
        updateSuccess: 'Updated successfully',
        tip: 'Tip',
        deleteSuccess: 'Deleted successfully',
        deleteFailed: 'Delete failed',
        yes: 'Yes',
        no: 'No',
        copied: 'Copied to clipboard',
        copyFailed: 'Failed to copy',
        close: 'Close',
    },

    // feature/daniel-academycoursemanagement-0507
    course: {
        commissionTypes: {
            direct: 'Direct',
            indirect: 'Indirect',
        },
        forms: {
            add: 'Add Course',
            edit: 'Edit Course',
            detailsLinkRequired: 'Course link is required',
            portalLinkRequired: 'Portal image URL is required',
            validLink: 'Please enter a valid URL',
            tokenRequired: 'Token name is required',
            langRequired: 'At least one language entry is required',
            titleRequired: 'Course title is required',
            detailsRequired: 'Course details are required',
            showHidden: 'Show Hidden Notice Tabs',
            addTab: 'Add Notice Tab',
            addNotice: 'Add Notice',
            hiddenTab: 'Hide',
            show: 'Show',
            referralGraph: 'Referral Graph',
            maxDepth: 'Max Depth',
        },
        placeholders: {
            title: 'Enter Course Name',
            tokenName: 'Enter Token Name',
            start: 'Start Date',
            end: 'End Date',
            selectLang: 'Select Language',
            rebateId: 'Enter User ID',
            sourceId: 'Enter From User ID',
            commissionType: 'Enter Commission Type',
            vipLevel: 'Select VIP Level',
        },
        tableHeaders: {
            status: 'Status',
            title: 'Title',
            totalSeats: 'Total Seats',
            totalSold: 'Total Sold',
            amount: 'Amount',
            coin: 'Token',
            begin: 'Purchase Launch Date',
            end: 'Purchase Deadline',
            operation: 'Operation',
            details: 'Details',
            detailsLink: 'Course Link',
            portalImage: 'Portal Image',
            langContent: 'Multi-language Content',
            language: 'Language',
            purchaseTime: 'Purchase Date',
            txId: 'Transaction ID',
            rebateId: 'User ID',
            sourceId: 'From User ID',
            commissionType: 'Commission Type',
            payoutTime: 'Payout Time',
            purchasedCourses: 'Courses Purchased',
            directReferrals: 'Direct Referrals',
            teamSize: 'Team Size',
            vipLevel: 'VIP Level',
            vipTierName: 'VIP Tier Name',
            directReward: 'Direct Reward',
            indirectReward: 'Indirect Reward',
        },
    },

    // feature/daniel-launchpadmanagement-0529
    lang: {
        zhCN: 'Simplified Chinese',
        en: 'English',
        zhTW: 'Traditional Chinese',
        ko: 'Korean',
        ja: 'Japanese',
    },
    status: {
        all: 'All',
        aboutToStart: 'About To Start',
        inProgress: 'In Progress',
        normal: 'Normal',
        registrationClosed: 'Registration Closed',
        ended: 'Ended',
        unknown: 'Unknown',
    },
    launchpad: {
        dialogs: {
            redemptionApprove: 'Has the redemption review been approved?',
        },
        forms: {
            detailsLinkRequired: 'Details link is required.',
            portalLinkRequired: 'Portal image URL is required.',
            validLink: 'Please enter a valid URL.',
            tokenRequired: 'Token name is required.',
            langRequired: 'At least one language entry is required.',
            titleRequired: 'Project title is required.',
            detailsRequired: 'Project details are required.',
            view: 'View subscription details',
            pledgeCoinRequired: 'Pledged asset/currency is required',
            success: 'Successfully distributed',
            loading: 'Distributing in progress',
            fail: 'Distribution failed',
            noNeed: 'Do not',     
        },
        placeholders: {
            projectTitle: 'Please enter the project title',
            tokenName: 'Please enter the token name',
            begin: 'Start date',
            end: 'End date',
            selectLang: 'Select language',
            pricePerUnit: 'Price (per unit)',
            priceNotEntered: 'Please enter the price',
            selectToken: 'Select token',
            pledgeStatusNormal: 'Normal',
	        pledgeStatusReview: 'Redemption Under Review',
            pledgeStatusRedeeming: 'Redeeming',
	        pledgeStatusRedeemed: 'Redeemed',
            pledgeCoin: 'Enter pledged asset/currency',
            redeemable: 'Redeemable at any time',
        },
        tableHeaders: {
            begin: 'Start Date',
            end: 'End Date',
            status: 'Status',
            title: 'Project Title/Series',
            details: 'Details',
            coin: 'Token Name',
            totalVolume: 'Total Subscription Volume',
            offerPrice: 'Subscribe in USD (fake)',
            unitPrice: 'Price Unit',
            totalFundsRaised: 'Total Funds Raised',
            fundsRaised: 'Funds Raised',
            usersFundRaised: 'User Participation',
            operation: 'Operation',
            portal: 'Project Banner Image',
            language: 'Language',
            icon: 'Project Icon',
            price: 'Amount',
            offered: 'Purchase Coin Obtained',
            paymentAmount: 'Payment Amount',
            paymentCoin: 'Payment Coin',
            purchasePrice: 'Token Price',
            quantitySubscribed: 'Subscription Quantity',
            category: 'Pledge Category',
            pledgeCoin: 'Pledged Assets/Currency',
            baseCoins: 'Total Pledge Amount',
            realCoins: 'Total Available Liquidity',
            basePledges: 'Adulterated Pledge',
            realPledges: 'Actual Pledge Amount',
            startPledge: 'Pledge Start Time',
            endPledge: 'Pledge End Time',
            releasePledge: 'Pledge Release Time',
            distributionMethod: 'Distribution Model',
            yield: 'Yield Rate (%)',
            network: 'Blockchain',
            iconUrl: 'Icon',  
            pledgeStatus: 'Pledge Status',
            pledgeTime: 'Pledge Time',
            income: 'Income',
            actualIncome: 'Actual Income',
            redemptionInitiated: 'Redemption Initiated Time',
            redemptionRedeemed: 'Redemption Redeemed Time',
            redemptionState: 'Redemption State',
            automaticReviewStatus: 'Automatic Review Status',
        }
    },

    ...enLocale
}
