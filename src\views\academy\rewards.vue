<!-- src/views/academy/rewards.vue -->
<template>
	<div class="academy-container">
		<!-- Filter Bar -->
		<el-form :inline="true" :model="filters" class="filter-form">
			<el-form-item :label="$t('course.placeholders.rebateId')">
				<el-input v-model="filters.user_id" clearable />
			</el-form-item>
			<el-form-item :label="$t('course.placeholders.sourceId')">
				<el-input v-model="filters.from_user_id" clearable />
			</el-form-item>
			<el-form-item :label="$t('course.placeholders.commissionType')">
				<el-select v-model="filters.commission_type" clearable placeholder="All">
					<el-option :label="$t('course.commissionTypes.direct')" :value="1" />
					<el-option :label="$t('course.commissionTypes.indirect')" :value="2" />
				</el-select>
			</el-form-item>
			<el-form-item :label="$t('course.placeholders.start')">
				<el-date-picker v-model="filters.start" type="date" value-format="yyyy-MM-dd" />
			</el-form-item>
			<el-form-item :label="$t('course.placeholders.end')">
				<el-date-picker v-model="filters.end" type="date" value-format="yyyy-MM-dd" />
			</el-form-item>
			<el-form-item>
				<el-button type="primary" icon="el-icon-search" @click="handleSearch">
					{{ $t('buttons.search') }}
				</el-button>
			</el-form-item>
		</el-form>

		<!-- Rewards Table -->
		<el-table :data="tableData" border fit size="mini" class="rewards-table"
			:header-cell-style="{ background: '#F0F8FF', color: '#333', fontWeight: 'bold' }">
			<el-table-column :label="$t('course.tableHeaders.rebateId')" align="center" min-width="120">
				<template #default="{ row }">
					{{ row.user_id }}
				</template>
			</el-table-column>

			<el-table-column :label="$t('course.tableHeaders.sourceId')" align="center" min-width="120">
				<template #default="{ row }">
					{{ row.from_user_id }}
				</template>
			</el-table-column>

			<el-table-column :label="$t('course.tableHeaders.commissionType')" align="center">
				<template #default="{ row }">
					<el-tag
						:type="row.commission_type === 1 ? 'success' : row.commission_type === 2 ? 'info' : 'warning'">
						{{
							row.commission_type === 1
								? $t('course.commissionTypes.direct')
								: row.commission_type === 2
									? $t('course.commissionTypes.indirect')
									: '?'
						}}
					</el-tag>
				</template>
			</el-table-column>

			<el-table-column prop="price" :label="$t('course.tableHeaders.amount')" align="center" />

			<el-table-column prop="coin" :label="$t('course.tableHeaders.coin')" align="center" />

			<el-table-column prop="created_at" :label="$t('course.tableHeaders.payoutTime')" align="center">
				<template #default="{ row }">
					{{ formatUnix(row.created_at) }}
				</template>
			</el-table-column>

			<el-table-column prop="from_address" :label="$t('course.tableHeaders.txId')" align="center">
				<template #default="{ row }">
					<el-tooltip :content="row.tx_id || '-'" placement="top">
						<span>{{ shortenMiddle(row.tx_id || '-', 6) }}</span>
					</el-tooltip>
					<el-button v-if="row.tx_id" type="text" icon="el-icon-document-copy" size="small"
						@click="copyToClipboard(row.tx_id)" style="margin-left: 8px" />
				</template>
			</el-table-column>
		</el-table>

		<!-- 引入封装分页组件 -->
		<pagina-tion v-show="total > 0" :total="total" :page-sizes="[10, 50, 100, 300]" :page.sync="filters.pageNo"
			:limit.sync="filters.pagesize" @pagination="fetchData" />
	</div>
</template>

<script>
import { formatUnix } from '@/utils/time';
import { getRewards } from '@/api/academy';
import { copyToClipboard } from '@/utils/course';
import { shortenMiddle } from '@/utils/format';

export default {
	data() {
		return {
			filters: {
				user_id: '',
				from_user_id: '',
				commission_type: null,
				start: '',
				end: '',
				pageNo: 1,
				pagesize: 50
			},
			tableData: [],
			total: 0
		}
	},
	methods: {
		formatUnix,
		copyToClipboard,
		shortenMiddle,
		async fetchData() {
			try {
				const user_id = this.filters.user_id
				const from_user_id = this.filters.from_user_id
				const commissionType = this.filters.commission_type

				const res = await getRewards({
					pageNo: this.filters.pageNo,
					pagesize: this.filters.pagesize,
					user_id: commissionType !== '' && !isNaN(Number(user_id)) ? Number(user_id) : undefined,
					from_user_id: commissionType !== '' && !isNaN(Number(from_user_id)) ? Number(from_user_id) : undefined,
					start: this.filters.start,
					end: this.filters.end,
					commission_type: commissionType !== '' && !isNaN(Number(commissionType)) ? Number(commissionType) : undefined,
				})
				this.tableData = res.data.list || [];
				this.total = res.data.total;
			} catch (error) {
				console.error('Failed to fetch data', error);
			}
		},
		handleSearch() {
			this.filters.pageNo = 1;
			this.fetchData();
		},
	},
	mounted() {
		this.fetchData();
	}
}
</script>

<style>
.academy-container {
	padding: 20px;
}

.filter-form {
	margin-bottom: 20px;
}
</style>
