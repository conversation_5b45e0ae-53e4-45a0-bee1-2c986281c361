<template>
  <div class="daypnllist-cantainer">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        :placeholder="$t('filters.agent')"
        style="width: 160px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <span style="font-size: 12px; margin-left: 20px">{{$t('tableHeader.time')}}</span>
      <el-date-picker
        style="margin-left:5px;width: 220px; margin-top: 10px"
        v-model="filterTime"
        clearable
        size="mini" 
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change='filterTimeTransform'
      >
      </el-date-picker>

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-left: 20px;"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="openList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.contractName')" prop="contract_code" min-width="90px" align="center"></el-table-column>
      <el-table-column align="center" :label="$t('tableHeader.moreFlat')">
        <el-table-column :label="$t('tableHeader.moreFlatNum')" prop="close_buy_amount" min-width="90px" align="center"></el-table-column>
        <el-table-column :label="$t('tableHeader.moreFlatPNL')" prop="close_buy_pnl" min-width="90px" align="center"></el-table-column>
        <el-table-column :label="$t('tableHeader.longPositionsPoundage')" prop="buy_commission" min-width="90px" align="center"></el-table-column>
      </el-table-column>
      <el-table-column align="center" :label="$t('tableHeader.sidesJersey')">
        <el-table-column :label="$t('tableHeader.sidesJerseyNum')" prop="close_sell_amount" min-width="90px" align="center"></el-table-column>
        <el-table-column :label="$t('tableHeader.sidesJerseyPNL')" prop="close_sell_pnl" min-width="90px" align="center"></el-table-column>
        <el-table-column :label="$t('tableHeader.sidesJerseyPoundage')" prop="sell_commission" min-width="90px" align="center"></el-table-column>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.buyingSellingSingleDifferential')" prop="buy_sub_sell_amount" align="center" min-width="100px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.combinedPNL')" prop="total_pnl" align="center" min-width="100px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.combinedPNLPoundage')" prop="total_commission" align="center" min-width="100px"> </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,50,100,200,300]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
//封装的api
import { getpnlperday } from "@/api/fundQuery";
// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";
export default {
  name: "daypnllist",
  data() {
    return {
      listLoading: false,
      total: 0,
      openList: null,
      filterTime: [],
      listQuery: {
        sname: "",
        sagent: "",
        pageNo: 1,
        pagesize: 100,
        star: '', //开始
        end: '', //结束
      },
      accountTypeOptions: [
        { key: 1, name: this.$t('tableHeader.all_warehouse') },
        { key: 2, name: this.$t('tableHeader.by_warehouse') },
      ],
      sizeOptions: [
        { key: "B", name: this.$t('tableHeader.open_to_buy_more') },
        { key: "S", name: this.$t('tableHeader.sell_empty') },
      ],
      orderTypeObj: {
        0: this.$t('filters.Market_orders'),
        1: this.$t('tableHeader.order'), 
        2: this.$t('tableHeader.check_single'), 
        4: this.$t('tableHeader.stop_loss_orders'), 
        5: this.$t('filters.Strong_flat_sheet')
      },
    };
  },

  components: {},

  computed: {
    // 默认时间
    timeDefault () {
      let date = new Date()
      // 通过时间戳计算
      let defalutStartTime = ((date.getTime() - 1 * 24 * 3600 * 1000)/1000).toDate('yyyy-MM-dd') // 转化为时间戳
      let defalutEndTime = ((date.getTime()/1000)).toDate('yyyy-MM-dd')
      return [defalutStartTime, defalutEndTime]
    }
  },

  mounted() {
    this.filterTime = this.timeDefault;
    this.getList();
  },

  methods: {
    getList() {
      this.listLoading = true;
      let data = {}
      this.listQuery.star = (this.filterTime && this.filterTime[0]) || "";
      this.listQuery.end = this.filterTime ? this.filterTime[1] : "";
      Object.assign(data, this.listQuery)
      getpnlperday(data).then((res) => {
        this.openList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    filterTimeTransform(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val? val[1]:'';
    },
    handleClick() {
      this.getList()
    }
  },
};
</script>
<style lang="scss" scoped>
</style>