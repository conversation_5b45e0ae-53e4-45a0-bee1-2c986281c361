<!-- src/views/launchpad/index.vue -->
<template>
	<div class="launchpad-container">
		<!-- Filter Bar -->
		<el-form :inline="true" :model="filters" class="filter-form">
			<el-form-item>
				<el-input v-model="filters.title" :placeholder="$t(`launchpad.placeholders.projectTitle`)" clearable />
			</el-form-item>
			<el-form-item>
				<el-input v-model="filters.coin" :placeholder="$t(`launchpad.placeholders.tokenName`)" clearable />
			</el-form-item>
			<el-form-item :placeholder="$t(`launchpad.tableHeaders.pledgeStatus`)">
				<el-select v-model="filters.status" :placeholder="$t(`status.all`)" clearable>
					<el-option :label="$t(`status.aboutToStart`)" value="aboutToStart" />
					<el-option :label="$t(`status.inProgress`)" value="inProgress" />
					<el-option :label="$t(`status.ended`)" value="ended" />
				</el-select>
			</el-form-item>
			<el-form-item :label="$t(filters.startTime)">
				<el-date-picker v-model="filters.begin" type="date" :placeholder="$t(`launchpad.placeholders.begin`)" />
			</el-form-item>
			<el-form-item :label="$t(filters.endTime)">
				<el-date-picker v-model="filters.end" type="date" :placeholder="$t(`launchpad.placeholders.end`)" />
			</el-form-item>
			<el-form-item>
				<el-button type="primary" icon="el-icon-search" @click="fetchData">
					{{ $t('buttons.search') }}
				</el-button>
				<el-button type="success" icon="el-icon-plus" @click="openDialog('add')">
					{{ $t('buttons.add') }}
				</el-button>
			</el-form-item>
		</el-form>

		<!-- Table -->
		<el-table :data="filteredTableData" border fit size="mini" class="subscription-table"
			:row-class-name="tableRowClassName" :span-method="tableSpanMethod"
			:header-cell-style="{ background: '#F0F8FF', color: '#333', fontWeight: 'bold' }">
			<el-table-column prop="id" label="Id" width="80" align="center" />

			<el-table-column :label="$t(`launchpad.tableHeaders.status`)" align="center" min-width="100">
				<template #default="{ row }">
					<el-tag :type="getStateTagType(row)">
						{{ $t(`status.${getStatusText(row).replace(/\s+/g, '').charAt(0).toLowerCase() +
							getStatusText(row).replace(/\s+/g, '').slice(1)}`) }}
					</el-tag>
				</template>
			</el-table-column>

			<el-table-column prop="title" :label="$t('launchpad.tableHeaders.title')" align="center">
				<template #default="{ row }">
					{{ getSubscriptionTitle(row.langs) }}
				</template>
			</el-table-column>

			<el-table-column prop="coin" :label="$t('launchpad.tableHeaders.coin')" align="center" />

			<el-table-column :label="$t('launchpad.tableHeaders.network')" align="center">
				<template #default="{ row }">
					-
				</template>
			</el-table-column>
			<el-table-column :label="$t('launchpad.tableHeaders.totalVolume')" align="center">
				<template #default="{ row }">
					{{ formatNumber(row.total) }}
				</template>
			</el-table-column>

			<el-table-column :label="$t(`launchpad.tableHeaders.price`)" align="center">
				<template #default="{ row }">
					{{ row.priceItem.price }}
				</template>
			</el-table-column>
			<el-table-column :label="$t(`launchpad.tableHeaders.coin`)" align="center">
				<template #default="{ row }">
					{{ row.priceItem.coin }}
				</template>
			</el-table-column>

			<el-table-column :label="$t('launchpad.tableHeaders.offerPrice')" align="center">
				<template #default="{ row }">
					{{ formatNumber(row.offer_price) }} U
				</template>
			</el-table-column>

			<el-table-column :label="$t('launchpad.tableHeaders.totalFundsRaised')" align="center">
				<template #default="{ row }">
					{{ formatNumber(row.offered) }} U
				</template>
			</el-table-column>

			<el-table-column prop="users" :label="$t('launchpad.tableHeaders.usersFundRaised')" align="center" />

			<el-table-column :label="$t('launchpad.tableHeaders.begin')" align="center" min-width="150">
				<template #default="{ row }">
					{{ formatIsoDatetime(row.begin) }}
				</template>
			</el-table-column>

			<el-table-column :label="$t('launchpad.tableHeaders.end')" align="center" min-width="150">
				<template #default="{ row }">
					{{ formatIsoDatetime(row.end) }}
				</template>
			</el-table-column>

			<el-table-column :label="$t('launchpad.tableHeaders.operation')" align="center" min-width="250">
				<template slot-scope="{ row }">
					<template v-if="row._isFirstPrice">
						<el-button type="text" size="mini" icon="el-icon-search" @click="openDialog('view', row)">
							{{ $t('buttons.toView') }}
						</el-button>
						<el-button type="text" size="mini" icon="el-icon-edit" @click="openDialog('edit', row)">
							{{ $t('buttons.modify') }}
						</el-button>
						<el-button type="text" size="mini" icon="el-icon-delete" @click="handleDelete(row)">
							{{ $t('buttons.delete') }}
						</el-button>
					</template>
				</template>
			</el-table-column>
		</el-table>

		<!-- 引入封装分页组件 -->
		<pagina-tion v-show="filteredTotal > 0" :total="filteredTotal" :page-sizes="[10, 50, 100, 300]"
			:page.sync="filters.pageNo" :limit.sync="filters.pagesize" @pagination="fetchData" />

		<!-- Add / View Subscription Dialog -->
		<el-dialog class="subscription-form" :visible.sync="dialogVisible" :title="dialogMode === 'view'
			? $t('launchpad.forms.view')
			: dialogForm.id
				? $t('common.edit')
				: $t('common.add')" width="70%">
			<el-form ref="subscriptionForm" :model="dialogForm" :rules="rules" label-width="140px"
				:disabled="dialogMode === 'view'">
				<!-- Common Fields -->
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item :label="$t('launchpad.tableHeaders.coin')" prop="coin">
							<el-input v-model="dialogForm.coin" />
						</el-form-item>

						<el-form-item :label="$t('launchpad.tableHeaders.totalVolume')" prop="total"
							:class="{ 'long-label': $t('launchpad.tableHeaders.totalVolume').length > 12 }">
							<el-input-number v-model="dialogForm.total" :min="0" style="width: 100%" />
						</el-form-item>

						<el-form-item :label="$t('launchpad.tableHeaders.offerPrice')" prop="offer_price"
							:class="{ 'long-label': $t('launchpad.tableHeaders.offerPrice').length > 12 }">
							<el-input-number v-model="dialogForm.offerPrice" :min="0" :step="0.01"
								style="width: 100%" />
						</el-form-item>

						<el-form-item :label="$t('launchpad.tableHeaders.totalVolume')" prop="offered"
							:class="{ 'long-label': $t('launchpad.tableHeaders.totalVolume').length > 12 }">
							<el-input-number v-model="dialogForm.offered" :min="0" :step="0.01" style="width: 100%" />
						</el-form-item>
					</el-col>

					<el-col :span="12">
						<el-form-item :label="$t('launchpad.tableHeaders.icon')">
							<el-input v-model="dialogForm.icon" placeholder="https://..." />
						</el-form-item>

						<el-form-item :label="$t('launchpad.tableHeaders.portal')"
							:class="{ 'long-label': $t('launchpad.tableHeaders.portal').length > 12 }">
							<el-input v-model="dialogForm.portal" placeholder="https://..." />
						</el-form-item>

						<el-form-item :label="$t('launchpad.tableHeaders.usersFundRaised')" prop="users">
							<el-input-number v-model="dialogForm.users" :min="0" style="width: 100%" />
						</el-form-item>
					</el-col>
				</el-row>

				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item :label="$t('launchpad.tableHeaders.begin')" prop="begin">
							<el-date-picker v-model="dialogForm.begin" type="datetime"
								:placeholder="$t('launchpad.placeholders.begin')" value-format="timestamp"
								style="width: 100%" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item :label="$t('launchpad.tableHeaders.end')" prop="end">
							<el-date-picker v-model="dialogForm.end" type="datetime"
								:placeholder="$t('launchpad.placeholders.end')" value-format="timestamp"
								style="width: 100%" />
						</el-form-item>
					</el-col>
				</el-row>

				<!-- Multi-coin Section -->
				<el-divider content-position="left" />
				<div v-for="(coinEntry, index) in dialogForm.coins" :key="`coin-${index}`" class="mb-2"
					style="margin-bottom: 10px;">
					<el-form-item :label="$t('launchpad.placeholders.pricePerUnit')" :prop="'coins.' + index + '.price'"
						:rules="[{ required: true, message: $t('launchpad.forms.tokenRequired'), trigger: 'blur' }]">

						<el-input-number v-model="coinEntry.price" :min="0" :step="0.01" style="min-width: 200px" />

						<el-select v-model="coinEntry.coin" :placeholder="$t('launchpad.placeholders.selectToken')"
							style="width: 100px; margin-left: 10px;">
							<el-option v-for="opt in availableCoinOptions(index)" :key="opt.value" :label="opt.label"
								:value="opt.value" />
						</el-select>

						<el-button type="danger" icon="el-icon-delete" plain size="mini" style="margin-left: 10px;"
							@click="removeCoin(index)" :disabled="dialogForm.coins.length <= 1" />
					</el-form-item>
				</div>

				<el-form-item>
					<el-button type="primary" icon="el-icon-plus" plain size="mini" @click="addCoin"
						:disabled="dialogForm.coins.length >= coinOptions.length">
						{{ $t('common.add') }}
					</el-button>
				</el-form-item>

				<!-- Multi-language Section -->
				<el-divider content-position="left" />
				<div v-for="(lang, index) in dialogForm.langs" :key="`lang-${index}`" style="margin-bottom: 10px;">

					<el-form-item :label="$t('launchpad.tableHeaders.language')" :prop="'langs.' + index + '.lang_code'"
						:rules="[{ required: true, message: $t('launchpad.forms.langRequired'), trigger: 'change' }]">
						<el-select v-model="lang.lang_code" style="width: 200px"
							:placeholder="$t('launchpad.placeholders.selectLang')">
							<el-option v-for="opt in availableLangOptions(index)" :key="opt.value" :label="opt.label"
								:value="opt.value" />
						</el-select>
					</el-form-item>

					<el-form-item :label="$t('launchpad.tableHeaders.title')" :prop="'langs.' + index + '.title'"
						:rules="[{ required: true, message: $t('launchpad.forms.titleRequired'), trigger: 'blur' }]"
						:class="{ 'long-label': $t('launchpad.tableHeaders.title').length > 12 }">
						<el-input v-model="lang.title" :placeholder="$t('launchpad.tableHeaders.title')" />
					</el-form-item>

					<el-form-item :label="$t('launchpad.tableHeaders.details')" :prop="'langs.' + index + '.details'"
						:rules="[{ required: true, message: $t('launchpad.forms.detailsRequired'), trigger: 'blur' }]">
						<el-input type="textarea" v-model="lang.details" :rows="5" />
					</el-form-item>

					<el-button v-if="dialogForm.langs.length > 1" type="danger" icon="el-icon-delete" plain size="mini"
						@click="removeLang(index)">
						{{ $t('common.delete') }}
					</el-button>
				</div>

				<el-form-item>
					<el-button type="primary" icon="el-icon-plus" plain size="mini" @click="addLang"
						:disabled="dialogForm.langs.length >= langOptions.length">
						{{ $t('common.add') }}
					</el-button>
				</el-form-item>
			</el-form>

			<!-- Footer -->
			<div slot="footer" class="dialog-footer">
				<template v-if="dialogMode === 'view'">
					<el-button @click="dialogVisible = false">{{ $t('common.close') }}</el-button>
				</template>
				<template v-else>
					<el-button @click="dialogVisible = false">{{ $t('common.cancel') }}</el-button>
					<el-button type="primary" @click="handleSubmit">
						{{ dialogMode === 'edit' ? $t('common.confirm') : $t('common.add') }}
					</el-button>
				</template>
			</div>
		</el-dialog>
	</div>
</template>

<script>
const defaultDialogForm = {
	id: null,
	title: '',
	begin: '',
	coin: '',
	totalSubscription: 0,
	unitPrice: 0,
	totalRaised: 0,
	end: '',
	icon: '',
	portal: '',
	langs: [{ title: '', details: '', lang_code: 0 }],
	coins: [{ coin: 'USDT', price: 0, }]
};

import { getSubscriptionList, addSubscription, updateSubscription, deleteSubscription } from '@/api/launchpad';
import { getSubscriptionTitle } from '@/utils/launchpad';
import { formatIsoDatetime } from '@/utils/time';
import { formatNumber } from '@/utils/format';

export default {
	name: 'LaunchPadIndex',
	data() {
		return {
			filters: {
				title: '',
				coin: '',
				status: '',
				begin: null,
				end: null,
				pageNo: 1,
				pagesize: 10
			},
			tableData: [],
			total: 0,
			dialogVisible: false,
			dialogForm: defaultDialogForm,
			coinOptions: [
				{ label: 'USDT', value: 'USDT' },
				{ label: 'USDC', value: 'USDC' },
			],
			langOptions: [
				{ value: 0, label: this.$t('lang.zhCN') },
				{ value: 1, label: this.$t('lang.en') },
				{ value: 2, label: this.$t('lang.zhTW') },
				{ value: 3, label: this.$t('lang.ko') },
				{ value: 4, label: this.$t('lang.ja') }
			],
			dialogMode: 'add', // or 'edit', 'view'
			rules: {},
		}
	},
	methods: {
		getSubscriptionTitle,
		formatIsoDatetime,
		formatNumber,
		getStatus(item) {
			const now = Date.now();
			const start = new Date(item.begin).getTime();
			const end = new Date(item.end).getTime();
			if (now < start) return 'aboutToStart';
			if (now >= start && now <= end) return 'inProgress';
			return 'ended';
		},
		async fetchData() {
			try {
				// Reset pageNo if any filter is applied
				const hasFilter = Object.entries(this.filters).some(
					([key, value]) =>
						!['pageNo', 'pagesize'].includes(key) &&
						value !== '' &&
						value !== null &&
						value !== undefined
				);
				if (hasFilter) {
					this.filters.pageNo = 1;
				}
				await getSubscriptionList(this.filters).then(({ data }) => {
					let groupIndex = 0;
					const flattened = [];
					const list = data.list || [];
					list.forEach((offer) => {
						if (!Array.isArray(offer.coins)) return;

						offer._groupColorIndex = groupIndex;

						offer.coins.forEach((priceItem, index) => {
							flattened.push({
								...offer,
								_isFirstPrice: index === 0,
								_priceRowSpan: offer.coins.length,
								priceItem,
							});
						});

						groupIndex++;
					});

					// console.log(JSON.stringify(flattened))
					this.tableData = flattened;
					this.total = data.total;
				});
			} catch (err) {
				console.error('Failed to fetch subscriptions:', err);
			}
		},
		getStatusText(row) {
			const status = this.getStatus(row);
			switch (status) {
				case 'aboutToStart': return 'aboutToStart';
				case 'inProgress': return 'inProgress';
				case 'ended': return 'ended';
				default: return 'Unknown';
			}
		},
		getStateTagType(row) {
			const status = this.getStatus(row);
			return status === 'inProgress' ? 'success' : status === 'ended' ? 'info' : 'warning';
		},
		openDialog(mode, row = null) {
			// console.log('openDialog called with:', mode, row);
			this.dialogMode = mode;
			const start = new Date(row?.begin).getTime(); // from ISO string to timestamp
			const end = new Date(row?.end).getTime(); // from ISO string to timestamp
			this.dialogForm = row
				? {
					...row,
					begin: start,
					end: end
				}
				: defaultDialogForm;
			this.dialogVisible = true;
		},
		availableCoinOptions(index) {
			const used = this.dialogForm.coins
				.map((coin, i) => i === index ? null : coin.coin)
				.filter(c => c !== null && c !== undefined);

			return this.coinOptions.filter(opt =>
				!used.includes(opt.value) || opt.value === this.dialogForm.coins[index].coin
			);
		},
		addCoin() {
			const used = this.dialogForm.coins.map(c => c.coin);
			const available = this.coinOptions
				.map(opt => opt.value)
				.filter(coin => !used.includes(coin));

			if (available.length > 0) {
				this.dialogForm.coins.push({
					coin: available[0],
					price: 0
				});
			}
		},
		removeCoin(index) {
			this.dialogForm.coins.splice(index, 1)
		},
		availableLangOptions(index) {
			const used = this.dialogForm.langs
				.map((lang, idx) => idx === index ? null : lang.lang_code)
				.filter(code => code !== null && code !== undefined);

			return this.langOptions.filter(opt =>
				!used.includes(opt.value) || opt.value === this.dialogForm.langs[index].lang_code
			);
		},
		addLang() {
			this.dialogForm.langs.push({ lang_code: null, title: '', details: '' });
		},
		removeLang(index) {
			this.dialogForm.langs.splice(index, 1);
		},
		handleSubmit() {
			if (this.dialogMode === 'view') {
				this.dialogVisible = false;
				return;
			}

			const payload = {
				...this.dialogForm,
				begin: Math.floor(new Date(this.dialogForm.begin).getTime() / 1000),
				end: Math.floor(new Date(this.dialogForm.end).getTime() / 1000),
				coins: this.dialogForm.coins.map((c) => ({ coin: c.coin, price: Number(c.price) })),
			};

			const action = this.dialogMode === 'edit'
				? updateSubscription(payload)
				: addSubscription(payload);

			action.then(() => {
				this.dialogVisible = false;
				this.fetchData();
			});
		},
		handleDelete(row) {
			this.$confirm('Delete this subscription offer?', this.$t('dialog.Prompt'), {
				confirmButtonText: this.$t('buttons.determine'),
				cancelButtonText: this.$t('buttons.cancel'),
				type: "warning",
			}).then(() => {
				var data = {
					id: JSON.parse(row.id)
				};
				deleteSubscription(data).then(async (res) => {
					// console.log(res)
					if (this.tableData.length - 1 == 0) {
						this.currentPage -= 1;
						this.currentPage = this.currentPage < 1 ? 1 : this.currentPage;
					}
					await this.fetchData();
					this.$message({
						type: "error",
						message: this.$t('dialog.Delete_the_success'),
						title: this.$t('dialog.Prompt'),
					});
				});
			})
		},
		tableSpanMethod({ row, column, rowIndex, columnIndex }) {
			// Define column indices that require rowspan
			const mergeCols = [0, 1, 2, 3, 4, 5, 9, 10, 11, 12, 13]; // Adjust as per actual column order

			if (!mergeCols.includes(columnIndex)) {
				return [1, 1]; // Normal cell
			}

			if (row._isFirstPrice) {
				return [row._priceRowSpan, 1]; // rowspan, colspan
			} else {
				return [0, 0]; // merged cell, don't render
			}
		},
		tableRowClassName({ row, rowIndex }) {
			if (row._isFirstPrice) {
				this._currentStripeIndex = (this._currentStripeIndex || 0) + 1;
			}
			const groupIndex = this._currentStripeIndex || 1;
			return groupIndex % 2 === 1 ? 'group-row-odd' : 'group-row-even';
		},
	},
	computed: {
		filteredRawData() {
			// Filter first, no pagination
			if (!this.filters.status) return this.tableData;
			return this.tableData.filter(item => this.filters.status === this.getStatus(item));
		},
		filteredTableData() {
			// Paginate the filtered data
			const start = (this.filters.pageNo - 1) * this.filters.pagesize;
			const end = start + this.filters.pagesize;
			return this.filteredRawData.slice(start, end);
		},
		filteredTotal() {
			// Total number of filtered items (not paginated)
			return this.filteredRawData.length;
		},
	},
	mounted() {
		this.fetchData();
	},
}
</script>

<style>
.launchpad-container {
	padding: 20px;
}

.filter-form {
	margin-bottom: 20px;
}

.mb-2 {
	margin-bottom: 8px;
}

.subscription-table .group-row-even {
	background-color: #f5f6f7 !important;
}

.subscription-table .group-row-odd {
	background-color: #ffffff !important;
}

.subscription-table .el-table__row:hover>td {
	background-color: inherit !important;
	transition: none;
}

.subscription-table th .cell {
	white-space: normal !important;
	word-wrap: break-word !important;
	word-break: break-word !important;
}

.subscription-form .el-form-item.long-label .el-form-item__label {
	white-space: normal;
	word-wrap: break-word;
	word-break: break-word;
	line-height: 1.2;
}
</style>