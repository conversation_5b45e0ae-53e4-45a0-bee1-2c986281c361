<template>
  <div class="spotAssetsQuery">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.uid"
        :placeholder="$t('tableHeader.uid')"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.coinname"
        :placeholder="$t('filters.currency')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in coinnameOptions"
          :key="item.key"
          :label="item.currencyname"
          :value="item.currencyname"
        />
      </el-select>

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-top: 10px"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="levelList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column prop="userid"        :label="$t('tableHeader.uid')" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.userid || '--' }}</span>
        </template>
      </el-table-column>
       <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.user_name || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topID')" prop="top_agent_id" align="center" min-width="78"> 
        <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topNick')" prop="petname" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorID')" prop="pareid" align="center" min-width="78">
         <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="currency_name" :label="$t('filters.currency')" align="center" min-width="78">
        <template slot-scope="{ row }">
          <div v-for="(item,index) in row.futuresaccountlist" :key="index">{{ item.currency_name }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="balance"       :label="$t('others.assets')" align="center" min-width="78">
        <template slot-scope="{ row }">
          <div v-for="(item,index) in row.futuresaccountlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.balance }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="available"     :label="$t('tableHeader.available')" align="center" min-width="78">
        <template slot-scope="{ row }">
          <div v-for="(item,index) in row.futuresaccountlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.available }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="lock_amount"   :label="$t('tableHeader.freeze')" align="center" min-width="78">
        <template slot-scope="{ row }">
          <div v-for="(item,index) in row.futuresaccountlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.lock_amount }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="totalin,totalout"       :label="$t('tableHeader.net_into')" align="center" min-width="78">
        <template slot-scope="{ row }">
          <div v-for="(item,index) in row.futuresaccountlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.totalin - item.totalout }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="totalin"       :label="$t('tableHeader.Total_transfer')" align="center" min-width="78">
        <template slot-scope="{ row }">
          <div v-for="(item,index) in row.futuresaccountlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.totalin }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="totalout"      :label="$t('tableHeader.Total_outgoing')" align="center" min-width="78">
        <template slot-scope="{ row }">
          <div v-for="(item,index) in row.futuresaccountlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.totalout }}</div>
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,20,30,50]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getprocoinList } from "@/api/user";
import { accountinfo } from "@/api/spotTrading"
export default {
  name: "spotAssetsQuery",
  data() {
    return {
      listLoading: false,
      total: 0,
      levelList: null,
      listQuery: {
        uid: "",        // UID
        coinname: "",   // 币种
        pageNo: 1,
        pagesize: 10,
      },
      coinnameOptions: [],   // 币种
    };
  },

  components: {},

  computed: {},

  mounted() {
    getprocoinList({}).then((res) => {
      this.coinnameOptions = res.data.filter(v=>v.status)
    })
    this.getList();
  },

  methods: {
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    // 获取数据
    getList() {
      this.listLoading = true;
      let data = {};
      Object.assign(data, {
        uid: this.listQuery.uid,                // uid
        coinname: this.listQuery.coinname,      // 币种
        pageNo: this.listQuery.pageNo,          // int 页数
        pagesize: this.listQuery.pagesize,      // int 分页数量
      })
      accountinfo(data).then((res) => {
        // console.log(res)
        this.levelList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      })
    },
  },
};
</script>

<style lang="scss" scoped>
</style>