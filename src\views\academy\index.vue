<!-- src/views/academy/index.vue -->
<template>
	<div class="academy-container">
		<!-- Filter Bar -->
		<el-form :inline="true" :model="filters" class="filter-form">
			<el-form-item>
				<el-input v-model="filters.courseName" :placeholder="$t(`course.placeholders.title`)" clearable />
			</el-form-item>
			<el-form-item>
				<el-input v-model="filters.coin" :placeholder="$t(`course.placeholders.tokenName`)" clearable />
			</el-form-item>
			<el-form-item label="Start Date">
				<el-date-picker v-model="filters.start" type="date" :placeholder="$t(`course.placeholders.start`)"
					value-format="yyyy-MM-dd" />
			</el-form-item>
			<el-form-item label="End Date">
				<el-date-picker v-model="filters.end" type="date" :placeholder="$t(`course.placeholders.end`)"
					value-format="yyyy-MM-dd" />
			</el-form-item>
			<el-form-item>
				<el-button type="primary" icon="el-icon-search" @click="handleSearch">
					{{ $t('buttons.search') }}
				</el-button>
				<!-- <el-button type="success" icon="el-icon-plus" @click="openAddDialog">{{ $t('buttons.add') }}</el-button> -->
			</el-form-item>
		</el-form>

		<!-- Course Table -->
		<el-table :data="courseList" border fit size="mini" class="course-table" :row-class-name="tableRowClassName"
			:header-cell-style="{ background: '#F0F8FF', color: '#333', fontWeight: 'bold' }"
			:span-method="tableSpanMethod">
			<el-table-column :label="$t(`course.tableHeaders.status`)" align="center" min-width="80">
				<template #default="{ row }">
					<el-tag :type="getStateTagType(row)">
						{{ $t(`status.${computeState(row).replace(/\s+/g, '').charAt(0).toLowerCase() +
							computeState(row).replace(/\s+/g, '').slice(1)}`) }}
					</el-tag>
				</template>
			</el-table-column>

			<el-table-column :label="$t(`course.tableHeaders.title`)" align="center" min-width="180">
				<template #default="{ row }">
					{{ getCourseTitle(row.langs) }}
				</template>
			</el-table-column>

			<el-table-column :label="$t(`course.tableHeaders.totalSeats`)" align="center" width="120">
				<template #default="{ row }">
					{{ row.target_share }}
				</template>
			</el-table-column>

			<el-table-column :label="$t(`course.tableHeaders.totalSold`)" align="center" width="120">
				<template #default="{ row }">
					{{ row.join_num }}
				</template>
			</el-table-column>

			<el-table-column :label="$t(`course.tableHeaders.amount`)" align="center" width="100">
				<template #default="{ row }">
					{{ row.priceItem.val }}
				</template>
			</el-table-column>
			<el-table-column :label="$t(`course.tableHeaders.coin`)" align="center" width="100">
				<template #default="{ row }">
					{{ row.priceItem.coin }}
				</template>
			</el-table-column>

			<el-table-column :label="$t(`course.tableHeaders.begin`)" align="center" min-width="160">
				<template #default="{ row }">
					{{ formatUnix(row.begin_at) }}
				</template>
			</el-table-column>

			<el-table-column :label="$t(`course.tableHeaders.end`)" align="center" min-width="160">
				<template #default="{ row }">
					{{ formatUnix(row.end_at) }}
				</template>
			</el-table-column>

			<el-table-column :label="$t(`course.tableHeaders.operation`)" align="center" width="100">
				<template #default="{ row }">
					<el-button size="mini" type="text" icon="el-icon-edit" @click="openEditDialog(row)">
						{{ $t('buttons.modify') }}
					</el-button>
				</template>
			</el-table-column>
		</el-table>

		<!-- 引入封装分页组件 -->
		<pagina-tion v-show="total> 0" :total="total" :page-sizes="[10, 50, 100, 300]" :page.sync="filters.pageNo"
			:limit.sync="filters.pagesize" @pagination="fetchData" />

		<!-- Add/Edit Dialog -->
		<el-dialog :visible.sync="courseDialogVisible"
			:title="isEditing ? $t('course.forms.edit') : $t('course.forms.add')" width="600px">
			<el-form ref="courseForm" :model="editingCourse" :rules="rules" label-width="120px">
				<!-- Course Details -->
				<el-form-item :label="$t(`course.tableHeaders.detailsLink`)" prop="detail_link">
					<el-input v-model="editingCourse.detail_link" />
				</el-form-item>

				<el-form-item :label="$t(`course.tableHeaders.totalSeats`)">
					<el-input-number v-model="editingCourse.target_share" :min="0" style="min-width: 220px" />
				</el-form-item>

				<el-form-item v-if="isEditing" :label="$t(`course.tableHeaders.totalSold`)">
					<el-input-number v-model="editingCourse.join_num" :min="0" style="min-width: 220px" />
				</el-form-item>

				<el-form-item :label="$t(`course.tableHeaders.portalImage`)" prop="portal_image">
					<el-input v-model="editingCourse.portal_image" />
				</el-form-item>

				<el-form-item :label="$t(`course.tableHeaders.begin`)" class="label-multi-line">
					<el-date-picker v-model="editingCourse.begin_at" type="datetime"
						:placeholder="$t(`course.placeholders.start`)" value-format="timestamp" />
				</el-form-item>

				<el-form-item :label="$t(`course.tableHeaders.end`)" class="label-multi-line">
					<el-date-picker v-model="editingCourse.end_at" type="datetime"
						:placeholder="$t(`course.placeholders.end`)" value-format="timestamp" />
				</el-form-item>

				<!-- Multi-language Content -->
				<el-divider content-position="left" />
				<div v-for="(lang, index) in editingCourse.langs" :key="`${index}-${lang.lang_code}`"
					style="border: 1px solid #ebeef5; padding: 16px; margin-bottom: 16px; border-radius: 4px;">
					<el-form-item :label="$t('course.tableHeaders.language')" :prop="'langs.' + index + '.lang_code'"
						:rules="[{ required: true, message: $t('course.forms.langRequired'), trigger: 'change' }]">
						<el-select v-model="lang.lang_code" :placeholder="$t('course.placeholders.selectLang')"
							style="width: 200px">
							<el-option v-for="option in availableLangOptions(index)" :key="`${index}-${option.value}`"
								:label="option.label" :value="option.value" />
						</el-select>
					</el-form-item>

					<el-form-item :label="$t('course.tableHeaders.title')" :prop="'langs.' + index + '.title'"
						:rules="[{ required: true, message: $t('course.forms.titleRequired'), trigger: 'blur' }]">
						<el-input v-model="lang.title" :placeholder="$t('course.placeholders.title')" />
					</el-form-item>

					<el-form-item :label="$t('course.tableHeaders.details')" :prop="'langs.' + index + '.details'"
						:rules="[{ required: true, message: $t('course.forms.detailsRequired'), trigger: 'blur' }]">
						<el-input type="textarea" v-model="lang.details" :rows="3" />
					</el-form-item>

					<el-button type="danger" icon="el-icon-delete" plain size="mini" @click="removeLang(index)"
						v-if="editingCourse.langs.length > 1">
						{{ $t('common.delete') }}
					</el-button>
				</div>

				<el-form-item>
					<el-button type="primary" icon="el-icon-plus" plain size="mini" @click="addLang"
						:disabled="editingCourse.langs.length >= langOptions.length">
						{{ $t('common.add') }}
					</el-button>
				</el-form-item>

				<!-- Pricing -->
				<el-divider content-position="left" />
				<div v-for="(price, index) in editingCourse.prices" :key="index" class="mb-2"
					style="margin-bottom: 10px;">
					<el-form-item :label="$t(`course.tableHeaders.amount`)" :prop="'prices.' + index + '.coin'"
						:rules="[{ required: true, message: $t('course.forms.tokenRequired'), trigger: 'blur' }]">
						<el-input-number v-model="price.val" :min="0" :step="0.01" style="min-width: 220px" />
						<el-input v-model="price.coin" :placeholder="$t(`course.placeholders.tokenName`)"
							style="width: 100px; margin-right: 10px" />
						<el-button type="danger" icon="el-icon-delete" plain size="mini"
							@click="editingCourse.prices.splice(index, 1)" style="margin-left: 10px;"
							:disabled="editingCourse.prices.length <= 1" />
					</el-form-item>
				</div>

				<el-form-item>
					<el-button type="primary" icon="el-icon-plus" plain size="mini" @click="addPrice"
						:disabled="editingCourse.prices.length >= 3">
						{{ $t('common.add') }}
					</el-button>
				</el-form-item>
			</el-form>

			<div slot="footer" class="dialog-footer">
				<el-button @click="courseDialogVisible = false">{{ $t('common.cancel') }}</el-button>
				<el-button type="primary" @click="saveCourse">
					{{ isEditing ? $t('common.confirm') : $t('common.add') }}
				</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import { formatUnix } from '@/utils/time';
import { getCourseList, /*addCourse,*/ updateCourse } from '@/api/academy';
import { getCourseTitle } from '@/utils/course';

export default {
	name: 'AcademyIndex',
	data() {
		return {
			datatotal: 0, //总数据条数
			filters: {
				coin: '',
				courseName: '',
				start: '',
				end: '',
				courseLang: '',
				pageNo: 1,
				pagesize: 50
			},
			langOptions: [
				{ value: 0, label: this.$t('lang.zhCN') },
				{ value: 1, label: this.$t('lang.en') },
				{ value: 2, label: this.$t('lang.zhTW') },
				{ value: 3, label: this.$t('lang.ko') },
				{ value: 4, label: this.$t('lang.ja') }
			],
			courseList: [],
			total: 0,
			courseDialogVisible: false,
			isEditing: false,
			editingCourse: {
				detail_link: '',
				target_share: 0,
				join_num: 0,
				portal_image: '',
				begin_at: 0,
				end_at: 0,
				prices: [{ coin: '', val: 0 }],
				langs: [
					{
						lang_code: '',
						title: '',
						details: ''
					}
				]
			},
			rules: {
				detail_link: [
					{ required: true, message: this.$t('course.forms.detailsLinkRequired'), trigger: 'blur' },
					{ type: 'url', message: this.$t('course.forms.validLink'), trigger: 'blur' }
				],
				portal_image: [
					{ required: true, message: this.$t('course.forms.portalLinkRequired'), trigger: 'blur' },
					{ type: 'url', message: this.$t('course.forms.validLink'), trigger: 'blur' }
				],
			}
		};
	},
	mounted() {
		this.fetchData();
	},
	methods: {
		formatUnix,
		async fetchData() {
			try {
				const response = await getCourseList({
					pageNo: this.filters.pageNo,
					pagesize: this.filters.pagesize,
					coin: this.filters.coin,
					course_name: this.filters.courseName,
					start: this.filters.start,
					end: this.filters.end,
					lang_code: (
						this.filters.courseLang !== '' &&
						!isNaN(Number(this.filters.courseLang))
					) ? Number(this.filters.courseLang) : null, // 0-简体中文，1-英文，2-繁体中文，3-韩文，4-日文
				});

				let groupIndex = 0;
				const flattened = [];
				const list = response.data.list || [];
				list.forEach((course) => {
					if (!Array.isArray(course.prices)) return;

					course._groupColorIndex = groupIndex;

					course.prices.forEach((priceItem, index) => {
						flattened.push({
							...course,
							_isFirstPrice: index === 0,
							_priceRowSpan: course.prices.length,
							priceItem,
						});
					});

					groupIndex++;
				});

				this.courseList = flattened;
				this.datatotal = response.data.total || 0;
				// console.log(JSON.stringify(this.courseList))
			} catch (err) {
				console.error('Failed to fetch courses:', err);
			}
		},
		getCourseTitle,
		// openAddDialog() {
		// 	this.reset();
		// 	this.isEditing = false;
		// 	this.courseDialogVisible = true;
		// },
		openEditDialog(course) {
			const id = course.id;
			getCourseList({
				pageNo: 1,
				pagesize: 1,
				id: id,
			}).then(response => {
				const fetched = response.data.list[0];

				// Convert seconds → milliseconds for el-date-picker
				fetched.begin_at *= 1000;
				fetched.end_at *= 1000;

				if (!Array.isArray(fetched.langs) || fetched.langs.length === 0) {
					fetched.langs = [
						{ lang_code: 0, title: '', details: '' }
					];
				}
				else {
					fetched.langs = fetched.langs.map(l => ({
						lang_code: typeof l.lang_code === 'number' ? l.lang_code : 0,
						title: l.title || '',
						details: l.details || ''
					}));
				}

				this.editingCourse = fetched;
				this.isEditing = true;
				this.courseDialogVisible = true;

				// console.log(JSON.stringify(this.editingCourse));
			})
		},
		async saveCourse() {
			this.$refs.courseForm.validate(async valid => {
				if (!valid) return;

				if (!this.editingCourse || !Array.isArray(this.editingCourse.langs) || this.editingCourse.langs.length === 0) {
					this.$message.error(this.$t('course.forms.atLeastOneLangRequired'));
					return;
				}
				const courseToSend = { ...this.editingCourse };

				// Convert milliseconds → seconds before sending
				courseToSend.begin_at = Math.floor(courseToSend.begin_at / 1000);
				courseToSend.end_at = Math.floor(courseToSend.end_at / 1000);
				courseToSend.langs = courseToSend.langs.filter(l => l.title && l.details);

				// console.log(JSON.stringify(courseToSend));

				if (this.isEditing) {
					await updateCourse(courseToSend);
				} else {
					// await addCourse(courseToSend);
				}
				this.isEditing = false;
				this.courseDialogVisible = false;
				this.reset();
				await this.fetchData();
			});
		},
		addPrice() {
			if (this.editingCourse.prices.length < 3) {
				this.editingCourse.prices.push({ coin: '', val: 0 });
			}
		},
		addLang() {
			const used = this.editingCourse.langs.map(l => l.lang_code);
			const remaining = this.langOptions.find(opt => !used.includes(opt.value));
			if (remaining) {
				this.editingCourse.langs.push({
					language: remaining.value,
					title: '',
					details: ''
				});
			}
		},
		removeLang(index) {
			if (this.editingCourse.langs.length > 1) {
				this.editingCourse.langs.splice(index, 1);
			}
		},
		availableLangOptions(index) {
			const used = this.editingCourse.langs
				.map((lang, idx) => idx === index ? null : lang.lang_code)
				.filter(code => code !== null && code !== undefined);
			return this.langOptions.filter(opt =>
				!used.includes(opt.value) || opt.value === this.editingCourse.langs[index].lang_code
			);
		},
		reset() {
			const now = Date.now();
			this.editingCourse = {
				detail_link: '',
				target_share: 0,
				join_num: 0,
				portal_image: '',
				begin_at: now,
				end_at: now + 7 * 24 * 60 * 60 * 1000,
				prices: [{ coin: '', val: 0 }],
				langs: [
					{
						lang_code: 0,
						title: '',
						details: ''
					}
				]
			};
		},
		computeState(row) {
			const now = Date.now() / 1000;
			if (row.join_num >= row.target_share) return 'Registration Closed';
			if (now < row.begin_at) return 'About To Start';
			if (now >= row.begin_at && now <= row.end_at) return 'In Progress';
			return 'Ended';
		},
		getStateTagType(row) {
			const state = this.computeState(row);
			switch (state) {
				case 'In Progress': return 'success';
				case 'About To Start': return 'warning';
				case 'Registration Closed': return 'danger';
				case 'Ended': return 'info';
				default: return '';
			}
		},
		tableSpanMethod({ row, column, rowIndex, columnIndex }) {
			// Define column indices that require rowspan
			const mergeCols = [0, 1, 2, 3, 6, 7, 8]; // Adjust as per actual column order

			if (!mergeCols.includes(columnIndex)) {
				return [1, 1]; // Normal cell
			}

			if (row._isFirstPrice) {
				return [row._priceRowSpan, 1]; // rowspan, colspan
			} else {
				return [0, 0]; // merged cell, don't render
			}
		},
		tableRowClassName({ row, rowIndex }) {
			if (row._isFirstPrice) {
				this._currentStripeIndex = (this._currentStripeIndex || 0) + 1;
			}
			const groupIndex = this._currentStripeIndex || 1;
			return groupIndex % 2 === 1 ? 'group-row-odd' : 'group-row-even';
		},
		handleSearch() {
			this.filters.pageNo = 1;
			this.fetchData();
		}
	}
};
</script>

<style>
.academy-container {
	padding: 20px;
}

.filter-form {
	margin-bottom: 20px;
}

.course-table .group-row-even {
	background-color: #f5f6f7 !important;
}

.course-table .group-row-odd {
	background-color: #ffffff !important;
}

.course-table .el-table__row:hover>td {
	background-color: inherit !important;
	transition: none;
}

.label-multi-line .el-form-item__label {
	white-space: normal;
	word-wrap: break-word;
	word-break: break-word;
	line-height: 1.2;
}
</style>