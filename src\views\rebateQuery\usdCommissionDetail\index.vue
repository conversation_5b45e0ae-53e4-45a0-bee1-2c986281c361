 <template>
  <div class="rebatelistdetail_wrap">
    <div class="parList_wrap">
      <table style="table-layout: fixed; border-collapse:collapse;">
        <tr>
          <th>{{$t('tableHeader.uid')}}</th>
          <th>{{$t('tableHeader.userName')}}</th>
          <th>{{$t('tableHeader.userTransactionAmount')}}</th>
          <th>{{$t('tableHeader.commissionMoney')}}</th>
          <th>{{$t('filters.backRate')}}</th>
          <th>{{$t('tableHeader.backTime')}}</th>
          <th>{{$t('tableHeader.state')}}</th>
          <th>{{$t('tableHeader.statisticalTime')}}</th>
          <th>{{$t('tableHeader.issueTime')}}</th>
        </tr>

        <tr>
          <td>{{ (rebateList && rebateList.userid) || "--" }}</td>
          <td>{{ rebateList.user_name || "--" }}</td>
          <td>{{ rebateList.trading_amount || "--" }}</td>
          <td>{{ rebateList.rebate_commission || "--" }}</td>
          <td>{{ rebateList.agent_rebate_ratio+'%' }}</td>
          <td>{{ rebateList.period +this.$t('forms.day') }}</td>
          <td>
            <span>{{
              status[rebateList.status_stype]
            }}</span>
          </td>
          <td>{{ rebateList.star_period }}</td>
          <td>
            <span>{{
              rebateList.status_stype !==1 ? rebateList.send_time : "--"
            }}</span>
          </td>
        </tr>
      </table>
    </div>

    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        :placeholder="$t('filters.agent')"
        style="width: 160px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-button
        class="filter-item"
        style="margin-left: 10px"
        size="mini"
        type="primary"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="rebateDetailList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.tradersID')" prop="contributor" align="center" min-width="165">
        <template slot-scope="{row}">
          <span>{{row.contributor || '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center" min-width="135px">
        <template slot-scope="{row}">
          <span>{{row.user_name || '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('others.topAgent')" prop="pareid" align="center" min-width="165px">
       <template slot-scope="{row}">
          <span>{{row.pareid || '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.topAgentUserName')" prop="parename" align="center" min-width="135px">
        <template slot-scope="{row}">
          <span>{{row.parename || '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.state')" prop="parename" align="center" min-width="90px">
        <template slot-scope="{row}">
          <span>{{categoryStatus[row.category]}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.transactionAmount')" prop="order_amount" align="center" min-width="95px" />
      <el-table-column :label="$t('tableHeader.poundage')" prop="order_commission" align="center" min-width="90px" />
      <el-table-column :label="$t('filters.backRate')" prop="rebate_ratio" align="center" min-width="100px" />
      <el-table-column :label="$t('tableHeader.commissionMoney')" prop="reward" align="center" min-width="100px"/>
      <el-table-column :label="$t('tableHeader.operation')" align="center" min-width="80px">
        <template slot-scope="{ row }">
          <el-button type="primary" size="mini" @click="viewClick(row)">{{$t('buttons.toView')}}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- <pagina-tion
      :total="total"
      :page.sync="page.size"
      :limit.sync="page.count"
      @pagination="rebatelistDetail"
    /> -->

    <el-dialog v-dialogDrag :title="$t('dialog.poundageCommissionDetail')" :visible.sync="viewDialogVisible" width="80%">
      <el-table
        v-loading="viewlistLoading"
        :data="viewList"
        border
        fit
        highlight-current-row
      size="mini"
        style="width: 100%;"
        :header-cell-style="{ background: '#F0F8FF' }"
      >
        <el-table-column :label="$t('tableHeader.clinchDealSerial')" prop="tradeid" align="center" min-width="120" />
        <el-table-column :label="$t('tableHeader.contract')" prop="contractcode" align="center" min-width="90px"/>
        <el-table-column :label="$t('filters.positionType')" align="center" min-width="95px">
          <template slot-scope="{ row }">
            <span>{{row.offset == 'O'?$t('tableHeader.positions'):$t('tableHeader.unwind')}}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('tableHeader.leverage')" align="center" min-width="90px">
          <template slot-scope="{ row }">
            <span>&times;{{ row.lever }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('tableHeader.pieceNum')" prop="volume" align="center" min-width="95px"/>

        <el-table-column :label="$t('tableHeader.openPositionsPrice')" prop="buyprice" align="center" min-width="100px"/>
        <el-table-column :label="$t('tableHeader.unwindPri')" prop="sellprice" align="center" min-width="100px"/>
        <el-table-column :label="$t('tableHeader.poundage')" prop="commission" align="center" min-width="100px"/>
        <el-table-column :label="$t('tableHeader.net_PNl')" align="center" min-width="90px">
          <template slot-scope="{ row }">
            <!-- <span>{{ Number(row.closeprofit).add(row.commission) }}</span> -->
            <span>{{row.profit}}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('tableHeader.PNL')" prop="closeprofit" align="center" min-width="90px"> </el-table-column>
        <el-table-column :label="$t('filters.transactionType')" align="center" min-width="90px">
          <template slot-scope="{ row }">
            <span>{{ orderTypeObj[row.ordertype]  }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('tableHeader.IP_addr')" prop="ipaddress" align="center" min-width="100px"> </el-table-column>
        <el-table-column :label="$t('tableHeader.clinchDealTime')" prop="tradetime" align="center" min-width="100px"> </el-table-column>
      </el-table>
      <pagina-tion
         v-show="viewtotal > 0"
        :total="viewtotal"
        :page.sync="viewQuery.pageNo"
        :limit.sync="viewQuery.pagesize"
        @pagination="viewClicks"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="viewDialogVisible = false">{{$t('buttons.close')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getuserrebot, getusertrade } from "@/api/rebateQuery";

export default {
  name: "USDCommissionDetail",
  data() {
    return {
      listLoading: false,

      rebateDetailList: null, //接受详情数据

      rebateList: {
        end_period: "",
        rebate_commission: null,
        send_time: "",
        star_period: "",
        status_stype: null,
        trading_amount: null,
        user_name: null,
        userid: null,
      }, //详情带进来的数据
      filterTime: [],
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        sagent: "", //代理id或者名字
        star: "", //结算开始
        end: "", //结算结束
      },
      id: "",
      total: 0,
      //控制查看对话框
      viewDialogVisible: false,
      viewList: [],
      viewtotal: 0,
      viewlistLoading: false,
      viewQuery: {
        user_id: null, //用户id
        category:null,
        star: "", //结算开始
        end: "", //结算结束
        pageNo: 1,
        pagesize: 10,
      },
      orderTypeObj: {
        0: this.$t('filters.Market_orders'),
        1: this.$t('tableHeader.order'), 
        2: this.$t('tableHeader.check_single'), 
        4: this.$t('tableHeader.stop_loss_orders'), 
        5: this.$t('filters.Strong_flat_sheet')
      },
      status: {
        1: this.$t('filters.Stay_out'),
        2: this.$t('filters.Issued'),
        3: this.$t('filters.Has_been_cancelled'),
      },
      categoryStatus:{
        1:this.$t('filters.Invite_the_commission'),
        2:this.$t('filters.The_agent_commission'),
        3:this.$t('filters.Documentary_commission'),
      }
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.rebateList = JSON.parse(this.$route.query.dt);
    console.log(this.rebateList)
    this.rebatelistDetail();
  },

  methods: {
    rebatelistDetail() {
      this.listLoading = true;
      let data = {};
      Object.assign(data, {
        user_id: JSON.parse(this.rebateList.userid),
        coinname: this.rebateList.coin_name,
        star: this.rebateList.star_period,
        end: this.rebateList.end_period,
      });
      // console.log(data)
      getuserrebot(data).then((res) => {
        this.rebateDetailList = res.data.list;
        // console.log(this.rebateDetailList)
        this.total = res.data.total_count;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.rebatelistDetail();
    },
    //查看对话框
    viewClick(row) {
      this.viewQuery.user_id = row.contributor;
      this.viewQuery.category = row.category;
      this.viewDialogVisible = true;
      this.viewClicks();
    },
    viewClicks() {
      this.viewlistLoading = true;
      let data = {}
      Object.assign(data, this.viewQuery, {
        user_id:JSON.parse(this.viewQuery.user_id),
        star: this.rebateList.star_period,
        end: this.rebateList.end_period,
      });
      getusertrade(data).then((res) => {
        this.viewList = res.data.list;
        // console.log(this.viewList)
        this.viewtotal = res.data.total;
        this.viewlistLoading = false;
      });
    },
    filterTimeTransform(val) {
      this.listQuery.star = (val && val[0]) || "";
      this.listQuery.end = (val && val[1] + " 23:59:59") || "";
    },
  },
};
</script>
<style lang="scss" scoped>
.rebatelistdetail_wrap {
  .parList_wrap {
    table {
      width: 95%;
      border: 1px solid #c0c4cc;
      border-collapse: collapse;
      margin-bottom: 15px;
      font-size: 14px;
      margin: 20px auto;
      background: #f7f9fc;
      tr {
        width: 100%;
        text-align: center;
        height: 40px;
        th {
          width: 10%;
          border: 1px solid #c0c4cc;
          font-size: 14px;
          height: 40px;
           white-space:nowrap; /* 自适应宽度*/
            word-break:keep-all; /* 避免长单词截断，保持全部 */
             white-space:pre-line;
            word-break:break-all !important;
            word-wrap:break-word !important;
            display:table-cell;
            vertical-align:middle !important;
            white-space: normal !important;
            vertical-align:text-top;
            padding:2px 2px 0 2px;
        }
        td {
          width: 10%;
          border: 1px solid #c0c4cc;
           white-space:nowrap; /* 自适应宽度*/
            word-break:keep-all; /* 避免长单词截断，保持全部 */
             white-space:pre-line;
            word-break:break-all !important;
            word-wrap:break-word !important;
            display:table-cell;
            vertical-align:middle !important;
            white-space: normal !important;
            vertical-align:text-top;
            padding:2px 2px 0 2px;
            display: table-cell;
        }
      }
    }
  }
}
</style>