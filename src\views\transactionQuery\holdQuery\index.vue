<template>
  <div class="hold-cantainer">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        :placeholder="$t('filters.topIDNick')"
        style="width: 130px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        :placeholder="$t('filters.agent')"
        style="width: 150px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.contract_code"
        :placeholder="$t('tableHeader.contract')"
        clearable
        style="width: 120px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in contractOptions"
          :key="item.traderpairs"
          :label="item.traderpairs"
          :value="item.traderpairs"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.account_type"
        :placeholder="$t('filters.positionType')"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in accountTypeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.side"
        :placeholder="$t('filters.direction')"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in sizeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>

      <el-button
        class="filter-item"
        size="mini"
        style="margin-left: 20px; margin-top: 5px"
        type="primary"
        @click="handleFilter"
      >
        <!-- @click="handleFilter" -->
        {{$t('buttons.search')}}
      </el-button>
      <el-button
        class="filter-item"
        v-if="$store.getters.roles.indexOf('positionlistexport')>-1"
        :loading="exportLoading"
        style="margin-left: 20px; margin-top: 5px"
        @click="handleExport"
        size="mini"
        type="success"
      >
        {{$t('buttons.export')}}
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="holdList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="userid" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center" min-width="95"> </el-table-column>
      <el-table-column :label="$t('filters.topID')" prop="top_agent_id" align="center" min-width="78"> 
        <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topNick')" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorID')" prop="pareid" align="center" min-width="78">
         <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.contract')" prop="contractcode" min-width="90px" align="center"></el-table-column>
      <el-table-column :label="$t('filters.positionType')" min-width="80px" align="center">
        <template slot-scope="{ row }">
          <span>
            {{ row.accounttype == 1 ? $t('tableHeader.all_warehouse') : 
              row.accounttype == 2 ? $t('tableHeader.by_warehouse') :
              row.accounttype == 5 ? $t('filters.All_warehouse_Points_storehouse') :
              row.accounttype == 6 ? $t('filters.By_warehouse_Points_storehouse') : '--'
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.direction')" min-width="80px" align="center">
        <template slot-scope="{ row }">
          <span>{{row.side=='S'?$t('tableHeader.sell_empty'):$t('tableHeader.open_to_buy_more')}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.leverage')" align="center" min-width="60px">
        <template slot-scope="{ row }">
          <span>&times;{{ row.lever }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.number')" prop="volume" align="center" min-width="110px">
          <template slot-scope="{ row }">
          <span>{{ row.volume }}{{$t('others.piece')}}</span><span>/{{row.conversion}}{{row.contractcode.slice(0,-4)}}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('others.frozenMargin')" prop="init_margin" align="center" min-width="100px">
          <template slot-scope="{ row }">
          <span>{{ Number(row.initmargin + row.commission).toFixed(6) }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('tableHeader.averageOpen')" prop="price" align="center" min-width="90px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.poundage')" prop="commission" align="center" min-width="90px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.positionID')" prop="id" align="center" min-width="100px">
        <template slot-scope="{ row }">
          <span>{{ row.id || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.flatPrice')" prop="forceprice" align="center" min-width="90px"> </el-table-column>
      <!-- <el-table-column :label="$t('tableHeader.checkFullPrice')" prop="limit" align="center" min-width="90px"> </el-table-column> -->
      <!-- <el-table-column :label="$t('tableHeader.StopLossPrice')" prop="stop" align="center" min-width="90px"> </el-table-column> -->
      <el-table-column :label="$t('tableHeader.float_PNL')" prop="floatprofit" align="center" min-width="90px"> </el-table-column>
      <!-- <el-table-column :label="$t('tableHeader.IP_addr)" prop="ipaddress" align="center" min-width="100px"> </el-table-column> -->
    </el-table>

    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,50,100,200,300]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";
//引入的自定义指令
import waves from "@/directive/waves";
//引入封装接口
import { positionlist, positionlistexport } from "@/api/transactionQuery";
import { bcprocontractset } from "@/api/user";

export default {
  name: "holdquery",
  data() {
    return {
      listLoading: false,
      total: 0,
      holdList: null,
      contractOptions: [],
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        account_type: null, //账户模式 1：全仓 2：逐仓
        contract_code: "", //合约代码
        side: null, //方向 B买S卖
        pageNo: 1,
        pagesize: 10,
      },
      accountTypeOptions: [
        { key: 1, name: this.$t('tableHeader.all_warehouse') },
        { key: 2, name: this.$t('tableHeader.by_warehouse') },
        { key: 5, name: this.$t('filters.All_warehouse_Points_storehouse') },
        { key: 6, name: this.$t('filters.By_warehouse_Points_storehouse') },
      ],
      sizeOptions: [
        { key: "B", name: this.$t('tableHeader.open_to_buy_more') },
        { key: "S", name: this.$t('tableHeader.sell_empty') },
      ],
      exportLoading: false,//导出加载中效果
    };
  },

  components: {},

  computed: {},

  mounted() {
    bcprocontractset({}).then((res) => {
      this.contractOptions = res.data.filter(v=>v.isshow == 1)
    })
    this.getList();
  },

  methods: {
    //  请求表格数据
    getList() {
      this.listLoading = true;
      this.listQuery.side = this.listQuery.side || undefined
      this.listQuery.account_type = this.listQuery.account_type || undefined
      positionlist(this.listQuery).then((response) => {
        this.holdList = response.data.list;
        this.total = response.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    //表格导出功能
    handleExport() {
      this.exportLoading = true;
      let data = {}
      Object.assign(data,this.listQuery)
      let newDate = parseInt(new Date().getTime()/1000);
      data.end = this.listQuery.end ? this.listQuery.end : (newDate.toDate('yyyy-MM-dd')+ ' 23:59:59')
      newDate = parseInt(newDate-518400)
      data.star = this.listQuery.star ? (this.listQuery.star + ' 00:00:00' ): (newDate.toDate('yyyy-MM-dd')+ ' 00:00:00')
      positionlistexport(data).then((res) => {
        if(res.ret == 0){
            window.location.href=res.data.download_url;
            this.exportLoading = false
        }
      }).catch(err=>{
        this.exportLoading = false
      })
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) =>
        filterVal.map((j) => {
          if (j === "transaction_time") {
            return parseTime(v[j]);
          } else {
            return v[j];
          }
        })
      );
    },
  },
};
</script>
<style lang="scss" scoped>
</style>