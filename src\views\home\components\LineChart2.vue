<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    chartData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      // console.log(this.$el)
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(this.chartData)
    },
    setOptions({ dataList, dataTime, dataList2 } = {}) {
      this.chart.setOption({
        xAxis: {
          data: dataTime,
          boundaryGap: false,
          axisTick: {
            show: false
          }
        },
        grid: {
          left: 10,
          right: 10,
          bottom: 20,
          top: 30,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          padding: [5, 10]
        },
        yAxis: {
          axisTick: {
            show: false
          }
        },
        legend: {data: [this.$t('filters.Channel_new_users'),this.$t('filters.Individual_new_users')]},
        series: [{
          name: this.$t('filters.Channel_new_users'),
          smooth: false,
          type: 'line',
          data: dataList,
          animationDuration: 1000,
          animationEasing: 'cubicInOut'
        },{
          name: this.$t('filters.Individual_new_users'), 
          smooth: false,
          type: 'line',
          data: dataList2,
          animationDuration: 1000,
          animationEasing: 'cubicInOut'
        }]
      })
    }
  }
}
</script>
