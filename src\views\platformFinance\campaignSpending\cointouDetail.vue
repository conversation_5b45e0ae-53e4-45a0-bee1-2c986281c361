<template>
  <div class="campaignSpending">
    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column prop="currencyname"  :label="$t('filters.Margin_currency')" align="center" min-width="90"></el-table-column>
      <el-table-column prop="userid"        :label="$t('tableHeader.uid')" align="center" min-width="90"></el-table-column>
      <el-table-column prop="type"          :label="$t('filters.type')" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>
            {{
              row.type == 1 ? $t('filters.Top_up') :
              row.type == 2 ? $t('filters.Mention_money') :
              row.type == 4 ? $t('filters.Transfer_to_trading_account') :
              row.type == 8 ? $t('filters.Transfer_to_assets_account') :
              row.type == 16 ? $t('filters.Invitation_commission_Award') :
              row.type == 32 ? $t('filters.Agency_commission_Award') :
              row.type == 64 ? $t('filters.Fiat_order_received') :
              row.type == 128 ? $t('filters.Airdrop_rewards') :
              row.type == 256 ? $t('filters.Transfer_from_asset_account_to_documentary_account') :
              row.type == 512 ? $t('filters.Transfer_from_documentary_account_to_asset_account') :
              row.type == 1024 ? $t('filters.Commission_income') :
              row.type == 2048 ? $t('filters.Activities_to_reward') : '--'
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="amount"        :label="$t('tableHeader.Number_of_activities')" align="center" min-width="90"></el-table-column>
      <el-table-column prop="balance"       :label="$t('tableHeader.Remaining_account_equity')" align="center" min-width="90"></el-table-column>
      <el-table-column prop="balance"       :label="$t('tableHeader.remainingAvailableBalance')" align="center" min-width="90">
          <template slot-scope="{ row }">
            <span>{{Number(Number(row.balance)-Number(row.lockamount))}}</span>
          </template>
      </el-table-column>
      <el-table-column prop="lockamount"    :label="$t('tableHeader.Remaining_frozen_quantity')" align="center" min-width="90"></el-table-column>
      <el-table-column prop="createdtime"   :label="$t('tableHeader.Time_of_occurrence')" align="center" min-width="90">
          <template slot-scope="{ row }">
            <span>{{row.createdtime.substring(0,row.createdtime.indexOf(" "))}}</span>
            <br />
            <span>{{row.createdtime.substring(row.createdtime.indexOf(" "))}}</span>
          </template>
      </el-table-column>
      <el-table-column prop="billid"        :label="$t('tableHeader.Serial_number')" align="center" min-width="90">
          <template slot-scope="{ row }">
            <span>{{ JSON.parse(row.billid) }}</span>
          </template>
      </el-table-column>
      <el-table-column prop="orderclient"   :label="$t('forms.client')" align="center" min-width="90">
          <template slot-scope="{ row }">
            <span>
              {{
                row.orderclient == 1 ? 'android' :
                row.orderclient == 2 ? 'ios' :
                row.orderclient == 3 ? 'WEB' :
                row.orderclient == 4 ? 'H5' :
                row.orderclient == 5 ? 'open_api' : $t('others.system_automatically')
              }}
            </span>
          </template>
      </el-table-column>
      <el-table-column prop="ipaddress"     :label="$t('tableHeader.IP_addr')" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{row.ipaddress || '--'}}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,20,30,50]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getusecointout } from "@/api/platformFinance";
export default {
  name: "cointouDetail",
  data() {
    return {
      listLoading: false,
      tableData: [],
      rebateList: [],

      total: 0,
      listQuery: {
        pageNo: 1,
        pagesize: 10,
      },
    };
  },

  computed: {},

  components: {},

  mounted() {
    this.rebateList = JSON.parse(this.$route.query.dt);
    // console.log(this.rebateList);
    
    this.getList()
  },

  methods: {
    getList() {
      this.listLoading = true
      let data = {};

      Object.assign(data, {
        ownday: this.rebateList.own_day,      // 日期
        pageNo: this.listQuery.pageNo,        // int 页数
        pagesize: this.listQuery.pagesize,    // int 分页数量
      })
      getusecointout(data).then((res) => {
        this.tableData = res.data.list
        this.total = res.data.total;
        this.listLoading = false;
      })
    },
  },
};
</script>

<style lang="scss" scoped>
</style>