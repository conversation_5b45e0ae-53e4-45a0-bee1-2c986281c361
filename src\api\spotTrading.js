import request from '@/utils/request'

// 交易区
export function coinarea(data) {
  return request({
    url: '/managers/v1/futures/coinarea',
    method: 'post',
    data: { data },
  })
}

//交易对
export function futuresconcode(data) {
    return request({
      url: '/managers/v1/futures/futuresconcode',
      method: 'post',
      data: { data },
    })
}

//委托历史
export function historderlist(data) {
    return request({
      url: '/managers/v1/futures/historderlist',
      method: 'post',
      data: { data },
    })
}

//委托历史--交易明细
export function futurestradlist(data) {
  return request({
    url: '/managers/v1/futures/futurestradlist',
    method: 'post',
    data: { data },
  })
}


//当前委托
export function orderlist(data) {
    return request({
      url: '/managers/v1/futures/orderlist',
      method: 'post',
      data: { data },
    })
}

//计划委托
export function planorder(data) {
    return request({
      url: '/managers/v1/futures/planorder',
      method: 'post',
      data: { data },
    })
}

//现货资产查询
export function accountinfo(data) {
  return request({
    url: '/managers/v1/futures/accountinfo',
    method: 'post',
    data: { data },
  })
}

//现货手续费查询
export function commisfee(data) {
  return request({
    url: '/managers/v1/futures/commisfee',
    method: 'post',
    data: { data },
  })
}

//现货数据监控
export function usermonitor(data) {
  return request({
    url: '/managers/v1/futures/usermonitor',
    method: 'post',
    data: { data },
  })
}

//用户财务记录
export function useraccounthis(data) {
  return request({
    url: '/managers/v1/futures/useraccounthis',
    method: 'post',
    data: { data },
  })
}

//财务资产
export function financeasset(data) {
  return request({
    url: '/managers/v1/futures/financeasset',
    method: 'post',
    data: { data },
  })
}