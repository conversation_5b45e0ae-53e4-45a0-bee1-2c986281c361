<!--角色组内容编辑-->
<template>
  <div class="tjfz">
    <div class="w_menu">
      <span class="searchTitle">{{$t('forms.grouping_name')}}：</span>
      <el-input
        v-model="zname"
        :placeholder="$t('filters.please_enter_group_name')"
        class="inputw"
        clearable
        size="mini"
      ></el-input>
      <el-button :loading="btnLoading" size="mini" type="primary" @click="addHandler" class="buttons">{{$t('buttons.save')}}</el-button>
    </div>
    <!-- 欢迎主页 -->
    <div class="big-box">
      <el-divider content-position="left">{{$t('menus.welcome_homepage')}}</el-divider>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.homepage')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.zhsjChart" >{{$t('filters.comprehensive_data')}}</el-checkbox>
          <el-checkbox v-model="qx.xzyhChart" >{{$t('filters.new_users')}}</el-checkbox>
          <el-checkbox v-model="qx.yhsbChart" >{{$t('filters.user_equipment')}}</el-checkbox>
          <el-checkbox v-model="qx.appxzChart" >{{$t('filters.app_download')}}</el-checkbox>
        </div>
      </div>
    </div>
    <!-- 公告管理 -->
    <div class="big-box">
      <el-divider content-position="left">{{$t('menus.announcement_management')}}</el-divider>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.noticeList')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.gglb"
            @change="()=>{
              qx.ggxg = !qx.gglb?false:qx.ggxg
              qx.ggdel = !qx.gglb?false:qx.ggdel
            }">{{$t('buttons.toView')}}</el-checkbox>
            <el-checkbox
            v-model="qx.ggxg"
            @change="qx.gglb = qx.ggxg || qx.gglb"
          >{{$t('buttons.modify')}}</el-checkbox>
           <el-checkbox
            v-model="qx.ggdel"
            @change="qx.gglb = qx.ggdel || qx.gglb"
          >{{$t('buttons.delete')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.noticeRelease')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.ggfb">{{$t('buttons.toView')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.noticeImportant')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.getnoticeimportant"
            @change="()=>{
              qx.noticeimportantadd = !qx.getnoticeimportant?false:qx.noticeimportantadd
              qx.noticeimportantdel = !qx.getnoticeimportant?false:qx.noticeimportantdel
              qx.noticeimportantvalid = !qx.getnoticeimportant?false:qx.noticeimportantvalid
            }">{{$t('buttons.toView')}}</el-checkbox>
            <el-checkbox
            v-model="qx.noticeimportantadd"
            @change="qx.getnoticeimportant = qx.noticeimportantadd || qx.getnoticeimportant"
          >{{$t('buttons.add_delete')}}</el-checkbox>
           <el-checkbox
            v-model="qx.noticeimportantdel"
            @change="qx.getnoticeimportant = qx.noticeimportantdel || qx.getnoticeimportant"
          >{{$t('buttons.delete')}}</el-checkbox>
           <el-checkbox
            v-model="qx.noticeimportantvalid"
            @change="qx.getnoticeimportant = qx.noticeimportantvalid || qx.getnoticeimportant"
          >{{$t('buttons.stand_up_down')}}</el-checkbox>
        </div>
      </div>
    </div>
    <!-- 用户查询 -->
    <div class="big-box">
      <el-divider content-position="left">{{$t('menus.userQuery')}}</el-divider>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.userList')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.list"
            @change="()=>{
              qx.addcont = !qx.list?false:qx.addcont
              qx.upduserwithdrawlimit = !qx.list?false:qx.upduserwithdrawlimit
              qx.whetherrebate = !qx.list?false:qx.whetherrebate
              qx.bcprouserup = !qx.list?false:qx.bcprouserup
              qx.upagentstatus = !qx.list?false:qx.upagentstatus
              qx.setagent = !qx.list?false:qx.setagent
              qx.setagent1 = !qx.list?false:qx.setagent1
              qx.resetuserphone = !qx.list?false:qx.resetuserphone
              qx.setUsersLabel = !qx.list?false:qx.setUsersLabel
            }"
          >{{$t('buttons.toView')}}</el-checkbox>
          <el-checkbox 
            v-model="qx.addcont"
            @change="qx.list = qx.addcont || qx.list"
          >{{$t('tableHeader.note')}}</el-checkbox>
          <el-checkbox
            v-model="qx.bcprouserup"
            @change="qx.list = qx.bcprouserup || qx.list"
          >{{$t('buttons.edit')}}</el-checkbox>
          <el-checkbox 
            v-model="qx.upagentstatus"
            @change="qx.list = qx.upagentstatus || qx.list"
          >{{$t('buttons.remove_startUsing')}}</el-checkbox>
          <el-checkbox
            v-model="qx.setagent1"
            @change="qx.list = qx.setagent1 || qx.list"
          >{{$t('buttons.proTopAgent')}}</el-checkbox>
          <el-checkbox
            v-model="qx.setagent"
            @change="qx.list = qx.setagent || qx.list"
          >{{$t('buttons.proAgent')}}</el-checkbox>
          <el-checkbox
            v-model="qx.resetuserphone"
            @change="qx.list = qx.resetuserphone || qx.list"
          >{{$t('buttons.reset')}}</el-checkbox>
          <el-checkbox
            v-model="qx.whetherrebate"
            @change="qx.list = qx.whetherrebate || qx.list"
          >{{$t('filters.no_commission_restore')}}</el-checkbox>
          <el-checkbox
            v-model="qx.setUsersLabel"
            @change="qx.list = qx.setUsersLabel || qx.list"
          >{{$t('filters.label_deletelabel')}}</el-checkbox>
          <el-checkbox
            v-model="qx.upduserwithdrawlimit"
            @change="qx.list = qx.upduserwithdrawlimit || qx.list"
          >{{$t('filters.tbxzhqx')}}</el-checkbox>
          <!-- <el-checkbox v-model="qx.bcupverifyreset">资金划转</el-checkbox> -->
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.overseas_KYC_audit')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.verifyhistorylist"
            @change="()=>{
              qx.bcupverifyhistory = !qx.verifyhistorylist?false:qx.bcupverifyhistory
              qx.bcupverifyreset = !qx.verifyhistorylist?false:qx.bcupverifyreset
            }"
          >{{$t('buttons.toView')}}</el-checkbox>
          <el-checkbox 
            v-model="qx.bcupverifyhistory"
            @change="qx.verifyhistorylist = qx.bcupverifyhistory || qx.verifyhistorylist"
          >{{$t('buttons.audit')}}</el-checkbox>
          <el-checkbox 
            v-model="qx.bcupverifyreset"
            @change="qx.verifyhistorylist = qx.bcupverifyreset || qx.verifyhistorylist"
          >{{$t('buttons.reset')}}</el-checkbox>
        </div>
      </div>
    </div>
    <!-- USDT合约查询 -->
    <div class="big-box">
      <el-divider content-position="left">{{$t('menus.trading_query')}}</el-divider>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.position_query')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.positionlist"
            @change="()=>{
              qx.positionlistexport = !qx.positionlist?false:qx.positionlistexport
            }"
          >{{$t("buttons.toView")}}</el-checkbox>
          <el-checkbox 
            v-model="qx.positionlistexport"
            @change="qx.positionlist = qx.positionlistexport || qx.positionlist"
          >{{$t('buttons.export')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.unwind_query')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.closetradelist"
            @change="()=>{
              qx.closetradeexport = !qx.closetradelist?false:qx.closetradeexport
              qx.closetradelistConfluence = !qx.closetradelist?false:qx.closetradelistConfluence
            }"
          >{{$t("buttons.toView")}}</el-checkbox>
          <el-checkbox 
            v-model="qx.closetradeexport"
            @change="qx.closetradelist = qx.closetradeexport || qx.closetradelist"
          >{{$t('buttons.export')}}</el-checkbox>
          <el-checkbox 
            v-model="qx.closetradelistConfluence"
            @change="qx.closetradelist = qx.closetradelistConfluence || qx.closetradelist"
          >{{$t('buttons.summary')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.openPositions_query')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.opentradelist"
            @change="()=>{
              qx.opentradeexport = !qx.opentradelist?false:qx.opentradeexport
            }"
          >{{$t("buttons.toView")}}</el-checkbox>
          <el-checkbox 
            v-model="qx.opentradeexport"
            @change="qx.opentradelist = qx.opentradeexport || qx.opentradelist"
          >{{$t('buttons.export')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.open_unwind_query')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.tradelist"
            @change="()=>{
              qx.tradelistConfluence = !qx.tradelist?false:qx.tradelistConfluence
            }"
          >{{$t("buttons.toView")}}</el-checkbox>
          <el-checkbox 
            v-model="qx.tradelistConfluence"
            @change="qx.tradelist = qx.tradelistConfluence || qx.tradelist"
          >{{$t('buttons.summary')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.liquidationListQuery')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.stopoutQuery"
            @change="()=>{
              qx.getstopoutexport = !qx.stopoutQuery?false:qx.getstopoutexport
            }"
          >{{$t("buttons.toView")}}</el-checkbox>
          <el-checkbox 
            v-model="qx.getstopoutexport"
            @change="qx.stopoutQuery = qx.getstopoutexport || qx.stopoutQuery"
          >{{$t('buttons.export')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.check_full_stop_query')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.plancloseorder">{{$t("buttons.toView")}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.order_query')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.getplanorder">{{$t("buttons.toView")}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.user_position_monitoring')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.getposstioncap">{{$t("buttons.toView")}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.exports_download_list')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.getmanageexpotlist"
            @change="()=>{
              qx.exprotlistDownload = !qx.getmanageexpotlist?false:qx.exprotlistDownload
            }"
          >{{$t("buttons.toView")}}</el-checkbox>
          <el-checkbox 
            v-model="qx.exprotlistDownload"
            @change="qx.getmanageexpotlist = qx.exprotlistDownload || qx.getmanageexpotlist"
          >{{$t('buttons.export')}}</el-checkbox>
        </div>
      </div>
    </div>
    <!-- 币本位合约查询 -->
    <div class="big-box">
      <el-divider content-position="left">{{ $t('menus.Multi_currency_contract_management') }}</el-divider>
      <div class="big-box-one">
        <div class="big-box-one_1">{{ $t('menus.open_unwind_query') }}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.USDOpenCloseQuery"
            @change="()=>{
              qx.USDOpenCloseQuerySummary = !qx.USDOpenCloseQuery?false:qx.USDOpenCloseQuerySummary
              qx.USDOpenCloseQueryexport = !qx.USDOpenCloseQuery?false:qx.USDOpenCloseQueryexport
            }">{{ $t('buttons.toView') }}</el-checkbox>
          <el-checkbox
            v-model="qx.USDOpenCloseQuerySummary"
            @change="qx.USDOpenCloseQuery = qx.USDOpenCloseQuerySummary || qx.USDOpenCloseQuery"
            >{{ $t('buttons.summary') }}</el-checkbox>
          <el-checkbox
            v-model="qx.USDOpenCloseQueryexport"
            @change="qx.USDOpenCloseQuery = qx.USDOpenCloseQueryexport || qx.USDOpenCloseQuery"
            >{{ $t('buttons.export') }}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{ $t('menus.position_query') }}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.USDPositionQuery">{{ $t('buttons.toView') }}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{ $t('menus.User_liquidation_data') }}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.USDLiquidationData"
            @change="()=>{
              qx.USDLiquidationDataexport = !qx.USDLiquidationData?false:qx.USDLiquidationDataexport
            }">{{ $t('buttons.toView') }}</el-checkbox>
          <el-checkbox
            v-model="qx.USDLiquidationDataexport"
            @change="qx.USDLiquidationData = qx.USDLiquidationDataexport || qx.USDLiquidationData"
            >{{ $t('buttons.export') }}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{ $t('menus.check_full_stop_query') }}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.USDPlanPL">{{ $t('buttons.toView') }}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{ $t('menus.order_query') }}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.USDPlanQuery">{{ $t('buttons.toView') }}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{ $t('menus.user_position_monitoring') }}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.USDPositionMonitoring">{{ $t('buttons.toView') }}</el-checkbox>
        </div>
      </div>
    </div>
    <!-- 跟单查询 -->
    <div class="big-box">
      <el-divider content-position="left">{{$t('menus.documentary_query')}}</el-divider>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.documentary_position_query')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.docpositionlist">{{$t("buttons.toView")}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.documentary_unwind_query')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.docclosetradelist"
            @change="()=>{
              qx.followcloseexport = !qx.docclosetradelist?false:qx.followcloseexport
              qx.docclosetradelistConfluence = !qx.docclosetradelist?false:qx.docclosetradelistConfluence
            }"
          >{{$t("buttons.toView")}}</el-checkbox>
          <el-checkbox 
            v-model="qx.followcloseexport"
            @change="qx.docclosetradelist = qx.followcloseexport || qx.docclosetradelist"
          >{{$t('buttons.export')}}</el-checkbox>
          <el-checkbox 
            v-model="qx.docclosetradelistConfluence"
            @change="qx.docclosetradelist = qx.docclosetradelistConfluence || qx.docclosetradelist"
          >{{$t('buttons.summary')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.documentary_openPositions_query')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.docopentradelist"
            @change="()=>{
              qx.followopenexport = !qx.docopentradelist?false:qx.followopenexport
            }"
          >{{$t("buttons.toView")}}</el-checkbox>
          <el-checkbox 
            v-model="qx.followopenexport"
            @change="qx.docopentradelist = qx.followopenexport || qx.docopentradelist"
          >{{$t('buttons.export')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.documentary_open_unwind_query')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.followtradelist"
            @change="()=>{
              qx.doctradelistConfluence = !qx.followtradelist?false:qx.doctradelistConfluence
            }"
          >{{$t("buttons.toView")}}</el-checkbox>
          <el-checkbox 
            v-model="qx.doctradelistConfluence"
            @change="qx.followtradelist = qx.doctradelistConfluence || qx.followtradelist"
          >{{$t('buttons.summary')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.documentary_check_full_stop_query')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.docplancloseorder">{{$t("buttons.toView")}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.documentary_order_query')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.docgetplanorder">{{$t("buttons.toView")}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.documentary_assets_query')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.followaccinfo"
            @change="()=>{
              qx.followaccinfoexport = !qx.followaccinfo?false:qx.followaccinfoexport
            }">{{$t("buttons.toView")}}</el-checkbox>
          <el-checkbox 
            v-model="qx.followaccinfoexport"
            @change="qx.followaccinfo = qx.followaccinfoexport || qx.followaccinfo"
          >{{$t('buttons.export')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.documentary_position_monitoring')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.getflposstioncap">{{$t("buttons.toView")}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.documentary_data_monitoring')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.getfollowcapital">{{$t("buttons.toView")}}</el-checkbox>
        </div>
      </div>
    </div>
    <!-- 现货交易 -->
    <div class="big-box">
      <el-divider content-position="left">{{ $t('menus.spot_trading') }}</el-divider>
      <div class="big-box-one">
        <div class="big-box-one_1">{{ $t('menus.commissioned_history') }}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.spothistorderlist"
            @change="()=>{
              qx.spothistorderlistsummary = !qx.spothistorderlist?false:qx.spothistorderlistsummary
            }">{{ $t('buttons.toView') }}</el-checkbox>
          <el-checkbox
            v-model="qx.spothistorderlistsummary"
            @change="qx.spothistorderlist = qx.spothistorderlistsummary || qx.spothistorderlist"
            >{{ $t('buttons.summary') }}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{ $t('menus.commissioned_current') }}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.spotorderlist">{{ $t('buttons.toView') }}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{ $t('menus.plan_entrust') }}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.spotplanorder">{{ $t('buttons.toView') }}</el-checkbox>
        </div>
      </div>
    </div>
    <!-- 资金查询 -->
    <div class="big-box">
      <el-divider content-position="left">{{$t('menus.money_query')}}</el-divider>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.spot_assets_query')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.spotaccountinfo">{{$t("buttons.toView")}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.spot_poundage_query')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.spotcommisfee">{{$t("buttons.toView")}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.spot_monitioring')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.spotusermonitor">{{$t("buttons.toView")}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.user_total_assets_query')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.userTotalAssetQueryLook">{{$t("buttons.toView")}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.usd_assets_query')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.USDAssetsQueryLook">{{$t("buttons.toView")}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.user_assets_query')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.getwellinfo">{{$t("buttons.toView")}}</el-checkbox>
          <!-- <el-checkbox 
            v-model="qx.getwellinfo"
            @change="()=>{
              qx.getwellinfoexport = !qx.getwellinfo?false:qx.getwellinfoexport
            }">{{$t("buttons.toView")}}</el-checkbox>
          <el-checkbox 
            v-model="qx.getwellinfoexport"
            @change="qx.getwellinfo = qx.getwellinfoexport || qx.getwellinfo"
          >{{$t('buttons.export')}}</el-checkbox> -->
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.trading_assets_query')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.getaccinfo"
            @change="()=>{
              qx.advancedsearch = !qx.getaccinfo?false:qx.advancedsearch
              qx.setuserlabl = !qx.getaccinfo?false:qx.setuserlabl
              qx.setuserlabldel = !qx.getaccinfo?false:qx.setuserlabldel
            }"
          >{{$t("buttons.toView")}}</el-checkbox>
          <el-checkbox 
            v-model="qx.advancedsearch"
            @change="qx.getaccinfo = qx.advancedsearch || qx.getaccinfo"
          >{{$t('filters.advanced_filter')}}</el-checkbox>
          <el-checkbox 
            v-model="qx.setuserlabl"
            @change="qx.getaccinfo = qx.setuserlabl || qx.getaccinfo"
          >{{$t('buttons.addLabel')}}</el-checkbox>
           <el-checkbox 
            v-model="qx.setuserlabldel"
            @change="qx.getaccinfo = qx.setuserlabldel || qx.getaccinfo"
          >{{$t('filters.delete_label')}}</el-checkbox>
          <el-checkbox 
            v-model="qx.getaccinfoexport"
            @change="qx.getaccinfo = qx.getaccinfoexport || qx.getaccinfo"
          >{{$t('buttons.export')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.user_in_out_money_query')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.getwalletbill">{{$t("buttons.toView")}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.user_financial_query')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.getwallerhistory"
            @change="qx.getacchistory = qx.getwallerhistory"
          >{{$t("buttons.toView")}}</el-checkbox>
        </div>
        <div v-show="false" class="big-box-one_2">
          <el-checkbox v-model="qx.getacchistory">{{$t("buttons.toView")}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.user_extract_management')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.getwithdrawlist"
            @change="()=>{
              qx.withdrawcheck = !qx.getwithdrawlist?false:qx.withdrawcheck
            }"
          >{{$t("buttons.toView")}}</el-checkbox>
          <el-checkbox 
            v-model="qx.withdrawcheck"
            @change="qx.getwithdrawlist = qx.withdrawcheck || qx.getwithdrawlist"
          >{{$t('buttons.audit')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{ $t('menus.Currency_exchange_management') }}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.userexchangelist"
          >{{$t("buttons.toView")}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.fiat_sell_management')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.legalorderlist"
            @change="()=>{
              qx.legalordercheck = !qx.legalorderlist?false:qx.legalordercheck
            }"
          >{{$t("buttons.toView")}}</el-checkbox>
          <el-checkbox 
            v-model="qx.legalordercheck"
            @change="qx.legalorderlist = qx.legalordercheck || qx.legalorderlist"
          >{{$t('buttons.audit')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.user_data_monitoring')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.getagentcapital">{{$t("buttons.toView")}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.PNL_query')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.getuserpnl">{{$t("buttons.toView")}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.capital_cost_query')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.getusercaption"
            @change="()=>{
              qx.getusercaptionexport = !qx.getusercaption?false:qx.getusercaptionexport
            }"
          >{{$t("buttons.toView")}}</el-checkbox>
          <el-checkbox 
            v-model="qx.getusercaptionexport"
            @change="qx.getusercaption = qx.getusercaptionexport || qx.getusercaption"
          >{{$t('buttons.export')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.poundage_query')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.getcommiss"
            @change="()=>{
              qx.getcommissexport = !qx.getcommiss?false:qx.getcommissexport
            }"
          >{{$t("buttons.toView")}}</el-checkbox>
          <el-checkbox 
            v-model="qx.getcommissexport"
            @change="qx.getcommiss = qx.getcommissexport || qx.getcommiss"
          >{{$t('buttons.export')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.daily_PNL_summary')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.daypnllist">{{$t("buttons.toView")}}</el-checkbox>
        </div>
      </div>
    </div>
    <!-- 返佣查询 -->
    <div class="big-box">
      <el-divider content-position="left">{{$t('menus.commission_query')}}</el-divider>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.poundage_commission')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.getrebotlist"
            @change="()=>{
              qx.sendrebot = !qx.getrebotlist?false:qx.sendrebot
              qx.getuserrebot = !qx.getrebotlist?false:qx.getuserrebot
              qx.getusertrade = !qx.getrebotlist?false:qx.getusertrade
              qx.rebotswitchstatus = !qx.getrebotlist?false:qx.rebotswitchstatus
              qx.getrebotlistexport = !qx.getrebotlist?false:qx.getrebotlistexport
            }">{{$t("buttons.toView")}}</el-checkbox>
          <el-checkbox 
            v-model="qx.getrebotlistexport"
            @change="qx.getrebotlist = qx.getrebotlistexport || qx.getrebotlist"
          >{{$t('buttons.export')}}</el-checkbox>
          <el-checkbox 
            v-model="qx.sendrebot"
            @change="qx.getrebotlist = qx.sendrebot || qx.getrebotlist"
          >{{$t('menus.issue_cancelIssue')}}</el-checkbox>
          <el-checkbox 
            v-model="qx.rebotswitchstatus"
            @change="qx.getrebotlist = qx.rebotswitchstatus || qx.getrebotlist"
          >{{$t('menus.freeze_restore')}}</el-checkbox>
          <el-checkbox 
            v-model="qx.getuserrebot"
            @change="()=>{
              qx.getrebotlist = qx.getuserrebot || qx.getrebotlist
              qx.getusertrade = qx.getuserrebot
            }"
          >{{$t('dialog.detail')}}</el-checkbox>
          <el-checkbox v-show="false" v-model="qx.getusertrade">{{$t('menus.trading_record')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.usd_contract_commission')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.UsdCommissionLook"
            @change="()=>{
              qx.UsdCommissionIssue = !qx.UsdCommissionLook?false:qx.UsdCommissionIssue
              qx.UsdCommissionDetailLooK = !qx.UsdCommissionLook?false:qx.UsdCommissionDetailLooK
              qx.UsdCommissionDetailRecord = !qx.UsdCommissionLook?false:qx.UsdCommissionDetailRecord
              qx.UsdCommissionFreeze = !qx.UsdCommissionLook?false:qx.UsdCommissionFreeze
              qx.UsdCommissionExport = !qx.UsdCommissionLook?false:qx.UsdCommissionExport
            }">{{$t("buttons.toView")}}</el-checkbox>
          <el-checkbox 
            v-model="qx.UsdCommissionExport"
            @change="qx.UsdCommissionLook = qx.UsdCommissionExport || qx.UsdCommissionLook"
          >{{$t('buttons.export')}}</el-checkbox>
          <el-checkbox 
            v-model="qx.UsdCommissionIssue"
            @change="qx.UsdCommissionLook = qx.UsdCommissionIssue || qx.UsdCommissionLook"
          >{{$t('menus.issue_cancelIssue')}}</el-checkbox>
          <el-checkbox 
            v-model="qx.UsdCommissionFreeze"
            @change="qx.UsdCommissionLook = qx.UsdCommissionFreeze || qx.UsdCommissionLook"
          >{{$t('menus.freeze_restore')}}</el-checkbox>
          <el-checkbox 
            v-model="qx.UsdCommissionDetailLooK"
            @change="()=>{
              qx.UsdCommissionLook = qx.UsdCommissionDetailLooK || qx.UsdCommissionLook
              qx.UsdCommissionDetailRecord = qx.UsdCommissionDetailLooK
            }"
          >{{$t('dialog.detail')}}</el-checkbox>
          <el-checkbox v-show="false" v-model="qx.UsdCommissionDetailRecord">{{$t('menus.trading_record')}}</el-checkbox>
        </div>
      </div>
    </div>
    <!-- 平台财务 -->
    <div class="big-box">
      <el-divider content-position="left">{{$t('menus.platform_financial')}}</el-divider>
      <!-- <div class="big-box-one">
        <div class="big-box-one_1">资产概览</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.getfinacewallet" >{{$t("buttons.toView")}}</el-checkbox>
        </div>
      </div> -->
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.spot_account')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.spotfinanceasset">{{$t("buttons.toView")}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.assetsAccount')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.platwalletlist"
            @change="()=>{
              qx.platwalletdaillist = !qx.platwalletlist?false:qx.platwalletdaillist
            }">{{$t("buttons.toView")}}</el-checkbox>
          <el-checkbox
            v-model="qx.platwalletdaillist"
            @change="qx.platwalletlist = qx.platwalletdaillist || qx.platwalletlist"
            >{{$t('menus.historicalRecord')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.transactionAccount')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.getplataccount"
            @change="()=>{
              qx.plataccountdail = !qx.getplataccount?false:qx.plataccountdail
            }">{{$t("buttons.toView")}}</el-checkbox>
          <el-checkbox
            v-model="qx.plataccountdail"
            @change="qx.getplataccount = qx.plataccountdail || qx.getplataccount"
            >{{$t('menus.historicalRecord')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.liquidationGetFund')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.liquidationGetList"
            @change="()=>{
              qx.bursthousexport = !qx.liquidationGetList?false:qx.bursthousexport
            }"
          >{{$t("buttons.toView")}}</el-checkbox>
          <el-checkbox 
            v-model="qx.bursthousexport"
            @change="qx.liquidationGetList = qx.bursthousexport || qx.liquidationGetList"
          >{{$t('buttons.export')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.throughPayFund')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.throughPayList"
            @change="()=>{
              qx.throughhousexport = !qx.throughPayList?false:qx.throughhousexport
            }"
          >{{$t("buttons.toView")}}</el-checkbox>
          <el-checkbox 
            v-model="qx.throughhousexport"
            @change="qx.throughPayList = qx.throughhousexport || qx.throughPayList"
          >{{$t('buttons.export')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.Campaign_spending')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.campaignSpending"
            @change="()=>{
              qx.giftoutDetail = !qx.campaignSpending?false:qx.giftoutDetail
              qx.cointouDetail = !qx.campaignSpending?false:qx.cointouDetail
            }"
          >{{$t("buttons.toView")}}</el-checkbox>
          <el-checkbox 
            v-model="qx.giftoutDetail"
            @change="qx.campaignSpending = qx.giftoutDetail || qx.campaignSpending"
          >{{$t('menus.giftoutDetail')}}</el-checkbox>
          <el-checkbox 
            v-model="qx.cointouDetail"
            @change="qx.campaignSpending = qx.cointouDetail || qx.campaignSpending"
          >{{$t('menus.cointouDetail')}}</el-checkbox>
        </div>
      </div>
    </div>
    <!-- 活动管理 -->
    <div class="big-box">
      <el-divider content-position="left">{{$t('menus.activities_management')}}</el-divider>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.carve_up_poundage')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.bcactivitylist"
            @change="()=>{
              qx.setactivityhash = !qx.bcactivitylist?false:qx.setactivityhash
              qx.setactivitylssued = !qx.bcactivitylist?false:qx.setactivitylssued
              qx.setactivity = !qx.bcactivitylist?false:qx.setactivity
              qx.upuserward = !qx.bcactivitylist?false:qx.upuserward
              qx.getuseractivitylist = !qx.bcactivitylist?false:qx.getuseractivitylist
              qx.useractivitycheck = !qx.bcactivitylist?false:qx.useractivitycheck
            }">{{$t("buttons.toView")}}</el-checkbox>
          <el-checkbox 
            v-model="qx.setactivity"
            @change="qx.bcactivitylist = qx.setactivity || qx.bcactivitylist"
          >{{$t('menus.set_activity')}}</el-checkbox>
          <el-checkbox 
            v-model="qx.upuserward"
            @change="qx.bcactivitylist = qx.upuserward || qx.bcactivitylist"
          >{{$t('buttons.modifyLeaderboard')}}</el-checkbox>
          <el-checkbox 
            v-model="qx.setactivityhash"
            @change="qx.bcactivitylist = qx.setactivityhash || qx.bcactivitylist"
          >{{$t('dialog.setHASH')}}</el-checkbox>
          <el-checkbox 
            v-model="qx.setactivitylssued"
            @change="()=>{
              qx.bcactivitylist = qx.setactivitylssued || qx.bcactivitylist
            }"
          >{{$t('menus.set_bonus')}}</el-checkbox>
          <el-checkbox 
            v-model="qx.getuseractivitylist"
            @change="()=>{
              qx.bcactivitylist = qx.getuseractivitylist || qx.bcactivitylist
            }"
          >{{$t('menus.view_award_winning_users')}}</el-checkbox>
          <el-checkbox 
            v-model="qx.useractivitycheck"
            @change="()=>{
              qx.bcactivitylist = qx.useractivitycheck || qx.bcactivitylist
            }"
          >{{$t('menus.review_award_winning_users')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.giftCash_manage')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.giftCashManageLook">{{$t("buttons.toView")}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.giftCash_active')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.giftCashActiveLook">{{$t('buttons.toView')}}</el-checkbox>
        </div>
      </div>
    </div>
    <!-- 渠道管理 -->
    <div class="big-box">
      <el-divider content-position="left">{{$t('menus.channel_management')}}</el-divider>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.top_agent_statistical')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.topAgentList">{{$t("buttons.toView")}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.agent_direct_drive_statistical')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.agentDirectlist">{{$t('buttons.toView')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.channel_statistics')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.channelStatisticsListLook">{{$t('buttons.toView')}}</el-checkbox>
        </div>
      </div>
    </div>
    <!-- 风控管理 -->
    <div class="big-box">
      <el-divider content-position="left">{{$t('menus.risk_control_management')}}</el-divider>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.label_management')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.bcprocontractcoinset"
            @change="()=>{
              qx.bcprocontractcoinadd = !qx.bcprocontractcoinset?false:qx.bcprocontractcoinadd
            }">{{$t('buttons.toView')}}</el-checkbox>
          <el-checkbox
            v-model="qx.bcprocontractcoinadd"
            @change="qx.bcprocontractcoinset = qx.bcprocontractcoinadd || qx.bcprocontractcoinset"
            >{{$t('tableHeader.operation')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.trading_frequency')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.frequency"
            @change="()=>{
              qx.setignorerebate = !qx.frequency?false:qx.setignorerebate
              qx.tradefrequencyexport = !qx.frequency?false:qx.tradefrequencyexport
            }">{{$t('buttons.toView')}}</el-checkbox>
          <el-checkbox
            v-model="qx.setignorerebate"
            @change="qx.frequency = qx.setignorerebate || qx.frequency"
            >{{$t('tableHeader.operation')}}</el-checkbox>
          <el-checkbox
            v-model="qx.tradefrequencyexport"
            @change="qx.frequency = qx.tradefrequencyexport || qx.frequency"
            >{{$t('buttons.export')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.usdtrading_frequency')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.usdfrequency"
            @change="()=>{
              qx.usdsetignorerebate = !qx.usdfrequency?false:qx.usdsetignorerebate
              qx.usdtradefrequencyexport = !qx.usdfrequency?false:qx.usdtradefrequencyexport
            }">{{$t('buttons.toView')}}</el-checkbox>
          <el-checkbox
            v-model="qx.usdsetignorerebate"
            @change="qx.usdfrequency = qx.usdsetignorerebate || qx.usdfrequency"
            >{{$t('tableHeader.operation')}}</el-checkbox>
          <el-checkbox
            v-model="qx.usdtradefrequencyexport"
            @change="qx.usdfrequency = qx.usdtradefrequencyexport || qx.usdfrequency"
            >{{$t('buttons.export')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.high_frequency_user_identification')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.highfrequency">{{$t('buttons.toView')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.high_monetization_user_recognition')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.highpnl"
            @change="()=>{
              qx.userpnlexport = !qx.highpnl?false:qx.userpnlexport
            }">{{$t('buttons.toView')}}</el-checkbox>
          <el-checkbox
            v-model="qx.userpnlexport"
            @change="qx.highpnl = qx.userpnlexport || qx.highpnl"
            >{{$t('buttons.export')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.user_trade_pnl')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.tradepnl">{{$t('buttons.toView')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.high_win_rate_user_identification')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.highwinning"
            @change="()=>{
              qx.highwinningexport = !qx.highwinning?false:qx.highwinningexport
            }">{{$t('buttons.toView')}}</el-checkbox>
          <el-checkbox
            v-model="qx.highwinningexport"
            @change="qx.highwinning = qx.highwinningexport || qx.highwinning"
            >{{$t('buttons.export')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.usd_high_win_rate_user_identification')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.usdhighwinning"
            @change="()=>{
              qx.usdhighwinningoperation = !qx.usdhighwinning?false:qx.usdhighwinningoperation
            }">{{$t('buttons.toView')}}</el-checkbox>
          <el-checkbox
            v-model="qx.usdhighwinningoperation"
            @change="qx.usdhighwinning = qx.usdhighwinningoperation || qx.usdhighwinning"
            >{{$t('tableHeader.operation')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.system_monitoring')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.systemmonitoring">{{$t('buttons.toView')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.same_IP_behavior_analysis')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.ipstatisticslist"
            @change="()=>{
              qx.ipuserlist = !qx.ipstatisticslist?false:qx.ipuserlist
              qx.iptradelist = !qx.ipstatisticslist?false:qx.iptradelist
            }">{{$t('buttons.toView')}}</el-checkbox>
          <el-checkbox
            v-model="qx.ipuserlist"
            @change="qx.ipstatisticslist = qx.ipuserlist || qx.ipstatisticslist"
            >{{$t('menus.IP_user_overview')}}</el-checkbox>
          <el-checkbox
            v-model="qx.iptradelist"
            @change="qx.ipstatisticslist = qx.iptradelist || qx.ipstatisticslist"
            >{{$t('menus.IP_transaction_detailsv')}}</el-checkbox>
            
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.observing_User_List')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.watchuserlist"
            @change="()=>{
              qx.watchusermark = !qx.watchuserlist?false:qx.watchusermark
            }">{{$t('buttons.toView')}}</el-checkbox>
          <el-checkbox
            v-model="qx.watchusermark"
            @change="qx.watchuserlist = qx.watchusermark || qx.watchuserlist"
            >{{$t('tableHeader.operation')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.list_risk_control_groups')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.getgrouplist"
            @change="()=>{
              qx.setgrouplist = !qx.getgrouplist?false:qx.setgrouplist
            }">{{$t('buttons.toView')}}</el-checkbox>
          <el-checkbox
            v-model="qx.setgrouplist"
            @change="qx.getgrouplist = qx.setgrouplist || qx.getgrouplist"
            >{{$t('tableHeader.operation')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.list_risk_control_users')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.getriskuserlist"
            @change="()=>{
              qx.getristlist = !qx.getriskuserlist?false:qx.getristlist
              qx.getwhitelist = !qx.getriskuserlist?false:qx.getwhitelist
              qx.setwhitelist = !qx.getriskuserlist?false:qx.setwhitelist
            }">{{$t('buttons.toView')}}</el-checkbox>
          <el-checkbox
            v-model="qx.getristlist"
            @change="qx.getriskuserlist = qx.getristlist || qx.getriskuserlist"
            >{{$t('menus.viewing_historical_records')}}</el-checkbox>
          <el-checkbox
            v-model="qx.getwhitelist"
            @change="()=>{
              qx.getriskuserlist = qx.getwhitelist || qx.getriskuserlist
              qx.setwhitelist = !qx.getwhitelist?false:qx.setwhitelist
            }"
            >{{$t('menus.view_risk_control_whitelist')}}</el-checkbox>
          <el-checkbox
            v-model="qx.setwhitelist"
            @change="()=>{
              qx.getriskuserlist = qx.setwhitelist || qx.getriskuserlist
              qx.getwhitelist = qx.setwhitelist || qx.getwhitelist
            }"
            >{{$t('menus.risk_control_whitelist_operation')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.contract_PNL_query')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.contractPNLQuery">{{$t('buttons.toView')}}</el-checkbox>
        </div>
      </div>
    </div>
    <!-- 系统管理 -->
    <div class="big-box">
      <el-divider content-position="left">{{$t('menus.system_management')}}</el-divider>
      <div class="big-box-one">
        <div class="big-box-one_1">{{ $t('menus.Download_the_configuration') }}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.configurationDownloadList"
            @change="()=>{
              qx.configurationDownloadOperation = !qx.configurationDownloadList?false:qx.configurationDownloadOperation
            }">{{$t('buttons.toView')}}</el-checkbox>
          <el-checkbox
            v-model="qx.configurationDownloadOperation"
            @change="qx.configurationDownloadList = qx.configurationDownloadOperation || qx.configurationDownloadList"
            >{{$t('tableHeader.operation')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{ $t('menus.Contract_point_difference_setting') }}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.contractSlippageList"
            @change="()=>{
              qx.contractSlippageSet = !qx.contractSlippageList?false:qx.contractSlippageSet
            }">{{$t('buttons.toView')}}</el-checkbox>
          <el-checkbox
            v-model="qx.contractSlippageSet"
            @change="qx.contractSlippageList = qx.contractSlippageSet || qx.contractSlippageList"
            >{{$t('tableHeader.operation')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one"> 
        <div class="big-box-one_1">{{$t('menus.system_dictionary_management')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.sysdict">{{$t('buttons.toView')}}</el-checkbox>
          <el-checkbox v-model="qx.sysdictadd"
            @change="()=>{
              qx.sysdict = qx.sysdictadd || qx.sysdict
            }"
            >{{$t('buttons.add')}}</el-checkbox>
          <el-checkbox v-model="qx.sysdictdel"
            @change="()=>{
              qx.sysdict = qx.sysdictdel || qx.sysdict}"
            >{{$t('buttons.delete')}}</el-checkbox>
          <el-checkbox v-model="qx.sysdictedit">{{$t('buttons.modify')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.system_dictionary_data_management')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.sysdictdata">{{$t('buttons.toView')}}</el-checkbox>
          <el-checkbox v-model="qx.sysdictdataadd"
            @change="()=>{
              qx.sysdictdata = qx.sysdictdataadd || qx.sysdictdata
            }"
            >{{$t('buttons.add')}}</el-checkbox>
          <el-checkbox v-model="qx.sysdictdatadel"
            >{{$t('buttons.delete')}}</el-checkbox>
          <el-checkbox v-model="qx.sysdictdataedit">{{$t('buttons.modify')}}</el-checkbox>
          
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.home_Banner_configuration')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.bannerConfig"
            @change="()=>{
              qx.bannerConfigadd = !qx.bannerConfig?false:qx.bannerConfigadd
              qx.bannerConfigdel = !qx.bannerConfig?false:qx.bannerConfigdel
            }">{{$t('buttons.toView')}}</el-checkbox>
          <el-checkbox
            v-model="qx.bannerConfigadd"
            @change="qx.bannerConfig = qx.bannerConfigadd || qx.bannerConfig"
            >{{$t('menus.add_Banner')}}</el-checkbox>
          <el-checkbox
            v-model="qx.bannerConfigdel"
            @change="qx.bannerConfig = qx.bannerConfigdel || qx.bannerConfig"
            >{{$t('menus.delete_Banner')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.role_management')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.managelist"
            @change="()=>{
              qx.manageadd = !qx.managelist?false:qx.manageadd
              qx.managedel = !qx.managelist?false:qx.managedel
              qx.managesave = !qx.managelist?false:qx.managesave
              qx.managesave1 = !qx.managelist?false:qx.managesave1
              qx.managesave2 = !qx.managelist?false:qx.managesave2
            }">{{$t('buttons.toView')}}</el-checkbox>
            
          <el-checkbox
            v-model="qx.manageadd"
            @change="qx.managelist = qx.manageadd || qx.managelist"
            >{{$t('buttons.add')}}</el-checkbox>
          <el-checkbox
            v-model="qx.managedel"
            @change="qx.managelist = qx.managedel || qx.managelist"
            >{{$t('buttons.delete')}}</el-checkbox>
          <el-checkbox
            v-model="qx.managesave"
            @change="qx.managelist = qx.managesave || qx.managelist"
            >{{$t('buttons.modify')}}</el-checkbox>
          <el-checkbox
            v-model="qx.managesave1"
            @change="qx.managelist = qx.managesave1 || qx.managelist"
            >{{$t('buttons.reset_passwords')}}</el-checkbox>
          <el-checkbox
            v-model="qx.managesave2"
            @change="qx.managelist = qx.managesave2 || qx.managelist"
            >{{$t('buttons.reset_google')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.rights_group')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.moldelist"
            @change="()=>{
              qx.moldeladd1 = !qx.moldelist?false:qx.moldeladd1
              qx.moldeldel = !qx.moldelist?false:qx.moldeldel
              qx.moldeladd3 = !qx.moldelist?false:qx.moldeladd3
              qx.moldelview = !qx.moldelist?false:qx.moldelview
            }">{{$t('buttons.toView')}}</el-checkbox>
          <el-checkbox
            v-model="qx.moldeladd1"
            @change="qx.moldelist = qx.moldeladd1 || qx.moldelist"
            >{{$t('buttons.add_grouping')}}</el-checkbox>
          <el-checkbox
            v-model="qx.moldeldel"
            @change="qx.moldelist = qx.moldeldel || qx.moldelist"
            >{{$t('buttons.delete')}}</el-checkbox>
          <el-checkbox
            v-model="qx.moldeladd3"
            @change="()=>{
              qx.moldelist = qx.moldeladd3 || qx.moldelist
              qx.moldelview = qx.moldeladd3
            }"
            >{{$t('buttons.modify')}}</el-checkbox>
          <el-checkbox v-show="false" v-model="qx.moldelview">{{$t('dialog.detail')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.operation_log')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.getmanagelogs">{{$t('buttons.toView')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.CRM_operation_log')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.getagentoplog">{{$t('buttons.toView')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.contact_us')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.contactUs">{{$t('buttons.toView')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.user_log')}}</div>
        <div class="big-box-one_2">
          <el-checkbox 
            v-model="qx.userlog"
            @change="()=>{
              qx.getuserlogexport = !qx.userlog?false:qx.getuserlogexport
            }">{{$t('buttons.toView')}}</el-checkbox>
          <el-checkbox
            v-model="qx.getuserlogexport"
            @change="qx.userlog = qx.getuserlogexport || qx.userlog"
            >{{$t('buttons.export')}}</el-checkbox>
        </div>
      </div>
      <div class="big-box-one">
        <div class="big-box-one_1">{{$t('menus.feedback_data')}}</div>
        <div class="big-box-one_2">
          <el-checkbox v-model="qx.usererrlog">{{$t('buttons.toView')}}</el-checkbox>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { moldeladd, moldelview } from "@/api/systemManagement";
export default {
  name: "addGroup",
  data() {
    return {
      zname: "",
      id: "",
      type: "1",
      qx: {
        // 首页
        zhsjChart: false,  // 综合数据 图表
        xzyhChart: false,  // 新增用户 图表
        yhsbChart: false,  // 用户设备 图表
        appxzChart: false, // app下载  图表

        //公告管理
        gglb:false,                  // 公告列表 查看
        ggxg:false,                  // 公告列表 修改
        ggdel:false,                 // 公告列表 删除
        ggfb:false,                  // 公告发布 查看
        getnoticeimportant: false,   // 首页浮层 列表
        noticeimportantadd: false,   // 首页浮层 添加/修改
        noticeimportantdel: false,   // 首页浮层 删除
        noticeimportantvalid: false, // 首页浮层 上下架


        // 用户查询
        list: false,              // 用户列表    查看
        bcprouserup: false,       // 用户列表    编辑 用户编辑
        addcont: false,           // 用户列表    备注
        setagent1: false,         // 用户列表    提升顶级代理
        setagent: false,          // 用户列表    提升代理
        upagentstatus: false,     // 用户列表    解除/开启
        bcupverifyreset: false,   // 用户列表    资金划转
        resetuserphone: false,    // 用户列表    重置
        whetherrebate: false,     // 用户列表    不设返佣
        upduserwithdrawlimit: false,// 用户列表    提币限制/取消
        setUsersLabel: false,     // 用户列表    设置标签
        verifyhistorylist: false, // 海外kfc审核 查看
        bcupverifyhistory: false, // 海外kfc审核 审核
        bcupverifyreset: false,   // 海外kfc审核 重置

        // USDT合约查询
        planEntrustlist: false,           // 计划委托     查看
        positionlist: false,              // 持仓查询     查看
        positionlistexport: false,        // 持仓查询     导出
        closetradelist: false,            // 平仓查询     查看
        closetradeexport: false,          // 平仓查询     导出
        closetradelistConfluence: false,  // 平仓查询     汇总
        opentradelist: false,             // 开仓查询     查看
        opentradeexport: false,           // 开仓查询     导出
        tradelist: false,                 // 开平仓查询   查看
        tradelistConfluence: false,       // 开平仓查询   汇总
        stopoutQuery: false,              // 强平查询     查看
        getstopoutexport: false,          // 强平查询     导出
        plancloseorder: false,            // 止盈止损查询 查看
        getplanorder: false,              // 计划单查询   查看
        getposstioncap: false,            // 用户持仓监控 查看
        getmanageexpotlist: false,        // 导出下载列表 查看
        exprotlistDownload: false,        // 导出下载列表 下载

        // 多币种合约管理
        USDOpenCloseQuery: false,         // 开平仓查询   查看
        USDOpenCloseQuerySummary: false,  // 开平仓查询   汇总
        USDOpenCloseQueryexport: false,   // 开平仓查询   导出
        USDPositionQuery: false,          // 持仓查询     查看
        USDLiquidationData: false,        // 用户强平数据 查看
        USDLiquidationDataexport: false,  // 用户强平数据 导出
        USDPlanPL: false,                 // 止盈止损查询 查看
        USDPlanQuery: false,              // 计划委托查询 查看
        USDPositionMonitoring: false,     // 用户持仓数据 查看

         //跟单查询
        docpositionlist: false,             // 持仓查询     查看
        docclosetradelist: false,           // 平仓查询     查看
        followcloseexport: false,           // 平仓查询     导出
        docclosetradelistConfluence: false, // 平仓查询     汇总
        docopentradelist: false,            // 开仓查询     查看
        followopenexport: false,            // 开仓查询     导出
        followtradelist: false,             // 开平仓查询   查看
        doctradelistConfluence: false,      // 开平仓查询   汇总
        docplancloseorder:false,            // 止盈止损查询 查看
        docgetplanorder:false,              // 计划单查询   查看
        followaccinfo:false,                // 资产查询     查看
        getflposstioncap: false,            // 用户持仓监控 查看

        // 现货交易查询
        spothistorderlist: false,         // 历史委托 查看
        spothistorderlistsummary: false,  // 历史委托 汇总
        spotorderlist: false,             // 当前委托 查看
        spotplanorder: false,             // 计划委托 查看


        // 资金查询
        spotaccountinfo: false,           // 现货资产查询       查看
        spotcommisfee: false,             // 现货手续费查询     查看
        spotusermonitor: false,           // 现货数据监控       查看
        userTotalAssetQueryLook: false,   // 用户总资产查询     查看
        USDAssetsQueryLook: false,        // 币本位合约资产查询 查看
        getwellinfo: false,               // 用户资产查询       查看
        getaccinfo: false,                // USDT资产查询       查看
        advancedsearch: false,            // USDT资产查询       高级筛选器
        setuserlabl: false,               // USDT资产查询       添加标签
        setuserlabldel:false,             // USDT资产查询       删除标签
        getwalletbill: false,             // 用户出入金查询     查看
        getwallerhistory: false,          // 用户财务记录       钱包账户财务记录 查看
        getacchistory: false,             // 用户财务记录       合约账户财务记录 查看
        getwithdrawlist: false,           // 用户提币管理       查看
        withdrawcheck: false,             // 用户提币管理       审核
        userexchangelist: false,          // 币币兑换列表       查看
        legalorderlist: false,            // 法币卖出管理       查看
        legalordercheck: false,           // 法币卖出管理       审核
        getagentcapital: false,           // 用户数据监控       查看
        getuserpnl: false,                // PNL查询            查看
        getusercaption: false,            // 资金费用查询       查看
        getusercaptionexport: false,      // 资金费用查询       导出
        getcommiss: false,                // 手续费查询         查看
        getcommissexport: false,          // 手续费查询         导出
        daypnllist: false,                // 每日PNL汇总        查看

        // 返佣查询
        getrebotlist: false,              // USDT合约返佣   查看
        getrebotlistexport: false,        // USDT合约返佣   导出
        sendrebot: false,                 // USDT合约返佣   发放
        rebotswitchstatus: false,         // USDT合约返佣   冻结
        getuserrebot: false,              // USDT合约返佣   详情 查看
        getusertrade: false,              // USDT合约返佣   用户返佣交易 记录
        UsdCommissionLook: false,         // 币本位合约返佣 查看
        UsdCommissionExport: false,       // 币本位合约返佣 导出
        UsdCommissionIssue: false,        // 币本位合约返佣 发放
        UsdCommissionFreeze: false,       // 币本位合约返佣 冻结
        UsdCommissionDetailLooK: false,   // 币本位合约返佣 详情 查看
        UsdCommissionDetailRecord: false, // 币本位合约返佣 用户返佣交易 记录
        
        // 平台财务
        // getfinacewallet: false,     // 资产概览     查看
        spotfinanceasset: false,    // 现货账户     查看
        platwalletlist: false,      // 资产账户     查看
        platwalletdaillist: false,  // 资产账户     历史记录 
        getplataccount: false,      // 交易账户     查看
        plataccountdail: false,     // 交易账户     历史记录
        liquidationGetList: false,  // 强平注入资金 查看 
        bursthousexport: false,     // 强平注入资金 导出 
        throughPayList: false,      // 穿仓支出资金 查看
        throughhousexport: false,   // 穿仓支出资金 导出
        campaignSpending: false,    // 每日活动支出 查看
        giftoutDetail: false,       // 穿仓支出资金 导出
        cointouDetail: false,       // 穿仓支出资金 导出

        // 活动管理
        bcactivitylist: false,      // 瓜分手续费 查看
        setactivity: false,         // 瓜分手续费 设置活动
        upuserward: false,          // 瓜分手续费 修改排行
        setactivityhash: false,     // 瓜分手续费 设置哈希
        setactivitylssued: false,   // 瓜分手续费 设置奖金
        getuseractivitylist: false, // 瓜分手续费 获取用户获奖列表
        useractivitycheck: false,   // 瓜分手续费 获取用户获奖列表 审核
        giftCashManageLook: false,  // 赠金管理   查看
        giftCashActiveLook: false,  // 赠金活动   查看
        
        //渠道管理
        topAgentList: false,    // 顶级代理统计 查看
        agentDirectlist: false, // 代理直推统计 查看
        channelStatisticsListLook: false, // 渠道统计 查看

        // 风控管理
        bcprocontractcoinset: false,  // 标签管理                 查看
        bcprocontractcoinadd: false,  // 标签管理                 操作
        frequency:false,              // 交易频次                 查看
        setignorerebate :false,       // 交易频次                 去除返佣
        usdfrequency:false,           // 交易频次                 查看
        usdsetignorerebate:false,     // 币本位合约交易频次       去除返佣
        usdtradefrequencyexport:false,// 币本位合约交易频次       导出
        highfrequency:false,          // 高频用户识别             查看
        highpnl:false,                // 高盈利用户识别           查看
        userpnlexport: false,         // 高盈利用户识别           导出
        tradepnl:false,               // 用户收益数据             查看
        userpnloperation: false,      // 用户收益数据             操作
        highwinning:false,            // 高胜率用户识别           查看
        highwinningexport: false,     // 高胜率用户识别           导出
        usdhighwinning:false,         // 币本位合约高胜率用户识别 查看
        usdhighwinningoperation:false,// 币本位合约高胜率用户识别 操作
        systemmonitoring:false,       // 系统监控                 查看
        ipstatisticslist:false,       // 同IP行为分析             查看
        ipuserlist: false,            // 同IP行为分析             IP用户概况
        iptradelist: false,           // 同IP行为分析             IP交易详情
        watchuserlist:false,          // 观察用户列表             查看
        watchusermark: false,         // 观察用户列表             操作
        getgrouplist:false,           // 风控组别列表             查看
        setgrouplist: false,          // 风控组别列表             操作
        getriskuserlist:false,        // 风控用户列表             查看
        getristlist: false,           // 风控用户列表             历史记录
        getwhitelist: false,          // 风控用户列表             风控白名单
        contractPNLQuery:false,       // 合约PNL查询              查看

        // 系统管理
        configurationDownloadList: false,       // 配置下载       查看
        configurationDownloadOperation: false,  // 配置下载       操作
        contractSlippageList: false,            // 合约点差设置   查看
        contractSlippageSet: false,             // 合约点差设置   操作
        sysdict: false,                         // 系统字典管理   查看
        sysdictadd: false,                      // 系统字典管理   添加
        sysdictdel: false,                      // 系统字典管理   删除
        sysdictedit: false,                     // 系统字典管理   修改
        bannerConfig:false,                     // 首页Banner配置 查看
        bannerConfigdel:false,                  // 首页Banner配置 添加
        bannerConfigadd:false,                  // 首页Banner配置 删除
        managelist: false,                      // 角色管理       查看
        manageadd: false,                       // 角色管理       添加
        managedel: false,                       // 角色管理       删除
        managesave: false,                      // 角色管理       修改
        managesave1: false,                     // 角色管理       重置登陆密码
        managesave2: false,                     // 角色管理       重置谷歌密码
        moldelist: false,                       // 权限分组       查看
        moldeladd1: false,                      // 权限分组       添加分组
        moldeladd3: false,                      // 权限分组       修改分组
        moldeldel: false,                       // 权限分组       删除
        moldelview: false,                      // 权限分组       详情
        getmanagelogs: false,                   // 操作日志       查看
        getagentoplog: false,                   // CRM 操作日志   查看
        userlog: false,                         // 用户日志       查看
        getuserlogexport: false,                // 用户日志       导出
        usererrlog: false,                      // 反馈数据       查看
        contactUs:false                         // 联系我们       查看
      },
      btnLoading: false,
    };
  },
  components: {},
  mounted() {
    if (this.$route.query.type == "1") {
      // 获取详情
      moldelview({ upid: parseInt(this.$route.query.id) }).then((res) => {
        let mode = JSON.parse(JSON.parse(res.data.modedata));
        Object.assign(this.qx, mode);
      })
    }
    this.type = this.$route.query.type;
    if (this.$route.query.name) {
      this.zname = this.$route.query.name;
    }
  },
  computed: {},
  methods: {
    addHandler() {
      if (this.type == "1") {
        // let model_data = "{\"zhsjChart\":true,\"xzyhChart\":true,\"yhsbChart\":true,\"appxzChart\":true,\"gglb\":true,\"ggfb\":true,\"ggxg\":true,\"ggdel\":true,\"getnoticeimportant\":true,\"noticeimportantadd\":true,\"noticeimportantdel\":true,\"noticeimportantvalid\":true,\"list\":true,\"bcprouserup\":true,\"addcont\":true,\"setagent1\":true,\"setagent\":true,\"upagentstatus\":true,\"bcupverifyreset\":true,\"resetuserphone\":true,\"whetherrebate\":true,\"setUsersLabel\":true,\"verifyhistorylist\":true,\"bcupverifyhistory\":true,\"positionlist\":true,\"positionlistexport\":true,\"closetradelist\":true,\"closetradeexport\":true,\"closetradelistConfluence\":true,\"opentradelist\":true,\"opentradeexport\":true,\"tradelist\":true,\"tradelistConfluence\":true,\"stopoutQuery\":true,\"getstopoutexport\":true,\"plancloseorder\":true,\"getplanorder\":true,\"getposstioncap\":true,\"getmanageexpotlist\":true,\"exprotlistDownload\":true,\"docpositionlist\":true,\"docclosetradelist\":true,\"followcloseexport\":true,\"docclosetradelistConfluence\":true,\"docopentradelist\":true,\"followopenexport\":true,\"followtradelist\":true,\"doctradelistConfluence\":true,\"docplancloseorder\":true,\"docgetplanorder\":true,\"followaccinfo\":true,\"getflposstioncap\":true,\"getusertotalassets\":true,\"getusdassetsquery\":true,\"getwellinfo\":true,\"getaccinfo\":true,\"advancedsearch\":true,\"setuserlabl\":true,\"setuserlabldel\":true,\"getwalletbill\":true,\"getwallerhistory\":true,\"getacchistory\":true,\"getwithdrawlist\":true,\"withdrawcheck\":true,\"userexchangelist\":true,\"legalorderlist\":true,\"legalordercheck\":true,\"getuserpnl\":true,\"getusercaption\":true,\"getusercaptionexport\":true,\"getcommiss\":true,\"getcommissexport\":true,\"getagentcapital\":true,\"daypnllist\":true,\"getrebotlist\":true,\"getrebotlistexport\":true,\"sendrebot\":true,\"rebotswitchstatus\":true,\"getuserrebot\":true,\"getusertrade\":true,\"getusdrebotlist\":true,\"getusdrebotlistexport\":true,\"usdsendrebot\":true,\"usdrebotswitchstatus\":true,\"getusduserrebot\":true,\"getusdusertrade\":true,\"platwalletlist\":true,\"platwalletdaillist\":true,\"getplataccount\":true,\"plataccountdail\":true,\"liquidationGetList\":true,\"bursthousexport\":true,\"throughPayList\":true,\"throughhousexport\":true,\"bcactivitylist\":true,\"setactivity\":true,\"upuserward\":true,\"setactivityhash\":true,\"setactivitylssued\":true,\"getuseractivitylist\":true,\"useractivitycheck\":true,\"giftCashManageLook\":true,\"giftCashActiveLook\":true,\"topAgentList\":true,\"agentDirectlist\":true,\"bcprocontractcoinset\":true,\"bcprocontractcoinadd\":true,\"frequency\":true,\"setignorerebate\":true,\"highfrequency\":true,\"highpnl\":true,\"userpnlexport\":true,\"tradepnl\":true,\"userpnloperation\":false,\"highwinning\":true,\"highwinningexport\":true,\"systemmonitoring\":true,\"ipstatisticslist\":true,\"ipuserlist\":true,\"iptradelist\":true,\"watchuserlist\":true,\"watchusermark\":true,\"getgrouplist\":true,\"setgrouplist\":true,\"getriskuserlist\":true,\"getristlist\":true,\"getwhitelist\":true,\"configurationDownloadList\":true,\"configurationDownloadOperation\":true,\"contractAccount\":false,\"contractAccountTransfer\":false,\"contractAccountHistory\":false,\"contractAccountHistorySummary\":false,\"openCloseQuery\":true,\"openCloseQuerySummary\":true,\"positionQuery\":true,\"liquidationData\":true,\"planPL\":true,\"planQuery\":true,\"planPLExport\":true,\"positionMonitoring\":true,\"contractSlippageList\":true,\"contractSlippageSet\":true,\"bannerConfig\":true,\"bannerConfigdel\":true,\"bannerConfigadd\":true,\"managelist\":true,\"manageadd\":true,\"managedel\":true,\"managesave\":true,\"managesave1\":true,\"managesave2\":true,\"moldelist\":true,\"moldeladd1\":true,\"moldeladd3\":true,\"moldeldel\":true,\"moldelview\":true,\"getmanagelogs\":true,\"getagentoplog\":true,\"userlog\":true,\"getuserlogexport\":true,\"usererrlog\":true,\"contactUs\":true,\"openCloseQueryOperation\":true,\"openCloseQueryExport\":true,\"positionliste\":true,\"configurationDownloadMallAddrManagement\":true,\"configurationDownloadCreateNewVersion\":true,\"addLink\":true,\"addLinkOperation\":true,\"addLinkAdd\":true,\"configurationDownload\":true,\"configurationDownloadAdd\":true,\"followaccinfoexport\":true,\"getfollowcapital\":true,\"getwellinfoexport\":true,\"getaccinfoexport\":true,\"tradefrequencyexport\":true,\"setwhitelist\":true}"
        // moldeladd({model_data: model_data, model_name: this.zname, type: 3, modelid: parseInt(this.$route.query.id),}).then((res) => {
        moldeladd({model_data: JSON.stringify(this.qx), model_name: this.zname, type: 3, modelid: parseInt(this.$route.query.id),}).then((res) => {
          this.btnLoading = false;
          // console.log(this.qx)
          setTimeout(() => {
            this.$router.go(-1);
            this.$notify({ title: this.$t('dialog.Successful'), message: res.msg, type: "success" });
          }, 0.375 * 1000);
        });
      } else {
        let t = false;
        for (let pro in this.qx) {
          if (this.qx[pro]) {
            t = true;
          }
        }
        if (this.zname == null || this.zname == "") {
          this.$notify({
            title: this.$t('dialog.Warning'),
            message: this.$t('dialog.Please_fill_in_the_group_name'),
            type: "warning"
          });
        } else if (!t) {
          this.$notify({ title: this.$t('dialog.Warning'), message: this.$t('dialog.Please_check_permissions'), type: "warning" });
        } else {
          moldeladd({model_data: JSON.stringify(this.qx), model_name: this.zname, type: 1,}).then((res) => {
            this.btnLoading = false;
            setTimeout(() => {
              this.$router.go(-1);
              this.$notify({ title: this.$t('dialog.Successful'), message: res.msg, type: "success" });
            }, 0.375 * 1000);
          });
        }
      }
    },
    // isShow() {
    //   let level = localStorage.getItem("level");
    //   level = JSON.parse(level);
    //   return level === "1";
    // },
    // message(tag, data) {
    //   switch (tag.dataType) {
    //     case GETJSFZXQ:
    //       if (data.data) {
    //         let mode = JSON.parse(JSON.parse(data.data.modedata));
    //         Object.assign(this.qx, mode);
    //         // console.log(typeof mode)
    //       }
    //       break;
    //     case TJFZ: //添加分组
    //       if (data.ret === 0) {
    //         this.$router.go(-1);
    //         this.$notify({ title: "成功", message: data.msg, type: "success" });
    //       }
    //       break;
    //     case XGFZ2: //修改分组内容
    //       if (data.ret === 0) {
    //         this.$router.go(-1);
    //         this.$notify({ title: "成功", message: data.msg, type: "success" });
    //       }
    //       break;
    //   }
    // }
  },

};
</script>

<style lang="scss" scoped>
.big-box:nth-of-type(2) {
  margin-top: 20px;
}
.big-box {
  width: 99%;
  // border-top: 1px solid #f0f2f5;
  display: flex;
  flex-direction: column;
  // align-items: center;
  margin-top: 20px;
  justify-content: space-around;
  ::v-deep .el-divider--horizontal{
    margin-top: 10px;
    margin-bottom: 12px;
    .el-divider__text{
      color: #000;
      font-size: 14px;
      font-weight: bold;
    }
  }
  .big-box-one {
    display: flex;
    align-items: center;
    margin: 5px;
    .big-box-one_1 {
      min-width: 130px;
      font-size: 13px;
    }
    .big-box-one_2 {
      margin-left: 20px;
    }
  }
}
</style>

<style lang="scss" scoped>
.inputw {
  width: 200px;
  padding-right: 30px;
}
.name_span {
  padding-top: 2px;
}
.inputs {
  padding-right: 30px;
}

.buttons {
  margin-left: 20px;
}

.checkboxp {
  padding: 3px 0;
}

.dc {
  display: flex;
  padding: 10px 0px;
  box-sizing: border-box;
  border-bottom: 1px solid#F0F2F5;
  width: 100%;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;
  // flex-direction: column;
}

.tjfz {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 10px 30px;
  .w_menu {
    width: 100%;
    height: 50px;
    // padding-left:15px;
    padding-top: 15px;
    padding-bottom: 15px;
    flex-shrink: 0;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .searchTitle {
      padding: 0;
    }
  }
  .zc {
    & > div {
      display: flex;
    }
  }
  .w_c1 {
    .dc {
      display: flex;
      padding: 10px 0px;
      box-sizing: border-box;
      border-bottom: 1px solid#F0F2F5;
      width: 100%;
      justify-content: flex-start;
      align-items: flex-start;
      flex-wrap: wrap;
      flex-direction: column;
    }
    // display: flex;

    // margin-left: 30px;
    margin-top: 10px;
    flex-direction: column;
    border-bottom: 1px solid grey;
    .titlespanb {
      text-align: left;
      font-weight: 900;
      padding: 2px 0px;
    }
    .w_c2 {
      display: flex;
      flex-wrap: wrap;
      padding: 2px 0px;
      //   margin-top: ;
    }
    .itemdiv {
      height: 35px;
      // display: flex;
      min-width: 300px;
      padding: 4px 0;
      //   margin-top: 10px;
      .titlespan {
        display: inline-block;
        width: 300px;
        text-align: left;
        font-size: 14px;
        font-weight: bold;
      }
    }
  }
  .w_page {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-top: 10px;
    padding-right: 30px;
  }

  .dialogc {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .dialogstip {
    color: red;
    padding: 20px 10px;
  }
}
</style>
