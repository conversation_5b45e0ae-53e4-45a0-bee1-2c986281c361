import Vue from 'vue'

import 'normalize.css/normalize.css' // A modern alternative to CSS resets

//封装分页全局注册
import PaginaTion from '@/components/Pagination';

Vue.component('pagina-tion', PaginaTion);

import '@/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'

import '@/icons' // icon
import '@/permission' // permission control

import * as filters from './filters' // global filters

import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'

import * as echarts from 'echarts';
// 全局注册组件
Vue.prototype.$echarts = echarts
import "@/utils/math.js";


//对话框拖拽事件
import './directive/el-drag-dialog/drag'
//时间拖拽事件
// import './directive/el-data-picker'
/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */
if (process.env.NODE_ENV === 'production') {
    const { mockXHR } = require('../mock')
    mockXHR()
}


// register global utility filters
Object.keys(filters).forEach(key => {
    Vue.filter(key, filters[key])
})

Vue.config.productionTip = false

import VueI18n from 'vue-i18n'
Vue.use(VueI18n)
import zhLocale from '@/assets/lang/zh'
import enLocale from '@/assets/lang/en'
// import viLocale from '@/assets/lang/vi'
// import jaLocale from '@/assets/lang/ja'
// import zhTWLocale from '@/assets/lang/zhTW'
import enElLocale from 'element-ui/lib/locale/lang/en'
import zhElLocale from 'element-ui/lib/locale/lang/zh-CN'
// import viElLocale from 'element-ui/lib/locale/lang/vi'
const i18n = new VueI18n({
  locale: localStorage.getItem('lang')||'zh', // 语言标识
  messages: {
    'zh': Object.assign(zhLocale, zhElLocale),
    'en':  Object.assign(enLocale, enElLocale),
  }
})
Vue.use(ElementUI,{
  i18n:(key,value) => i18n.t(key,value)
})

new Vue({
    el: '#app',
    router,
    store,
    i18n,
    render: h => h(App)
})