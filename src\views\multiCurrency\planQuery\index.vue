<template>
  <div class="planQuery-cantainer">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        :placeholder="$t('filters.topIDNick')"
        style="width: 130px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        :placeholder="$t('filters.agent')"
        style="width: 150px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.contract_code"
        :placeholder="$t('tableHeader.contract')"
        clearable
        style="width: 120px; margin-right: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in contractOptions"
          :key="item.traderpairs"
          :label="item.traderpairs"
          :value="item.traderpairs"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.coinname"
        :placeholder="$t('filters.Margin_currency')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in marginCurrencyOptions"
          :key="item.currencyid"
          :label="item.currencyname"
          :value="item.currencyname"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.account_type"
        :placeholder="$t('filters.positionType')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in accountTypeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.side"
        :placeholder="$t('filters.direction')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in sizeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.status"
        :placeholder="$t('tableHeader.state')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in statusOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <span style="margin: 0 10px; font-size: 12px">{{ $t('tableHeader.triggerTime') }}</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px; margin-right: 10px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change="filterTimeTransform"
      >
      </el-date-picker>

      <el-button
        class="filter-item"
        size="mini"
        style="margin-left: 20px; margin-top: 5px"
        type="primary"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="holdList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="userid" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('filters.topID')" prop="top_agent_id" align="center" min-width="78"> 
        <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topNick')" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorID')" prop="pareid" align="center" min-width="78">
         <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.Position_number')" prop="position_id" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.position_id || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.contract')" prop="contractcode" min-width="78" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.contractcode || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.Margin_currency')"   prop="currency_name" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.currency_name || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.positionType')" min-width="80px" align="center">
        <template slot-scope="{ row }">
          <span>
            {{ row.account_type == 1 ? $t('tableHeader.all_warehouse') : 
              row.account_type == 2 ? $t('tableHeader.by_warehouse') :
              row.account_type == 5 ? $t('filters.All_warehouse_Points_storehouse') :
              row.account_type == 6 ? $t('filters.By_warehouse_Points_storehouse') : '--'
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.direction')" min-width="78" align="center">
        <template slot-scope="{ row }">
          <span>{{row.side=='S'?$t('tableHeader.buy_sides_jersey'):$t('tableHeader.sell_more_flat')}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.leverage')" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>&times;{{ row.lever }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.number')" prop="amount" align="center" min-width="78">
          <template slot-scope="{ row }">
          <span>{{ row.amount }}&nbsp;{{ row.currency_name }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('tableHeader.triggerPrice')" prop="trigger_price" align="center" min-width="78"> 
        <template slot-scope="{ row }">
          <span>{{ row.trigger_price || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.checkFullPrice')" prop="limit" align="center" min-width="78"> 
        <template slot-scope="{ row }">
          <span>{{ row.limit || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.StopLossPrice')" prop="stop" align="center" min-width="78"> 
        <template slot-scope="{ row }">
          <span>{{ row.stop || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.submitTime')" prop="create_time" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{row.create_time == "0"? '--' : row.create_time }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.triggerTime')" prop="order_time" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{row.order_time == "0"? '--' : row.order_time}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.cancelTime')" prop="cancel_time" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{row.cancel_time == "0"? '--' : row.cancel_time}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.triggerState')" prop="status" align="center" min-width="78">
          <template slot-scope="{ row }">
          <span>{{ statusObj[row.status] }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('tableHeader.Executive_price')"  prop="entrust_price" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('tableHeader.orderID')" prop="plan_order_id" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('tableHeader.entrustedClientIP')" prop="ip_address" align="center" min-width="78"></el-table-column>
      <el-table-column :label="$t('tableHeader.equipmentIdentificationCode')" prop="imei" align="center" min-width="78"></el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10, 50, 100, 200, 300]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getplanorder, bcusdprocontractset } from "@/api/multiCurrency";
import { getprocoinList } from "@/api/user";
export default {
  name: "usdPlanQuery",
  data() {
    return {
      listLoading: false,
      total: 0,
      holdList: null,
      contractOptions: [],
      filterTime: [],
      listQuery: {
        sname: "",          //用户id,手机号，邮箱
        sectop: "",         //顶级代理id或昵称
        sagent: "",         //代理id或者名字
        account_type: null, //账户模式 1：全仓 2：逐仓
        contract_code: "",  //合约代码
        side: null,         //方向 B买S卖
        status: undefined,  //状态 1: 未触发 0：取消 2：已触发 3: 触发失败
        stype: null,        //类型 2：止盈单 4：止损单
        coinname: '',       //保证金币种
        ip_address: "",
        pageNo: 1,
        pagesize: 10,
        star: "",                 // 开始
        end: "",                  // 结束
      },
      marginCurrencyOptions: [],  // 保证金币种
      accountTypeOptions: [
        { key: 1, name: this.$t("tableHeader.all_warehouse") },
        { key: 2, name: this.$t("tableHeader.by_warehouse") },
        // { key: 5, name: this.$t("filters.All_warehouse_Points_storehouse") },
        // { key: 6, name: this.$t("filters.By_warehouse_Points_storehouse") },
      ],
      sizeOptions: [
        { key: "S", name: this.$t("tableHeader.buy_sides_jersey") },
        { key: "B", name: this.$t("tableHeader.sell_more_flat") },
      ],
      statusOptions: [
        { key: 0, name: this.$t("filters.Has_been_cancelled") },
        { key: 1, name: this.$t("filters.Not_trigger") },
        { key: 2, name: this.$t("filters.Has_triggered") },
        { key: 3, name: this.$t("filters.Trigger_failure") },
        // { key: 4, name: '平仓撤销' },
        { key: 5, name: this.$t("filters.Unwind_to_cancel") },
      ],
      stypeOptions: [
        { key: 2, name: this.$t("tableHeader.check_single") },
        { key: 4, name: this.$t("tableHeader.stop_loss_orders") },
      ],

      statusObj: {
        0: this.$t("filters.Has_been_cancelled"),
        1: this.$t("filters.Not_trigger"),
        2: this.$t("filters.Has_triggered"),
        3: this.$t("filters.Trigger_failure"),
        5: this.$t("filters.Unwind_to_cancel"),
      },
    };
  },
  components: {},

  computed: {},

  mounted() {
    getprocoinList({}).then((res) => {
      for(var i=0; i<res.data.length; i++) {
        if(res.data[i].currencyname == "USDT") {
          res.data.splice(i,1)
        }
      }
      this.marginCurrencyOptions = res.data.filter(v => v.status)
    })
    bcusdprocontractset({}).then((res) => {
      this.contractOptions = res.data.filter((v) => v.isshow == 1);
    });
    this.getList();
  },
  methods: {
    getList() {
      this.listLoading = true;
      let data = {};
      Object.assign(data, this.listQuery, {
        coinname: this.listQuery.coinname, //  保证金币种 传入id
        status: this.listQuery.status === 0 ? 0 : this.listQuery.status || -1,
        side: this.listQuery.side || undefined,
        stype: this.listQuery.stype || undefined,
        account_type: this.listQuery.account_type || undefined,
      });
      getplanorder(data).then((response) => {
        this.holdList = response.data.list;
        this.total = response.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    // 时间
    filterTimeTransform(val) {
      this.listQuery.star = (val && val[0]) || "";
      this.listQuery.end = val ? val[1] + " 23:59:59" : "";
    },
  },
};
</script>

<style lang="scss" scoped>
</style>