import request from '@/utils/request'

//用户列表接口
export function userList(data) {
  return request({
    url: '/managers/v1/user/list',
    method: 'post',
    data: { data },
  })
}
//备注
export function addcont(data) {
  return request({
    url: '/managers/v1/user/addcont',
    method: 'post',
    data: { data },
  })
}
// 详情
export function userinfo(data) {
  return request({
    url: '/managers/v1/user/userinfo',
    method: 'post',
    data: { data },
  })
}
// 用户详情获取用户支付信息
export function userpayments(data) {
  return request({
    url: '/managers/v1/user/userpayments',
    method: 'post',
    data: { data },
  })
}
//编辑
export function bcprouserup(data) {
  return request({
    url: '/managers/v1/user/bcprouserup',
    method: 'post',
    data: { data },
  })
}
//修改代理状态
export function upagentstatus(data) {
  return request({
    url: '/managers/v1/user/upagentstatus',
    method: 'post',
    data: { data },
  })
}
//提升代理
export function setagent(data) {
  return request({
    url: '/managers/v1/user/setagent',
    method: 'post',
    data: { data },
  })
}
//KYC审核列表
export function verifyhistorylist(data) {
  return request({
    url: '/managers/v1/user/verifyhistorylist',
    method: 'post',
    data: { data },
  })
}
//KYC审核
export function bcupverifyhistory(data) {
  return request({
    url: '/managers/v1/user/bcupverifyhistory',
    method: 'post',
    data: { data },
  })
}
//KYC重置
export function bcupverifyreset(data) {
  return request({
    url: '/managers/v1/user/bcupverifyreset',
    method: 'post',
    data: { data },
  })
}
// 重置手机、邮箱、谷歌验证、资金密码
export function resetuserphone(data) {
  return request({
    url: '/managers/v1/user/resetuserphone',
    method: 'post',
    data: { data },
  })
}
// 重置CRM密码
export function resetagentpass(data) {
  return request({
    url: '/managers/v1/user/resetagentpass',
    method: 'post',
    data: { data },
  })
}

// 不返佣金/恢复返佣
export function whetherrebate(data) {
  return request({
    url: '/managers/v1/user/whetherrebate',
    method: 'post',
    data: { data },
  })
}

// 修改用户提币限制
export function upuserwithdrawlimit(data) {
  return request({
    url: '/managers/v1/user/upuserwithdrawlimit',
    method: 'post',
    data: { data },
  })
}

// 首页数据统计
export function getdaytotal(data) {
  return request({
    url: '/managers/v1/comm/getdaytotal',
    method: 'post',
    data: { data },
  })
}
