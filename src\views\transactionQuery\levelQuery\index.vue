<template>
  <div class="level-cantainer">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        :placeholder="$t('filters.topIDNick')"
        style="width: 130px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        :placeholder="$t('filters.agent')"
        style="width: 150px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.contract_code"
        :placeholder="$t('tableHeader.contract')"
        clearable
        style="width: 120px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in contractOptions"
          :key="item.traderpairs"
          :label="item.traderpairs"
          :value="item.traderpairs"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.account_type"
        :placeholder="$t('filters.positionType')"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in accountTypeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.side"
        :placeholder="$t('filters.direction')"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in sizeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.order_type"
        :placeholder="$t('filters.transactionType')"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in orderTypeArr"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <!-- <el-select
        size="mini"
        v-model="listQuery.status"
        :placeholder="$t('tableHeader.state')"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in statusOptions"
          :key="item.key"
          :label="item.status_name"
          :value="item.key"
        />
      </el-select> -->
      <span style="margin-left: 20px; font-size: 12px">{{$t('tableHeader.clinchDealTime')}}</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change='filterTimeTransform'
      >
      </el-date-picker>
      <el-input
        v-model="listQuery.ip"
        size="mini"
        placeholder="IP"
        style="width: 150px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        size="mini"
        v-model="listQuery.tradeid"
        :placeholder="$t('filters.transactionType')"
        style="width: 150px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-left: 20px; font-size: 12px">{{$t('filters.Opening_and_closing_time')}}</span>
      <el-input
        size="mini"
        v-model="listQuery.close_time"
        style="width: 150px; margin-left: 20px"
        class="filter-item"
        oninput="value= value.match(/\d+(\.\d{0})?/) ? value.match(/\d+(\d{0})?/)[0] : ''"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-left: 20px;"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
      <el-button
        class="filter-item"
        :loading="exportLoading"
        v-if="$store.getters.roles.indexOf('closetradeexport')>-1"
        @click="handleExport"
        size="mini"
        type="success"
      >
        {{$t('buttons.export')}}
      </el-button>
    </div>

    <!-- <el-row>
      <el-col :span="18" style="border: 1px solid #c9c9c9; margin: 25px 10%"> -->
  
        <div class="box_se" v-if="$store.getters.roles.indexOf('closetradelistConfluence')>-1">
             <div class="protradepnl_main protradepnlVal">
              <div class="hei">{{$t('others.Net_positions_PNL')}}</div>
               <!-- <div :class="`hei ${(protradepnl.netpnl == 0 || protradepnl.netpnl == '--')?'':protradepnl.netpnl>0?'green':'red'}`"> -->
                <div v-show="protradepnl.calNetpnl != '--'" :class="`hei ${(Number(protradepnl.calNetpnl) == 0 || Number(protradepnl.calNetpnl) == '--')?'':Number(protradepnl.calNetpnl)>0?'green':'red'}`">
                  <span>
                    <!-- {{ (protradepnl.netpnl>0 && '+' || '') + protradepnl.netpnl }} -->
                     {{ (Number(protradepnl.calNetpnl)>0 && '+' || '') + Number(protradepnl.clospnl).sub(Math.abs(protradepnl.commission))}}
                  </span>
                </div>
                <span v-show="protradepnl.calNetpnl == '--'">--</span>
            </div>
            <div class="protradepnl_main protradepnlVal">
              <div class="hei">{{$t('others.unwind_PNL')}}</div>
               <div :class="`hei ${(protradepnl.clospnl == 0 || protradepnl.clospnl == '--')?'':protradepnl.clospnl>0?'green':'red'}`">
                <span>{{ (protradepnl.clospnl>0 && '+' || '') + protradepnl.clospnl }}</span>
              </div>
            </div>
             <div class="protradepnl_main protradepnlVal">
              <div class="hei">{{$t('tableHeader.poundage')}}</div>
              <div :class="`hei ${(protradepnl.commission == 0 || protradepnl.commission == '--')?'':protradepnl.commission>0?'green':'red'}`">
                <span>{{ (protradepnl.commission>0 && '+' || '') + protradepnl.commission }}</span>
              </div>
            </div>
        </div>
       
      <!-- </el-col>
    </el-row> -->

    <el-table
      v-loading="listLoading"
      :data="levelList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="userid" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center" min-width="95"> </el-table-column>
      <el-table-column :label="$t('filters.topID')" prop="top_agent_id" align="center" min-width="78">
           <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('filters.topNick')" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorID')" prop="pareid" align="center" min-width="90">
           <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.transactionNumber')" prop="tradeid" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('tableHeader.contract')" prop="contractcode" min-width="90px" align="center"></el-table-column>
      <el-table-column :label="$t('filters.positionType')" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>
            {{ row.accounttype == 1 ? $t('tableHeader.all_warehouse') : 
              row.accounttype == 2 ? $t('tableHeader.by_warehouse') :
              row.accounttype == 5 ? $t('filters.All_warehouse_Points_storehouse') :
              row.accounttype == 6 ? $t('filters.By_warehouse_Points_storehouse') : '--'
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.direction')" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{row.side=='S'?$t('tableHeader.sell_more_flat'):$t('tableHeader.buy_sides_jersey')}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.leverage')" align="center" min-width="60px">
        <template slot-scope="{ row }">
          <span>&times;{{ row.lever }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.number')" prop="volume" align="center" min-width="110px"> 
        <template slot-scope="{ row }">
          <span>{{ row.volume }}{{$t('others.piece')}}</span><span>/{{row.conversion}}{{row.contractcode.slice(0,-4)}}</span>
        </template>
      </el-table-column>
     <el-table-column :label="$t('tableHeader.averageOpen')" prop="open_avg_price" align="center" min-width="90px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.unwindPrice')" prop="price" align="center" min-width="90px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.poundage')" prop="commission" align="center" min-width="90px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.net_PNl')" align="center" min-width="90px">
        <template slot-scope="{ row }">
          <span>{{ row.profit}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.PNL')" prop="closeprofit" align="center" min-width="90px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.Amount_of_use_of_bonus')" prop="gift_amount" align="center" min-width="90px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.Bonus_handling_fee')" prop="gift_fee" align="center" min-width="90px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.Available_balance_of_bonus')" prop="gift_available" align="center" min-width="90px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.Give_gold_balance')" prop="gift_balance" align="center" min-width="90px"> </el-table-column>
      <!-- <el-table-column :label="$t('tableHeader.Time_stamp_of_last_open_position')" prop="cur_open_time" align="center" min-width="90px"> </el-table-column> -->
      <el-table-column :label="$t('filters.transactionType')" align="center" min-width="90px">
        <template slot-scope="{ row }">
          <span>{{ orderTypeObj[row.ordertype]  }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.Open_and_close_interval')" prop="diff_open_time" align="center" min-width="100px">>
        <template slot-scope="{ row }">
          <span>{{ row.diff_open_time || '--'  }}</span> <span v-if="row.diff_open_time">s</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.IP_addr')" prop="ipaddress" align="center" min-width="100px">>
        <template slot-scope="{ row }">
          <span>{{ row.ipaddress || '--'  }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.clinchDealTime')" prop="tradetime" align="center" min-width="75"> </el-table-column>
      <el-table-column :label="$t('tableHeader.openPositionsTime')" prop="last_open_time" align="center" min-width="75">
        <template slot-scope="{ row }">
          <span>{{ parseInt((new Date(row.last_open_time)).getTime()/1000).toDate() }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.positionID')" prop="position_id" align="center" min-width="75">
        <template slot-scope="{ row }">
          <span>{{ row.position_id || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.orderSource')" prop="imei" align="center" min-width="130px">
        <template slot-scope="{ row }">
          <!-- <span>{{orderTypeObj[row.ordertype]+'--'+os_typeObj[row.orderclient]}}</span> -->
          <span>{{row.fromType}}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,50,100,200,300]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
//封装的api
import { closetradelist,closetradeexport } from "@/api/transactionQuery";
import { bcprocontractset } from "@/api/user";

// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";

export default {
  name: "levelquery",
  data() {
    return {
      listLoading: false,
      total: 0,
      levelList: null,
      filterTime: [],
      listQuery: {
        tradeid: "", //
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        account_type: undefined, //账户模式 1：全仓 2：逐仓
        contract_code: "", //合约代码
        side: undefined, //方向 B买S卖
        order_type: "",  // 交易类型
        close_time: "", // 开平仓时间
        pageNo: 1,
        pagesize: 10,
        star: '', //开始
        end: '', //结束
        ip:""
      },
      contractOptions: [],
      accountTypeOptions: [
        { key: 1, name: this.$t('tableHeader.all_warehouse') },
        { key: 2, name: this.$t('tableHeader.by_warehouse') },
        { key: 5, name: this.$t('filters.All_warehouse_Points_storehouse') },
        { key: 6, name: this.$t('filters.By_warehouse_Points_storehouse') },
      ],
      sizeOptions: [
        // { key: "B", name: '买入开多' },
        // { key: "S", name: '卖出开空' },
         { key: "B", name: this.$t('tableHeader.buy_sides_jersey') },
        { key: "S", name: this.$t('tableHeader.sell_more_flat') },
      ],
      orderTypeArr:[
        {key: 0, name: this.$t('filters.Market_orders')},
        {key: 1, name: this.$t('tableHeader.order')}, 
        {key: 2, name: this.$t('tableHeader.check_single')}, 
        {key: 4, name: this.$t('tableHeader.stop_loss_orders')}, 
        {key: 5, name: this.$t('filters.Strong_flat_sheet')},
      ],
      orderTypeObj: {
        0: this.$t('filters.Market_orders'),
        1: this.$t('tableHeader.order'), 
        2: this.$t('tableHeader.check_single'), 
        4: this.$t('tableHeader.stop_loss_orders'), 
        5: this.$t('filters.Strong_flat_sheet')
      },
      os_typeObj:{
        0: 'open_api',
        1: "android",
        2: "iOS",
        3: "WEB",
        4: "H5",
        5: "open_api",
        6: this.$t('others.system_automatically'),
      },
      protradepnl: {
        netpnl: '--', //净netpnl
        clospnl: '--', //平仓pnl
        commission: '--', //手续费
        calNetpnl: '--', //计算净平仓
      },
      //总条数，默认为0
      exportLoading: false, //导出加载中效果
    };
  },

  components: {},

  computed: {
    // 默认时间
    timeDefault () {
      let date = new Date()
      // 通过时间戳计算
      let defalutStartTime = ((date.getTime() - 1 * 24 * 3600 * 1000)/1000).toDate('yyyy-MM-dd') // 转化为时间戳
      let defalutEndTime = ((date.getTime()/1000)).toDate('yyyy-MM-dd')
      return [defalutStartTime, defalutEndTime]
    }
  },

  mounted() {
    bcprocontractset({}).then((res) => {
      this.contractOptions = res.data.filter(v=>v.isshow == 1)
    })
    this.filterTime = this.timeDefault;
    this.getList();
  },

  methods: {
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList();
    },
    getList() {
      this.listLoading = true;
      let data = {}
      Object.assign(data, this.listQuery)
      data.side = this.listQuery.side || undefined
      data.account_type = this.listQuery.account_type || undefined
      data.star = (this.filterTime && this.filterTime[0]) || "";
      data.end = this.filterTime ? this.filterTime[1] + " 23:59:59" : "";
      data.order_type = data.order_type === ''?-1:data.order_type
      data.close_time = Number(this.listQuery.close_time)
      
      closetradelist(data).then((res) => {
        if(res.data.list && res.data.list.length){
          this.levelList = res.data.list.map((v)=>{
            // <span>{{orderTypeObj[row.ordertype]+'--'+os_typeObj[row.orderclient]}}</span>
            // orderclient ==> 1: android 2: iOS 3: WEB 4: H5 0: open_api 6: 系统自动
            // ordertype ==> 0: 市价单 1：计划单 2：止盈单 4：止损单 5：强平单  
                // 带单平仓：带单交易员自主平仓所产生的跟单平仓
                // 带单止盈：带单交易员设置的止盈价触发后的平仓
                // 带单止损：带单交易员设置的止损价触发后的平仓
            // api平仓：用户通过api的平仓
            // 用户平仓：用户自主平仓
            // 止盈平仓：用户自己设置的止盈价触发后的平仓
            // 止损平仓：用户自己设置的止损价触发后的平仓
            // 系统平仓：如强制平仓等系统触发的平仓
            if(v.orderclient == 0 || v.orderclient == 5){
            // api
              if(v.ordertype == 2){
                v.fromType = this.$t('filters.Check_surplus')
              }else if(v.ordertype == 4){
                v.fromType = this.$t('filters.Stop_positions')
              }else if(v.ordertype == 5){
                v.fromType = this.$t('filters.System_to_unwind')
              }else{
                v.fromType = this.$t('filters.API_to_unwind')
              }
            }else if(v.orderclient == 6){
            // 系统自动
              v.fromType = this.$t('filters.System_to_unwind')
            }else{
            // 用户
              if(v.ordertype == 2){
                v.fromType = this.$t('filters.Check_surplus')
              }else if(v.ordertype == 4){
                v.fromType = this.$t('filters.Stop_positions')
              }else{
                v.fromType = this.$t('filters.Users_to_unwind')+this.os_typeObj[v.orderclient]
              }
            }
            return v
          });
        }else{
          this.levelList = []
        }
        this.total = res.data.total;
        let protradepnl = res.data.protradepnl
        Object.assign(this.protradepnl, protradepnl, {
          calNetpnl: Number(protradepnl.clospnl).sub(protradepnl.commission)
        })
        this.listLoading = false;
      });
    },
    filterTimeTransform(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val? val[1]+' 23:59:59':'';
    },
    //表格导出功能
    handleExport() {
      this.exportLoading = true;
      let data = {}
      Object.assign(data,this.listQuery)
      let newDate = parseInt(new Date().getTime()/1000);
      data.end = this.listQuery.end ? this.listQuery.end : (newDate.toDate('yyyy-MM-dd')+ ' 23:59:59')
      newDate = parseInt(newDate-518400)
      data.star = this.listQuery.star ? (this.listQuery.star + ' 00:00:00' ): (newDate.toDate('yyyy-MM-dd')+ ' 00:00:00')
      data.side = this.listQuery.side || undefined
      data.account_type = this.listQuery.account_type || undefined
      data.order_type = data.order_type === ''?-1:data.order_type
      data.close_time = Number(this.listQuery.close_time)
      closetradeexport(data).then((res) => {
        if(res.ret == 0){
            // window.location.href=res.data.download_url;
          this.$notify.success({title:this.$t('dialog.Operation_is_successful'),message:this.$t('dialog.Please_jiaoyi_daochu_download')})
          this.exportLoading = false
        }
      }).catch(err=>{
        this.exportLoading = false
      })
    },
  },
};
</script>
<style lang="scss" scoped>
.box_se{
  border: 1px solid #c9c9c9;
  margin: 25px 10%;
  display:flex;
  flex-wrap: wrap;
} 
.protradepnl_main {
  display: flex;
  align-items: center;
  flex-direction: column;
  flex-wrap: wrap;
  width: 33%;
  .red {
    color: #DF334E;
  }
  .green {
    color: #309F72;
  }
  &>div{
    // width: 33.3%;
    text-align: center;
  }
}
.protradepnlKey{
  margin-top: 15px;
  margin-bottom: 5px;
}
.protradepnlVal{
  font-size: 18px;
  margin: 15px auto;
  // padding: 10px auto;
}
</style>