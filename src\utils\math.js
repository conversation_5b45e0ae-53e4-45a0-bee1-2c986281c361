import { BigNumber } from 'bignumber.js'
import JSONbig from 'json-bigint';
JSON.stringify = (...parmas) => JSONbig.stringify(...parmas);
JSON.parse = (...parmas) => JSONbig.parse(...parmas);
//加  
Number.prototype.add = function (num) {
    return (new BigNumber(this)).plus(num);
}
//减   
Number.prototype.sub = function (num) {
    return (new BigNumber(this)).minus(num);
}
// 乘
Number.prototype.mul = function (num) {
    return (new BigNumber(this)).multipliedBy(num);
};
// 除
Number.prototype.div = function (num) {
    return (new BigNumber(this)).dividedBy(num);
};
// 指数
Number.prototype.pow = function (num) {
    return (new BigNumber(this)).pow(num);
};
// 截取小数位
Number.prototype.cutXiaoNum = function (len = 4) {
    var numStr = this.toString();
    var index = numStr.indexOf('.');
    if (index == -1) {
        index = numStr.length;
        numStr += ".0000000000000";
    } else {
        numStr += "0000000000000";
    }
    var newNum = numStr.substring(0, index + len + 1);
    return newNum;
}
//时间戳转换
Number.prototype.toDate = function (fmt) {
    let date = new Date(this * 1000);
    return date.dateHandle(fmt)
};
Date.prototype.dateHandle = function (fmt) {
    fmt = fmt || 'yyyy-MM-dd HH:mm:ss';
    let obj =
    {
        'y': this.getFullYear(), // 年份，注意必须用getFullYear
        'M': this.getMonth() + 1, // 月份，注意是从0-11
        'd': this.getDate(), // 日期
        'w': this.getDay(),
        'H': this.getHours(), // 24小时制
        'h': this.getHours() % 12 === 0 ? 12 : this.getHours() % 12, // 12小时制
        'm': this.getMinutes(), // 分钟
        's': this.getSeconds(), // 秒
        'S': this.getMilliseconds() // 毫秒
    };
    let week = ['日', '一', '二', '三', '四', '五', '六'];
    for (let i in obj) {
        fmt = fmt.replace(new RegExp(i + '+', 'g'), function (wfy) {
            let val = obj[i] + '';
            if (i === 'w') return (wfy.length > 2 ? '星期' : '周') + week[val];
            for (let j = 0, len = val.length; j < wfy.length - len; j++) val = '0' + val;
            return wfy.length === 1 ? val : val.substring(val.length - wfy.length);
        });
    }
    return fmt;
};