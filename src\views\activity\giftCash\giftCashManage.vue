<template>
  <div class="giftCashManage-container">
    <!-- <div class="btns-container">
      <el-button @click="handleIssue" type="primary">{{ $t('menus.Issue_experience_gold') }}</el-button>
      <el-button @click="handleRecycle" type="primary">{{ $t('menus.Recycle_experience_gold') }}</el-button>
    </div> -->

    <!-- 汇总数据 -->
    <div class="summaryList" v-loading="listLoading0">
      <div>
        <span v-for="(item,index) in arrTitle" :key="index">{{item}}</span>
      </div>
      <div>
        <span v-for="(item,index) in summaryData" :key="index">{{item}}</span>
      </div>
    </div>

    <el-tabs v-model="activeName" @tab-click="handleClick" style="margin:20px 0">
      <!-- 领取赠金 -->
      <el-tab-pane :label="$t('menus.To_receive_a_gift_of_gold')" name="first">
        <div class="filter-container">
          <el-input 
            size="mini"
            v-model="listQueryToreceive.sname" 
            :placeholder="$t('filters.name')"
            style="width: 150px"
            class="filter-item"
            clearable
            @keyup.enter.native="handleFilter"
          />
          <!-- <el-select
            size="mini"
            v-model="listQueryToreceive.withGoldName"
            :placeholder="$t('tableHeader.Give_name_of_gold')"
            clearable
            style="width: 120px; margin-left: 20px"
            class="filter-item"
          >
            <el-option
              v-for="item in withGoldNameOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select> -->
          <el-select
            size="mini"
            v-model="listQueryToreceive.status"
            :placeholder="$t('tableHeader.state')"
            clearable
            style="width: 120px; margin-left: 20px"
            class="filter-item"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <span style="margin-left: 20px; font-size: 12px">{{ $t('filters.Get_the_time') }}</span>
          <el-date-picker
            style="width: 220px; margin-left: 20px; margin-top: 10px"
            v-model="filterTimeToreceive"
            size="mini"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="-"
            :start-placeholder="$t('filters.startTime')"
            :end-placeholder="$t('filters.endTime')"
            @change="filterTimeTransformToreceive"
          >
          </el-date-picker>

          <el-button
            class="filter-item"
            size="mini"
            type="primary"
            style="margin: 10px 0 0 20px;"
            @click="handleFilter(1)"
          >
            {{$t('buttons.search')}}
          </el-button>
        </div>

        <el-table
          v-loading="listLoading1"
          :data="tableListToreceive"
          border
          fit
          highlight-current-row
          size="mini"
          style="width: 100%; margin-top: 30px"
          :header-cell-style="{ background: '#F0F8FF' }"
        >
          <el-table-column prop="user_id"       :label="$t('tableHeader.uid')" align="center" min-width="75px"></el-table-column>
          <!-- <el-table-column prop=""              :label="$t('tableHeader.userName')" align="center" min-width="75px"></el-table-column> -->
          <el-table-column prop="gift_name"     :label="$t('tableHeader.Give_name_of_gold')" align="center" min-width="75px"></el-table-column>
          <el-table-column prop="amount"        :label="$t('tableHeader.amount')" align="center" min-width="75px"></el-table-column>
          <el-table-column prop="after_amount"  :label="$t('tableHeader.Balance_of_users_bonus')" align="center" min-width="75px"></el-table-column>
          <el-table-column prop="state"         :label="$t('tableHeader.state')" align="center" min-width="75px">
            <template slot-scope="{ row }">
              <span>
                {{
                  row.state == 1 ? $t('filters.Not_at_the') :
                  row.state == 2 ? $t('others.ongoing') :
                  row.state == 3 ? $t('filters.To_receive') :
                  row.state == 4 ? $t('filters.Have_to_receive') : 
                  row.state == 5 ? $t('filters.Expired') : 
                  row.state == 6 ? $t('filters.Take_back') : '--'
                }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="create_time"   :label="$t('tableHeader.creationTime')" align="center" min-width="75px"></el-table-column>
          <el-table-column prop="expire_time"   :label="$t('tableHeader.Expiration_time')" align="center" min-width="75px"></el-table-column>
          <el-table-column prop="receive_time"  :label="$t('filters.Get_the_time')" align="center" min-width="75px"></el-table-column>
          <el-table-column prop="active_time"   :label="$t('tableHeader.The_activation_time')" align="center" min-width="75px"></el-table-column>
          <el-table-column prop="recovery_time" :label="$t('tableHeader.Recovery_time')" align="center" min-width="75px"></el-table-column>
          <el-table-column prop="imei"          :label="$t('tableHeader.Equipment_information')" align="center" min-width="75px">
            <template slot-scope="{ row }">
              <span>{{ row.imei || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="ip"            :label="$t('tableHeader.IP')" align="center" min-width="75px">
            <template slot-scope="{ row }">
              <span>{{ row.ip || '--' }}</span>
            </template>
          </el-table-column>
        </el-table>

        <pagina-tion
          v-show="totalToreceive > 0"
          :total="totalToreceive"
          :page-sizes="[10, 25, 50]"
          :page.sync="listQueryToreceive.pageNo"
          :limit.sync="listQueryToreceive.pagesize"
          @pagination="getList(1)"
        />
      </el-tab-pane>

      <!-- 使用赠金 -->
      <el-tab-pane :label="$t('menus.Give_use_a_gold')" name="second">
        <div class="filter-container">
          <el-input 
            size="mini"
            v-model="listQueryUse.sname" 
            :placeholder="$t('filters.name')"
            style="width: 150px"
            class="filter-item"
            clearable
            @keyup.enter.native="handleFilter"
          />
          <el-select
            size="mini"
            v-model="listQueryUse.type"
            :placeholder="$t('filters.type')"
            clearable
            style="width: 120px; margin-left: 20px"
            class="filter-item"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <span style="margin-left: 20px; font-size: 12px">{{ $t('tableHeader.time') }}</span>
          <el-date-picker
            style="width: 220px; margin-left: 20px; margin-top: 10px"
            v-model="filterTimeUse"
            size="mini"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="-"
            :start-placeholder="$t('filters.startTime')"
            :end-placeholder="$t('filters.endTime')"
            @change="filterTimeTransformUse"
          >
          </el-date-picker>

          <el-button
            class="filter-item"
            size="mini"
            type="primary"
            style="margin: 10px 0 0 20px;"
            @click="handleFilter(2)"
          >
            {{$t('buttons.search')}}
          </el-button>
        </div>

        <el-table
          v-loading="listLoading2"
          :data="tableListUse"
          border
          fit
          highlight-current-row
          size="mini"
          style="width: 100%; margin-top: 30px"
          :header-cell-style="{ background: '#F0F8FF' }"
        >
          <el-table-column prop="user_id"       :label="$t('tableHeader.uid')" align="center" min-width="75px"></el-table-column>
          <el-table-column prop="user_name"     :label="$t('tableHeader.userName')" align="center" min-width="75px"></el-table-column>
          <el-table-column prop="amount"        :label="$t('tableHeader.amount')" align="center" min-width="75px"></el-table-column>
          <el-table-column prop="gift_balance"  :label="$t('tableHeader.Balance_of_users_bonus')" align="center" min-width="75px"></el-table-column>
          <el-table-column prop="type"          :label="$t('tableHeader.Use_the_type')" align="center" min-width="75px">
            <template slot-scope="{ row }">
              <span>
                {{
                  row.type == 1       ? $t('filters.Opening_charge')  :
                  row.type == 2       ? $t('tableHeader.moneyCost')  :
                  row.type == 4       ? $t('filters.Transfer_from_asset_account')  :
                  row.type == 8       ? $t('filters.Transfer_to_assets_account')  :
                  row.type == 16      ? $t('filters.Unwinding_of_profit_and_loss')  :
                  row.type == 32      ? $t('filters.Closing_charge')  :
                  row.type == 64      ? $t('filters.Analog_disk_supplement_assets')  :
                  row.type == 128     ? $t('filters.Margin_reduction')  :
                  row.type == 256     ? $t('filters.Withhold_commissions')  :
                  row.type == 512     ? $t('filters.Commission_refund')  :
                  row.type == 1024    ? $t('filters.Commission_income')  :
                  row.type == 2048    ? $t('filters.Transfer_from_trading_account_to_documentary_account')  :
                  row.type == 4096    ? $t('filters.Transfer_from_documentary_account_to_trading_account')  :
                  row.type == 8192    ? $t('filters.Transfer_from_asset_account_to_documentary_account')  :
                  row.type == 16384   ? $t('filters.Transfer_from_documentary_account_to_asset_account')  :
                  row.type == 32768   ? $t('filters.Strong_flat_back')  :
                  row.type == 65536   ? $t('filters.Analog_disk_reduces_assets')  :
                  row.type == 131072  ? $t('filters.Liquidation_commission')  :
                  row.type == 262144  ? $t('filters.Deduction_of_abnormal_assets')  :
                  row.type == 524288  ? $t('filters.Margin_increase')  :
                  row.type == 1048576 ? $t('filters.Give_gold_to_receive')  :
                  row.type == 2097152 ? $t('filters.Give_gold_failure')  :
                  row.type == 4194304 ? $t('filters.Give_gold_recovery')  : '--'
                }}
            </span>
            </template>
          </el-table-column>
          <el-table-column prop="created_time"  :label="$t('tableHeader.time')" align="center" min-width="75px"></el-table-column>
        </el-table>

        <pagina-tion
          v-show="totalUse > 0"
          :total="totalUse"
          :page-sizes="[10, 25, 50]"
          :page.sync="listQueryUse.pageNo"
          :limit.sync="listQueryUse.pagesize"
          @pagination="getList(2)"
        />
      </el-tab-pane>
    </el-tabs>

    <!-- 发放体验金 -->
    <!-- <div class="issue">
      <div class="title">
        {{ $t('menus.Issue_experience_gold') }}
      </div>
      <div class="filter-container">
        <el-input 
          size="mini"
          v-model="listQueryIssue.sname" 
          :placeholder="$t('filters.name')"
          style="width: 150px"
          class="filter-item"
          clearable
          @keyup.enter.native="handleFilter"
        />
        <el-input 
          size="mini"
          v-model="listQueryIssue.sectop" 
          :placeholder="$t('filters.topID')"
          style="width: 150px; margin-left: 20px"
          class="filter-item"
          clearable
          @keyup.enter.native="handleFilter"
        />
        <el-input 
          size="mini"
          v-model="listQueryIssue.sagent" 
          :placeholder="$t('filters.agent')"
          style="width: 150px; margin-left: 20px"
          class="filter-item"
          clearable
          @keyup.enter.native="handleFilter"
        />
        <span style="margin-left: 20px; font-size: 12px">{{ $t('tableHeader.issueTime') }}</span>
        <el-date-picker
          style="width: 220px; margin-left: 20px; margin-top: 10px"
          v-model="filterTimeIssue"
          size="mini"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="-"
          :start-placeholder="$t('filters.startTime')"
          :end-placeholder="$t('filters.endTime')"
          @change="filterTimeTransformIssue"
        >
        </el-date-picker>

        <el-button
          class="filter-item"
          size="mini"
          type="primary"
          style="margin: 10px 0 0 20px;"
          @click="handleFilter(3)"
        >
          {{$t('buttons.search')}}
        </el-button>
      </div>

      <el-table
        v-loading="listLoading3"
        :data="tableListIssue"
        border
        fit
        highlight-current-row
        size="mini"
        style="width: 100%; margin-top: 30px"
        :header-cell-style="{ background: '#F0F8FF' }"
      >
        <el-table-column prop="user_id"       :label="$t('tableHeader.uid')" align="center"></el-table-column>
        <el-table-column prop="user_name"     :label="$t('tableHeader.userName')" align="center"></el-table-column>
        <el-table-column prop="amount"        :label="$t('tableHeader.amount')" align="center"></el-table-column>
        <el-table-column :label="$t('filters.type')" align="center">
          <template>
            <span>{{ $t('tableHeader.Administrator_issue') }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="after_amount"  :label="$t('tableHeader.Give_gold_balance')" align="center"></el-table-column>
        <el-table-column prop="created_time"  :label="$t('tableHeader.creationTime')" align="center"></el-table-column>
        <el-table-column prop="manage"        :label="$t('tableHeader.handlers')" align="center"></el-table-column>
      </el-table>
      <pagina-tion
        v-show="totalIssue > 0"
        :total="totalIssue"
        :page-sizes="[10, 25, 50]"
        :page.sync="listQueryIssue.pageNo"
        :limit.sync="listQueryIssue.pagesize"
        @pagination="getList(3)"
      />
    </div> -->

    <!-- 回收体验金 -->
    <!-- <div class="recycle">
      <div class="title">
        {{ $t('menus.Recycle_experience_gold') }}
      </div>
      <div class="filter-container">
        <el-input 
          size="mini"
          v-model="listQueryRecycle.sname" 
          :placeholder="$t('filters.name')"
          style="width: 150px"
          class="filter-item"
          clearable
          @keyup.enter.native="handleFilter"
        />
        <el-input 
          size="mini"
          v-model="listQueryRecycle.sectop" 
          :placeholder="$t('filters.topID')"
          style="width: 150px; margin-left: 20px"
          class="filter-item"
          clearable
          @keyup.enter.native="handleFilter"
        />
        <el-input 
          size="mini"
          v-model="listQueryRecycle.sagent" 
          :placeholder="$t('filters.agent')"
          style="width: 150px; margin-left: 20px"
          class="filter-item"
          clearable
          @keyup.enter.native="handleFilter"
        />
        <span style="margin-left: 20px; font-size: 12px">{{ $t('tableHeader.Recovery_time') }}</span>
        <el-date-picker
          style="width: 220px; margin-left: 20px; margin-top: 10px"
          v-model="filterTimeRecycle"
          size="mini"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="-"
          :start-placeholder="$t('filters.startTime')"
          :end-placeholder="$t('filters.endTime')"
          @change="filterTimeTransformRecycle"
        >
        </el-date-picker>

        <el-button
          class="filter-item"
          size="mini"
          type="primary"
          style="margin: 10px 0 0 20px;"
          @click="handleFilter(4)"
        >
          {{$t('buttons.search')}}
        </el-button>
      </div>

      <el-table
        v-loading="listLoading4"
        :data="tableListRecycle"
        border
        fit
        highlight-current-row
        size="mini"
        style="width: 100%; margin-top: 30px"
        :header-cell-style="{ background: '#F0F8FF' }"
      >
        <el-table-column prop="user_id"       :label="$t('tableHeader.uid')" align="center"></el-table-column>
        <el-table-column prop="user_name"     :label="$t('tableHeader.userName')" align="center"></el-table-column>
        <el-table-column prop="amount"        :label="$t('tableHeader.amount')" align="center"></el-table-column>
        <el-table-column :label="$t('filters.type')" align="center">
          <template>
            <span>{{ $t('tableHeader.Administrator_recycling') }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="after_amount"  :label="$t('tableHeader.Give_gold_balance')" align="center"></el-table-column>
        <el-table-column prop="created_time"  :label="$t('tableHeader.creationTime')" align="center"></el-table-column>
        <el-table-column prop="manage"        :label="$t('tableHeader.handlers')" align="center"></el-table-column>
      </el-table>

      <pagina-tion
        v-show="totalRecycle > 0"
        :total="totalRecycle"
        :page-sizes="[10, 25, 50]"
        :page.sync="listQueryRecycle.pageNo"
        :limit.sync="listQueryRecycle.pagesize"
        @pagination="getList(4)"
      />
    </div> -->


    <!-- 发放体验金 -->
    <!-- <el-dialog
      :title="$t('menus.Issue_experience_gold')"
      :visible.sync="dialogVisibleIssue"
      width="550px"
      :before-close="handleCloseIssue">

      <el-form ref="formIssue" :model="formIssue" :rules="rulesFormIssue" :label-position="labelPosition" label-width="80px">
        <el-form-item :label="$t('tableHeader.Give_name_of_gold')" prop="WithGoldName">
          <el-input v-model="formIssue.WithGoldName" clearable></el-input>
        </el-form-item>
        <el-form-item :label="$t('forms.Input_the_UID')">
          <el-input v-model="formIssue.UserID" clearable oninput="value=value.match(/\d+(\d{0})?/) ? value.match(/\d+(\d{0})?/)[0] : ''"></el-input>
          <el-button type="primary" :disabled="!formIssue.UserID" @click="handleAddIssue" class="addBtn">{{ $t('buttons.add') }}</el-button>
        </el-form-item>
        <el-form-item label="" prop="UidList">
          <el-input type="textarea" v-model="formIssue.UidList" disabled></el-input>
        </el-form-item>
        <el-form-item :label="$t('forms.Amount_of_grant_issued')" prop="IssueAmount">
          <el-input v-model="formIssue.IssueAmount" clearable oninput="value=value.match(/\d+(\.\d{0,2})?/) ? value.match(/\d+(\d{0})?/)[0] : ''"></el-input>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleIssueCancel">{{ $t('buttons.cancel') }}</el-button>
        <el-button type="primary" @click="handleIssueSure">{{ $t('buttons.determine') }}</el-button>
      </span>
    </el-dialog> -->

    <!-- 回收体验金 -->
    <!-- <el-dialog
      :title="$t('menus.Recycle_experience_gold')"
      :visible.sync="dialogVisibleRecycle"
      width="550px"
      :before-close="handleCloseRecycle">
      
      <el-form ref="formRecycle" :model="formRecycle" :rules="rulesFormRecycle" :label-position="labelPosition" label-width="80px">
        <el-form-item :label="$t('forms.Input_the_UID')">
          <el-input v-model="formRecycle.UserID" clearable oninput="value=value.match(/\d+(\d{0})?/) ? value.match(/\d+(\d{0})?/)[0] : ''"></el-input>
          <el-button type="primary" :disabled="!formRecycle.UserID" @click="handleAddRecycle" class="addBtn">{{ $t('buttons.add') }}</el-button>
        </el-form-item>
        <el-form-item label="" prop="UidList">
          <el-input type="textarea" v-model="formRecycle.UidList" disabled></el-input>
        </el-form-item>
        <el-form-item :label="$t('forms.Recovery_of_amount_of_the_bonus')" prop="RecycleAmount">
          <el-input v-model="formRecycle.RecycleAmount" clearable oninput="value=value.match(/\d+(\.\d{0,2})?/) ? value.match(/\d+(\d{0})?/)[0] : ''"></el-input>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleRecycleCancel">{{ $t('buttons.cancel') }}</el-button>
        <el-button type="primary" @click="handleRecycleSure">{{ $t('buttons.determine') }}</el-button>
      </span>
    </el-dialog> -->
  </div>
</template>

<script>
import { 
  gifttotal,      // 获取体验金汇总
  getgiftrecord,  // 获取用户赠金领取列表
  usegiftrecord,  // 获取用户赠金使用
  sentgiftrecord, // 发放体验金
  getsentgift,    // 获取用户赠金列表
  recoverygift,   // 回收体验金
  getrecoverygift,// 获取用户取消赠金列表
} from "@/api/activity";
import { userinfo } from "@/api/userQuery"
export default {
  name: "giftCashManage",
  data() {
    // 赠金名称
    let WithGoldName_v = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('dialog.Please_enter_the_name_of_the_donation')))
      }  else {
        callback()
      }
    }
    // uidList
    let Issue_UidList_v = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('dialog.Please_enter_the_uid')))
      } else {
        callback()
      }
    }
    // 发放金额
    let IssueAmount_v = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('dialog.Please_enter_the_amount_of_the_grant')))
      } else if(value <= 0) {
        return callback(new Error(this.$t('dialog.The_amount_than_zero')))
      } else if (value > 50) {
        return callback(new Error(this.$t('dialog.The_amount_than_fifty')))
      } else {
        callback()
      }
    }
    // uidList
    let Recycle_UidList_v = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('dialog.Please_enter_the_uid')))
      } else {
        callback()
      }
    }
    // 回收赠金
    let RecycleAmount_v = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('dialog.Please_enter_the_amount_of_refund')))
      } else if (value <= 0) {
        return callback(new Error(this.$t('dialog.Recycling_amount_than_zero')))
      } else {
        callback()
      }
    }
    return {
      listLoading0: false,
      listLoading1: false,
      listLoading2: false,
      listLoading3: false,
      listLoading4: false,
      arrTitle: [
        this.$t('menus.Total_claim_by_user'),
        this.$t('menus.Total_user_availability'),
        this.$t('menus.Freezing_total_Users'),
        this.$t('menus.Total_loss_of_users'),
        this.$t('menus.Total_User_expiration'),
        this.$t('menus.Always_accept'),
      ],
      summaryData: {},        // 总数据
      activeName: 'first',    // tab切换
      labelPosition: 'top',   // label top
      withGoldNameOptions: [
        { value: 1, label: this.$t('tableHeader.Top_up_for_the_first_time') },
        { value: 2, label: this.$t('tableHeader.For_the_first_time_trade') },
        { value: 3, label: this.$t('tableHeader.Novice_transaction_volume_meets_the_standard') },
        { value: 4, label: this.$t('tableHeader.Active_users') },
        { value: 5, label: this.$t('tableHeader.Top_up_rebate') },
        { value: 6, label: this.$t('tableHeader.Invite_friends') },
      ],    // 赠金名称
      statusOptions: [
        { value: 1, label: this.$t('filters.Not_at_the') },
        { value: 2, label: this.$t('others.ongoing') },
        { value: 3, label: this.$t('filters.To_receive') },
        { value: 4, label: this.$t('filters.Have_to_receive') },
        { value: 5, label: this.$t('filters.Expired') },
        { value: 6, label: this.$t('filters.Take_back') },
      ],          // 状态
      typeOptions: [
        { value: 1,       label: this.$t('filters.Opening_charge') },
        { value: 2,       label: this.$t('tableHeader.moneyCost') },
        // { value: 4,       label: this.$t('filters.Transfer_from_asset_account') },
        // { value: 8,       label: this.$t('filters.Transfer_to_assets_account') },
        { value: 16,      label: this.$t('filters.Unwinding_of_profit_and_loss') },
        { value: 32,      label: this.$t('filters.Closing_charge') },
        // { value: 64,      label: this.$t('filters.Analog_disk_supplement_assets') },
        { value: 128,     label: this.$t('filters.Margin_reduction') },
        { value: 256,     label: this.$t('filters.Withhold_commissions') },
        { value: 512,     label: this.$t('filters.Commission_refund') },
        { value: 1024,    label: this.$t('filters.Commission_income') },
        // { value: 2048,    label: this.$t('filters.Transfer_from_trading_account_to_documentary_account') },
        // { value: 4096,    label: this.$t('filters.Transfer_from_documentary_account_to_trading_account') },
        // { value: 8192,    label: this.$t('filters.Transfer_from_asset_account_to_documentary_account') },
        // { value: 16384,   label: this.$t('filters.Transfer_from_documentary_account_to_asset_account') },
        { value: 32768,   label: this.$t('filters.Strong_flat_back') },
        // { value: 65536,   label: this.$t('filters.Analog_disk_reduces_assets') },
        { value: 131072,  label: this.$t('filters.Liquidation_commission') },
        { value: 262144,  label: this.$t('filters.Deduction_of_abnormal_assets') },
        { value: 524288,  label: this.$t('filters.Margin_increase') },
        { value: 1048576, label: this.$t('filters.Give_gold_to_receive') },
        { value: 2097152, label: this.$t('filters.Give_gold_failure') },
        { value: 4194304, label: this.$t('filters.Give_gold_recovery') },
      ],            // 类型


      // 领取
      totalToreceive: 0,
      listQueryToreceive: {
        sname: '',            // UID/手机/邮箱
        withGoldName: '',     // 赠金名称
        status: '',           // 状态
        star: "",             // 开始
        end: "",              // 结束
        pageNo: 1,
        pagesize: 10,
      },
      filterTimeToreceive: [],
      tableListToreceive: [],

      // 使用
      totalUse: 0,
      listQueryUse: {
        sname: '',            // UID/手机/邮箱
        type: '',             // 类型
        star: "",             // 开始
        end: "",              // 结束
        pageNo: 1,
        pagesize: 10,
      },
      tableListUse: [],
      filterTimeUse: [],

      // 发放
      totalIssue: 0,
      dialogVisibleIssue: false,
      formIssue: {
        WithGoldName: '',       // 赠金名称
        UserID: '',             // UID
        UidList: '',            // 存放uid
        IssueAmount: '',        // 发放赠金金额
      },
      listQueryIssue: {
        sname: '',              // UID/手机/邮箱
        sectop: '',             // 顶级代理ID
        sagent: '',             // 上级代理ID/用户名
        star: '',
        end: '',
        pageNo: 1,
        pagesize: 10,
      },
      tableListIssue: [],
      filterTimeIssue: [],
      rulesFormIssue: {
        WithGoldName:       [{ validator: WithGoldName_v,       trigger: 'blur'}],   // 赠金名称
        UidList:            [{ validator: Issue_UidList_v,      trigger: 'change'}], // uidList
        IssueAmount:        [{ validator: IssueAmount_v,        trigger: 'blur'}],   // 发放金额
      },

      // 回收
      totalRecycle: 0,
      dialogVisibleRecycle: false, 
      formRecycle: {
        UserID: '',             // UID
        UidList: '',            // 存放uid
        RecycleAmount: '',      // 回收赠金金额
      },
      listQueryRecycle: {
        sname: '',              // UID/手机/邮箱
        sectop: '',             // 顶级代理ID
        sagent: '',             // 上级代理ID/用户名
        star: '',
        end: '',
        pageNo: 1,
        pagesize: 10,
      },
      tableListRecycle: [],     
      filterTimeRecycle: [],
      rulesFormRecycle: {
        UidList:              [{ validator: Recycle_UidList_v,      trigger: 'change'}],  // uidList
        RecycleAmount:        [{ validator: RecycleAmount_v,        trigger: 'blur'}],    // 回收赠金金额
      },
    }
  },

  components: {},

  computed: {},

  mounted() {
    this.getList(0);
    this.getList(1);
    // this.getList(2);
    // this.getList(3);
    // this.getList(4);
  },

  methods: {
    getList(v) {
      if (v == 0) {
        this.listLoading0 = true
        // 获取体验金汇总
        gifttotal().then((res) => {
          // console.log(res,'汇总数据')
          this.summaryData = res.data;
          this.listLoading0 = false
        })
      } else if (v == 1) {
        this.listLoading1 = true
        // 获取用户赠金领取列表
        getgiftrecord({
          sname: this.listQueryToreceive.sname,           // uid或用户名字
          state: this.listQueryToreceive.status == '' ? Number(-1) : Number(this.listQueryToreceive.status),  // 状态 1-未开始 2-进行中 3-待领取 4-已领取 5-已过期,6收回
          star: this.listQueryToreceive.star,             // 开始时间
          end: this.listQueryToreceive.end,               // 结束时间
          pageNo: this.listQueryToreceive.pageNo,         // 第几页
          pagesize: this.listQueryToreceive.pagesize,     // 多少条
        }).then((res) => {
          console.log(res,'领取')
          this.tableListToreceive = res.data.list
          this.totalToreceive = res.data.total
          this.listLoading1 = false;
        })
      } else if (v == 2) {
        this.listLoading2 = true
        // 获取用户赠金使用
        usegiftrecord({
          sname: this.listQueryUse.sname,                 // uid或用户名字
          state: this.listQueryUse.type == '' ? Number(-1) : Number(this.listQueryUse.type),          // 类型
          star: this.listQueryUse.star,                   // 开始时间
          end: this.listQueryUse.end,                     // 结束时间
          pageNo: this.listQueryUse.pageNo,               // 第几页
          pagesize: this.listQueryUse.pagesize,           // 多少条
        }).then((res) => {
          // console.log(res,'使用')
          this.tableListUse = res.data.list
          this.totalUse = res.data.total;
          this.listLoading2 = false;
        })
      } else if (v == 3) {
        this.listLoading3 = true
        // 获取用户赠金列表
        getsentgift(this.listQueryIssue).then((res) => {
          // console.log(res,'发放')
          this.tableListIssue = res.data.list;
          this.totalIssue = res.data.total;
          this.listLoading3 = false;
        })
      } else if (v == 4) {
        this.listLoading4 = true
        // 获取用户取消赠金列表
        getrecoverygift(this.listQueryRecycle).then((res) => {
          // console.log(res,'回收')
          this.tableListRecycle = res.data.list;
          this.totalRecycle = res.data.total;
          this.listLoading4 = false;
        })
      }
    },

    formClearIssue() {
      this.formIssue =  {
        WithGoldName: '',       // 赠金名称
        UserID: '',             // UID
        UidList: '',            // 存放uid
        IssueAmount: '',        // 发放赠金金额
      };
      this.$refs['formIssue'].resetFields();
    },
    // 添加uid
    handleAddIssue() {
      userinfo({
        user_id :JSON.parse(this.formIssue.UserID)
      }).then((res) => {
        // console.log(res)
        if (res.data.user_id !== 0) {
          let str = this.formIssue.UidList
          let arr = null
          if (str == '') {
            arr = []
          } else {
            arr = str.split(',')
          }
          arr.push(this.formIssue.UserID)
          this.formIssue.UidList = arr.join(',')
          this.formIssue.UserID = ''
        } else {
          this.$notify({
            title: this.$t('dialog.The_UID_you_entered_does_not_exist'),
            type: "warning",
            duration: 2000,
          });
          return false
        }
      })
    },
    // 发放体验金
    handleIssue() {
      this.dialogVisibleIssue = true;
    },
    // 发放体验金 icon
    handleCloseIssue() {
      this.dialogVisibleIssue = false;
      this.formClearIssue();
    },
    // 发放体验金 取消
    handleIssueCancel() {
      this.dialogVisibleIssue = false;
      this.formClearIssue();
    },
    // 发放体验金 确定
    handleIssueSure() {
      this.$refs['formIssue'].validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('dialog.Confirm_the_payment_of_experience_money'), {
            distinguishCancelAndClose: true,
            confirmButtonText: this.$t('buttons.determine'),
            cancelButtonText: this.$t('buttons.cancel')
          })
          .then(() => {
            sentgiftrecord({
              // sends: '',  // 发送
              uids: this.formIssue.UidList,               // 集合,
              gfitname: this.formIssue.WithGoldName,      // 赠金名，
              amount: Number(this.formIssue.IssueAmount), // 数量
            }).then(() => {
              this.$notify({
                title: this.$t('dialog.Distribution_of_success'),
                type: "success",
                duration: 2000,
              });
              this.dialogVisibleIssue = false;
              this.formClearIssue();
              this.getList();
            })
          })
          .catch(action => {})
        } else {
          return false
        }
      })
    },

    formClearRecycle() {
      this.formRecycle = {
        UserID: '',               // UID
        UidList: '',              // 存放uid
        RecycleAmount: '',        // 发放赠金金额
      };
      this.$refs['formRecycle'].resetFields();
    },
    // 添加uid
    handleAddRecycle() {
      userinfo({
        user_id : JSON.parse(this.formRecycle.UserID)
      }).then((res) => {
        if (res.data.user_id !== 0) {
          let str = this.formRecycle.UidList
          let arr = null
          if (str == '') {
            arr = []
          } else {
            arr = str.split(',')
          }
          arr.push(this.formRecycle.UserID)
          this.formRecycle.UidList = arr.join(',')
          this.formRecycle.UserID = ''
        } else {
          this.$notify({
            title: this.$t('dialog.The_UID_you_entered_does_not_exist'),
            type: "warning",
            duration: 2000,
          });
          return false
        }
      })
    },
    // 回收体验金
    handleRecycle() {
      this.dialogVisibleRecycle = true;
    },
    // 回收体验金 icon
    handleCloseRecycle() {
      this.dialogVisibleRecycle = false;
      this.formClearRecycle();
    },
    // 回收体验金 取消
    handleRecycleCancel() {
      this.dialogVisibleRecycle = false;
      this.formClearRecycle();
    },
    // 回收体验金 确定
    handleRecycleSure() {
      this.$refs['formRecycle'].validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('dialog.Whether_to_confirm_recovery_of_experience_money'), {
            distinguishCancelAndClose: true,
            confirmButtonText: this.$t('buttons.determine'),
            cancelButtonText: this.$t('buttons.cancel')
          })
          .then(() => {
            recoverygift({
              recoverys: this.formRecycle.UidList,            // 取消集合，
              amount: Number(this.formRecycle.RecycleAmount), // 金额
            }).then(() => {
              this.$notify({
                title: this.$t('dialog.Recycling_success'),
                type: "success",
                duration: 2000,
              });
              this.dialogVisibleRecycle = false;
              this.formClearRecycle();
              this.getList();
            })
          })
          .catch(action => {})
        } else {
          return false
        }
      })
    },

    // tab切换
    handleClick() {
      if (this.activeName == 'first') {
        this.getList(1)
      } else if (this.activeName == 'second') {
        this.getList(2)
      }
    },
    handleFilter(v) {
      if (v == 1) {
        this.listQueryToreceive.pageNo = 1;
        this.getList(1);
      } else if (v == 2) {
        this.listQueryUse.pageNo = 1;
        this.getList(2);
      } else if (v == 3) {
        this.listQueryIssue.pageNo = 1;
        this.getList(3);
      } else if (v == 4) {
        this.listQueryRecycle.pageNo = 1;
        this.getList(4);
      }
    },

    filterTimeTransformToreceive(val) {
      this.listQueryToreceive.star = (val && val[0]) || "";
      this.listQueryToreceive.end = (val && val[1] + " 23:59:59") || "";
    },
    filterTimeTransformUse(val) {
      this.listQueryUse.star = (val && val[0]) || "";
      this.listQueryUse.end = (val && val[1] + " 23:59:59") || "";
    },
    filterTimeTransformIssue(val) {
      this.listQueryIssue.star = (val && val[0]) || "";
      this.listQueryIssue.end = (val && val[1] + " 23:59:59") || "";
    },
    filterTimeTransformRecycle(val) {
      this.listQueryRecycle.star = (val && val[0]) || "";
      this.listQueryRecycle.end = (val && val[1] + " 23:59:59") || "";
    },
  },
}
</script>

<style lang="scss" scoped>
.giftCashManage-container {
  padding: 10px;
  .btns-container {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    margin: 15px 0;
  }
  .summaryList {
    width: 100%;
    border: 1px solid #f4f4f4;
    padding: 10px;
    display: flex;
    flex-direction: column;
    div {
      height: 35px;
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      align-items: center;
      span {
        width: 125px;
        text-align: center;
      }
    }
  }
  ::v-deep .el-form-item__content{
    display: flex;
  }
  .addBtn {
    margin-left: 10px;
  }
  .store {
    width: 100%;
    min-height: 50px;
    padding: 0px 10px;
    background: #f4f4f4;
    border-radius: 3px;
    span {
      display: inline-block;
      margin: 5px;
    }
  }
  .issue {
    margin: 35px 0;
    border: 1px solid #f4f4f4;
    padding: 10px;
  }
  .recycle {
    margin: 35px 0;
    border: 1px solid #f4f4f4;
    padding: 10px;
  }
  .title {
    font-weight: bold;
    font-size: 16px;
    margin: 10px 0;
  }
}
</style>