<template>
  <div class="legalOrder-container">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        :placeholder="$t('filters.topIDNick')"
        style="width: 130px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        :placeholder="$t('filters.agent')"
        style="width: 150px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.status"
        :placeholder="$t('tableHeader.state')"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in stypeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <span style="margin-left: 20px; font-size: 12px">{{$t('tableHeader.toApplyForTime')}}</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px"
        v-model="filterTime1"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change="filterTimeTransform1"
      >
      </el-date-picker>
      <span style="margin-left: 20px; font-size: 12px">{{$t('filters.auditTime')}}</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px"
        v-model="filterTime2"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change="filterTimeTransform2"
      >
      </el-date-picker>

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin: 10px 0 0 20px"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="tableList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="userid" align="center" min-width="78">
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.userName')"
        prop="user_name"
        align="center"
        min-width="95"
      >
      </el-table-column>
      <el-table-column
        :label="$t('filters.topID')"
        prop="top_agent_id"
        align="center"
        min-width="78"
      >
        <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topNick')" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.superiorID')"
        prop="pareid"
        align="center"
        min-width="78"
      >
        <template slot-scope="{ row }">
          <span>{{ row.pareid || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.orderSerialNumber')"
        prop="order_id"
        align="center"
        min-width="78"
      >
        <template slot-scope="{ row }">
          <span>{{ row.order_id }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('filters.currency')"
        prop="coin_name"
        align="center"
        min-width="80px"
      >
      </el-table-column>
      <!-- <el-table-column label="渠道" prop="coin_name" align="center" min-width="80px"> </el-table-column> -->
      <el-table-column
        :label="$t('filters.type')"
        prop="order_type"
        align="center"
        min-width="80px"
      >
        <template slot-scope="{ row }">
          <span>{{ row.order_type == 1 ? $t('tableHeader.purchase') : $t('tableHeader.sells') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.transactionAmount')"
        prop="legal_amount"
        min-width="90px"
        align="center"
      ></el-table-column>
      <el-table-column
        :label="$t('tableHeader.transactionNum')"
        prop="amount"
        min-width="90px"
        align="center"
      ></el-table-column>
      <el-table-column
        :label="$t('tableHeader.merchantsExchangeRate')"
        prop="platform_price"
        min-width="120px"
        align="center"
      ></el-table-column>
      <el-table-column
        :label="$t('tableHeader.state')"
        prop="netcash"
        min-width="90px"
        align="center"
      >
        <template slot-scope="{ row }">
          <span>{{stypeObj[row.state] ||"--"}}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.by_the_time')"
        prop="create_time"
        width="75"
        align="center"
      >
        <template slot-scope="{ row }">
          <span
            v-html="
              row.create_time
                ? row.create_time.split(' ')[0] +
                  '<br/>' +
                  row.create_time.split(' ')[1]
                : '--'
            "
          ></span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.processingTime')"
        prop="audit_time"
        width="75"
        align="center"
      >
        <template slot-scope="{ row }">
          <span v-if="row.audit_time.indexOf('1970') > -1">--</span>
          <span
            v-else
            v-html="
              row.audit_time
                ? row.audit_time.split(' ')[0] +
                  '<br/>' +
                  row.audit_time.split(' ')[1]
                : '--'
            "
          ></span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.operation')"
        align="center"
        v-if="$store.getters.roles.indexOf('legalorderlist')>-1 && $store.getters.roles.indexOf('legalordercheck')>-1"
        min-width="180px"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{ row }">
          <el-button
            v-show="row.state == 0"
            type="primary"
            size="mini"
            @click="handleTgJj(row, true)"
            >{{$t('buttons.through')}}</el-button
          >
          <el-button
            v-show="row.state == 0"
            type="primary"
            size="mini"
            @click="handleTgJj(row, false)"
            >{{$t('buttons.refuse')}}</el-button
          >
          <span v-show="row.state != 0">--</span>
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
    <el-dialog
      :title="dialogDetails.handleType ? $t('buttons.through') : $t('buttons.refuse')"
      :visible.sync="handleDialog"
      width="30%"
      @closed="handleClose"
    >
      <div class="item_wrap">
        <span>{{$t('tableHeader.uid')}}：</span><span>{{ dialogDetails.userid }}</span>
      </div>
      <div class="item_wrap">
        <span>{{$t('filters.currency')}}：</span><span>{{ dialogDetails.coin_name }}</span>
      </div>
      <div class="item_wrap">
        <span>{{$t('tableHeader.transactionNum')}}：</span><span>{{ dialogDetails.amount }}</span>
      </div>
      <div class="item_wrap">
        <span>{{$t('tableHeader.transactionAmount')}}：</span><span>{{ dialogDetails.legal_amount }}</span>
      </div>
      <div class="item_wrap">
        <span>{{$t('tableHeader.merchantsExchangeRate')}}：</span><span>{{ dialogDetails.platform_price }}</span>
      </div>
      <div class="item_wrap">
        <span>{{$t('tableHeader.toApplyForTime')}}：</span><span>{{ dialogDetails.create_time }}</span>
      </div>
      <br />
      <div class="item_wrap">
        <span>{{$t('others.noteOptional')}}：</span>
        <el-input v-model="remarks" :placeholder="$t('filters.pleaseEnterTheContent')"></el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleDialog = false">{{$t('buttons.cancel')}}</el-button>
        <el-button type="primary" @click="confirmEntry">{{$t('buttons.determine')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { legalorderlist, legalordercheck } from "@/api/fundQuery";
import { getprocoinList } from "@/api/user";

// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";
import { status } from "nprogress";

export default {
  name: "legalOrder",
  data() {
    return {
      listLoading: false,
      total: 0,
      tableList: null,
      filterTime1: [],
      filterTime2: [],
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        star: "", //开始
        end: "", //结束
        checkstar: "", //审核时间开始
        checkend: "", //审核结束结束
        status: undefined, // 状态 （-1全部 0，待审核 1 等待买家付款; 2 买家确认收款／等待卖家发货; 3用户取消; 4 商家取消; 5 超时取消; 6交易完成(已放币）; 7：补充放款;）
        stype: 1, // 2购买 1 出售，
        pageNo: 1,
        pagesize: 10,
      },
      coinOptions: [],
      stypeOptions: [
        { key: 0, name: this.$t('filters.To_audit') },
        { key: 1, name: this.$t('filters.For_the_payment') },
        { key: 2, name: this.$t('filters.To_put_money') },
        // { key: 3, name: "已拒绝" },
        // { key: 4, name: "已拒绝" },
        { key: 8, name: this.$t('filters.Has_refused_to') },
        { key: 5, name: this.$t('filters.System_refuse_to') },
        { key: 6, name: this.$t('filters.Has_put_the_coin') },
        { key: 7, name: this.$t('filters.Administrator_release') },
      ],
      stypeObj: {
        0: this.$t('filters.To_audit'),
        1: this.$t('filters.For_the_payment'),
        2: this.$t('filters.To_put_money'),
        3: this.$t('filters.Has_refused_to'),
        4: this.$t('filters.Has_refused_to'),
        5: this.$t('filters.System_refuse_to'),
        6: this.$t('filters.Has_put_the_coin'),
        7: this.$t('filters.Administrator_release'),
        8: this.$t('filters.Has_refused_to'),
      },
      handleDialog: false, // 操作弹框
      dialogDetails: {},
      remarks: "",
    };
  },

  components: {},

  computed: {},

  mounted() {
    // getprocoinList().then((res)=>{
    //   this.coinOptions = res.data.filter(v=>v.status)
    // })
    this.getList();
  },

  methods: {
    confirmEntry() {
      legalordercheck({
        id: this.dialogDetails.order_id,
        pass: this.dialogDetails.handleType,
        content: this.remarks,
      }).then((res) => {
        this.$notify({
          title: this.$t('dialog.Successful'),
          message: this.$t('dialog.Operation_is_successful'),
          type: "success",
          duration: 2000,
        });
        this.getList();
        this.handleDialog = false;
      });
    },
    handleTgJj(v, type) {
      this.handleDialog = true;
      this.dialogDetails = v;
      console.log(this.dialogDetails);
      this.dialogDetails.handleType = type;
    },
    handleClose() {
      this.remarks = "";
    },
    //  渲染table列表
    getList() {
      this.listLoading = true;
      let data = {};
      Object.assign(data, this.listQuery, {
        status: this.listQuery.status === 0 ? 0 : this.listQuery.status || -1,
      });
      legalorderlist(data).then((res) => {
        this.tableList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    filterTimeTransform1(val) {
      this.listQuery.star = (val && val[0]) || "";
      this.listQuery.end = (val && val[1] + " 23:59:59") || "";
    },
    filterTimeTransform2(val) {
      this.listQuery.checkstar = (val && val[0]) || "";
      this.listQuery.checkend = (val && val[1] + " 23:59:59") || "";
    },
  },
};
</script>
<style lang="scss" scoped>
.legalOrder-container {
  .item_wrap {
    line-height: 30px;
    margin-left: 20px;
  }
}
</style>