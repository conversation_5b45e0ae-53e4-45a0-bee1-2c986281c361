<!--合约配置 手续费-->
<template>
  <div class="bannerConfig_wrap">
    <div class="w_menu">
      <el-button
        v-if="$store.getters.roles.indexOf('bannerConfigadd') > -1"
        type="primary"
        class="buttons"
        @click="addbanner"
        >{{$t('buttons.addBANNER')}}</el-button
      >
    </div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane
        v-for="(item, index) in langOptions"
        :key="index"
        :label="$t(`filters.${item.name}`)"
        :name="item.value"
      ></el-tab-pane>
    </el-tabs>
    <el-radio-group @change="limitChange" v-model="limit">
      <el-radio-button :label="-1">{{$t('buttons.all')}}</el-radio-button>
      <el-radio-button :label="0">{{$t('buttons.public')}}</el-radio-button>
      <el-radio-button :label="1">{{$t('buttons.android')}}</el-radio-button>
      <el-radio-button :label="2">{{$t('buttons.IOS')}}</el-radio-button>
      <el-radio-button :label="3">{{$t('buttons.WEB')}}</el-radio-button>
    </el-radio-group>
    <div id="wcdiv_lsjl" class="w_c">
      <el-table
        :data="tableData"
        style="width: 100%"
        sort-by="index"
        sortable
        :cell-style="{ height: '60px', 'min-width': '300px' }"
      >
        <el-table-column align="center" min-width="300px">
          <template slot-scope="scope">
            <div class="titlediv">
              <span class="titleSpan" 
                >BANNER类型：{{ scope.row.banner_type === 1 ? 'BANNER图片' : scope.row.banner_type === 2 ? '邀请海报' : '其它' }} </span>
                <br />
                <span class="titleSpan" style="font-size: 18px; font-weight: bold"
                > {{$t('tableHeader.title')}}：{{ scope.row.title }}</span>
              <br />
              <span class="titleSpan">{{$t('forms.uploadPictures')}}：{{ scope.row.image || $t('others.not_have') }}</span>
              <span class="titleSpan">{{$t('others.linkAddress')}}：{{ scope.row.link || $t('others.not_have') }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column min-width="150px" align="center" style="width: 100px">
          <template slot-scope="scope">
            <div style="min-width: 300px">
              <el-button
                :disabled="!upBtnAble || scope.$index == 0"
                type="primary"
                size="small"
                @click="sortBannerList(0, scope)"
                >{{ scope.$index == 0 ? $t('buttons.has_been_to') : $t('buttons.move_up') }}</el-button
              >
              <el-button
                :disabled="!downBtnAble || scope.$index == tableData.length - 1"
                type="primary"
                size="small"
                @click="sortBannerList(1, scope)"
                >{{
                  scope.$index == tableData.length - 1 ? $t('buttons.have_what') : $t('buttons.move_down')
                }}</el-button
              >
              <el-button
                plain
                size="small"
                v-if="$store.getters.roles.indexOf('bannerConfigdel') > -1"
                @click="deleteBanner(scope.row)"
                >{{$t('buttons.delete')}}</el-button
              >
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog
      :title="$t('buttons.addBANNER')"
      :visible.sync="dialogVisible"
      width="710px"
      height="320px"
      :before-close="handleDialogClose"
    >
      <div class="dialogc">
        <div>
          <el-form :rules="rules" ref="ruleFormmr" style="margin:0" :model="ruleForm">
            <div class="divliang">
              <el-form-item :label="$t('others.bannerType')" prop="banner_type">
                <el-select
                  v-model="ruleForm.banner_type"
                  :placeholder="$t('filters.pleaseBannerType')"
                >
                  <el-option
                    v-for="item in bannerTypeOptions"
                    :key="item.value"
                    :label="$t(`filters.${item.name}`)"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('others.languageType')" prop="lang_type">
                <el-select
                  v-model="ruleForm.lang_type"
                  :placeholder="$t('filters.pleaseLanguageType')"
                >
                  <el-option
                    v-for="item in langOptions"
                    :key="item.value"
                    :label="$t(`filters.${item.name}`)"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('others.systemColorMode')" prop="system_color_mode">
                <el-select
                  v-model="ruleForm.system_color_mode"
                  :placeholder="$t('filters.pleaseSystemColorMode')"
                >
                  <el-option
                    v-for="item in systemColorModeOptions"
                    :key="item.value"
                    :label="$t(`filters.${item.name}`)"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('forms.client')" prop="limit">
                <el-select
                  v-model="ruleForm.limit"
                  :placeholder="$t('filters.selectClient')"
                >
                  <el-option
                    v-for="item in limitOptions"
                    :key="item.value"
                    :label="item.name"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('tableHeader.title')" prop="bannertitle">
                <el-input
                  class="w156"
                  :placeholder="$t('dialog.Please_enter_a_title')"
                  v-model="ruleForm.bannertitle"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item :label="$t('forms.uploadPictures')" prop="linkaddr">
                <div class="uploaddisplay">
                  <!-- <el-upload
                    class="avatar-uploader"
                    action="https://jsonplaceholder.typicode.com/posts/"
                    :show-file-list="false"
                    :on-success="handleAvatarSuccess"
                    :before-upload="beforeAvatarUpload">
                    <img v-if="ruleForm.imageUrl" :src="ruleForm.imageUrl" class="avatar">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload> -->
                  <el-upload
                    :limit="1"
                    :action="actionUrl"
                    accept="image/png"
                    drag
                    :data="uploadData"
                    :auto-upload="false"
                    :on-error="uploadError"
                    ref="uploadzh"
                    :on-success="uploadSuccessZh"
                    list-type="picture"
                  >
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">
                      {{$t('dialog.el_upload__text')}}
                      <em>{{$t('buttons.clickOnUpload')}}</em>
                    </div>
                  </el-upload>
                  <div style="margin-left: 10px">
                    {{$t('dialog.Image_format')}}
                    <br />{{$t('dialog.max_upload')}} <br />{{$t('dialog.Image_size')}}
                    <br />
                  </div>
                </div>
              </el-form-item>
              <el-form-item :label="$t('forms.point_to_link')" prop="linkaddr">
                <el-input
                  class="w156"
                  :placeholder="$t('filters.pleaseLinkAddr')"
                  v-model="ruleForm.linkaddr"
                  clearable
                ></el-input>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="handleDialogClose">{{$t('buttons.cancel')}}</el-button>
        <el-button
          size="small"
          type="primary"
          @click="handleDialogOkClick"
          :loading="uploading"
          >{{ uploading ? $t('buttons.on_the_cross') : $t('buttons.determine') }}</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  getbcprobannerlist,
  bcprobanneradd,
  bcprobannerup,
} from "@/api/systemManagement";
export default {
  name: 'bannerConfig',
  data() {
    let validate_http = (rule, value, callback) => {
      if (value === "") {
        callback();
      }
      let reg = /(http|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?/;
      if (!reg.test(value)) {
        return callback(new Error(this.$t('dialog.The_link_is_not_formatted_correctly')));
      } else {
        callback();
      }
    };
    return {
      tableData: [],
      uploading: false,
      dialogVisible: false,
      upBtnAble: true,
      downBtnAble: true,
      uploadData: {},
      actionUrl: "",
      ruleForm: {
        linkaddr: "",
        lang_type: "",
        system_color_mode: "",
        banner_type: "",
        limit: "",
        imageUrl: '',
        bannertitle: ''
      },
      rules: {
        bannertitle: [
          { required: true, message: this.$t('dialog.The_input_box_cannot_be_empty'), trigger: "blur" }
        ],
        linkaddr: [{ validator: validate_http, trigger: "blur" }],
        lang_type: [
          { required: true, message: this.$t('filters.pleaseLanguageType'), trigger: "change" },
        ],
        system_color_mode: [
          { required: true, message: this.$t('filters.pleaseSystemColorMode'), trigger: "change" },
          ],
          banner_type: [
          { required: true, message: this.$t('filters.pleaseBannerType'), trigger: "change" },
        ],
        limit: [
          { required: true, message: this.$t('filters.selectClient'), trigger: "change" },
        ],
      },
      fileListzh: [],
      fileListen: [],
      activeName: "0",
      langOptions: [
        { name: 'ChineseSimplifiedNotice',  value: "0",},
        { name: 'EnglishNotice',  value: "1",},
        { name: 'ChineseTraditionalNotice',  value: "2",},
        { name: 'KoreanNotice',  value: "3",},
        { name: 'VietnameseNotice',  value: "4",},
        { name: 'IndonesianNotice',  value: "5",},
        { name: 'RussianNotice',  value: "6",},
        { name: 'GermanNotice',  value: "7",},
        { name: 'JapaneseNotice',  value: "8",},
      ], // 语言tab
      systemColorModeOptions: [
        { name: 'LightMode',  value: "1",},
        { name: 'DarkMode',  value: "2",},
      ], // 系统色彩模式选项
      bannerTypeOptions: [
        { name: 'BannerImage',  value: "1",},
        { name: 'InvitePoster',  value: "2",},
      ], // banner类型选项
      limitOptions: [
        { name: this.$t('buttons.public'), value: "0" },
        { name: this.$t('buttons.android'), value: "1" },
        { name: this.$t('buttons.IOS'), value: "2" },
        { name: this.$t('buttons.WEB'), value: "3" },
      ], // 语言tab
      limit: -1,
    };
  },
  components: {},
  mounted() {
    this.getData();
    this.actionUrl =
      (process.env.VUE_APP_API == '/' ? window.location.protocol+'//'+window.location.host : process.env.VUE_APP_API)+ "/managers/v1/banner/bcprobannerupload";
  },
  computed: {
  },
  methods: {
    limitChange(){
      this.getData()
    },
    handleAvatarSuccess(res, file) {
        this.ruleForm.imageUrl = URL.createObjectURL(file.raw);
    },
    handleClick(tab, event) {
      this.getData();
    },
    uploadError(error, file, fileList) {
      this.$refs.uploadzh.clearFiles();
      this.uploading = false;
      this.$notify({
        title: this.$t('dialog.Failure'),
        message: this.$t('dialog.BANNER_Please_try_again'),
        type: "error",
      });
    },
    beforeAvatarUpload(image) {
      let file = image.raw;
      const isPNG = file.type === "image/png";
      const isLt700K = file.size / 1024 < 700;
      if (!isPNG) {
        this.$notify.error({title:this.$t('dialog.Prompt'),message: this.$t('dialog.Upload_images_in_PNG_format_only')});
        this.uploading = false;
      }
      if (!isLt700K) {
        this.$notify.error({title:this.$t('dialog.Prompt'),message: this.$t('dialog.The_size_of_uploaded_image_cannot_exceed_700K')});
        this.uploading = false;
      }
      return isPNG && isLt700K;
    },
    uploadSuccessZh(response, file, fileList) {
      this.$refs.uploadzh.clearFiles();
      if (response.ret === 0) {
        var data = {
          title: this.ruleForm.bannertitle,
          image: response.data,
          imageen: "",
          link: this.ruleForm.linkaddr,
          linken: "",
          stype: Number(this.ruleForm.lang_type),
          systemColorMode: Number(this.ruleForm.system_color_mode),
          bannerType: Number(this.ruleForm.banner_type),
          limit: Number(this.ruleForm.limit),
        };
        this.uploading = false;
        bcprobanneradd(data).then((res) => {
          this.$notify({
            title: this.$t('dialog.Successful'),
            message: this.$t('dialog.Uploaded_successfully'),
            type: "success",
          });
          this.getData();
          this.$refs["ruleFormmr"].resetFields();
          this.dialogVisible = false;
        });
      } else {
        this.uploading = false;
        this.$notify({
          title: this.$t('dialog.Failure'),
          message: this.$t('dialog.Chinese_BANNER_Please_try_again'),
          type: "error",
        });
      }
    },
    handleDialogOkClick() {
      this.$refs["ruleFormmr"].validate((valid) => {
        if (valid) {
          let time = parseInt(new Date().getTime() / 1000) + "";
          let sin = md5(
            md5(process.env.VUE_APP_APIKEY) + time
          );
          this.uploadData["sign"] = sin;
          this.uploadData["ts"] = time;
          if (this.$refs.uploadzh.uploadFiles.length > 0) {
            if (this.beforeAvatarUpload(this.$refs.uploadzh.uploadFiles[0])) {
              this.uploading = true;
              this.$refs.uploadzh.submit();
            } else {
              this.$refs.uploadzh.clearFiles();
            }
          } else {
            this.$notify.error(this.$t('dialog.Please_select_the_image_to_upload'));
          }
        }
      });
    },
    handleDialogClose() {
      this.$refs.uploadzh.clearFiles();
      this.$refs["ruleFormmr"].resetFields();
      this.dialogVisible = false;
    },
    getData() {
      this.tableData = [];
      //获取全部banner
      let data = {};
      Object.assign(data, {
        stype: Number(this.activeName),
        limit: this.limit === 0 ? 0 : this.limit || -1,
      });
      getbcprobannerlist(data).then((data) => {
        this.tableData = data.data;
      });
    },
    addbanner() {
      // if (this.tableData.length >= 5) {
      //   this.$notify({
      //     title: "警告",
      //     message: "BANNER最多可添加5个",
      //     type: "warning",
      //     duration: 2000,
      //   });
      //   return;
      // }
      this.dialogVisible = true;
    },
    async sortBannerList(type, item) {
      let weight;
      if (type == 0) {
        //上移
        if (item.$index == 1) {
          weight = this.tableData[0].weight + 1;
        } else {
          weight =
            (this.tableData[item.$index - 1].weight +
              this.tableData[item.$index - 2].weight) *
            0.5;
        }
        this.upBtnAble = false;
      } else {
        if (item.$index == this.tableData.length - 2) {
          weight = this.tableData[this.tableData.length - 1].weight - 1;
        } else {
          weight =
            (this.tableData[item.$index + 1].weight +
              this.tableData[item.$index + 2].weight) *
            0.5;
        }
        this.downBtnAble = false;
      }
      await bcprobannerup({
        id: item.row.id,
        weight: weight,
        type: 0,
      }).then((res) => {
        this.$notify({ title: this.$t('dialog.Successful'), message: this.$t('dialog.Operation_is_successful'), type: "success" });
        this.upBtnAble = true;
        this.downBtnAble = true;
        this.getData();
      });
    },
    deleteBanner(v) {
      this.$confirm(this.$t('dialog.Please_confirm_whether_to_delete_it') + v.title + '"', this.$t('dialog.confirmInformation'), {
        distinguishCancelAndClose: true,
        confirmButtonText: this.$t('buttons.determine'),
        cancelButtonText: this.$t('buttons.cancel'),
      }).then(() => {
        bcprobannerup({
          id: v.id,
          weight: v.weight,
          type: 1,
        }).then((res) => {
          this.$notify({
            title: this.$t('dialog.Successful'),
            message: this.$t('dialog.Operation_is_successful'),
            type: "success",
          });
          this.upBtnAble = true;
          this.downBtnAble = true;
          this.getData();
        });
      });
    },
  },
};
</script>
<style lang="scss">
.bannerConfig_wrap{
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
}
</style>
<style lang="scss" scoped>
.uploaddisplay {
  width: 100%;
  display: flex;
}
.inputw {
  width: 180px;
  padding-right: 30px;
}
.titlediv {
  max-width: 70%;
  display: flex;
  flex-direction: column;
  line-height: 20px;
  .titleSpan {
    // max-width: 400px;
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-align: left;
  }
}

.inputs {
  padding-right: 30px;
  width: 140px;
}

.buttons {
  margin-left: 30px;
}
.devide:not(:first-child) {
  font-size: 16px;
  margin-top: 60px;
}
.divliang {
  margin-bottom: 20px;
  //border: 1px solid #ff8866;
}
.bannerConfig_wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  .w_menu {
    width: 100%;
    flex-shrink: 0;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    padding-bottom: 10px;
    justify-content: flex-end;
  }
  .w_c {
    width: 100%;
    // height: 100%;
    flex-grow: 1;
    box-sizing: border-box;
    display: flex;
    // align-items:center;
    justify-content: flex-start;
    .el-aside {
      background: none;
      color: #000;
      min-width: 400px;
      text-align: left;
      line-height: 25px;
      height: 60px;
      padding-left: 50px;
    }

    .el-main {
      background: none;
      color: #000;
      text-align: left;
      line-height: 58px;
      height: 60px;
      padding: 0;
    }
  }
  .w_page {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-top: 10px;
    padding-right: 30px;
  }
}

.dialogc {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  .spantitle {
    display: inline-block;
    width: 110px;
    text-align: left;
    padding-right: 20px;
    flex-shrink: 0;
  }
  .dialogc2 {
    display: block;
    width: 100%;
    padding-top: 10px;
    text-align: left;
  }
  .spanleft {
    padding-left: 80px;
  }
  .w156 {
    width: 380px;
  }

  .divfee {
    display: flex;
    align-items: center;
    padding-top: 20px;
    padding-bottom: 20px;
  }
}

.dialogstip {
  color: red;
  padding: 20px 10px;
}
</style>
