<template>
  <div class="campaignSpending">
    <div class="filter-container">
      <el-date-picker
        style="width: 180px; margin-top: 5px; margin-right: 20px"
        :placeholder="$t('dialog.Please_select_the_time')"
        v-model="listQuery.filterTime"
        size="mini"
        type="date"
        value-format="yyyy-MM-dd"
      />

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-top: 10px"
        @click="handleFilter"
      >
        {{ $t("buttons.search") }}
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column type="index"     :label="$t('tableHeader.serial_number')" align="center" min-width="78"></el-table-column>
      <el-table-column prop="total_out" :label="$t('tableHeader.The_total_amount_of_spending')" align="center" min-width="78">
        <template slot-scope="{ row }">
          <div v-for="(item,index) in row.plationoutlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.total_out }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="coin_out"  :label="$t('tableHeader.Payout_coin_reward')" align="center" min-width="78">
        <template slot-scope="{ row }">
          <div v-for="(item,index) in row.plationoutlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.coin_out }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="gift_out"  :label="$t('tableHeader.Expenditure_bonus')" align="center" min-width="78">
        <template slot-scope="{ row }">
          <div v-for="(item,index) in row.plationoutlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.gift_out }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="own_day"   :label="$t('tableHeader.The_statistical_date')" align="center" min-width="78"></el-table-column>
      <el-table-column :label="$t('tableHeader.operation')" align="center" min-width="78" v-if="$store.getters.roles.indexOf('giftoutDetail') > -1 || $store.getters.roles.indexOf('cointouDetail') > -1">
        <template slot-scope="{ row }">
          <el-button style="margin: 2px" size="mini" type="primary" @click="handleToViewGiftout(row)" v-if="$store.getters.roles.indexOf('giftoutDetail') > -1">{{ $t('menus.giftoutDetail') }}</el-button>
          <el-button style="margin: 2px" size="mini" type="primary" @click="handleToViewCointout(row)" v-if="$store.getters.roles.indexOf('cointouDetail') > -1">{{ $t('menus.cointouDetail') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,20,30,50]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { plationtaltolout } from "@/api/platformFinance";
export default {
  name: "campaignSpending",
  data() {
    return {
      listLoading: false,
      tableData: [],

      total: 0,
      listQuery: {
        filterTime: "",
        pageNo: 1,
        pagesize: 10,
      },
    };
  },

  computed: {},

  components: {},

  mounted() {
    this.getList()
  },

  methods: {
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList()
    },
    // 赠金支出详情
    handleToViewGiftout(row) {
      this.$router.push({
        path: "/platformFinance/giftoutDetail",
        query: { 
          dt: JSON.stringify(row)
        },
      });
    },
    // 活动支出详情
    handleToViewCointout(row) {
      this.$router.push({
        path: "/platformFinance/cointouDetail",
        query: { 
          dt: JSON.stringify(row)
        },
      });
    },

    getList() {
      this.listLoading = true
      let data = {};
      
      Object.assign(data, {
        ownday: this.listQuery.filterTime,
        pageNo: this.listQuery.pageNo,        // int 页数
        pagesize: this.listQuery.pagesize,    // int 分页数量
      })
      plationtaltolout(data).then((res) => {
        // console.log(res)
        this.tableData = res.data.list
        this.total = res.data.total
        this.listLoading = false
      })
    }
  },
};
</script>

<style lang="scss" scoped>
</style>