import request from '@/utils/request'

/**跟单-用户持仓查询*/
export function docfollowposition(data) {
    return request({
      url: 'managers/v1/follow/followposition',
      method: 'post',
      data: { data }
    })
  }

  /**跟单平仓记录*/
export function docclosetradelist(data) {
    return request({
      url: 'managers/v1/follow/closetradelist',
      method: 'post',
      data: { data }
    })
  }
  /**跟单平仓记录 - 导出*/
export function followcloseexport(data) {
  return request({
    url: 'managers/v1/follow/followcloseexport',
    method: 'post',
    data: { data }
  })
}
    /**跟单开仓记录*/
export function docopentradelist(data) {
    return request({
      url: 'managers/v1/follow/opentradelist',
      method: 'post',
      data: { data }
    })
  }
  /**跟单开仓记录 - 导出*/
export function followopenexport(data) {
  return request({
    url: 'managers/v1/follow/followopenexport',
    method: 'post',
    data: { data }
  })
}
/**跟单开平仓记录*/
export function gettradelist(data) {
return request({
  url: 'managers/v1/follow/gettradelist',
  method: 'post',
  data: { data }
})
}
/**跟单-查询跟单账户资产*/
export function followaccinfo(data) {
  return request({
    url: 'managers/v1/follow/followaccinfo',
    method: 'post',
    data: { data }
  })
}
//用户持仓监控
export function getposstioncap(data) {
  return request({
    url: 'managers/v1/follow/getposstioncap',
    method: 'post',
    data: { data }
  })
}
//跟单账户数据监控
export function getfollowcapital(data) {
  return request({
    url: 'managers/v1/follow/getfollowcapital',
    method: 'post',
    data: { data }
  })
}

/**跟单-跟单资产查询 导出*/
export function followaccinfoexport(data) {
  return request({
    url: 'managers/v1/follow/followaccinfoexport',
    method: 'post',
    data: { data }
  })
}