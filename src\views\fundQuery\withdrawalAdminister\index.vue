<template>
  <div class="administer-container">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        :placeholder="$t('filters.topIDNick')"
        style="width: 130px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        :placeholder="$t('filters.agent')"
        style="width: 150px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.stype"
        :placeholder="$t('tableHeader.state')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
        @clear = "stypeclear()"
      >
        <el-option
          v-for="item in stypeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <span style="margin-right: 5px; font-size: 12px">{{$t('tableHeader.toApplyForTime')}}</span>
      <el-date-picker
        style="width: 220px;margin-right: 20px; margin-top: 10px"
        v-model="filterTime1"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change="filterTimeTransform1"
      />
      <span style="margin-right: 5px; font-size: 12px">{{$t('filters.auditTime')}}</span>
      <el-date-picker
        style="width: 220px;margin-right: 20px; margin-top: 10px"
        v-model="filterTime2"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change="filterTimeTransform2"
      />
      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="administerList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="userid" align="center" min-width="78"/>
      <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center" min-width="95" />
      <el-table-column :label="$t('filters.topID')" prop="top_agent_id" align="center" min-width="78">
         <template slot-scope="{row}">
          <span>{{row.top_agent_id || '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topNick')" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorID')" prop="pareid" align="center" min-width="78">
       <template slot-scope="{row}">
          <span>{{row.pareid || '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.orderSerialNumber')" prop="billid" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ parseFloat(row.billid).toPrecision(18)}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.currency')" prop="currencyname" align="center" min-width="80px"/>
      <el-table-column :label="$t('tableHeader.withdrawalAmount')" prop="amount" min-width="90px" align="center"/>
      <el-table-column :label="$t('tableHeader.rollOutAddr')" prop="toaddr" min-width="200px" align="center"/>
      <el-table-column
        :label="$t('tableHeader.state')"
        prop="netcash"
        min-width="110px"
        align="center"
      >
        <template slot-scope="{ row }">
          <span>{{
            (row.status && stypeOptions.find((v)=>(v.key == row.status)) && stypeOptions.find((v) => v.key == row.status).name) ||
            "--"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.by_the_time')" prop="createdtime" width="75" align="center"/>
      <el-table-column :label="$t('tableHeader.processingTime')" prop="platfom_time" width="75" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.status > 1 && row.platfom_time || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.operation')" align="center" min-width="270px" v-if="$store.getters.roles.indexOf('getwithdrawlist')>-1 || $store.getters.roles.indexOf('withdrawcheck')>-1">
        <template slot-scope="{ row }">
          <el-button type="success" v-if="row.status == 1 && $store.getters.roles.indexOf('withdrawcheck')>-1" size="mini" @click="handleUpdate(row,1,'first')">{{$t('buttons.trial_through')}}</el-button>
          <el-button  type="danger" v-if="row.status ==1 && $store.getters.roles.indexOf('withdrawcheck')>-1" size="mini" @click="handleUpdate(row,0,'first')">{{$t('buttons.trial_refuse')}}</el-button>
          <el-button v-if="row.status == 7 && $store.getters.roles.indexOf('withdrawcheck')>-1" type="success" size="mini" @click="handleUpdate(row,1,'second')">{{$t('buttons.recheck_through')}}</el-button>
          <el-button  type="danger" v-if="row.status == 7 && $store.getters.roles.indexOf('withdrawcheck')>-1" size="mini" @click="handleUpdate(row,0,'second')">{{$t('buttons.recheck_refuse')}}</el-button>
          <el-button type="primary" size="mini" v-if="$store.getters.roles.indexOf('getwithdrawlist')>-1" @click="handleUpdate(row,2)">{{$t('buttons.toView')}}</el-button>
        </template>
      </el-table-column>
      <el-table-column :label="$t('others.operator')" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.platfom_mange || "--" }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column :label="$t('tableHeader.note')" align="center" min-width="100px">
        <template slot-scope="{ row }">
          <span>{{ row.remarks || "--" }}</span>
        </template>
      </el-table-column> -->
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,50,100,200,300]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />

    <!-- 点击查看 -->
    <el-dialog
      :title="dialogStatus == 1 ? (this.reviewType == 'first'?$t('buttons.trial'):$t('buttons.recheck'))+$t('buttons.through') : dialogStatus == 0 ?  (this.reviewType == 'first'?$t('buttons.trial'):$t('buttons.recheck'))+$t('buttons.refuse') : $t('buttons.toView')"
      :visible.sync="UpdataDialogVisible"
      width="75%"
      v-dialogDrag
    >
      <div style="width: 100%;text-align: center;">
        <div class="wc_1">
          <div class="wc_1-one">{{$t('tableHeader.uid')}}:</div>
          <div>{{ updata.userid }}</div>
        </div>
        <div class="wc_1">
          <div class="wc_1-one">{{$t('filters.currency')}}:</div>
          <div>{{ updata.currencyname }}</div>
        </div>
        <div class="wc_1">
          <div class="wc_1-one">{{$t('dialog.toApplyForNum')}}:</div>
          <div>{{ updata.amount }}</div>
        </div>
        <div class="wc_1">
          <div class="wc_1-one">{{$t('dialog.accountNum')}}:</div>
          <div>{{ Number(updata.amount).sub(updata.commission) }}</div>
        </div>
        <div class="wc_1">
          <div class="wc_1-one">{{$t('tableHeader.poundage')}}:</div>
          <div>{{ updata.commission }}</div>
        </div>
        <div class="wc_1">
          <div class="wc_1-one">{{$t('tableHeader.rollOutAddr')}}:</div>
          <div >{{ updata.toaddr}}</div>
        </div>
        <div class="wc_1">
          <div class="wc_1-one">{{$t('tableHeader.toApplyForTime')}}:</div>
          <div>
            {{ updata.createdtime }}
          </div>
        </div>
        <div class="wc_1">
          <div class="wc_1-one">{{$t('tableHeader.processingTime')}}:</div>
          <div>{{ updata.status > 1 && updata.platfom_time || "--" }}</div>
        </div>
        <div v-if="dialogStatus == 2" class="wc_1">
          <div class="wc_1-one">{{$t('tableHeader.state')}}:</div>
          <div>
            <span>{{(updata.status && stypeOptions.find((v) => v.key == updata.status).name) || "--"}}</span>
          </div>
        </div>
        <div v-show="this.reviewType">
          <el-table
            v-loading="checkwithdrawlistLoading"
            :data="checkwithdrawlist"
            border
            fit
            highlight-current-row
            style="width: 100%; margin-top: 30px"
            :header-cell-style="{ background: '#F0F8FF' }"
            size="mini"
          >
            <el-table-column :label="$t('dialog.cumulativeChargeMoney')" prop="inamout" align="center" min-width="110px">
              <template slot-scope="{ row }">
                <span>{{row.inamout ? '+' : ''}}</span>
                <span>{{row.inamout}}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('dialog.cumulativeFiatBuy')" prop="inlegal" min-width="110px" align="center">
              <template slot-scope="{ row }">
                <span>{{row.inlegal ? '+' : ''}}</span>
                <span>{{row.inlegal}}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('dialog.cumulativeReward')" prop="inreward" min-width="110px" align="center">
              <template slot-scope="{ row }">
                <span>{{row.inreward ? '+' : ''}}</span>
                <span>{{row.inreward}}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('dialog.cumulativeDrop')" prop="inairdrop" min-width="110px" align="center">
              <template slot-scope="{ row }">
                <span>{{row.inairdrop ? '+' : ''}}</span>
                <span>{{row.inairdrop}}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('dialog.cumulativeUnwindPNL')" prop="totalprofit" min-width="110px" align="center">
              <template slot-scope="{ row }">
                <span>{{row.totalprofit && (row.totalprofit>0?'+':'')}}</span>
                <span>{{row.totalprofit}}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('dialog.cumulativePoundage')" prop="commission" min-width="110px" align="center">
              <template slot-scope="{ row }">
                <span>{{row.commission ? '-' : ''}}</span>
                <span>{{row.commission}}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('dialog.cumulativeCapitalCost')" prop="incapital" min-width="110px" align="center">
              <template slot-scope="{ row }">
                <span>{{row.incapital && (row.incapital>0?'+':'')}}</span>
                <span>{{row.incapital}}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('dialog.cumulativeWithdrawal')" prop="outamout" min-width="110px" align="center">
              <template slot-scope="{ row }">
                <span>{{row.outamout ? '-' : ''}}</span>
                <span>{{row.outamout}}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('dialog.cumulativeFiatSell')" prop="outlegal" min-width="110px" align="center">
              <template slot-scope="{ row }">
                <span>{{row.outlegal ? '-' : ''}}</span>
                <span>{{row.outlegal}}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('dialog.expectCanWithdrawal')" prop="createdtime" min-width="110px" align="center">
              <template>
                <span :style="{'color':computedCan<0?'red':'green'}">{{computedCan}}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div v-show="this.reviewType">
          <el-table
            v-loading="userloglistLoading"
            :data="userloglist"
            border
            fit
            highlight-current-row
            style="width: 100%; margin-top: 30px"
            :header-cell-style="{ background: '#F0F8FF' }"
            size="mini"
          >
            <el-table-column :label="$t('tableHeader.operationType')" min-width="90px" align="center">
              <template slot-scope="{ row }">
                <span>{{typeObj[row.op_type]}}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('tableHeader.IP_addr')" prop="ip_address" align="center" min-width="130"> </el-table-column>
            <el-table-column :label="$t('tableHeader.equipment')" prop="device" align="center" min-width="120"> </el-table-column>
            <el-table-column :label="$t('tableHeader.equipmentID')" prop="device_id" align="center" min-width="120"> </el-table-column>
            <el-table-column :label="$t('tableHeader.operationSystem')" prop="price" align="center" min-width="90px">
              <template slot-scope="{row}">
                <span>{{os_typeObj[row.os_type]}}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('others.operationTime')" prop="created_time" align="center" min-width="100px"> </el-table-column>
          </el-table>
        </div>
        <!-- <div v-else class="wc_1" style="margin-top: 20px">
          <div class="wc_1-one">备注(选填):</div>
          <div style="width:60%">
            <el-input
              type="textarea"
              :rows="2"
              placeholder="请输入内容"
              v-model="updata.remarks"
            >
            </el-input>
          </div>
        </div> -->
      </div>
      <div slot="footer" class="withdraw_audit_dialog_footer">
        <div class="remark_wrap">
          <span v-show="updata.remarks">
            {{updata.remarksText}}
          </span>
        </div>
        <div>
          <span  v-if="dialogStatus != 2" style="margin-left: 50px; margin-right: 10px;  color: #ffbe47; font-size: 14px"
            >{{$t('others.operation_after_no_error')}}</span
          >
          <el-button @click="UpdataDialogVisible = false">{{dialogStatus != 2?$t('buttons.cancel'):$t('buttons.close')}}</el-button>
          <el-button v-if="dialogStatus != 2" type="primary" @click="dialogEntry(updata)">{{dialogStatus?$t('buttons.confim_through'):$t('buttons.confim_refuse')}}</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getwithdrawlist, withdrawcheck ,firstwithdrawcheck, getcheckwithdrawlist, getuserloglis, getchecktoaddrlist} from "@/api/fundQuery";

export default {
  name: "withdrawaladminister",
  data() {
    return {
      listLoading: false,
      total: 0,
      administerList: null,
      filterTime1: [],
      filterTime2: [],
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        substar: "", //提交开始
        subend: "", //提交结束
        checkstar: "", //审核开始
        checkend: "", //审核结束
        stype: null, // 1：待审核 2：已到账  5平台拒绝，6 平台审核通过
        pageNo: 1,
        pagesize: 10,
      },
      dialogStatus: null,
      UpdataDialogVisible: false,//控制查看对话框的显示与隐藏
      updata: {
        remarks: ''
      },
      reviewType:null,
      stypeOptions: [
        { key: 1, name:this.$t('filters.To_audit') },
        { key: 2, name:this.$t('filters.Have_to_account') },
        { key: 3, name:this.$t('filters.On_chain_withdrawal_has_been_submitted') },
        { key: 4, name:this.$t('filters.On_chain_withdrawal_refused') },
        { key: 5, name:this.$t('filters.Platform_to_refuse') },
        { key: 6, name:this.$t('filters.Passed_the_platform_review') },
        { key: 7, name:this.$t('buttons.trial_through')},
        { key: 8, name:this.$t('buttons.trial_refuse')}
      ],
      checkwithdrawlist: [],
      checkwithdrawlistLoading: false,
      userloglist: [],
      userloglistLoading: false,
      typeObj: {
        0:this.$t('login.title'),
        1:this.$t('filters.Registered'),
        2:this.$t('filters.Retrieve_login_password'),
        3:this.$t('filters.Setting_a_Login_Password'),
        4:this.$t('filters.Changing_the_Login_Password'),
        5:this.$t('filters.Change_fund_Password'),
        6:this.$t('filters.Changing_Mobile_phone_Number'),
        7:this.$t('filters.Modify_email'),
        8:this.$t('filters.Withdrawal_application'),
        9: this.$t('filters.Set_fund_password'),
        10: this.$t('filters.Set_up_the_Google_validator'),
        11: this.$t('filters.Modify_the_Google_validator'),
        12: this.$t('filters.Set_mobile_phone_number'),
        13: this.$t('filters.Set_up_your_email'),
        14: this.$t('filters.KYC1_application'),
        15: this.$t('filters.KYC2_application'),
        16: this.$t('filters.Manual_KYC_application'),
      },
      os_typeObj:{
        1: "android",
        2: "iOS",
        3: "WEB",
        4: "H5",
        5: "open_api",
        6: this.$t('others.system_automatically'),
      },
    };
  },

  components: {},

  computed: {
    computedCan(){
      if(this.checkwithdrawlist.length>0){
        let data = this.checkwithdrawlist[0]
        let plus = Number(Number(Number(Number(data.inamout).add(data.inlegal)).add(data.inreward)).add(data.inairdrop)).add(data.incapital)
        let minus = Number(Number((Math.abs(data.commission))).add(Math.abs(data.outamout))).add(Math.abs(data.outlegal))
        return Number(Number(plus).sub(minus)).add(data.totalprofit)
      }else{
        return '--'
      }
    },
  },

  mounted() {
    this.getList();
  },

  methods: {
    stypeclear(){
      this.listQuery.stype = null
    },
    //  渲染table列表
    getList() {
      this.listLoading = true;
      getwithdrawlist(this.listQuery).then((res) => {
        this.administerList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },

    //查看
    handleUpdate(row,status,type) {
      // this.updata.billid = row.billid
      // this.updata.remarks = row.remarks
      this.updata = Object.assign({}, row);
      if(this.updata.remarks == '小额限制'){
        let arr = []
        getchecktoaddrlist({to_addr: row.toaddr}).then(res=>{
          if(res.data){
            res.data.forEach(element => {
              arr.push(element.user_id)
            });
          }
          if(arr.length>3){
            this.updata.remarksText = this.$t('others.xexzh')+'，'+this.$t('others.gluid')+arr.join()
          }else{
            this.updata.remarksText = this.$t('others.xexzh')
          }
        })
      }else if(this.updata.remarks == '首次提币'){
        this.updata.remarksText = this.$t('dialog.Prompt')+'：'+ this.$t('others.shctb')
      }
      this.UpdataDialogVisible = true;
      this.dialogStatus = status
      this.reviewType = type        
      if(!type) return
      this.checkwithdrawlistLoading = true
      getcheckwithdrawlist({user_id: row.userid}).then((res) => {
        this.checkwithdrawlist = []
        this.checkwithdrawlist.push(res.data);
        this.checkwithdrawlistLoading = false;
      });
      this.userloglistLoading = true;
      getuserloglis({user_id: row.userid}).then((res) => {
        this.userloglist = res.data;
        this.userloglistLoading = false;
      });
    },

    //审核通过或拒绝
    dialogEntry() {
      // 初审
      if(this.reviewType == 'first'){
        firstwithdrawcheck({
          id:this.updata.billid,
          pass: this.dialogStatus == 0 ? false : true,//true 通过 false 驳回
        }).then(res=>{
          this.UpdataDialogVisible = false
          this.$notify({
            title: this.$t('dialog.Operation_is_successful'),
            type: "success",
            duration: 2000,
          });
          this.getList()
        })
      }
      // 复审
      if(this.reviewType == 'second'){
        withdrawcheck({
          id: this.updata.billid,//数据id
          pass: this.dialogStatus == 0 ? false : true,//true 通过 false 驳回
          content: ''//备注
          // content: this.updata.remarks//备注
        }).then(() => {
          this.UpdataDialogVisible = false;
          // this.updata.remarks = ''
          this.$notify({
            title: this.$t('dialog.Operation_is_successful'),
            type: "success",
            duration: 2000,
          });
          this.getList()
        });
      }
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    filterTimeTransform1(val) {
      this.listQuery.substar = (val && val[0]) || "";
      this.listQuery.subend = (val && val[1] + " 23:59:59") || "";
    },
    filterTimeTransform2(val) {
      this.listQuery.checkstar = (val && val[0]) || "";
      this.listQuery.checkend = (val && val[1] + " 23:59:59") || "";
    },
  },
};
</script>
<style lang="scss">
.withdraw_audit_dialog_footer{
  display: flex;
  justify-content: space-between;
  align-items: center;
  .remark_wrap{
    float: left;
    color: rgb(255, 21, 21);
    font-size: 14px;
    flex: 1;
    text-align: left;
  }
}
</style>
<style lang="scss" scoped>
.wc_1 {
  width: 100%;
  height: 30px;
  display: flex;
  align-items: center;
  // justify-content: space-around;
  .wc_1-one{
    width: 150px;
    text-align: right;
    padding: 0 30px;
  }
}
</style>