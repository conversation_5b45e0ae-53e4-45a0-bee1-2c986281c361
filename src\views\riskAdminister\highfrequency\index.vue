<template>
  <div class="kfc-container">
    <div class="filter-container">
      <span style="margin-right: 5px; font-size: 12px">{{ $t('tableHeader.time') }}</span>
      <el-date-picker
        style="width: 220px; margin-top: 5px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change="filterTimeTransform"
      />
      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-left: 20px; margin-top: 5px"
        @click="handleFilter"
      >
        {{ $t('buttons.search') }}
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="user_id" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center" min-width="95"> </el-table-column>
      <el-table-column :label="$t('filters.topID')" prop="top_agent_id" align="center" min-width="78">
          <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('filters.topNick')" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorID')" prop="pareid" align="center" min-width="78">
          <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.transaction_number')" prop="trader_num" align="center" min-width="125"> </el-table-column>
      <el-table-column :label="$t('tableHeader.openPositions_number')" prop="open_num" align="center" min-width="125"> </el-table-column>
      <el-table-column :label="$t('tableHeader.unwindNum')" prop="close_num" align="center" min-width="125"> </el-table-column>
      <el-table-column :label="$t('tableHeader.singleAverageTime')" prop="trading_time" align="center" min-width="125"> </el-table-column>
      <el-table-column :label="$t('tableHeader.oddsUnwindNum')" prop="win_trading_close" align="center" min-width="125"> </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[200,100,50,20]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
//引入封装接口
import {
  highfrequency,
} from "@/api/riskAdminister";

export default {
  name: "highfrequency",
  data() {
    const typeOptions = {
      1: this.$t('filters.Unwind_the_export'),
      2: this.$t('filters.Open_the_export')
    };
    const statusOptions = {
      0: this.$t('filters.In_the_export'),
      1: this.$t('filters.Export_success'),
      2: this.$t('filters.Export_failure')
    };    
    return {
      total: 0,
      tableData: [],
      listLoading: true,
      filterTime: [],
      listQuery: {
        pageNo: 1,
        pagesize: 20,
        star: '',
        end: '',
      },
      updata: {
        
      },
      kycstate:{
          //备注
      content:''
      },
      updatas:[],
      ResetDialogVisible: false,//重置弹框显示控制
      typeOptions,//审核状态 Options
      statusOptions,
      stat:[],
      statIndex:[],
      rules:{
        audit_results:[
          { required: true,message:this.$t('dialog.pleaseSelect'), trigger: ['blur','change'] },
        ],
        err_info:[
          { required: true,message:this.$t('dialog.Mandatory'), trigger: ['blur'] },
        ]
      },
    
      //判断审核弹框里面是拒绝还是通过
      kycselet:false
    };
  },

  components: {},

  computed: {
    // 默认时间
    timeDefault() {
      let date = new Date();
      // 通过时间戳计算
      let defalutStartTime = (
        (date.getTime() - 3 * 24 * 3600 * 1000) /
        1000
      ).toDate("yyyy-MM-dd"); // 转化为时间戳
      let defalutEndTime = (date.getTime() / 1000).toDate("yyyy-MM-dd");
      return [defalutStartTime, defalutEndTime];
    },
  },

  mounted() {
    this.filterTime = this.timeDefault;
    this.getList();
  },

  methods: {
    // 时间
    filterTimeTransform(val) {
      this.listQuery.star = (val && val[0]) || "";
      this.listQuery.end = val ? val[1] + " 23:59:59" : "";
    },
    // 回车搜索事件
    handleFilter(){
      this.getList();
    },
    //  table列表数据
    getList() {
      //开始有加载中效果
      this.listLoading = true;
      let data = {}
      Object.assign(data, this.listQuery,{
        star: (this.filterTime && this.filterTime[0]) || "",
        end: this.filterTime ? this.filterTime[1] + " 23:59:59": "",
      })
      highfrequency(data).then((res) => {
        this.tableData = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    exportHandle(row){
      window.location.href = row.url;
    },  
  },
};
</script>
<style lang="scss" scoped>
.wc_1 {
  width: 100%;
  height: 30px;
  display: flex;
  align-items: center;
  margin: 0 10px;
 
  // justify-content: space-around;
  .wc_1-one {
    width: 45%;
  }
  .idPhotoImg_wrap ::v-deep .el-image__inner{
    width: auto;
   
  }
  .idPhotoImg_wrap ::v-deep .el-image__error{
    min-width: 100px;
  }
}
</style>