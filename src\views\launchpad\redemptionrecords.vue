<!-- src/views/launchpad/redemptionrecords.vue -->
<template>
	<div class="redemptionrecords-container">
		<!-- Filter Bar -->
		<el-form :inline="true" :model="filters" class="filter-form">
			<el-row>
				<el-form-item :label="$t('launchpad.tableHeaders.pledgeCoin')">
					<el-input v-model="filters.title" :placeholder="$t('launchpad.placeholders.pledgeCoin')"
						clearable />
				</el-form-item>
				<el-form-item :label="$t('launchpad.tableHeaders.coin')">
					<el-input v-model="filters.coin" :placeholder="$t('launchpad.placeholders.tokenName')" clearable />
				</el-form-item>
			</el-row>
			<el-row>
				<el-form-item :label="$t('launchpad.tableHeaders.redemptionState')">
					<el-select v-model="filters.redemption_state" placeholder="All" clearable>
						<el-option :label="$t('launchpad.placeholders.pledgeStatusRedeemed')" value="redeemed" />
						<el-option :label="$t('launchpad.placeholders.pledgeStatusRedeeming')" value="redeeming" />
						<el-option :label="$t('launchpad.placeholders.pledgeStatusReview')" value="-" />
					</el-select>
				</el-form-item>
				<el-form-item :label="$t('launchpad.tableHeaders.automaticReviewStatus')">
					<el-select v-model="filters.redemption_status" placeholder="All" clearable>
						<el-option :label="$t('launchpad.forms.success')" value="success" />
						<el-option :label="$t('launchpad.forms.loading')" value="loading" />
						<el-option :label="$t('launchpad.forms.fail')" value="fail" />
						<el-option :label="$t('launchpad.forms.noNeed')" value="noneed" />
					</el-select>
				</el-form-item>
				<el-form-item label="Start">
					<el-date-picker v-model="filters.start" type="date"
						:placeholder="$t('launchpad.placeholders.begin')" />
				</el-form-item>
				<el-form-item label="End">
					<el-date-picker v-model="filters.end" type="date" :placeholder="$t('launchpad.placeholders.end')" />
				</el-form-item>
				<el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="fetchData">
						{{ $t('buttons.search') }}
					</el-button>
				</el-form-item>
			</el-row>
		</el-form>

		<!-- Table -->
		<el-table :data="tableData" border fit stripe size="mini" class="redemption-table" :row-class-name="tableRowClassName"
			:span-method="tableSpanMethod"
			:header-cell-style="{ background: '#F0F8FF', color: '#333', fontWeight: 'bold' }">
			<el-table-column label="User ID" align="center" min-width="120">
				<template #default="{ row }">
					{{ shortenMiddle(row.user_id) }}
				</template>
			</el-table-column>
			<el-table-column :label="$t('launchpad.tableHeaders.category')" align="center">
				<template #default="{ row }">
					{{ row.pledge.category || '-' }}
				</template>
			</el-table-column>
			<el-table-column :label="$t('launchpad.tableHeaders.pledgeCoin')" align="center" min-width="100">
				<template #default="{ row }">
					{{ row.pledge.coin || '-' }}
				</template>
			</el-table-column>

			<el-table-column :label="$t('launchpad.tableHeaders.pledgeTime')" align="center" min-width="150">
				<template #default="{ row }">
					{{ formatUnix(row.created_at) }}
				</template>
			</el-table-column>
			<el-table-column :label="$t('launchpad.tableHeaders.redemptionInitiated')" align="center" min-width="150">
				<template #default="{ row }">
					<span v-if="row.initiated_at">{{ formatUnix(row.initiated_at) }}</span>
					<span v-else>-</span>
				</template>
			</el-table-column>
			<el-table-column :label="$t('launchpad.tableHeaders.redemptionRedeemed')" align="center" min-width="150">
				<template #default="{ row }">
					<span v-if="row.redeemed_at">{{ formatUnix(row.redeemed_at) }}</span>
					<span v-else>-</span>
				</template>
			</el-table-column>

			<el-table-column :label="$t('launchpad.tableHeaders.redemptionState')" align="center" min-width="150">
				<template #default="{ row }">
					{{ getRedemptionStateText(row.redemption_state) }}
				</template>
			</el-table-column>
			<el-table-column :label="$t('launchpad.tableHeaders.automaticReviewStatus')" align="center" min-width="120">
				<template #default="{ row }">
					<span :class="{ 'long-label': getRedemptionStatusText(row.status).length > 12 }">
						{{ getRedemptionStatusText(row.status) }}
					</span>
				</template>
			</el-table-column>

			<el-table-column :label="$t(`launchpad.tableHeaders.coin`)" align="center">
				<template #default="{ row }">
					{{ row.priceItem.coin }}
				</template>
			</el-table-column>
			<el-table-column :label="$t(`launchpad.tableHeaders.yield`)" align="center">
				<template #default="{ row }">
					{{ row.priceItem.yield_rate }}
				</template>
			</el-table-column>

			<el-table-column :label="$t('launchpad.tableHeaders.operation')" align="center" min-width="120">
				<template #default="{ row }">
					<el-button v-if="row.status !== 'success' && row.status !== 'loading' && row.status !== 'noneed'"
						type="text" size="mini" icon="el-icon-check" @click="openApproveDialog(row)">
						{{ $t('buttons.approve') }}
					</el-button>
				</template>
			</el-table-column>
		</el-table>

		<!-- 引入封装分页组件 -->
		<pagina-tion v-show="total > 0" :total="total" :page-sizes="[10, 50, 100, 300]" :page.sync="filters.pageNo"
			:limit.sync="filters.pagesize" @pagination="fetchData" />

		<!-- Approve Dialog -->
		<el-dialog :visible.sync="approveDialogVisible" width="350px" :before-close="closeApproveDialog">
			<span>{{ $t('launchpad.dialogs.redemptionApprove') || '赎回审核通过?' }}</span>
			<span slot="footer" class="dialog-footer">
				<el-button @click="closeApproveDialog">{{ $t('common.cancel') }}</el-button>
				<el-button type="primary" @click="confirmApprove">{{ $t('common.confirm') }}</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
import { getRedemptionRecordsList, approveRedemption } from '@/api/launchpad';
import { getRedemptionStateText, getRedemptionStatusText, shortenMiddle } from '@/utils/format';
import { formatUnix } from '@/utils/time';

export default {
	name: 'RedemptionRecords',
	data() {
		return {
			filters: {
				title: '',
				coin: '',
				redemption_state: '',
				redemption_status: '',
				start: null,
				end: null,
				pageNo: 1,
				pagesize: 10,
			},
			tableData: [],
			total: 0,
			approveDialogVisible: false,
			currentRow: null,
		};
	},
	methods: {
		getRedemptionStateText,
		getRedemptionStatusText,
		shortenMiddle,
		formatUnix,
		async fetchData() {
			try {
				// Reset pageNo if any filter is applied
				const hasFilter = Object.entries(this.filters).some(
					([key, value]) =>
						!['pageNo', 'pagesize'].includes(key) &&
						value !== '' &&
						value !== null &&
						value !== undefined
				);
				if (hasFilter) {
					this.filters.pageNo = 1;
				}
				await getRedemptionRecordsList(this.filters).then(({ data }) => {
					let groupIndex = 0;
					const flattened = [];
					const list = data.list || [];
					list.forEach((record) => {
						// if (!Array.isArray(record.coins)) return;

						// If coins is a non-empty array, flatten as usual
						if (Array.isArray(record.coins) && record.coins.length > 0) {
							record._groupColorIndex = groupIndex;
							record.coins.forEach((priceItem, index) => {
								flattened.push({
									...record,
									_isFirstPrice: index === 0,
									_priceRowSpan: record.coins.length,
									priceItem,
								});
							});
							groupIndex++;
						} else {
							// Note: To remove these (`record_coins` table not implemented as of June 3, 2025)
							// If coins is empty or not an array, push a dummy row
							flattened.push({
								...record,
								_isFirstPrice: true,
								_priceRowSpan: 1,
								priceItem: {
									coin: record.pledge.coin || '-',
									yield_rate: record.pledge.yield_rate || '-',
								},
							});
							groupIndex++;
						}
					});

					// console.log(JSON.stringify(flattened))
					this.tableData = flattened;
					this.total = data.total;
				});
			} catch (err) {
				console.error('Error fetching redemption records:', err);
			}
		},

		openApproveDialog(row) {
			this.currentRow = row;
			this.approveDialogVisible = true;
		},
		closeApproveDialog() {
			this.approveDialogVisible = false;
			this.currentRow = null;
		},
		async confirmApprove() {
			if (!this.currentRow) return;
			try {
				await approveRedemption({ id: this.currentRow.id });
				this.$message.success(this.$t('common.success'));
				this.fetchData();
			} catch (err) {
				this.$message.error(this.$t('common.fail'));
			}
			this.closeApproveDialog();
		},
		tableSpanMethod({ row, column, rowIndex, columnIndex }) {
			// Define column indices that require rowspan
			const mergeCols = [0, 1, 2, 3, 4, 5, 9, 10, 11, 12, 13]; // Adjust as per actual column order

			if (!mergeCols.includes(columnIndex)) {
				return [1, 1]; // Normal cell
			}

			if (row._isFirstPrice) {
				return [row._priceRowSpan, 1]; // rowspan, colspan
			} else {
				return [0, 0]; // merged cell, don't render
			}
		},
		tableRowClassName({ row, rowIndex }) {
			if (row._isFirstPrice) {
				this._currentStripeIndex = (this._currentStripeIndex || 0) + 1;
			}
			const groupIndex = this._currentStripeIndex || 1;
			return groupIndex % 2 === 1 ? 'group-row-odd' : 'group-row-even';
		},
	},
	mounted() {
		this.fetchData();
	},
};
</script>

<style>
.redemptionrecords-container {
	padding: 20px;
}

.filter-form {
	margin-bottom: 20px;
}

.redemption-table .el-table__row:hover>td {
	background-color: inherit !important;
	transition: none;
}

.redemption-table th .cell {
	white-space: normal !important;
	word-wrap: break-word !important;
	word-break: break-word !important;
	line-height: 1.2;
}

.long-label {
	white-space: normal;
	word-wrap: break-word;
	word-break: break-word;
	line-height: 1.2;
}
</style>