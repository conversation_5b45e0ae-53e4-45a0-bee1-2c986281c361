<template>
  <div class="asset-container">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.uid"
        :placeholder="$t('tableHeader.uid')"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-button
        style="margin-top: 10px; margin-left: 10px"
        class="filter-item"
        size="mini"
        type="primary"
        @click="handleFilter"
      >
        {{ $t("buttons.search") }}
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="totalAssetList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="uid" align="center" min-width="78"></el-table-column>
      <el-table-column :label="$t('tableHeader.Total_assets')" prop="balnces" align="center" min-width="78">
        <template slot-scope="{ row }">
          <div v-for="(item,index) in row.balnces" :key="index">{{ item.currency_name }}&nbsp;{{ item.balnce }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.Total_available')" prop="balnces" align="center" min-width="78">
        <template slot-scope="{ row }">
          <div v-for="(item,index) in row.balnces" :key="index">{{ item.currency_name }}&nbsp;{{ item.available }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.Total_freeze')" prop="balnces" align="center" min-width="78">
        <template slot-scope="{ row }">
          <div v-for="(item,index) in row.balnces" :key="index">{{ item.currency_name }}&nbsp;{{ item.lock_amount }}</div>
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10, 20, 30, 50]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { reversetotalassets } from "@/api/fundQuery";
export default {
  name: "userTotalAssetQuery",
  data() {
    return {
      listLoading: false,
      total: 0,
      totalAssetList: null,
      listQuery: {
        uid: "", //用户id,手机号，邮箱
        pageNo: 1,
        pagesize: 10,
      },
    };
  },
  components: {},
  computed: {},
  mounted() {
    this.getList();
  },
  methods: {
    getList() {
      this.listLoading = true;
      let data =  {};
      Object.assign(data, this.listQuery, {
        uid: this.listQuery.uid,
        pageNo: this.listQuery.pageNo,
        pagesize: this.listQuery.pagesize
      })
      reversetotalassets(data).then((res) => {
        this.totalAssetList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
        console.log(res.data)
      })
    },
    // 搜索
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
  },
};
</script>

<style lang="scss" scoped>
</style>