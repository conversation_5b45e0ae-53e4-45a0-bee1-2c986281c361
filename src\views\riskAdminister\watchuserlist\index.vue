<template>
  <div class="watchuserlist-container">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-button
        style="margin-left: 20px; margin-top: 5px"
        class="filter-item"
        size="mini"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
      <el-button
        style="margin-left: 20px; margin-top: 5px;float:right;"
        class="filter-item"
        size="mini"
        type="success"
        v-if="$store.getters.roles.indexOf('watchusermark') > -1"
        @click="handleAdd()"
      >
        {{$t('buttons.add_suspected_user')}}
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="user_id" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.user_id || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center">
      </el-table-column>
      <el-table-column :label="$t('filters.userType')" min-width="105px" align="center">
        <template slot-scope="{ row }">
          <span>{{
            (row.user_type && userTypeOptions[row.user_type]) || "--"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.timeKYC')" prop="verify_time" align="center">
      </el-table-column>
      <el-table-column :label="$t('filters.regTime')" prop="register_time" align="center">
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.lastLoginTime')"
        prop="last_login_time"
        align="center"
      >
      </el-table-column>
      <el-table-column :label="$t('tableHeader.lastLoginIP')" prop="last_login_ip" align="center">
      </el-table-column>
      <el-table-column :label="$t('tableHeader.num_labeled_observations')" prop="watch_times" align="center">
      </el-table-column>
      <el-table-column :label="$t('tableHeader.addTime')" prop="created_time" align="center">
      </el-table-column>
      <el-table-column :label="$t('tableHeader.handlers')" prop="operator" align="center">
      </el-table-column>
      <el-table-column :label="$t('tableHeader.note')" prop="comment" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.comment || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.operation')" align="center" width="280">
        <template slot-scope="{ row }">
          <el-button
            size="mini"
            type="info"
            v-if="$store.getters.roles.indexOf('watchusermark') > -1"
            @click="handleClickCancel(row, 'ipuserlist')"
            >{{$t('buttons.cancel_watch')}}</el-button
          >
          <el-button
            size="mini"
            type="success"
            v-if="$store.getters.roles.indexOf('watchusermark') > -1"
            @click="handleClickComment(row, 'iptradelist')"
            >{{$t('tableHeader.note')}}</el-button
          >
          <el-button
            size="mini"
            type="primary"
            @click="detailClick(row)"
            >{{$t('buttons.toView')}}</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[200, 100, 50, 20]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />

    <!-- handle -->
    <el-dialog
      v-dialogDrag
      :visible.sync="handleDialogShow"
      width="500px"
      :title="$t('tableHeader.note')"
      @closed="closedDialog"
    >
      <el-form
        ref="handleform"
        :model="formData"
        :rules="rules"
        label-position="left"
        label-width="auto"
        style="margin: 0 20px"
      >
        <el-form-item :label="$t('tableHeader.uid')">
          <el-input
            :placeholder="$t('forms.pleaseUID')"
            :disabled="formData.stype==1"
            v-model="formData.user_id"
          ></el-input>
        </el-form-item>
        <el-form-item :label="$t('tableHeader.note')">
          <el-input
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
            :placeholder="$t('filters.pleaseEnterTheContent')"
            maxlength="20"
            show-word-limit
            v-model="formData.comment"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogEntryClick">{{$t('buttons.determine')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
//引入封装接口
import {
  watchuserlist,
  watchusermark,
  watchuserunmark,
} from "@/api/riskAdminister";

export default {
  name: "watchuserlist",
  data() {
    return {
      total: 0,
      tableData: [],
      listLoading: true,
      filterTime: [],
      listQuery: {
        sname: "",
        pageNo: 1,
        pagesize: 20,
      },
      handleDialogShow: false, // 操作弹框
      formData: {
        user_id: "",
        comment: "",
        stype: 0,
      },
      rules: {
        user_id: [{ required: true, message: this.$t('dialog.Please_fill_in_the_UID'), trigger: "blur" }],
        comment: [{ required: true, message: this.$t('dialog.Please_fill_in_remarks'), trigger: "blur" }],
      },
      
      userTypeOptions: {
        1: this.$t('filters.top_agent'),
        2: this.$t('filters.The_agent'),
        3: this.$t('filters.The_average_user'),
        4: this.$t('filters.The_proxy_directly_pushes_users'),
      },
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.getList();
  },

  methods: {
    dialogEntryClick() {
      watchusermark(this.formData).then((res) => {
        this.$notify({
          title: this.$t('dialog.Successful'),
          message: this.$t('dialog.Operation_is_successful'),
          type: "success",
          duration: 2000,
        });
        this.handleDialogShow = false
        this.getList();
      });
    },
    handleClickCancel(row) {
      this.$confirm(`${this.$t('dialog.Confirm_to_cancel_observing_the_user')}(${row.user_name})"？`)
        .then((_) => {
          watchuserunmark({
            user_id: JSON.stringify(row.user_id)
          }).then((res) => {
            this.$notify({
              title: this.$t('tableHeader.operation'),
              message: this.$t('dialog.Operation_is_successful'),
              type: "success",
              duration: 2000,
            });
            this.getList();
          });
        })
        .catch((_) => {});
    },
    handleClickComment(row) {
      this.handleDialogShow = true;
      this.formData = {
        user_id: JSON.stringify(row.user_id),
        comment: row.comment,
        stype: 1,
      }
    },
    // 弹框关闭
    closedDialog() {
      this.formData = {
        user_id: "",
        comment: "",
        stype: 0,
      };
    },
    // 添加嫌疑用户
    handleAdd() {
      this.handleDialogShow = true;
    },
    //跳转详情页
    detailClick(row) {
      this.$router.push({
        path: "/user/detail",
        query: {
          id: row.user_id,
        },
      });
    },
    // 回车搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    //  table列表数据
    getList() {
      //开始有加载中效果
      this.listLoading = true;
      let data = {};
      Object.assign(data, this.listQuery);
      watchuserlist(data).then((res) => {
        if (res.data) {
          this.tableData = res.data.list;
          this.total = res.data.total;
        } else {
          this.tableData = [];
          this.total = 0;
        }
        this.listLoading = false;
      });
    },
    dataTimeChange(val) {
      this.listQuery.star = (val && val[0]) || "";
      this.listQuery.end = (val && val[1]) || "";
    },
  },
};
</script>
<style lang="scss" scoped>
.wc_1 {
  width: 100%;
  height: 30px;
  display: flex;
  align-items: center;
  margin: 0 10px;

  // justify-content: space-around;
  .wc_1-one {
    width: 45%;
  }
  .idPhotoImg_wrap ::v-deep .el-image__inner {
    width: auto;
  }
  .idPhotoImg_wrap ::v-deep .el-image__error {
    min-width: 100px;
  }
}
</style>