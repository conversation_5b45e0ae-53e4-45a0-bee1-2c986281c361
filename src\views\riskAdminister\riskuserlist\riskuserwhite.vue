<template>
  <div class="riskuserwhite_wrap">
    <div v-if="$store.getters.roles.indexOf('setwhitelist') > -1" class="filter-container">
      <div class="add_wrap">
        <span>UID：</span>
        <el-input
          size="mini"
          v-model="addData.user_id"
          :placeholder="$t('filters.pleaseEnterTheContent')"
          style="width: 150px"
          class="filter-item"
          clearable
          @keyup.enter.native="handleFilter"
        />
        <span style="padding-left: 15px;">{{$t('tableHeader.note')}}：</span>
        <el-input
          size="mini"
          v-model="addData.remart"
          type="textarea"
          :placeholder="$t('filters.pleaseEnterTheContent')"
          style="width: 150px"
          class="filter-item"
          clearable
          @keyup.enter.native="handleFilter"
        />
        <el-button
          style="margin-left: 15px"
          class="filter-item"
          size="mini"
          type="success"
          @click="handleAddWhite"
          :disabled="!addData.user_id || !addData.remart"
        >
          {{$t('buttons.add')}}
        </el-button>
      </div>
      <el-divider></el-divider>
    </div>
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.userid"
        :placeholder="$t('filters.name')"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.groupid"
        :placeholder="$t('filters.riskControlGroup')"
        clearable
        style="width: 150px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in grouplist"
          :key="item.id"
          :label="item.group_name"
          :value="item.id"
        />
      </el-select>
      <el-button
        style=" margin-left: 10px"
        class="filter-item"
        size="mini"
        type="primary"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
    </div>
    <el-tabs v-model="tabActive" @tab-click="handleTab">
      <el-tab-pane :label="$t('menus.current_whitelisted_user')" name="first"></el-tab-pane>
      <el-tab-pane :label="$t('menus.historical_whitelisted_users')" name="second"></el-tab-pane>
    </el-tabs>
    <el-table
      v-loading="listLoading"
      :data="financiaList"
      border
      fit
      size="mini"
      highlight-current-row
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column 
        :label="$t('tableHeader.uid')" 
        prop="user_id" 
        align="center" 
        min-width="100"
      >
        <template slot-scope="{ row }">
          <span>{{ row.user_id || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.userName')"
        prop="user_name"
        align="center"
        min-width="100"
      >
        <template slot-scope="{ row }">
          <span>{{ row.user_name || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('filters.riskControlGroup')"
        prop="group_name"
        align="center"
        min-width="100"
      >
        <template slot-scope="{ row }">
          <span>{{ row.group_name || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.addTime')"
        prop="creat_time"
        align="center"
        min-width="100"
      >
        <template slot-scope="{ row }">
          <span>{{ row.creat_time || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.addpeople')"
        prop="add_manage"
        align="center"
        min-width="100"
      >
        <template slot-scope="{ row }">
          <span>{{ row.add_manage || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.note')"
        prop="add_remart"
        align="center"
        min-width="100"
      >
        <template slot-scope="{ row }">
          <span>{{ row.add_remart || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="$store.getters.roles.indexOf('setwhitelist') > -1 && tabActive == 'second'"
        :label="$t('tableHeader.deleteTime')"
        prop="del_time"
        align="center"
        min-width="100"
      >
        <template slot-scope="{ row }">
          <span>{{ row.del_time || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="$store.getters.roles.indexOf('setwhitelist') > -1 && tabActive == 'second'"
        :label="$t('tableHeader.deletepeople')"
        prop="del_manage"
        align="center"
        min-width="100"
      >
        <template slot-scope="{ row }">
          <span>{{ row.del_manage || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="$store.getters.roles.indexOf('setwhitelist') > -1 && tabActive == 'second'"
        :label="$t('tableHeader.deletenote')"
        prop="del_remart"
        align="center"
        min-width="100"
      >
        <template slot-scope="{ row }">
          <span>{{ row.del_remart || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="$store.getters.roles.indexOf('setwhitelist') > -1 && tabActive == 'first'"
        :label="$t('tableHeader.operation')"
        min-width="100"
        align="center"
      >
        <template 
        slot-scope="{ row }"
        v-if="$store.getters.roles.indexOf('setwhitelist') > -1 && tabActive == 'first'"        
        >
          <el-button type="primary" size="mini" @click="handleDel(row)"
            >{{$t('buttons.delete')}}</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[300, 100, 50, 10]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  getwhitelist,
  getgrouplist,
  addristwhite,
  delristwhite,
} from "@/api/riskAdminister";

export default {
  name: "riskuserwhite",
  data() {
    return {
      tabActive: "first",
      listLoading: false,
      total: 0,
      financiaList: null,
      grouplist: [],
      listQuery: {
        userid: "", //用户id,手机号，邮箱
        groupid: "", 
        pageNo: 1,
        pagesize: 10,
      },
      addData: {
        user_id: '',
        remart: '',
        group_id: '',
      }
    };
  },

  components: {},

  computed: {},

  mounted() {
    getgrouplist({pageNo: 1,pagesize: 10000 }).then(res=>{
      this.grouplist = res.data.list
    })
    this.getList();
  },

  methods: {
    // 列表添加按钮
    handleAddWhite(){
      this.$confirm(`${this.$t('dialog.Confirm_that_the_UID_is_set_to')}${this.addData.user_id}${this.$t('dialog.The_user')}?`, this.$t('dialog.Prompt'), {
        confirmButtonText: this.$t('buttons.determine'),
        cancelButtonText: this.$t('buttons.cancel'),
        type: "warning",
      })
        .then(() => {
          let data = this.addData
          Object.assign(data, this.addData, {
            group_id: 0,
          })
          addristwhite(this.addData).then((res) => {
            this.$notify({
              title: this.$t('tableHeader.operation'),
              message: this.$t('dialog.Add_a_success'),
              type: "success",
              duration: 2000,
            });
            this.getList();
          });
        })
        .catch(() => {});
    },
    // 列表删除按钮
    handleDel(row) {
      this.$prompt(`${this.$t('dialog.Are_you_sure_to_delete_the_UID')}${row.user_name}${this.$t('dialog.The_user')}?`, this.$t('dialog.Prompt'), {
        confirmButtonText: this.$t('buttons.determine'),
        cancelButtonText: this.$t('buttons.cancel'),
        inputPlaceholder: this.$t('dialog.Please_enter_delete_remarks'),
        type: "warning",
      })
        .then(({value}) => {
          delristwhite({ 
            remart: value,
            id: row.id, 
          }).then((res) => {
            this.$notify({
              title: this.$t('tableHeader.operation'),
              message: this.$t('dialog.Delete_the_success'),
              type: "success",
              duration: 2000,
            });
            this.getList();
          });
        })
        .catch(() => {});
    },
    handleTab(tab, event) {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    getList() {
      this.listLoading = true;
      let data = {};
      Object.assign(data, this.listQuery,{
        userid: this.listQuery.userid || undefined,
        groupid: this.listQuery.groupid || undefined,
        stype: this.tabActive == 'first'?1:2
      });
      getwhitelist(data).then((response) => {
        this.financiaList = response.data.list;
        this.total = response.data.total;
        this.listLoading = false;
        console.log(response.data)
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.page = 1;
      this.getList();
    },
  },
};
</script>
<style lang="scss" scoped>
.riskuserwhite_wrap {
  padding-top: 20px;
  &::v-deep .el-tabs {
    margin-left: 20px;
    margin-top: 10px;
  }
  .filter-container{
    margin-top: 0;
  }
  .add_wrap{
    display: flex;
    align-items: flex-start;
    span{
      display: flex;
      margin-top: 10px;
    }
  }
}
</style>