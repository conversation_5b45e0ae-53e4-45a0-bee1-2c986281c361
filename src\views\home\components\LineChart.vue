<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    chartData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      // this.chart = echarts.init(this.$el, 'macarons')
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    // load(val){
    //   this.chart.showLoading();
    //   this.setOptions(JSON.parse(val))
    // },
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(this.chartData)
    },
    setOptions({ dataList, unit, legend, dataTime } = {}) {
      // this.chart.hideLoading();
      this.chart.setOption({
        xAxis: {
          data: dataTime,
          boundaryGap: false,
          axisTick: {
            show: false
          }
        },
        grid: {
          left: 10,
          right: 10,
          bottom: 20,
          top: 30,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          padding: [5, 10]
        },
        yAxis: {
          axisTick: {
            show: false
          }
        },
        legend,
        series: [{
          name: unit, 
          // itemStyle: {
          //   normal: {
          //     color: '#ff0000',
          //     lineStyle: {
          //       color: '#ff0000',
          //       width: 2
          //     }
          //   }
          // },
          smooth: false,
          type: 'line',
          data: dataList,
          animationDuration: 1000,
          animationEasing: 'cubicInOut'
        }]
      })
    }
  }
}
</script>
