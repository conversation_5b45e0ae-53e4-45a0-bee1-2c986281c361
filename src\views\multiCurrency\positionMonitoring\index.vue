<template>
  <div class="position-monitoring">
    <div class="filter-container">
      <el-select
        size="mini"
        v-model="listQuery.contract_code"
        :placeholder="$t('tableHeader.contract')"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option
          v-for="item in contractOptions"
          :key="item.traderpairs"
          :label="item.traderpairs"
          :value="item.traderpairs"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.currency_name"
        :placeholder="$t('filters.Margin_currency')"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in marginCurrencyOptions"
          :key="item.currencyid"
          :label="item.currencyname"
          :value="item.currencyname"
        />
      </el-select>
      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-left: 20px"
        @click="handleFilter"
      >
        {{ $t('buttons.query') }}
      </el-button>
    </div>

    <el-table :data="levelList" style="width: 100%">
      <el-table-column prop="contractcode" :label="$t('tableHeader.contractName')" align="center"></el-table-column>
      <el-table-column prop="currency_name" :label="$t('filters.Margin_currency')" align="center" ></el-table-column>
      <el-table-column :label="$t('tableHeader.buyMore')" header-align="center">
        <el-table-column prop="bavgprice" :label="$t('tableHeader.positionAverage')" align="center"></el-table-column>
        <el-table-column prop="bvolume" :label="$t('tableHeader.number')" align="center"></el-table-column>
        <el-table-column prop="bpnl" :label="$t('tableHeader.PNL')" align="center"></el-table-column>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.sale')" header-align="center">
        <el-table-column prop="savgprice" :label="$t('tableHeader.positionAverage')" align="center"></el-table-column>
        <el-table-column prop="svolume" :label="$t('tableHeader.number')" align="center"></el-table-column>
        <el-table-column prop="spnl" :label="$t('tableHeader.PNL')" align="center"></el-table-column>
      </el-table-column>
      <el-table-column prop="price" :label="$t('tableHeader.currentPrice')" align="center"></el-table-column>
      <el-table-column prop="volume" :label="$t('tableHeader.buyingSellingSingleDifferential')" align="center" ></el-table-column>
      <el-table-column prop="pnl" :label="$t('tableHeader.combinedPNL')" align="center" ></el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getposstioncap,bcusdprocontractset } from "@/api/multiCurrency"
import { getprocoinList } from "@/api/user";
export default {
  name: "positionMonitoring",
  data() {
    return {
      listQuery: {
        contract_code: "",        // 合约
        currency_name: "",         // 保证金币种
      },
      contractOptions: [],        // 合约
      marginCurrencyOptions: [],  // 保证金币种
      levelList: [],
    };
  },
  components: {},
  computed: {},
  mounted() {
    getprocoinList({}).then((res) => {
      for(var i=0; i<res.data.length; i++) {
        if(res.data[i].currencyname == "USDT") {
          res.data.splice(i,1)
        }
      }
      this.marginCurrencyOptions = res.data.filter(v => v.status)
    })
    bcusdprocontractset({}).then((res) => {
      this.contractOptions = res.data.filter(v=>v.isshow == 1)
    })
    this.getList()
  },
  methods: {
    // 查询
    handleFilter() {
      this.getList()
    },
    getList() {
      this.listLoading = true
      let data = {};
      Object.assign(data, {
        contract_code: this.listQuery.contract_code,   // 合约
        currency_name: this.listQuery.currency_name,      // 保证金币种
      })
      getposstioncap(data).then((res) => {
        // console.log(res)
        this.levelList = res.data
        this.listLoading = false
      })
    }
  },
};
</script>

<style lang="scss" scoped>
.filter-container {
  margin-bottom: 15px;
}
</style>