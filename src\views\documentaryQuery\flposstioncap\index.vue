<template>
  <div class="hold-cantainer">
    <div class="filter-container">
      <!-- <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      /> -->
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        :placeholder="$t('filters.topIDNick')"
        style="width: 130px; margin-left: 20px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        :placeholder="$t('filters.agent')"
        style="width: 150px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />

      <el-button
        class="filter-item"
        size="mini"
        style="margin-left: 20px;"
        type="primary"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="tableList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.contractName')" prop="contractcode" align="center" min-width="120"> </el-table-column>
      <el-table-column :label="$t('tableHeader.buyMore')" align="center">
        <el-table-column :label="$t('tableHeader.positionAverage')" prop="bavgprice" align="center" min-width="110px"> </el-table-column>
        <el-table-column :label="$t('tableHeader.number')" prop="bvolume" align="center" min-width="110px"> </el-table-column>
        <el-table-column :label="$t('tableHeader.PNL')" prop="bpnl" align="center" min-width="110px"> </el-table-column>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.sale')" align="center">
        <el-table-column :label="$t('tableHeader.positionAverage')" prop="savgprice" align="center" min-width="110px"> </el-table-column>
        <el-table-column :label="$t('tableHeader.number')" prop="svolume" align="center" min-width="110px"> </el-table-column>
        <el-table-column :label="$t('tableHeader.PNL')" prop="spnl" align="center" min-width="110px"> </el-table-column>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.currentPrice')" prop="price" align="center" min-width="90px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.buyingSellingSingleDifferential')" prop="volume" align="center" min-width="90px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.combinedPNL')" prop="pnl" align="center" min-width="90px"> </el-table-column>
    </el-table>

    <!-- <pagina-tion
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    /> -->
  </div>
</template>

<script>
//引入封装接口
import { getposstioncap } from "@/api/documentaryQuery";

export default {
  name: "flposstioncap",
  data() {
    return {
      listLoading: false,
      total: 0,
      tableList: null,
      contractOptions: [],
      listQuery: {
        // sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        // pageNo: 1,
        // pagesize: 10,
      },
      accountTypeOptions: [
        { key: 1, name: this.$t('tableHeader.all_warehouse') },
        { key: 2, name: this.$t('tableHeader.by_warehouse') },
      ],
      sizeOptions: [
        { key: "B", name: this.$t('tableHeader.open_to_buy_more') },
        { key: "S", name: this.$t('tableHeader.sell_empty') },
      ],
      downloadLoading: false,//导出加载中效果
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.getList();
  },

  methods: {
    //  请求表格数据
    getList() {
      this.listLoading = true;
      getposstioncap(this.listQuery).then((response) => {
        this.tableList = response.data;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      // this.listQuery.pageNo = 1;
      this.getList();
    },
  },
};
</script>
<style lang="scss" scoped>
</style>