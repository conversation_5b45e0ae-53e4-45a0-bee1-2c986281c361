// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

@media screen and (max-width: 750px) {
  .el-picker-panel{
    width: 300px !important;
    height: auto !important;
    // position:absolute !important;
    // margin-top: 20px !important;
    // top: 20px !important;
    // left: 0 !important;
    // right: 0;
    // bottom: 0;
  }
  // .el-picker-panel__body{
  //   width: 100% !important;
  // }
  .el-date-range-picker .el-picker-panel__body{
    min-width: 100% !important;
  }
  .el-date-range-picker .el-picker-panel__content{
    margin: auto !important;
  }
  .el-date-range-picker__content.is-left{
    border-right: none !important;
    height: 265px !important;
    width: 100% !important;
    // border-bottom: 1px solid grey;
  }
  .el-date-range-picker__content.is-right{
    border-right: none !important;
    height: auto !important;
    width: 100% !important;
    margin-top: 40px !important;
  }
  .el-date-range-picker__content{
    // width: 100px;
    // float: none;
    // both:claer;
    clear: both !important;
    // float: none;
    // background: red;
  }
 
}
.el-form{
  margin: 0  20px;
}
// @media screen and (min-width: 750px) {
//   .el-dialog{
//     width: 50%;
//   }
//   .el-form{
//     margin: 0  20%;
//   }
//   .el-form-item__label{
//     // width: 200px !important;
//   }
// }
@media screen and (min-width: 1380px) {
  .el-dialog{
    width: 500px;
  }
}
.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}


// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}
.el-pagination__jump{
  margin-left: 15px !important;
}
// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}
