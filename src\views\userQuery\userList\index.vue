<template>
  <div class="userquery-container">
 <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.stype"
        :placeholder="$t('filters.userType')"
        clearable
        style="width: 120px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="(value, key, index) in userTypeOptions"
          :key="index"
          :label="value"
          :value="key"
        />
      </el-select>
      <el-input
        v-model="listQuery.topid"
        size="mini"
        :placeholder="$t('filters.topID')"
        style="width: 130px; margin-left: 20px; margin-top: 5px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.topnick"
        size="mini"
        :placeholder="$t('filters.topNick')"
        style="width: 130px; margin-left: 20px; margin-top: 5px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        :placeholder="$t('filters.agent')"
        style="width: 150px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <span style="margin-left: 20px; font-size: 12px">{{$t('filters.regTime')}}</span>
      <el-date-picker
        style="width: 220px; margin-top: 5px"
        class="picker"
        v-model="dataRegisterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change='dataRegisterTimeChange'
      >
      </el-date-picker>
      <span style="margin-left: 20px; font-size: 12px">{{$t('tableHeader.lastLoginTime')}}</span>
      <el-date-picker
        style="width: 220px; margin-top: 5px"
        v-model="dataLoginTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change='dataLoginTimeChange'
      >
      </el-date-picker>
      <el-select
        size="mini"
        v-model="listQuery.labal_id"
        :placeholder="$t('filters.labal')"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 5px"
        class="filter-item"
      >
        <el-option
          v-for="item in labelOptions"
          :key="item.labelid"
          :label="item.label_name"
          :value="item.labelid"
        />
      </el-select>
       <el-input
        v-model="listQuery.ip"
        size="mini"
        :placeholder="$t('tableHeader.IP')"
        style="width: 150px; margin-left: 20px; margin-top: 5px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
       <el-input
        v-model="listQuery.invite_code "
        size="mini"
        :placeholder="$t('filters.inviteCode')"
        style="width: 150px; margin-left: 20px; margin-top: 5px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"  
      />
       <el-input
        v-model="listQuery.back_rate "
        @input="()=>{listQuery.back_rate = listQuery.back_rate.replace(/[^0-9]/g, '')}"
        size="mini"
        :placeholder="$t('filters.backRate')"
        style="width: 150px; margin-left: 20px; margin-top: 5px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-button
        style="margin-left:20px; margin-top: 5px"
        v-waves
        class="filter-item"
        size="mini"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
      
    </div>
    <!-- :key="tableKey" -->
    <!-- @sort-change="sortChange" -->
   <el-table
      v-loading="listLoading"
      :data="userList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="user_id" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center" min-width="95"> </el-table-column>
      <el-table-column :label="$t('filters.userType')" min-width="105px" align="center">
        <template slot-scope="{ row }">
          <span>{{row.user_type && userTypeOptions[row.user_type] || '--'}}</span>
        </template> 
      </el-table-column>
      <el-table-column :label="$t('tableHeader.KYCstate')" min-width="100px" align="center">
        <template slot-scope="{ row }">
          <span>{{verifyStatus[row.verify]}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.withdrawalState')" min-width="80px" align="center">
        <template slot-scope="{ row }">
          <span v-html="withdrawVerify[row.withdraw_verify]"></span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.inviteCode')" prop="invite_code" align="center" min-width="70"></el-table-column>
      <el-table-column :label="$t('filters.topID')" prop="top_agent_id" align="center" min-width="78">
        <template slot-scope="{ row }">
            <span>{{row.top_agent_id||'--'}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topNick')" prop="petname" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
       <el-table-column :label="$t('tableHeader.superiorID')" prop="pareid" align="center" min-width="78">
          <template slot-scope="{ row }">
             <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" prop="parename" align="center" min-width="95">
        <template slot-scope="{row}">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.withSingle')" prop="is_dealer " align="center" min-width="60">
        <template slot-scope="{row}">
          <span>{{ row.is_dealer? 'KOL':'--' }}</span>
        </template>
      </el-table-column>
      
      <el-table-column :label="$t('filters.backRate')" prop="agent_rebate_ratio " align="center" min-width="60">
        <template slot-scope="{row}">
          <span v-if="row.is_agent">{{ row.agent_rebate_ratio+ '%' }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.backTime')" prop="rake_back_time " align="center" min-width="60">
        <template slot-scope="{row}">
          <span v-if="row.is_agent">{{ row.rake_back_time }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      
      <el-table-column :label="$t('tableHeader.evmaddress')" prop="evm_address" align="center" min-width="78">
        <template slot-scope="{row}">
          <span>{{ row.evm_address? row.evm_address :'--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.pseudoUID')" prop="pseudo_uid" align="center" min-width="78">
        <template slot-scope="{row}">
          <span>{{ row.pseudo_uid? row.pseudo_uid :'--' }}</span>
        </template>
      </el-table-column>

      <el-table-column :label="$t('filters.regTime')" prop="created_time" align="center" min-width="75"/>
      <el-table-column :label="$t('tableHeader.lastLoginTime')" prop="last_login_time" align="center" min-width="75"/>
      <el-table-column :label="$t('tableHeader.lastLoginIP')" prop="last_login_ip" align="center" min-width="110px"/>
      <el-table-column :label="$t('filters.labal')" prop="labal_name" align="center" min-width="105px"/>
      <el-table-column :label="$t('tableHeader.note')" prop="content" align="center" show-overflow-tooltip min-width="95px">
        <template slot-scope="{ row }">
          <span>{{ row.content || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="$store.getters.roles.indexOf('setUsersLabel')>-1 || $store.getters.roles.indexOf('whetherrebate')>-1 || $store.getters.roles.indexOf('bcprouserup')>-1 || $store.getters.roles.indexOf('addcont')>-1 ||$store.getters.roles.indexOf('setagent')>-1 ||$store.getters.roles.indexOf('setagent1')>-1 || $store.getters.roles.indexOf('upagentstatus')>-1 || $store.getters.roles.indexOf('list')>-1"
        :label="$t('tableHeader.operation')"
        align="right"
        min-width="800"
      >
        <template slot-scope="{ row, $index }">
          <!-- <el-button plain size="mini" @click="labelClick(row)">划转</el-button> -->
          <!-- <el-button :type="([2,3].indexOf(row.withdraw_limit)>-1)?'info':'primary'" size="mini" v-if="$store.getters.roles.indexOf('upduserwithdrawlimit')>-1" @click="hanleWithdrawLimit(row)">{{ ([2,3].indexOf(row.withdraw_limit)>-1)?$t('buttons.qxtbxzh'):$t('buttons.tbxzh') }}</el-button> -->
          <el-button type="warning" size="mini" v-if="$store.getters.roles.indexOf('setUsersLabel')>-1" @click="setLabel(row, $index)">{{$t(`buttons.${row.labal_id?'updLabal':'setLabal'}`)}}</el-button>
          <el-button type="info" size="mini" v-if="$store.getters.roles.indexOf('setUsersLabel')>-1 && row.labal_id" @click="LabelDelClick(row, $index)">{{$t('buttons.delLabal')}}</el-button>
          <el-button :type="row.is_back_agent?'info':'primary'" size="mini" v-if="$store.getters.roles.indexOf('whetherrebate')>-1" @click="handleBackAgent(row)">{{ row.is_back_agent?$t('filters.no_commission'):$t('filters.restore_commission') }}</el-button>
          <el-button type="primary" size="mini" v-if="$store.getters.roles.indexOf('bcprouserup')>-1" @click="handleUpdate(row)">{{$t('buttons.edit')}}</el-button>
          <span v-if="row.agent_status" style="margin-left:10px;margin-right:10px;">
              <el-button
            type="warning"
            size="mini"
            v-if="row.agent_status == 1 && $store.getters.roles.indexOf('upagentstatus')>-1"
            @click="handleModifyStatus(row)"
          >{{$t('buttons.remove')}}</el-button>
           <el-button
            type="primary"
            size="mini"
            v-else-if="row.agent_status == 3 && $store.getters.roles.indexOf('upagentstatus')>-1"
            @click="handleModifyStatus(row)"
          >{{$t('buttons.startUsing')}}</el-button>
          </span>
          <span v-show="!row.is_agent && !row.agent_status"  style="margin-left:10px;margin-right:10px;">
            <el-button
              type="success"
              size="mini"
              v-if="row.user_type != 4 && $store.getters.roles.indexOf('setagent1')>-1"
              @click="handPromote(row,1)"
            >{{$t('buttons.proTopAgent')}}</el-button>
            <el-button
              type="success"
              size="mini"
              v-if="$store.getters.roles.indexOf('setagent')>-1"
              @click="handPromote(row,'')"
            >{{$t('buttons.proAgent')}}</el-button>
          </span>
          <el-button type="primary" size="mini" @click="Remarks(row, $index)" v-if="$store.getters.roles.indexOf('addcont')>-1">{{$t('tableHeader.note')}}</el-button>
          <el-button type="primary" size="mini" @click="handleCK(row, $index)" v-if="$store.getters.roles.indexOf('list')>-1">{{$t('buttons.toView')}}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 引入封装分页组件 -->
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,50,100,300]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />

    <!-- 备注 -->
    <el-dialog
      v-dialogDrag
      :visible.sync="RemarksDialogPvVisible"
      width="60%"
      :title="$t('tableHeader.note')"
    >
      <el-form
        ref="remarksform"
        :model="temp"
        label-position="left"
        label-width="auto"
      >
        <el-form-item :label="$t('tableHeader.note')" >
          <el-input
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
            :placeholder="$t('filters.pleaseEnterTheContent')"
            maxlength="20"
            show-word-limit
            v-model="temp.content"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="RemarksDialogClick">{{$t('buttons.determine')}}</el-button>
      </span>
    </el-dialog>

    <el-dialog
      v-dialogDrag
      :title="$t('buttons.edit')"
      width="75%"
      :visible.sync="dialogFormVisible"
      @close="dialogClose"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="160px"
      >
        <el-form-item :label="$t('tableHeader.uid')" prop="user_id">
          <span>{{temp.user_id}}</span>
        </el-form-item>
        <el-form-item :label="$t('tableHeader.userName')" prop="user_name">
          <span>{{temp.user_name}}</span>
        </el-form-item>
        <el-form-item v-if="temp.user_type != 3 && temp.user_type !=4" :label="$t('filters.userType')" prop="user_type">
          <span>{{temp.user_type && userTypeOptions[temp.user_type] || '--'}}</span>
          <!-- <el-select
            v-model="temp.user_type"
            class="filter-item"
            placeholder="请选择"
            disabled
          >
            <el-option
              v-for="(value, key, index) in userTypeOptions"
              :key="index"
              :label="value"
              :value="key"
            />
          </el-select> -->
        </el-form-item>
        <el-form-item v-if="temp.user_type != 3 && temp.user_type !=4 && temp.user_type !=1" :label="$t('forms.superiorInviteCode')" prop="invite_codepar">
          <el-row>
            <el-col :span="24">
              <div class="grid-content bg-purple">
                <el-input
                  v-model="invite_codepar"
                  v-if="temp.invite_code_edit"
                ></el-input>
                <el-input
                  v-model="invite_codepar"
                  :disabled="true"
                  v-else
                ></el-input>
              </div>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item v-if="[1,2].indexOf(temp.user_type)>-1" :label="$t('filters.agentNick')" prop="petname">
          <el-input v-model="temp.petname" >
          </el-input>
        </el-form-item>
        <el-form-item v-if="temp.user_type != 3 && temp.user_type !=4" :label="$t('forms.commissionPercentage')" prop="agent_rebate_ratio">
          <el-input v-model.number="temp.agent_rebate_ratio" onkeyup="value=value.replace(/[^\d]/g,'')">
            <i slot="suffix" style="font-style:normal">%</i>
          </el-input>
        </el-form-item>
        <el-form-item v-if="temp.user_type != 3 && temp.user_type !=4" :label="$t('forms.rakeBackTime')"  prop="rake_back_time">
          <el-input v-model.number="temp.rake_back_time" onkeyup="value=value.replace(/[^\d]/g,'')">
            <i slot="suffix" style="font-style:normal">{{$t('forms.day')}}</i>
          </el-input>
        </el-form-item>
        <el-form-item v-if="temp.user_type != 3 && temp.user_type !=4" :label="$t('forms.toViewProfitLoss')" prop="is_view_profit">
          <el-radio-group v-model="temp.is_view_profit">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="temp.user_type != 3 && temp.user_type !=4" :label="$t('forms.catchPlate')" prop="enable_simulator">
          <el-radio-group v-model="temp.enable_simulator">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="temp.user_type != 3 && temp.user_type !=4" :label="$t('forms.poundageMonitoring')" prop="is_view_monitor">
          <el-radio-group v-model="temp.is_view_monitor">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="temp.user_type != 3 && temp.user_type !=4" :label="$t('forms.canProAgent')" prop="is_open_agent">
          <el-radio-group v-model="temp.is_open_agent">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="temp.user_type != 3 && temp.user_type !=4" :label="$t('forms.webBackRate')" prop="show_agent_ratio">
          <el-radio-group v-model="temp.show_agent_ratio">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="temp.user_type != 3 && temp.user_type !=4" :label="$t('forms.positionMonitoring')" prop="show_position_monitor">
          <el-radio-group v-model="temp.show_position_monitor">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="temp.user_type != 3 && temp.user_type !=4" :label="$t('forms.othersAPIpermissions')" prop="is_child_api">
          <el-radio-group v-model="temp.is_child_api">
            <el-radio :label="1">{{$t('forms.allow')}}</el-radio>
            <el-radio :label="0">{{$t('forms.notAllow')}}</el-radio>
          </el-radio-group>
        </el-form-item>
         <el-form-item v-if="temp.user_type != 3 && temp.user_type !=4" :label="$t('forms.needcode')" prop="login_verify_enable">
          <el-radio-group v-model="temp.login_verify_enable">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('forms.traders')" prop="is_show_dealer">
          <el-radio-group v-model="temp.is_show_dealer">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('forms.buyFiat')" prop="is_legal_buy">
          <el-radio-group v-model="temp.is_legal_buy">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('forms.sellFiat')" prop="is_legal_sell">
          <el-radio-group v-model="temp.is_legal_sell">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="temp.user_type != 3 && temp.user_type !=4" :label="$t('forms.tradingToOneself')" prop="is_back_self">
          <el-radio-group v-model="temp.is_back_self">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="temp.user_type != 3 && temp.user_type !=4" :label="$t('forms.desensitization')" prop="is_show_all_account">
          <el-radio-group v-model="temp.is_show_all_account">
            <el-radio :label="0">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="1">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="temp.user_type != 3 && temp.user_type !=4" :label="$t('forms.proStraightPush')" prop="is_edit_all_account">
          <el-radio-group v-model="temp.is_edit_all_account">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="temp.user_type != 3 && temp.user_type !=4" :label="$t('forms.allUser')" prop="can_view_subordinate">
          <el-radio-group v-model="temp.can_view_subordinate">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="temp.user_type != 3 && temp.user_type !=4" :label="$t('forms.seeHomeData')" prop="is_header">
          <el-radio-group v-model="temp.is_header">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
         <!-- <el-form-item v-if="temp.user_type != 3 && temp.user_type !=4" :label="$t('forms.assetQuery)" prop="is_wallet_view">
          <el-radio-group v-model="temp.is_wallet_view">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item> -->
        <!-- 分割线 -->
        <el-divider v-if="temp.user_type != 3 && temp.user_type !=4"></el-divider>
        <el-form-item :label="$t('forms.transactionStatus')">
          <el-select v-model="temp.enable_trade" class="filter-item">
            <el-option
              v-for="item in tradingStatusOptions"
              :key="item.key"
              :label="item.name"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('forms.loginStatus')" prop="enable_login">
          <el-select v-model="temp.enable_login" class="filter-item">
            <el-option
              v-for="item in loginStatusOptions"
              :key="item.key"
              :label="item.name"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('forms.mentionMoneyStatus')" prop="enable_withdraw">
          <el-select v-model="temp.enable_withdraw" class="filter-item">
            <el-option
              v-for="item in withdrawalStatusOptions"
              :key="item.key"
              :label="item.name"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('forms.mentionMoneyConfirm')" prop="withdraw_verify">
          <el-select v-model="temp.withdraw_verify" class="filter-item">
            <el-option
              v-for="item in withdrawVerifyOptions"
              :key="item.key"
              :label="item.name"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('forms.APIpermissions')" prop="is_open_api">
          <el-select v-model="temp.is_open_api" class="filter-item">
            <el-option
              v-for="item in isOpenApiOptions"
              :key="item.key"
              :label="item.name"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
        <el-divider v-if="temp.user_type != 3 && temp.user_type !=4"></el-divider>
        <el-form-item v-if="temp.user_type != 3 && temp.user_type !=4" :label="$t('forms.resetCRMPass')">
          <el-button
            type="primary"
            @click="resetPass"
            size="mini"
          >{{$t('buttons.reset')}}</el-button>
        </el-form-item>
        <!-- 分割线 -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="updateData"
        >{{$t('buttons.determine')}}</el-button>
      </div>
    </el-dialog>

    <!-- 提升代理对话框 -->
    <el-dialog
      :title="$t('buttons.proAgent')"
      :visible.sync="PromoteDialogVisible"
      width="75%"
      v-dialogDrag
      @close="dialogClose"
    >
      <el-form
        ref="temp"
        label-position="right"
        :rules="rules"
        :model="temp"
        label-width="160px" 
      >
        <el-form-item :label="$t('tableHeader.uid')" prop="user_id" >
          <span>{{temp.user_id}}</span>
        </el-form-item>
        <el-form-item :label="$t('tableHeader.userName')" prop="user_name">
          <span>{{temp.user_name}}</span>
        </el-form-item>
        <el-form-item :label="$t('filters.userType')" prop="user_type">
          <span>{{this.temp.user_type && userTypeOptions[this.temp.user_type] || '--'}}</span>
        </el-form-item>
        <el-form-item v-if="[1,2].indexOf(temp.user_type)>-1" :label="$t('filters.agentNick')" prop="petname">
          <el-input v-model="temp.petname" maxlength="10" show-word-limit></el-input>
        </el-form-item>
        <el-form-item v-if="temp.user_type == 2" :label="$t('forms.superiorInviteCode')" prop="invite_code">
          <el-input v-model="temp.invite_code"></el-input>
        </el-form-item>
        <el-form-item :label="$t('forms.commissionPercentage')" prop="agent_rebate_ratio">
          <el-input v-model="temp.agent_rebate_ratio" onkeyup="value=value.replace(/[^\d]/g,'')">
            <i slot="suffix" style="font-style:normal">%</i>
          </el-input>
        </el-form-item>
        <el-form-item :label="$t('forms.rakeBackTime')" prop="rake_back_time">
          <el-input v-model="temp.rake_back_time" onkeyup="value=value.replace(/[^\d]/g,'')">
            <i slot="suffix" style="font-style:normal">{{$t('forms.day')}}</i>
          </el-input>
        </el-form-item>
        <el-form-item :label="$t('forms.toViewProfitLoss')"  prop="is_view_profit">
          <el-radio-group v-model="temp.is_view_profit">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('forms.catchPlate')" prop="enable_simulator">
          <el-radio-group v-model="temp.enable_simulator">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('forms.poundageMonitoring')" prop="is_view_monitor">
          <el-radio-group v-model="temp.is_view_monitor">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('forms.canProAgent')" prop="is_open_agent">
          <el-radio-group v-model="temp.is_open_agent">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('forms.webBackRate')" prop="show_agent_ratio">
          <el-radio-group v-model="temp.show_agent_ratio">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('forms.positionMonitoring')" prop="show_position_monitor">
          <el-radio-group v-model="temp.show_position_monitor">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('forms.othersAPIpermissions')" prop="is_child_api">
          <el-radio-group v-model="temp.is_child_api">
            <el-radio :label="1">{{$t('forms.allow')}}</el-radio>
            <el-radio :label="0">{{$t('forms.notAllow')}}</el-radio>
          </el-radio-group>
        </el-form-item>
         <el-form-item :label="$t('forms.needcode')" prop="login_verify_enable">
          <el-radio-group v-model="temp.login_verify_enable">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
         <el-form-item :label="$t('forms.traders')" prop="is_show_dealer">
          <el-radio-group v-model="temp.is_show_dealer">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('forms.buyFiat')" prop="is_legal_buy">
          <el-radio-group v-model="temp.is_legal_buy">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('forms.sellFiat')" prop="is_legal_sell">
          <el-radio-group v-model="temp.is_legal_sell">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('forms.tradingToOneself')" prop="is_back_self">
          <el-radio-group v-model="temp.is_back_self">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('forms.desensitization')" prop="is_show_all_account">
          <el-radio-group v-model="temp.is_show_all_account">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('forms.proStraightPush')" prop="is_edit_all_account">
          <el-radio-group v-model="temp.is_edit_all_account">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('forms.allUser')" prop="can_view_subordinate">
          <el-radio-group v-model="temp.can_view_subordinate">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('forms.seeHomeData')" prop="is_header">
          <el-radio-group v-model="temp.is_header">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item :label="$t('forms.assetQuery')" prop="is_wallet_view">
          <el-radio-group v-model="temp.is_wallet_view">
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
          </el-radio-group>
        </el-form-item> -->
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="PromoteDialogClick()"
          >{{$t('buttons.confirm')}}</el-button
        >
      </span>
    </el-dialog>
    
    <el-dialog
      @close="closexgmm"
      class
      :title="$t('others.googleCode')"
      width="350px"
      :visible.sync="checkvkeyDialog"
      :close-on-click-modal="false"
      @submit.native.prevent
    >
      <el-form 
        :rules="rules" 
        label-position="left"
        ref="ruleForm" 
        :model="ruleForm"
        label-width="auto"
      >
        <el-form-item :label="$t('login.code')" prop="yzmVal">
         <!-- <div class="sixcode_div">-->
            <el-input v-model="ruleForm.yzmVal" class="input_div" autocomplete="off" maxlength="6" ></el-input>
         <!--  </div> -->
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="checkvkeyDialog = false">{{$t('buttons.cancel')}}</el-button>
        <el-button size="mini" type="primary" @click="entrySendyzm()">{{$t('buttons.determine')}}</el-button>
      </div>
    </el-dialog>
   
   <el-dialog
      :visible.sync="agentDialogVisible"
      width="330px"
      v-dialogDrag
      >
      <div v-if="agentList.agent_status == 3">
        {{$t('dialog.enableQualification')}}？
      </div>
       <div v-else-if="agentList.agent_status == 1">
        <div style="font-size:17px;font-weight:bold;">{{$t('dialog.removeQualification')}}？</div>
        <div style="margin-top:10px;">{{$t('dialog.longContent')}}</div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="agentDialogVisible = false">{{$t('buttons.cancel')}}</el-button>
        <el-button type="primary" @click="agentDialogVisibleClick()">{{$t('buttons.determine')}}</el-button>
      </span>
    </el-dialog>
    <!-- 标签 -->
    <el-dialog
      @closed="closeSetLabel"
      v-dialogDrag
      :visible.sync="setLabelData.dialog"
      width="70%"
      :title="$t('buttons.setLabal')"
    >
      <div class="select_wrap">
        <span>{{$t('filters.labal')}}</span>
        <el-select
          v-model="setLabelData.label_id"
          class="filter-item"
          :placeholder="$t('dialog.pleaseSelect')"
          clearable
          @change="selectLabel"
        >
          <el-option
            v-for="item in labelOptions"
            :key="item.labelid"
            :label="item.label_name"
            :value="item.labelid"
          />
        </el-select>
        <i v-if="$store.getters.roles.indexOf('setuserlabl')>-1" @click="()=>{this.$router.push('/riskAdminister/lebelAdminister')}" class="el-icon-circle-plus-outline" style="font-size:24px;padding-left:10px;"></i>
      </div>
      <div v-if="setLabelData.label_id">
        <el-table
            v-loading="setLabelData.labelListLoading"
            :data="setLabelData.labelList"
            border
            fit
            highlight-current-row
            size="mini"
            style="width: 100%; margin-top: 30px"
            :header-cell-style="{ background: '#F0F8FF' }"
          >
            <el-table-column :label="$t('tableHeader.contract')" prop="contract_code" align="center" min-width="90"> </el-table-column>
            <el-table-column :label="$t('tableHeader.maxLeverage')" prop="max_lever" align="center" min-width="110px"> </el-table-column>
           
            <el-table-column :label="$t('tableHeader.maxOrderQuantity')" prop="max_order_volume" align="center" min-width="130px">
                <template slot-scope="{ row }">
                <span>{{ row.max_order_volume || '--' }}</span>
              </template>
            </el-table-column>
              <el-table-column :label="$t('tableHeader.minOrderQuantity')" prop="min_order_volume" align="center" min-width="130px">
                <template slot-scope="{ row }">
                <span>{{ row.min_order_volume || '--' }}</span>
              </template>
            </el-table-column>
             <el-table-column :label="$t('tableHeader.maxPosition')" prop="max_posi_volume" align="center" min-width="110px">
                <template slot-scope="{ row }">
                <span>{{ row.max_posi_volume || '--' }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('tableHeader.minSomeBad')" align="center" min-width="110px">
              <template slot-scope="{ row }">
               <span>{{ row.min_slippage}}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('tableHeader.maxSomeBad')" align="center" min-width="110px">
              <template slot-scope="{ row }">
               <span>{{ row.slippage}}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('tableHeader.poundageReat')" prop="fee" align="center" min-width="95px">
                <template slot-scope="{ row }">
                <span>{{ row.fee}}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('tableHeader.moneyReat')" align="center" min-width="100px">
              <template slot-scope="{ row }">
               <span>{{ row.funding}}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('tableHeader.risk')" prop="risk_rate" align="center" min-width="110px"> </el-table-column>
            <el-table-column :label="$t('tableHeader.state')" prop="totalincash" min-width="90px" align="center">
              <template slot-scope="{row}">
                <span>{{row.status_stype == 1 ? $t('tableHeader.open'):$t('buttons.close')}}</span>
              </template>
            </el-table-column>
          </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button :disabled="!setLabelData.label_id" type="primary" @click="handelSetLabel">{{$t('buttons.determine')}}</el-button>
      </span>
    </el-dialog>
    
    <el-dialog
      @close="closexzhlx"
      class
      :title="$t([2,3].indexOf(xzhlxForm.withdraw_limit)>-1?'others.qxzqxxzhyhlx':'others.qxzxzhyhlx')"
      width="350px"
      :visible.sync="xzhtbxzhDialogShow"
      :close-on-click-modal="false"
      @submit.native.prevent
    >
      <el-form 
        :rules="rules" 
        label-position="left"
        ref="xzhlxForm" 
        :model="xzhlxForm"
        label-width="0"
      >
        <el-form-item label="" prop="type">
          <el-select v-model="xzhlxForm.type" :placeholder="$t('dialog.pleaseSelect')">
            <el-option :label="$t('others.xzhOption1')" :value="0"></el-option>
            <el-option :label="$t('others.xzhOption2')" :value="1"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="xzhtbxzhDialogShow = false">{{$t('buttons.cancel')}}</el-button>
        <el-button size="mini" type="primary" @click="xzhDialogEnter()">{{$t('buttons.determine')}}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  userList,
  addcont,
  bcprouserup,
  upagentstatus,
  setagent,
  resetagentpass,
  whetherrebate,
  upuserwithdrawlimit,
} from "@/api/userQuery";
import { setuserlabl, setuserlabldel } from "@/api/fundQuery";

import { getlabel, getlabllistbyid ,commckeckvkey } from '@/api/user'

import axios from "axios";

// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";

//引入的自定义指令
import waves from "@/directive/waves";
// import { values } from 'mock/user';


/**
 * 0-未认证 1-身份信息认证中 2-身份信息认证失败 3-身份信息认证通过 4-人脸信息认证中 
 * 5-人脸信息认证失败 6-人脸信息认证通过,7重置
 */

export default {
  name: "userList",
  directives: { waves },
  data() {
    const userTypeOptions = {
      1: this.$t('filters.top_agent'),
      2: this.$t('filters.The_agent'),
      3: this.$t('filters.The_average_user'),
      4: this.$t('filters.The_proxy_directly_pushes_users'),
    };
    const verifyStatus = {
      0: this.$t('filters.Unauthorized'),
      1: this.$t('filters.Authentication_of_identity_information'),
      2: this.$t('filters.Failed_to_authenticate_the_identity_information_Procedure'),
      3: this.$t('filters.The_identity_information_is_authenticated_Procedure'),
      4: this.$t('filters.Face_information_authentication'),
      5: this.$t('filters.Face_authentication_failed_Procedure'),
      6: this.$t('filters.The_face_information_is_authenticated'),
      7: this.$t('buttons.reset'),
    }
     // 自定义校验规则 方法
    var rake_back_time_rules = (rules,value,callback)=>{
      // 限制只能输入大于1的正整数的正则
      if(!value){
        return callback(new Error(this.$t('dialog.The_input_box_cannot_be_empty')))
      }else if(value == 0){
        return callback(new Error(this.$t('dialog.The_minimum_validity_period_is_1')))
      }else{
          callback()
      }
    };
   var agent_rebate_ratio_rules = (rules,value,callback)=>{
     if(!value && value !== 0){
       return callback(new Error(this.$t('dialog.The_input_box_cannot_be_empty')))
     }else if(value >95){
       return callback(new Error(this.$t('dialog.The_ratio_cannot_be_greater_than_95')))
     }else{
       callback()
     }
   }
    return {
      //表格数据
      userList: null,
      listLoading: true,  // 表格加载中效果
      total: 0,//分页的数量
      dataRegisterTime: [],  // 注册时间 筛选
      dataLoginTime: [],  //登录时间 筛选
      listQuery: {
        pageNo: 1,
        pagesize: 10,
        sname: "", // uid 手机号或邮箱
        stype: "", // 0全部 1 顶级代理 2代理，3普通用户
        topid: "", // 顶级代理id
        topnick: "", // 顶级代理昵称
        sagent: "", // 代理或者id
        labal_id: '', //  标签id 0全部
        invite_code: "", // 邀请码
        regstar: "", // 注册开始时间
        regend: "", // // 结束时间
        loginstar: "", // 开始时间
        loginend: "", // 结束时间
        ip:"", // 
        back_rate: "", // 返佣比例
      },
      checkvkeyDialog: false, // 验证谷歌弹框显示控制
      ruleForm: {
        yzmVal: "" // 谷歌验证码
      },
      RemarksDialogPvVisible: false,// 备注弹框显示控制
      textarea: "",//备注
      dialogFormVisible: false,// 编辑弹框显示控制
      PromoteDialogVisible: false, // 提升代理弹框显示控制
      temp: {  // 编辑 提升代理
        user_type: 2,
        is_view_profit: 1,
        enable_simulator: 1, 
        is_view_monitor: 1,
        is_open_agent: 0,
        show_agent_ratio: 0,
        show_position_monitor: 0,
        is_child_api: 0,
        login_verify_enable:1,
        is_show_dealer:0,
        is_legal_buy:0,
        is_legal_sell:0,
        is_back_self:0,
        is_show_all_account:0,
        is_edit_all_account:0,
        can_view_subordinate: 0,
        is_header: 1,
        // is_wallet_view:0,
        petname: "",
        invite_parent: "", // 邀请码
        enable_login: 1,
        enable_trade: 1,
        enable_withdraw: 1,
        withdraw_verify: 0, 
        is_open_api: 0,
        content:'',
        invite_code:'',
        agent_rebate_ratio:'',
        rake_back_time:1
      },
      handlerType: 1, // 1: 编辑  0: 提升代理
      userTypeOptions, // 用户类型 Options
      labelOptions:[], // 标签 Options
      verifyStatus, // 认证状态 字典表
      withdrawVerify: {
        0: this.$t('filters.Has_been_open_unauthorized'),
        1: this.$t('filters.Has_been_open_in_the_authentication'),
        2: this.$t('filters.Has_been_open_authentication_failed'),
        3: this.$t('filters.Closed_authentication_success'),
      },
      tradingStatusOptions: [  //交易状态 Options
        { key: 1, name: this.$t('filters.Allowed_to_trade') },
        { key: 0, name: this.$t('filters.Ban_on_trading') },
        { key: 2, name: this.$t('filters.Ban_on_open_positions') },
        { key: 3, name: this.$t('filters.Ban_unwind') },
      ],
      loginStatusOptions: [  //登录状态 Options
        { key: 1, name: this.$t('filters.Allowed_to_login') },
        { key: 0, name: this.$t('filters.Banned_logging') },
      ], 
      withdrawalStatusOptions: [  // 提币状态  Options
        { key: 1, name: this.$t('filters.Allowed_to_mention_money') },
        { key: 0, name: this.$t('filters.Prohibit_mention_money') },
      ], 
      withdrawVerifyOptions: [  // 提币二次确认  Options
        { key: 0, name: this.$t('tableHeader.open') },
        { key: 3, name: this.$t('buttons.close') },
      ], 
      isOpenApiOptions: [  // 是否开通api  Options
        { key: 1, name: this.$t('tableHeader.open') },
        { key: 0, name: this.$t('buttons.close') },
      ], 
      rules: {  // 验证规则
        invite_code: [{ required: true, message: this.$t('dialog.The_input_box_cannot_be_empty'), trigger: "blur" },],
        agent_rebate_ratio: [{ required: true, validator:agent_rebate_ratio_rules , trigger: "blur" },],
        rake_back_time: [{ required: true,validator:rake_back_time_rules, trigger: "blur", },],
        yzmVal: [{ required: true, message: this.$t('dialog.The_input_box_cannot_be_empty'), trigger: "blur" },],
        type: [{ required: true, message: this.$t('dialog.pleaseSelect'), trigger: 'change' }],
      },
      invite_codepar:'',
      agentDialogVisible:false,
      agentList:{},

      setLabelData: {
        dialog: false,
        user_id: '',
        label_id: undefined,
        labelListLoading: false,
        labelList: []
      },

      xzhtbxzhDialogShow: false,
      xzhlxForm: {
        type: '',
      }
    };
  },
  // 过滤器
  filters: {
    //过滤标签
    statusFilter(status) {
      const statusMap = {
        published: "success",
        draft: "info",
        deleted: "danger",
      };
      return statusMap[status];
    },
    typeFilter(type) {
      return calendarTypeKeyValue[type];
    },
  },

  components: {},

  computed: {},

  mounted() {
    // console.log(this.$store.getters.roles)
    this.getList();
    getlabel({}).then((res)=>{
      this.labelOptions = res.data
    })
    
    // this.userInfo()
  },

  methods: {
    // 提币限制/取消提币限制
    hanleWithdrawLimit(item){
      if([1,2].indexOf(item.user_type)>-1){
        this.xzhlxForm = Object.assign({},this.xzhlxForm,item)
        this.xzhtbxzhDialogShow = true
      }else{
        upuserwithdrawlimit({
          uid: item.user_id,
          is_child: 0,
          withdraw_limit: ([2,3].indexOf(item.withdraw_limit)>-1)?4:2
        }).then(res=>{
          this.$notify({
            title: this.$t('dialog.Successful'),
            message: this.$t('dialog.Operation_is_successful'),
            type: "success",
            duration: 2000,
          });
          this.getList();
        })
      }
    },
    // 提币限制弹框确认事件
    xzhDialogEnter(){
      this.$refs["xzhlxForm"].validate(valid => {
        if (valid) {
          upuserwithdrawlimit({
            uid: this.xzhlxForm.user_id,
            is_child: this.xzhlxForm.type,
            withdraw_limit: ([2,3].indexOf(this.xzhlxForm.withdraw_limit)>-1)?4:2
          }).then(res=>{
            this.xzhtbxzhDialogShow = false
            this.$notify({
              title: this.$t('dialog.Successful'),
              message: this.$t('dialog.Operation_is_successful'),
              type: "success",
              duration: 2000,
            });
            this.getList();
          })
        }
      })
    },
    // 关闭标签事件
    closeSetLabel(){
        this.setLabelData.label_id = undefined
        this.lebelList = null
    },
    // 设置标签确认点击事件
    handelSetLabel(){
      setuserlabl({
        user_id: JSON.parse(this.setLabelData.user_id),
        lablid: Number(this.setLabelData.label_id)
      }).then((res) => {
        this.setLabelData.dialog = false;
        this.setLabelData.label_id = undefined
        this.$notify({
          title: this.$t('dialog.Successful'),
          message: this.$t('dialog.Operation_is_successful'),
          type: "success",
          duration: 2000,
        });
        this.getList();
      });
    },
    // 选择标签
    selectLabel(val){
      console.log(val)
      this.setLabelData.label_id = val
      this.setLabelData.labelListLoading = true
      if(val){
        getlabllistbyid(val).then((res)=>{
          this.setLabelData.labelListLoading = false
          this.setLabelData.labelList = res.data
        })
      }
    },
    // 设置标签
    setLabel(row,index){
      this.setLabelData.dialog = true
      this.setLabelData.user_id = row.user_id
      console.log(row.labal_id)
      if(row.labal_id){
        this.selectLabel(row.labal_id)
      }
    },
    // 删除标签
    LabelDelClick(row){
      // console.log(row.userid)
      this.$confirm(this.$t('dialog.You_want_to_delete_a_label'), this.$t('dialog.Prompt'), {
        confirmButtonText: this.$t('buttons.determine'),
        cancelButtonText: this.$t('buttons.cancel'),
        type: 'warning'
      }).then(() => {
        // console.log('1111111111')
        setuserlabldel({
          user_id:JSON.parse(row.user_id),
        }).then(() => {
          this.$notify({
            title: this.$t('dialog.Delete_the_success'),
            type: "success",
            duration: 2000,
          });
          this.getList();
        });
      })
    },
    // 不返佣金/恢复返佣
    handleBackAgent(item){
      whetherrebate({
        user_id: item.user_id,
        is_rebate: item.is_back_agent?0:1
      }).then(res=>{
        this.$notify({
          title: this.$t('dialog.Successful'),
          message: this.$t('dialog.Operation_is_successful'),
          type: "success",
          duration: 2000,
        });
        this.getList();
      })
    },
    resetPass(){
      resetagentpass({user_id: this.temp.user_id}).then(res=>{
        this.$notify({
          title: this.$t('dialog.Successful'),
          message: this.$t('dialog.Operation_is_successful'),
          type: "success",
          duration: 2000,
        });
      })
    },
    getList() {
      var that = this;
      //开始有加载中效果
      that.listLoading = true;
      let data = {
        pageNo: 1, // 分页
        pagesize: 10, //数量
        sname: "", // uid 手机号或邮箱
        stype: 0, // 0全部 1 顶级代理 2代理，3普通用户
        topid: "", // 顶级代理id
        topnick: "", // 顶级代理昵称
        sagent: "", // 代理或者id
        labal_id: 0, //  标签id 0全部
        invite_code: "", // 邀请码
        regstar: "", // 注册开始时间
        regend: "", // // 结束时间
        loginstar: "", // 开始时间
        loginend: "", // 结束时间
      };
      Object.assign(data,this.listQuery)
      data.stype = parseInt(this.listQuery.stype) || 0
      data.labal_id = this.listQuery.labal_id || 0
      data.back_rate = this.listQuery.back_rate ? Number(this.listQuery.back_rate) : 0
      userList(data).then((response) => {
        that.userList = response.data.list;
        that.total = response.data.total;
        that.listLoading = false;
      });
    },
    //点击备注按钮
    Remarks(row){
      this.temp.user_id = JSON.parse(row.user_id)//Number(Number(a).mul(1000)).add(Number(b))
      this.temp.content = row.content
      this.RemarksDialogPvVisible = true;
    },
    //备注对话框里面确定按钮
    RemarksDialogClick(){
      addcont({
        user_id: this.temp.user_id,
        content: this.temp.content,
      }).then(() => {
        this.RemarksDialogPvVisible = false;
        this.$notify({
          title: this.$t('dialog.Successful'),
          message: this.$t('dialog.Operation_is_successful'),
          type: "success",
          duration: 2000,
        });
      });
      this.getList()
    },
    handleUpdate(row) {
      this.handlerType = '1'
      Object.assign(this.temp,row,{ agent_rebate_ratio: row.agent_rebate_ratio+'', petname: row.selfpetname })
      this.dialogStatus = "update";
      this.dialogFormVisible = true;
      if(this.temp.invite_parent.length>7){
        this.invite_codepar = this.temp.invite_parent.substring(this.temp.invite_parent.length-6)
      }else{
        this.invite_codepar = ''
      }
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    updateData() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
            // 不走google验证码
            this.entrySendyzm()
            // 走google
          // this.checkvkeyDialog = true
        }
      });
    },
    //kfc状态审核中和未审核
    handleModifyStatus(row) {
      this.agentDialogVisible = true
      this.agentList = row
      // console.log(row)
    },
    agentDialogVisibleClick(){
       upagentstatus({
          agentstatus:this.agentList.agent_status == 1?3:1, // 1正常 3注销
          user_id:JSON.parse(this.agentList.user_id) //用户id
        }).then((res)=>{
          this.getList();
        })
        this.agentDialogVisible = false
    },
    //点击划转事件
    labelClick(row) {
      console.log(row);
    },
    //普通用户提升代理
    handPromote(row,type){
      this.handlerType = '0'
      Object.assign(this.temp,row,{user_type: type?type:2,invite_code: '', petname: row.selfpetname, is_back_self: type?0:1,is_view_monitor: 1,is_header: 1});
      this.PromoteDialogVisible = true;
      if(this.temp.rake_back_time == 0){
        this.temp.rake_back_time = 1
      }
       if(this.temp.agent_rebate_ratio == 0){
        this.temp.agent_rebate_ratio = ''
      }
      // console.log(this.temp)
      this.$nextTick(() => {
        this.$refs["temp"].clearValidate();
      });
    },
    //普通用户提升代理对话框点击确定
    PromoteDialogClick() {
      this.$refs["temp"].validate((valid) => {
        if (valid) {
          if(this.temp.rake_back_time == 0){
            this.$message.error(this.$t('dialog.The_minimum_validity_period_is_1'))
          }else{
            // 不走google验证码
            this.entrySendyzm()
            // 走google
            // this.checkvkeyDialog = true
          }
        }
      });
    },
    entrySendyzm() {
      // this.$refs["ruleForm"].validate(valid => {
      //   if (valid) {
          // commckeckvkey({code: this.ruleForm.yzmVal}).then(res=>{
            if(this.handlerType == '0'){
              let data = {
                "uid": this.temp.user_id, //用户id
                "petname": [1,2].indexOf(this.temp.user_type)>-1?this.temp.petname:'', //代理昵称
                "setype": this.temp.user_type == 1?this.temp.user_type:0, // 1添加顶级代理 0 非顶级代理
                "parcode": this.temp.user_type == 2?this.temp.invite_code:'', // 归属代理邀请码
                "agent_rebate_ratio": Number(this.temp.agent_rebate_ratio), //返佣
                "rake_back_time": Number(this.temp.rake_back_time) || 1,//返佣期限
                "is_view_profit": this.temp.is_view_profit, //是否可查看盈亏 1可以查看 0不可以
                "enable_simulator": this.temp.enable_simulator,  //是否显示模拟盘 1: 展示模拟盘 0:不展示
                "is_view_monitor": this.temp.is_view_monitor, //是否可查看代理监控 
                "is_open_agent": this.temp.is_open_agent, //可提升代理 
                "show_agent_ratio": this.temp.show_agent_ratio, //是否在前端显示代理数据 
                "show_position_monitor": this.temp.show_position_monitor, //是否显示持仓监控 
                "is_child_api": this.temp.is_child_api, //其用户API管理权限 
                "is_open_api": this.temp.is_open_api, // 是否开通api 1是 0否
                "login_verify_enable":this.temp.login_verify_enable,//登录CRM是否需要验证码
                "is_show_dealer": this.temp.is_show_dealer, //是否可开通交易员 0否 1是，
                "is_legal_buy": this.temp.is_legal_buy,
                "is_legal_sell": this.temp.is_legal_sell,
                "is_back_self": this.temp.is_back_self, //自交易返自己 0否 1是，
                "is_show_all_account": this.temp.is_show_all_account, //非直推用户脱敏 0否 1是，
                "is_edit_all_account": this.temp.is_edit_all_account, //提升非直推 0否 1是，
                "can_view_subordinate": this.temp.can_view_subordinate, //显示所有用户 0否 1是，
                "is_header": this.temp.is_header, //是否可见首页数据 0否 1是，
                // "is_wallet_view": this.temp.is_wallet_view, //可查看资产查询，
              }
              setagent(data).then((res) => {
                this.PromoteDialogVisible = false;
                this.checkvkeyDialog = false;
                this.ruleForm.yzmVal = ''
                this.$notify({
                  title: this.$t('dialog.Successful'),
                  message: this.$t('dialog.Operation_is_successful'),
                  type: "success",
                  duration: 2000,
                });
                this.getList();
              });
            }else{
              let data = {
                "uid": this.temp.user_id, //用户id
                "petname": [1,2].indexOf(this.temp.user_type)>-1?this.temp.petname:'', //代理昵称
                "enablelogin": this.temp.enable_login, //1: 允许登录 0: 禁止登录
                "enablewithdraw": this.temp.enable_withdraw, //1: 允许提币 0: 禁止提币
                "enabletrade": this.temp.enable_trade, //1: 允许交易 0: 禁止交易
                "withdraw_verify": this.temp.withdraw_verify, /// 0-未验证(开启) 1-验证中 2-验证失败 3-已验证(关闭) 
                "upagent": [3,4].indexOf(this.temp.user_type) >-1 ? 0 : 1, //1 更新代理信息 0不更新
                "agent_rebate_ratio": Number(this.temp.agent_rebate_ratio), //返佣比例
                "rake_back_time":Number(this.temp.rake_back_time), //返佣发放期限
                "is_view_profit": this.temp.is_view_profit, //是否可查看盈亏 1可以查看 0不可以
                "enable_simulator": this.temp.enable_simulator,  //是否显示模拟盘 1: 展示模拟盘 0:不展示
                "is_view_monitor": this.temp.is_view_monitor, //是否可查看代理监控 
                "is_open_agent": this.temp.is_open_agent, //可提升代理 
                "show_agent_ratio": this.temp.show_agent_ratio, //是否在前端显示代理数据 
                "show_position_monitor": this.temp.show_position_monitor, //是否显示持仓监控 
                "is_child_api": this.temp.is_child_api, //其用户API管理权限 
                "is_open_api": this.temp.is_open_api, // 是否开通api 1是 0否
                "login_verify_enable":this.temp.login_verify_enable,//登录CRM是否需要验证码
                "is_show_dealer": this.temp.is_show_dealer, //是否可开通交易员 0否 1是，
                "is_legal_buy": this.temp.is_legal_buy,
                "is_legal_sell": this.temp.is_legal_sell,
                "is_back_self": this.temp.is_back_self, //自交易返自己 0否 1是，
                "is_show_all_account": this.temp.is_show_all_account, //非直推用户脱敏 0否 1是，
                "is_edit_all_account": this.temp.is_edit_all_account, //提升非直推 0否 1是，
                "can_view_subordinate": this.temp.can_view_subordinate, //显示所有用户 0否 1是，
                "is_header": this.temp.is_header, //是否可见首页数据 0否 1是，
                // "is_wallet_view": this.temp.is_wallet_view, //可查看资产查询，
              }
              bcprouserup(data).then(() => {
                this.dialogFormVisible = false;
                this.checkvkeyDialog = false;
                this.$notify({
                  title: this.$t('dialog.Successful'),
                  message: this.$t('dialog.Operation_is_successful'),
                  type: "success",
                  duration: 2000,
                });
                this.getList();
              });
            }
        //   })
        // } else {
        //   return false;
        // }
      // });
    },
    //点击查看跳转详情页
    handleCK(row){
      // var user_id = JSON.parse(row.user_id)
      this.$router.push({
        path: "/user/detail",
        query: { id:JSON.parse(row.user_id)},
      });
    },
    // 回车搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    dataRegisterTimeChange(val) {
      if(val){
        this.listQuery.regstar = val[0]
        this.listQuery.regend= val[1]+' 23:59:59';
      }else{
        this.listQuery.regstar = ''
        this.listQuery.regend= ''
      }
    },
    dataLoginTimeChange(val){
      if(val){
        this.listQuery.loginstar = val[0]
        this.listQuery.loginend= val[1]+' 23:59:59';
      }else{
        this.listQuery.loginstar = ''
        this.listQuery.loginend= ''
      }
    },
    closexzhlx(){
      this.$refs["xzhlxForm"].resetFields();
    },
    closexgmm(){
      this.$refs["ruleForm"].resetFields();
    },
    // 弹框关闭 初始化数据
    dialogClose(){
      this.temp = {        
        user_type: 2,
        is_view_profit: 1,
        enable_simulator: 1,
        is_view_monitor: 1,
        is_open_agent: 0,
        show_agent_ratio: 0,
        show_position_monitor: 0,
        is_child_api: 0,
        login_verify_enable:1,
        is_show_dealer:0,
        is_legal_buy: 0,
        is_legal_sell: 0,
        is_back_self:0,
        is_show_all_account: 0, 
        is_edit_all_account: 0,
        can_view_subordinate: 0,
        is_header: 1,
        is_wallet_view:0,
        petname: "",
        invite_code: "", // 邀请码
        enable_login: 1, //1: 允许登录 0: 禁止登录
        enable_withdraw:1, //1: 允许提币 0: 禁止提币
        enable_trade: 1, //1: 允许交易 0: 禁止交易
        withdraw_verify: 0, // 开启二次提币is_view_profit: 1,
        is_open_api: 0, // API管理权限默认 0,
        invite_parent: "", // 邀请码
        content:'',
        agent_rebate_ratio:'',
        rake_back_time: 1
      }
    }
  },
};
</script>
<style lang="scss" scoped>
.block {
  display: flex;
}.select_wrap{
    // padding: 0 30px 0 30px;
    display: flex;
    align-items: center;
    span{
      width:100px;
      // padding-right: 20px;
    }
  }
</style>