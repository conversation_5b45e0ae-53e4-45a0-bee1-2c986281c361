import request from '@/utils/request'

// 订阅列表
export function getSubscriptionList(data) {
    return request({
        url: '/managers/v1/launchpad/subscriptions',
        method: 'post',
        data: { data }
    })
}

export function addSubscription(data) {
    return request({
        url: '/managers/v1/launchpad/addupsubscription',
        method: 'post',
        data: { 
            ...data,
            type: 1
        }
    })
}

export function updateSubscription(data) {
    return request({
        url: '/managers/v1/launchpad/addupsubscription',
        method: 'post',
        data: { 
            ...data,
            type: 2 
        }
    })
}

export function deleteSubscription(data) {
    return request({
        url: '/managers/v1/launchpad/delsubscription',
        method: 'post',
        data: { data }
    })
}

export function getSubscriptionRecordsList(data) {
    return request({
        url: '/managers/v1/launchpad/subscriptionrecords',
        method: 'post',
        data: { data }
    })
}

export function getPledgeList(data) {
    return request({
        url: '/managers/v1/launchpad/pledges',
        method: 'post',
        data: { data }
    })
}

export function addPledge(data) {
    return request({
        url: '/managers/v1/launchpad/adduppledge',
        method: 'post',
        data: { 
            ...data,
            type: 1
        }
    })
}

export function updatePledge(data) {
    return request({
        url: '/managers/v1/launchpad/adduppledge',
        method: 'post',
        data: { 
            ...data,
            type: 2 
        }
    })
}

export function deletePledge(data) {
    return request({
        url: '/managers/v1/launchpad/delpledge',
        method: 'post',
        data: { data }
    })
}

export function getPledgeRecordsList(data) {
    return request({
        url: '/managers/v1/launchpad/pledgerecords',
        method: 'post',
        data: { data }
    })
}

export function getRedemptionRecordsList(data) {
    return request({
        url: '/managers/v1/launchpad/redemptionrecords',
        method: 'post',
        data: { data }
    })
}

export function approveRedemption(data) {
    return request({
        url: '/managers/v1/launchpad/approveredemption',
        method: 'post',
        data: { data }
    })
}
