import request from '@/utils/request'

// 公告列表
export function getNoticeMsg(data) {
    return request({
        url: '/managers/v1/notice/bcpronoticelist',
        method: 'post',
        data: { data }
    })
}

//公告删除
export function deleteNotice(data) {
    return request({
        url: '/managers/v1/notice/bcpronoticedel',
        method: 'post',
        data: { data }
    })
}

/**公告置顶与取消 */
export function zdOrqxNotice(data) {
    return request({
        url: '/managers/v1/notice/bcpronoticeroof',
        method: 'post',
        data: { data }
    })
}

// 修改公告
export function getupNotice(data) {
    return request({
        url: '/managers/v1/notice/bcpronoticeupadd',
        method: 'post',
        data: { data }
    })
}

// 公告发布 
export function getAddupNotice(data) {
    return request({
        url: '/managers/v1/notice/bcpronoticeupadd',
        method: 'post',
        data: { data }
    })
}

// 重要通知 列表
export function getnoticeimportant(data) {
    return request({
        url: '/managers/v1/notice/getnoticeimportant',
        method: 'post',
        data: { data }
    })
}

// 重要通知 添加&修改
export function noticeimportantadd(data) {
    return request({
        url: '/managers/v1/notice/noticeimportantadd',
        method: 'post',
        data: { data }
    })
}

// 重要通知 上下架
export function noticeimportantvalid(data) {
    return request({
        url: '/managers/v1/notice/noticeimportantvalid',
        method: 'post',
        data: { data }
    })
}

// 重要通知 删除
export function noticeimportantdel(data) {
    return request({
        url: '/managers/v1/notice/noticeimportantdel',
        method: 'post',
        data: { data }
    })
}

// feature/daniel-noticetab-0512
// 获取所有通知标签（左侧面板）
export function fetchNoticeTabs(data) {
    return request({
        url: '/managers/v1/notice/bcpronoticetablist',
        method: 'post',
        data: { data }
    })
}

// 添加新标签 (type = 1)
export function addNoticeTab(data) {
    return request({
        url: '/managers/v1/notice/bcpronoticetabupadd',
        method: 'post',
        data: { data }
    })
}

// 更新标签 (type = 2)
export function updateNoticeTab(data) {
    return request({
        url: '/managers/v1/notice/bcpronoticetabupadd',
        method: 'post',
        data: { data }
    })
}

// 删除标签
export function deleteNoticeTab(data) {
    return request({
        url: '/managers/v1/notice/bcpronoticetabdelete',
        method: 'post',
        data: { data }
    })
}

// 更新选项卡排序 (拖动后)
export function reorderNoticeTabs(data) {
    return request({
        url: '/managers/v1/notice/bcpronoticetabsort',
        method: 'post',
        data: { data }
    })
}
