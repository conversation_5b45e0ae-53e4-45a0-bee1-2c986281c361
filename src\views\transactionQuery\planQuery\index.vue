<template>
  <div class="plan-cantainer">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        :placeholder="$t('filters.topIDNick')"
        style="width: 130px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        :placeholder="$t('filters.agent')"
        style="width: 150px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <!--  -->
      <!-- <div class="block"> -->
      <el-select
        size="mini"
        v-model="listQuery.contract_code"
        :placeholder="$t('tableHeader.contract')"
        clearable
        style="width: 120px; margin-right: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in contractOptions"
          :key="item.traderpairs"
          :label="item.traderpairs"
          :value="item.traderpairs"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.account_type"
        :placeholder="$t('filters.positionType')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in accountTypeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.side"
        :placeholder="$t('filters.direction')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in sizeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.status"
        :placeholder="$t('tableHeader.state')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in statusOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-input
        v-model="listQuery.ip_address"
        size="mini"
        :placeholder="$t('tableHeader.IP')"
        clearable
        style="width: 130px; margin-right: 20px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />

      <el-button
        class="filter-item"
        size="mini"
        style="margin-left: 20px; margin-top: 5px"
        type="primary"
        @click="handleFilter"
      >
        <!-- @click="handleFilter" -->
        {{$t('buttons.search')}}
      </el-button>
      <!-- <el-button
        class="filter-item"
        :loading="downloadLoading"
        @click="handleDownload"
        size="mini"
        type="success"
      >
        {{$t('buttons.export')}}
      </el-button> -->
    </div>

    <el-table
      v-loading="listLoading"
      :data="holdList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="userid" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center" min-width="95"> </el-table-column>
      <el-table-column :label="$t('filters.topID')" prop="top_agent_id" align="center" min-width="78"> 
        <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topNick')" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorID')" prop="pareid" align="center" min-width="78">
         <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.contract')" prop="contractcode" min-width="90px" align="center"></el-table-column>
      <el-table-column :label="$t('filters.positionType')" min-width="80px" align="center">
        <template slot-scope="{ row }">
          <span>
            {{ row.account_type == 1 ? $t('tableHeader.all_warehouse') : 
              row.account_type == 2 ? $t('tableHeader.by_warehouse') :
              row.account_type == 5 ? $t('filters.All_warehouse_Points_storehouse') :
              row.account_type == 6 ? $t('filters.By_warehouse_Points_storehouse') : '--'
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.direction')" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{row.side=='S'?$t('tableHeader.sell_empty'):$t('tableHeader.open_to_buy_more')}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.leverage')" align="center" min-width="60px">
        <template slot-scope="{ row }">
          <span>&times;{{ row.lever }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.number')" prop="amount" align="center" min-width="110px">
          <template slot-scope="{ row }">
          <span>{{ row.amount }} {{$t('others.piece')}}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('tableHeader.triggerPrice')" prop="trigger_price" align="center" min-width="90px"> 
        <template slot-scope="{ row }">
          <span>{{ row.trigger_price || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.checkFullPrice')" prop="limit" align="center" min-width="90px"> 
        <template slot-scope="{ row }">
          <span>{{ row.limit || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.StopLossPrice')" prop="stop" align="center" min-width="90px"> 
        <template slot-scope="{ row }">
          <span>{{ row.stop || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.submitTime')" prop="create_time" align="center" min-width="75">
        <template slot-scope="{ row }">
          <span>{{row.create_time == "0"? '--' : row.create_time }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.triggerTime')" prop="order_time" align="center" min-width="75">
        <template slot-scope="{ row }">
          <span>{{row.order_time == "0"? '--' : row.order_time}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.cancelTime')" prop="cancel_time" align="center" min-width="75">
        <template slot-scope="{ row }">
          <span>{{row.cancel_time == "0"? '--' : row.cancel_time}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.triggerState')" prop="status" align="center" min-width="110px">
          <template slot-scope="{ row }">
          <span>{{ statusObj[row.status] }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('tableHeader.orderID')" prop="plan_order_id" align="center" min-width="180px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.positionID')" prop="position_id" align="center" min-width="100px">
        <template slot-scope="{ row }">
          <span>{{ row.position_id || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.entrustedClientIP')" prop="ip_address" align="center" min-width="130px"></el-table-column>
      <el-table-column :label="$t('tableHeader.equipmentIdentificationCode')" prop="imei" align="center" min-width="110px"></el-table-column>
    </el-table>

    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,50,100,200,300]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";
//引入的自定义指令
import waves from "@/directive/waves";
//引入封装接口
import { getplanorder } from "@/api/transactionQuery";
import { bcprocontractset } from "@/api/user";

export default {
  name: "planQuery",
  data() {
    return {
      listLoading: false,
      total: 0,
      holdList: null,
      contractOptions: [],
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        account_type: null, //账户模式 1：全仓 2：逐仓
        contract_code: "", //合约代码
        side: null, //方向 B买S卖
        status: undefined, //状态 1: 未触发 0：取消 2：已触发 3: 触发失败
        ip_address: '',
        pageNo: 1,
        pagesize: 10,
      },
      accountTypeOptions: [
        { key: 1, name: this.$t('tableHeader.all_warehouse') },
        { key: 2, name: this.$t('tableHeader.by_warehouse') },
        { key: 5, name: this.$t('filters.All_warehouse_Points_storehouse') },
        { key: 6, name: this.$t('filters.By_warehouse_Points_storehouse') },
      ],
      sizeOptions: [
        { key: "B", name: this.$t('tableHeader.open_to_buy_more') },
        { key: "S", name: this.$t('tableHeader.sell_empty') },
      ],
      statusOptions: [
        { key: 0, name: this.$t('filters.Has_been_cancelled') },
        { key: 1, name: this.$t('filters.Not_trigger') },
        { key: 2, name: this.$t('filters.Has_triggered') },
        { key: 3, name: this.$t('filters.Trigger_failure') },
        // { key: 4, name: '平仓撤销' },
        { key: 5, name: this.$t('filters.Unwind_to_cancel') },
      ],
      stypeOptions: [
        { key: 2, name: this.$t('tableHeader.check_single') },
        { key: 3, name: this.$t('tableHeader.stop_loss_orders') },
      ],
      
      statusObj: { 
        0: this.$t('filters.Has_been_cancelled'),
        1: this.$t('filters.Not_trigger'),
        2: this.$t('filters.Has_triggered'),
        3: this.$t('filters.Trigger_failure'),
      },
      downloadLoading: false,//导出加载中效果
    };
  },

  components: {},

  computed: {},

  mounted() {
    bcprocontractset({}).then((res) => {
      this.contractOptions = res.data.filter(v=>v.isshow == 1)
    })
    this.getList();
  },

  methods: {
    //  请求表格数据
    getList() {
      this.listLoading = true;
      let data = {};
      Object.assign(data, this.listQuery, {
        status: this.listQuery.status === 0 ? 0 : this.listQuery.status || -1,
        side: this.listQuery.side || undefined,
        account_type: this.listQuery.account_type || undefined,
      });
      getplanorder(data).then((response) => {
        this.holdList = response.data.list;
        this.total = response.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    //表格导出功能
    handleDownload() {
      this.downloadLoading = true;
      import("@/vendor/Export2Excel").then((excel) => {
        const tHeader = [
          "UID",
          "用户名",
          "顶级代理ID",
          "上级代理ID",
          "上机代理用户名",
          "合约",
          "仓位类型",
          "杠杆",
          "张数",
          "冻结保证金",
          "开仓均价",
          "手续费",
          "成交时间",
          "资金费用",
          "强平价格",
          "止盈价",
          "止损价",
          "浮动PNL",
          "IP地址",
        ];
        const filterVal = [
          "uid",
          "username",
          "top_id",
          "Superior_id",
          "top_username",
          "contract_type",
          "position_type",
          "lever",
          "sheets_number",
          "freeze_margin",
          "open_price",
          "handling_fee",
          "transaction_time",
          "funding_costs",
          "liquidation_price",
          "surplus_price",
          "damage_price",
          "float_profit_loss",
          "ip",
        ];
        const data = this.formatJson(filterVal, this.holdList);
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: "table-list",
        });
        this.downloadLoading = false;
      });
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) =>
        filterVal.map((j) => {
          if (j === "transaction_time") {
            return parseTime(v[j]);
          } else {
            return v[j];
          }
        })
      );
    },
  },
};
</script>
<style lang="scss" scoped>
</style>