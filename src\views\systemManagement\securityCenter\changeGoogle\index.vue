<template>
    <div class="changepassword-container">
        <el-timeline>
            <el-timeline-item timestamp="1" placement="top">
                <el-card>
                    <h4>{{$t('others.changeGoogle_h4_one')}}</h4>
                    <p style="color:orange">{{$t('others.changeGoogle_p_one')}}</p>
                    <!-- <img src="../../../../../public/QRcode.png" style="width:93px;height:93px;"> -->
                    <p>{{$t('others.changeGoogle_p_two')}}</p>
                </el-card>
                </el-timeline-item>
                <el-timeline-item timestamp="2" placement="top">
                <el-card>
                    <h4>{{$t('others.changeGoogle_h4_two')}}</h4>
                    <p>{{$t('others.changeGoogle_p_three')}}</p>
                    <el-input v-model="google" style="width:200px;"></el-input>
                </el-card>
                </el-timeline-item>
        </el-timeline>
         <el-button type="success" class="confirm-butoon" @click="confirmChange()">{{$t('buttons.determine_modify')}}</el-button>
    </div>
</template>

<script>
export default {
name:'changeGoogle',
 data () {
 return {
        google:''
    };
 },

 components: {},

 computed: {},

 mounted(){},

 methods: {
     confirmChange(){
         console.log('修改谷歌验证码')
     }
 }
}

</script>
<style lang="scss" scoped>
.confirm-butoon{
    margin-left: 30%;
}
.el-timeline{
    width: 100%;
}
.el-timeline-item {
    margin-right: 20px;
}

</style>