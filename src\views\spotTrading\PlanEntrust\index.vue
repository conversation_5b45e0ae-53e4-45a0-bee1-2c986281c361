<template>
  <div class="PlanEntrust">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.uid"
        :placeholder="$t('tableHeader.uid')"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.trading"
        :placeholder="$t('filters.Trading')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in tradingOptions"
          :key="item.key"
          :label="item.traderpairs"
          :value="item.traderpairs"
        />
      </el-select>
      <!-- <el-select
        size="mini"
        v-model="listQuery.orderType"
        placeholder="订单类型"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in orderTypeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select> -->
      <el-select
        size="mini"
        v-model="listQuery.side"
        :placeholder="$t('filters.direction')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in sizeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.state"
        :placeholder="$t('filters.Conditional_order_status')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in stateOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <span style="margin-right: 20px; font-size: 12px">{{ $t('tableHeader.creationTime') }}</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px; margin-right: 10px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change="filterTimeTransform"
      >
      </el-date-picker>
      <span style="margin-right: 20px; font-size: 12px">{{ $t('tableHeader.triggerTime') }}</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px; margin-right: 10px"
        v-model="filterTime2"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change="filterTimeTransform2"
      >
      </el-date-picker>

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-top: 10px"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
    </div>

    <el-tabs v-model="activeName" @tab-click="handleClick" style="margin: 15px 25px">
      <el-tab-pane v-for="(item, index) in coinnameOptions" :key="index" :label="item.coin_name + $t('tableHeader.Trading_area')" :name="item.coin_name"></el-tab-pane>
    </el-tabs>

    <el-table
      v-loading="listLoading"
      :data="levelList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="userid" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.userid || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.user_name || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topID')" prop="top_agent_id" align="center" min-width="78"> 
        <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topNick')" prop="petname" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorID')" prop="pareid" align="center" min-width="78">
         <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.Trading')" prop="contractcode" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.contractcode || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.Entrust_ID')" prop="plan_order_id" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.plan_order_id || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.direction')" prop="side" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.side == "B" ? $t('tableHeader.buy') : row.side == "S" ? $t('tableHeader.sell') : '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.entrustNum')" prop="amount" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.amount || '--' }}&nbsp;{{ row.coin_name }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.Commission_amount')" prop="money" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.money || '--' }}&nbsp;{{ row.market_name }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column :label="$t('tableHeader.Executive_price')" prop="entrust_price" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.condition == 1 ? "≥"+row.entrust_price : "≤"+row.entrust_price || '--' }}</span>
        </template>
      </el-table-column> -->
      <el-table-column :label="$t('tableHeader.state')" prop="status" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>
            {{
              row.status == 0 ? $t('buttons.cancel') :
              row.status == 1 ? $t('filters.Not_trigger') :
              row.status == 2 ? $t('filters.Has_triggered') :
              row.status == 3 ? $t('filters.Trigger_failure') : '--'
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.equipmentID')" prop="order_client" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>
            {{
              row.order_client == 0 ? "Open_API" :
              row.order_client == 1 ? "Android" :
              row.order_client == 2 ? "IOS" :
              row.order_client == 3 ? "WEB" :
              row.order_client == 4 ? "H5" :
              row.order_client == 6 ? $t('others.system_automatically') : "--"
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.Trigger_price')" prop="trigger_price" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.trigger_price || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.creationTime')" prop="create_time" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.create_time == "0"? '--' : row.create_time }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.triggerTime')" prop="order_time" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.order_time == "0"? '--' : row.order_time }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.cancelTime')" prop="cancel_time" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.cancel_time == "0"? '--' : row.cancel_time }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.orderSerialNumber')" prop="plan_order_id" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.plan_order_id || '--' }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,20,30,50]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { planorder, futuresconcode, coinarea } from "@/api/spotTrading"
export default {
  name: "PlanEntrust",
  data() {
    return {
      listLoading: false,
      total: 0,
      levelList: null,
      filterTime: [],
      filterTime2: [],
      listQuery: {
        uid: "",        // UID
        trading: "",    // 交易对
        // orderType: "",  // 订单类型
        side: "",       // 方向
        state: "",      // 条件单状态
        pageNo: 1,
        pagesize: 10,
        star: "",       // 创建时间 开始
        end: "",        // 创建时间 结束
        tstar: "",      // 触发时间 开始
        tend: "",       // 触发时间 结束
      },
      tradingOptions: [],   // 交易对
      orderTypeOptions: [
        { key: 0, name: '市价' },
        { key: 1, name: '委托' },
      ], // 订单类型
      sizeOptions: [
        { key: 'B', name: this.$t('tableHeader.buy') },
        { key: 'S', name: this.$t('tableHeader.sell') },
      ],  // 方向
      stateOptions: [
        { key: 0, name: this.$t('buttons.cancel') },
        { key: 1, name: this.$t('filters.Not_trigger') },
        { key: 2, name: this.$t('filters.Has_triggered') },
        { key: 3, name: this.$t('filters.Trigger_failure') },
      ],  // 条件单状态
      activeName: '',
      coinnameOptions: [],   // 币种
    };
  },

  components: {},

  computed: {
    // 默认时间
    // timeDefault () {
    //   let date = new Date()
    //   // 通过时间戳计算
    //   let defalutStartTime = ((date.getTime() - 1 * 24 * 3600 * 1000)/1000).toDate('yyyy-MM-dd') // 转化为时间戳
    //   let defalutEndTime = ((date.getTime()/1000)).toDate('yyyy-MM-dd')
    //   return [defalutStartTime, defalutEndTime]
    // }
  },

  mounted() {
    // 交易区
    // coinarea({}).then((res) => {
    //   this.coinnameOptions = res.data
    //   if(this.coinnameOptions.length>0){
    //     this.activeName = this.coinnameOptions[0].coin_name
    //   }
    //   this.getList();
    // })
    // 交易对
    futuresconcode({}).then((res) => {
      this.tradingOptions = res.data
    })

    this.getList();
  },

  methods: {
    // tab切换
    handleClick() {
      this.getList();
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    // 获取数据
    getList() {
      this.listLoading = true;
      let data = {};
      Object.assign(data, {
        coinname: this.activeName,                // 交易区
        uid: this.listQuery.uid,                  // uid
        contract_code: this.listQuery.trading,    // 交易对
        side: this.listQuery.side,                // 方向 B买 S卖
        // order_type: this.listQuery.orderType || Number(-1),     // 订单类型 -1全部 0市价 1委托
        order_status: this.listQuery.state + "" ? Number(this.listQuery.state) : -1,  // 条件单状态 1: 未触发 0：取消 2：已触发 3: 触发失败
        star: this.listQuery.star,                // string 开始时间
        end: this.listQuery.end,                  // string 结束时间
        tstar: this.listQuery.tstar,              // string 开始时间
        tend: this.listQuery.tend,                // string 结束时间
        pageNo: this.listQuery.pageNo,            // int 页数
        pagesize: this.listQuery.pagesize,        // int 分页数量
      })
      planorder(data).then((res) => {
        // console.log(res)
        // res.data.list.map(v => {
        //   v['entrust_price_s'] = v.condition==1?'>='+v.entrust_price:'<='+v.entrust_price;
        // })
        this.levelList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      })
    },

    filterTimeTransform(val) {
      this.listQuery.star = (val && val[0]) || "";
      this.listQuery.end = val ? val[1] + " 23:59:59" : "";
    },
    filterTimeTransform2(val) {
      this.listQuery.tstar = (val && val[0]) || "";
      this.listQuery.tend = val ? val[1] + " 23:59:59" : "";
    },
  },
};
</script>

<style lang="scss" scoped>
</style>