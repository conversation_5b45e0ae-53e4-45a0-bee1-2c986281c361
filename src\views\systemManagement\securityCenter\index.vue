<template>
    <div class="security-container">
        <div class="password">
            <div class="password-one">
                {{$t('login.ph_pass')}}
            </div>
            <div class="password-two" @click="changePasswordClick()">
                {{$t('buttons.modify')}}
            </div>
        </div>
        <div class="google">
             <div class="google-one">
                {{$t('others.Google_validator')}}
            </div>
            <div class="google-two" @click="changeGoogleClick()">
                {{$t('buttons.modify')}}
            </div>
        </div>
    </div>
</template>

<script>
export default {
name:'securityCenter',
 data () {
 return {

    };
 },

 components: {},

 computed: {},

 mounted(){},

 methods: {
     changePasswordClick(){
         this.$router.push({
             path:'/systemManagement/changePassword'
         })
     },
       changeGoogleClick(){
         this.$router.push({
             path:'/systemManagement/changeGoogle'
         })
     }
 }
}

</script>
<style lang="scss" scoped>
.password{
    width: 96%;
    height: 90px;
    border: 1px solid grey;
    margin: 0 auto;
    display: flex;
    align-items: center;
    .password-one{
        width: 80%;
        margin-left: 30px;
    }
    .password-two{
        color: blue;
        cursor: pointer;
    }
}

.google{
    width: 96%;
    height: 90px;
    border: 1px solid grey;
    margin: 20px auto;
     display: flex;
    align-items: center;
    .google-one{
        width: 80%;
        margin-left: 30px;
    }
    .google-two{
        color: blue;
        cursor: pointer;
    }
}

</style>