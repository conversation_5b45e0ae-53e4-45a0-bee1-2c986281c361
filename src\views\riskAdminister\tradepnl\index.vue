<template>
  <div class="asset-container">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        :placeholder="$t('filters.topIDtopNick')"
        style="width: 180px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        clearables
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        :placeholder="$t('filters.agent')"
        style="width: 180px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-left: 20px; font-size: 12px">{{$t('tableHeader.net_PNl')}}≥</span>
      <el-input
        v-model="listQuery.netpnl"
        size="mini"
        style="width: 180px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        oninput="value= value.match(/\d+(\.\d{0,2})?/) ? value.match(/\d+(\.\d{0,8})?/)[0] : ''"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-left: 20px; font-size: 12px">{{$t('tableHeader.PNL')}}≥</span>
      <el-input
        v-model="listQuery.pnl"
        size="mini"
        style="width: 180px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        oninput="value= value.match(/\d+(\.\d{0,2})?/) ? value.match(/\d+(\.\d{0,8})?/)[0] : ''"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <!-- <span style="margin-left: 20px; font-size: 12px">{{$t('tableHeader.time')}}</span>
      <el-date-picker
        style="width: 220px; margin-top: 5px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change="filterTimeTransform"
      /> -->
      <el-button
        style="margin-top: 10px; margin-left: 10px"
        class="filter-item"
        size="mini"
        type="primary"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
      <el-button
        class="filter-item"
        :loading="exportLoading"
        v-if="$store.getters.roles.indexOf('userpnlexport') > -1"
        @click="handleExport"
        size="mini"
        type="success"
      >
        {{$t('buttons.export')}}
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      style="width: 100%; margin-top: 30px"
      size="mini"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="userid" align="center" min-width="78">
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.userName')"
        prop="user_name"
        align="center"
        min-width="95"
      >
      </el-table-column>
      <el-table-column
        :label="$t('filters.topID')"
        prop="top_agent_id"
        align="center"
        min-width="78"
      >
        <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topNick')" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.superiorID')"
        prop="pareid"
        align="center"
        min-width="78"
      >
        <template slot-scope="{ row }">
          <span>{{ row.pareid || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('filters.topAgentNick')"
        prop="parenick"
        align="center"
        min-width="125"
      >
      <template slot-scope="{ row }">
          <span>{{ row.parenick || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.Nick_name')"
        prop="sefpetname"
        align="center"
        min-width="125"
      >
      <template slot-scope="{ row }">
          <span>{{ row.sefpetname || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.net_PNl')"
        prop="netpnl"
        align="center"
        min-width="125"
      >
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.PNL')"
        prop="pnl"
        align="center"
        min-width="125"
      >
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.operation')"
        align="center"
        min-width="125"
      >
        <template slot-scope="{ row }">
          <el-button type="primary" size="mini" @click="handleToView(row)">{{$t('buttons.toView')}}</el-button>
        </template>
      </el-table-column>
      <!-- <el-table-column
        label="最近24H净PNL"
        prop="within24_pnl"
        align="center"
        min-width="125"
      >
      </el-table-column> -->
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[200, 100, 50, 20]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />

    <el-dialog
      :visible.sync="dialogVisible"
      width="650px"
      :before-close="handleClose">

        <line-chart2 :chart-data="ChartData"></line-chart2>

        <div class="footer">
          <el-select @change="timeChange" v-model="selectTime" :placeholder="$t('dialog.pleaseSelect')">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <strong>UID:{{uid}}</strong>
        </div>
    </el-dialog>
  </div>
</template>

<script>
// 封装api
import { 
  getuserpnl, 
  userpnlexport,
  tradepnl,       //  用户收益数据
  usertradepnl,   //  收益曲线图
} from "@/api/fundQuery";
import LineChart2 from './components/LineChart2'


export default {
  name: "highpnl",
  data() {
    return {
      listLoading: false,
      total: 0,
      filterTime: [],
      tableData: null,
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        netpnl: "", // 净PNL≥
        pnl: "",    // PNL≥
        orderside: 3, //排序
        stype: 2, // 1正序 2反序
        pageNo: 1,
        pagesize: 20,
      },
      exportLoading: false, //导出加载中效果
      dialogVisible: false, //折线图

      // 天数
      options: [{
        value: '7',
        label: this.$t('filters.In_recent_7_days')
      }, {
        value: '14',
        label: this.$t('filters.In_recent_14_days')
      }, {
        value: '30',
        label: this.$t('filters.In_recent_30_days')
      },],
      selectTime: '7',
      ChartData: {
        dataList: [],
        dataList2: [],
        dataTime: [],
      },
      uid: '',
    };
  },
  components: {
    LineChart2
  },

  computed: {
    // 默认时间
    timeDefault() {
      let date = new Date();
      // 通过时间戳计算
      let defalutStartTime = (
        (date.getTime() - 1 * 24 * 3600 * 1000) /
        1000
      ).toDate("yyyy-MM-dd"); // 转化为时间戳
      let defalutEndTime = (date.getTime() / 1000).toDate("yyyy-MM-dd");
      return [defalutStartTime, defalutStartTime];
    },
  },

  mounted() {
    this.filterTime = this.timeDefault;
    this.getList();
  },
  methods: {
    // 折线图
    handleToView(v) {
      this.dialogVisible = true;
      this.uid = v.userid;
      this.getData(Number(7))
    },
    // 折线图icon关闭
    handleClose() {
      this.dialogVisible = false;
      this.uid = '',
      this.selectTime = '7'
    },
    // 切换时间
    timeChange(val){
      this.getData(Number(val))
    },

    getData(num) {
      this.ChartData = {
        dataList: [],
        dataList2: [],
        dataTime: [],
      };
      // 折线图数据接口
      usertradepnl({
        user_id: this.uid,
        slimit: Number(num)
      }).then((res) => {
        let data = res.data
        data.forEach(ele => {
          this.ChartData['dataList'].unshift(ele.netpnl)
          this.ChartData['dataList2'].unshift(ele.pnl)
          this.ChartData['dataTime'].unshift(ele.ownday)
        })
      })
    },

  
    // 搜索事件
    handleFilter() {
      this.listQuery.page = 1;
      this.getList();
    },
    //  渲染table列表
    getList() {
      this.listLoading = true;
      let data = {};
      Object.assign(data, this.listQuery, { web_id: 0 });
      data.netpnl = Number(this.listQuery.netpnl),
      data.pnl = Number(this.listQuery.pnl),
      // data.star = (this.filterTime && this.filterTime[0]) || "";
      // data.end = this.filterTime ? this.filterTime[1] : "";
      tradepnl(data).then((res) => {
        // console.log(res)
        this.tableData = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    //表格导出功能
    handleExport() {
      this.exportLoading = true;
      let data = {};
      Object.assign(data, this.listQuery, { web_id: 0 });
      data.star = (this.filterTime && this.filterTime[0]) || "";
      data.end = this.filterTime ? this.filterTime[1] : "";
      userpnlexport(data)
        .then((res) => {
          if (res.ret == 0) {
            this.$notify.success({
              title: this.$t('dialog.Operation_is_successful'),
              message: this.$t('dialog.Please_jiaoyi_daochu_download'),
            });
            this.exportLoading = false;
          }
        })
        .catch((err) => {
          this.exportLoading = false;
        });
    },
    filterTimeTransform(val) {
      this.listQuery.star = (val && val[0]) || "";
      this.listQuery.end = val ? val[1] : "";
    },
  },
};
</script>
<style lang="scss" scoped>
.asset-container {
  .filter-container {
    .highSwitch_wrap {
      margin-top: 15px;
      width: 100px;
      cursor: pointer;
      font-size: 14px;
    }
  }
  .select_wrap {
    // padding: 0 30px 0 30px;
    display: flex;
    align-items: center;
    span {
      width: 100px;
      // padding-right: 20px;
    }
  }
}
.footer {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  strong {
    font-size: 16px;
  }
}
</style>