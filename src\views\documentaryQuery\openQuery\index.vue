<template>
  <div class="open-cantainer">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        clearable
        :placeholder="$t('filters.name')"
        style="width: 150px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        clearable
        size="mini"
        :placeholder="$t('filters.topIDNick')"
        style="width: 130px; margin-left: 20px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        clearable
        size="mini"
        :placeholder="$t('filters.agent')"
        style="width: 150px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.contract_code"
        :placeholder="$t('tableHeader.contract')"
        clearable
        style="width: 120px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in contractOptions"
          :key="item.traderpairs"
          :label="item.traderpairs"
          :value="item.traderpairs"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.account_type"
       ::placeholder="$t('filters.positionType')"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in accountTypeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.side"
        :placeholder="$t('filters.direction')"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in sizeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <!-- <el-select
        size="mini"
        v-model="listQuery.order_type"
        :placeholder="$t('filters.transactionType')"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in orderTypeArr"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select> -->
      <span style="margin-left: 20px; font-size: 12px">{{$t('tableHeader.clinchDealTime')}}</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change='filterTimeTransform'
      >
      </el-date-picker>
      <el-input
        v-model="listQuery.ip"
        size="mini"
        placeholder="IP"
        style="width: 150px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        size="mini"
        v-model="listQuery.tradeid"
        :placeholder="$t('tableHeader.transactionNumber')"
        style="width: 150px; margin-left: 20px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-left: 20px;"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
      <el-button
        class="filter-item"
        :loading="exportLoading"
        v-if="$store.getters.roles.indexOf('followopenexport')>-1"
        @click="handleExport"
        size="mini"
        type="success"
      >
        {{$t('buttons.export')}}
      </el-button>
    </div>


    <el-table
      v-loading="listLoading"
      :data="openList"
      border
      fit
      highlight-current-row
      style="width: 100%; margin-top: 20px"
      size="mini"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="user_id" align="center" min-width="78">
         <template slot-scope="{ row }">
          <span>{{ row.user_id || '--' }}</span>
        </template> 
        </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center" min-width="95"> </el-table-column>
      <el-table-column :label="$t('filters.topID')" prop="top_agent_id" align="center" min-width="78">
          <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('filters.topNick')" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorID')" prop="pareid" align="center" min-width="78">
           <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.transactionNumber')" prop="trader_uid" align="center" min-width="78">
         <template slot-scope="{ row }">
          <span>{{ row.deal_id || '--' }}</span>
        </template>   </el-table-column>
      <el-table-column :label="$t('tableHeader.contract')" prop="contract_code" min-width="90px" align="center"></el-table-column>
      <el-table-column :label="$t('filters.positionType')" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <!-- <span>{{row.accounttype==3?'跟单':'带单'}}</span> -->
          <span>{{row.trader_uid>0?$t('tableHeader.documentary'):$t('tableHeader.withSin')}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.direction')" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{row.side=='S'?$t('tableHeader.sell_empty'):$t('tableHeader.open_to_buy_more')}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.leverage')" align="center" min-width="60px">
        <template slot-scope="{ row }">
          <span>&times;{{ row.lever }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.number')" prop="volume" align="center" min-width="110px"> 
         <template slot-scope="{ row }">
          <span>{{ row.volume }}{{$t('others.piece')}}</span><span>/{{row.conversion}}{{row.contract_code.slice(0,-4)}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.openPositionsPrice')" prop="price" align="center" min-width="90px"> </el-table-column>
       <!--<el-table-column :label="$t('tableHeader.unwindPrice')" prop="tradevalue" align="center" min-width="90px"> </el-table-column>-->
      <el-table-column :label="$t('tableHeader.poundage')" prop="commission" align="center" min-width="90px"> </el-table-column>
     <!-- <el-table-column :label="$t('tableHeader.net_PNl')" align="center" min-width="90px">
        <template slot-scope="{ row }">
          <span>{{ Number(row.closeprofit).add(row.commission) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.PNL')" prop="closeprofit" align="center" min-width="90px"> </el-table-column> -->
      <el-table-column :label="$t('filters.transactionType')" align="center" min-width="90px">
        <template slot-scope="{ row }">
          <span>{{ orderTypeObj[row.order_type]  }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.clinchDealTime')" prop="trade_time" align="center" width="75"> </el-table-column>
      <el-table-column :label="$t('tableHeader.orderSource')" prop="imei" align="center" min-width="130px">
        <template slot-scope="{ row }">
          <!-- <span>{{orderTypeObj[row.order_type]+'--'+os_typeObj[row.order_client]}}</span> -->
          <span>{{row.fromType}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.IP_addr')" prop="ip_address" align="center" min-width="100px">>
        <template slot-scope="{ row }">
          <span>{{ row.ip_address || '--'  }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,50,100,200,300]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
//封装的api
import { docopentradelist, followopenexport } from "@/api/documentaryQuery"; //opentradeexport
import { bcprocontractset } from "@/api/user";
// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";
export default {
  name: "followopenquery",
  data() {
    return {
      listLoading: false,
      total: 0,
      openList: null,
      filterTime: [],
      listQuery: {
        tradeid: "", //
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        account_type: undefined, //账户模式 1：全仓 2：逐仓
        contract_code: "", //合约代码
        trade_id: "", //交易id
        side: undefined, //方向 B买S卖
        order_type: "",  // 交易类型
        pageNo: 1,
        pagesize: 10,
        star: '', //开始
        end: '', //结束
        ip:""
      },
      contractOptions: [],
      accountTypeOptions: [
        { key: 3, name: this.$t('tableHeader.documentary') },
        { key: 4, name: this.$t('tableHeader.withSin') },
      ],
      sizeOptions: [
        { key: "B", name: this.$t('tableHeader.open_to_buy_more') },
        { key: "S", name: this.$t('tableHeader.sell_empty') },
      ],
      orderTypeArr:[
        {key: 0, name: this.$t('filters.Market_orders')},
        {key: 1, name: this.$t('tableHeader.order')}, 
        {key: 2, name: this.$t('tableHeader.check_single')}, 
        {key: 4, name: this.$t('tableHeader.stop_loss_orders')}, 
        {key: 5, name: this.$t('filters.Strong_flat_sheet')},
      ],
      orderTypeObj: {
        0: this.$t('filters.Market_orders'),
        1: this.$t('tableHeader.order'), 
        2: this.$t('tableHeader.check_single'), 
        4: this.$t('tableHeader.stop_loss_orders'), 
        5: this.$t('filters.Strong_flat_sheet')
      },
      os_typeObj:{
        0: 'open_api',
        1: "android",
        2: "iOS",
        3: "WEB",
        4: "H5",
        5: "open_api",
        6: this.$t('others.system_automatically'),
      },
      exportLoading: false, //导出加载中效果
    };
  },

  components: {},

  computed: {
    // 默认时间
    timeDefault () {
      let date = new Date()
      // 通过时间戳计算
      let defalutStartTime = ((date.getTime() - 1 * 24 * 3600 * 1000)/1000).toDate('yyyy-MM-dd') // 转化为时间戳
      let defalutEndTime = ((date.getTime()/1000)).toDate('yyyy-MM-dd')
      return [defalutStartTime, defalutEndTime]
    }
  },

  mounted() {
    bcprocontractset({}).then((res) => {
      this.contractOptions = res.data.filter(v=>v.isshow == 1)
    })
    this.filterTime = this.timeDefault;
    this.getList();
  },

  methods: {
    getList() {
      this.listLoading = true;
      let data = {}
      Object.assign(data,this.listQuery)
      data.side = this.listQuery.side || undefined
      data.account_type = this.listQuery.account_type || undefined
      data.star = (this.filterTime && this.filterTime[0]) || "";
      data.end = this.filterTime ? this.filterTime[1] + " 23:59:59" : "";
      data.order_type = data.order_type === ''?-1:data.order_type
      docopentradelist(data).then((res) => {
        if(res.data.list && res.data.list.length){
          this.openList = res.data.list.map((v)=>{
            // <span>{{orderTypeObj[row.order_type]+'--'+os_typeObj[row.order_client]}}</span>
            // order_client ==> 1: android 2: iOS 3: WEB 4: H5 0: open_api 6: 系统自动
            // order_type ==> 0: 市价单 1：计划单 2：止盈单 4：止损单 5：强平单  6: "条件平仓" 7: "带单平仓" 8: "带单止盈" 9: "带单止损",
                
            // api平仓：用户通过api的平仓
            // 用户平仓：用户自主平仓
            // 止盈平仓：用户自己设置的止盈价触发后的平仓
            // 止损平仓：用户自己设置的止损价触发后的平仓
            // 系统平仓：如强制平仓等系统触发的平仓
            // 带单平仓：带单交易员自主平仓所产生的跟单平仓
            // 带单止盈：带单交易员设置的止盈价触发后的平仓
            // 带单止损：带单交易员设置的止损价触发后的平仓
            if(v.order_client == 0){
              if(v.order_type == 0){
                v.fromType = this.$t('filters.With_a_single_open_positions')
              }else if(v.order_type == 2){
                v.fromType = this.$t('filters.Check_surplus')
              }else if(v.order_type == 4){
                v.fromType = this.$t('filters.Stop_positions')
              }else if(v.order_type == 5 || v.order_type == 6 ){
                v.fromType = this.$t('filters.With_a_single_open_positions')
              }else if(v.order_type == 7){
                v.fromType = this.$t('filters.With_single_warehouse')
              }else if(v.order_type == 8){
                v.fromType = this.$t('filters.With_a_single_check')
              }else if(v.order_type == 9){
                v.fromType = this.$t('filters.With_a_single_stop')
              }
            }else{
              // 用户操作
              v.fromType = this.$t('filters.Users_to_unwind') +this.os_typeObj[v.order_client]
            }
            return v
          });
        }else{
          this.openList = []
        }
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    //表格导出功能
    handleExport() {
      this.exportLoading = true;
      let data = {}
      Object.assign(data,this.listQuery)
      let newDate = parseInt(new Date().getTime()/1000);
      data.end = this.listQuery.end ? this.listQuery.end : (newDate.toDate('yyyy-MM-dd')+ ' 23:59:59')
      newDate = parseInt(newDate-518400)
      data.star = this.listQuery.star ? (this.listQuery.star + ' 00:00:00' ): (newDate.toDate('yyyy-MM-dd')+ ' 00:00:00')
      data.side = this.listQuery.side || undefined
      data.account_type = this.listQuery.account_type || undefined
      data.order_type = data.order_type === ''?-1:data.order_type
      followopenexport(data).then((res) => {
        if(res.ret == 0){
          this.$notify.success({title:this.$t('dialog.Operation_is_successful'),message:this.$t('dialog.Please_jiaoyi_daochu_download')})
          this.exportLoading = false
        }
      }).catch(err=>{
        this.exportLoading = false
      })
    },
    filterTimeTransform(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val? val[1]+' 23:59:59':'';
    },
  },
};
</script>
<style lang="scss" scoped>
</style>