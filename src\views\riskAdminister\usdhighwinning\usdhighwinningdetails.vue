<template>
  <div class="userlistdetail-container">
    <el-row
      :gutter="6"
      style="border: 1px solid #f0f2f5; width: 99.5%; margin: 20px auto"
      class="top_info"
    >
      <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8" style="padding: 0">
        <div class="grid-content">
          <span class="grid-content-left">{{$t("tableHeader.uid")}}:</span>
          <span class="grid-content-right">{{ListDetail.user_id || "--"}}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t("filters.topID")}}:</span>
          <span class="grid-content-right">{{ListDetail.topagent || "--"}}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t("tableHeader.superiorID")}}:</span>
          <span class="grid-content-right">{{ListDetail.paregant || "--"}}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t("tableHeader.historyOdds")}}:</span>
          <span class="grid-content-right">{{ListDetail.win_rate || "--"}}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t("tableHeader.ershisi_h_tradingNum")}}:</span>
          <span class="grid-content-right">{{ListDetail.within24_tradenum || "--"}}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t("tableHeader.maxSingleProfit")}}:</span
          >
          <span class="grid-content-right">{{ListDetail.max_profit_per || "--"}}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t("tableHeader.currentPositionValue")}}:</span>
          <span class="grid-content-right">{{ListDetail.position_value || "--"}}</span>
        </div>
      </el-col>
      <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8" style="padding: 0">
        <div class="grid-content">
          <span class="grid-content-left">{{$t("tableHeader.userName")}}:</span>
          <span class="grid-content-right">{{ListDetail.user_name || "--"}}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t("tableHeader.profitnum")}}:</span>
          <span class="grid-content-right">{{ListDetail.win_close || "--"}}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t("tableHeader.net_PNl")}}:</span>
          <span class="grid-content-right">{{ListDetail.pure_pnl || "--"}}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t("tableHeader.PNL")}}:</span>
          <span class="grid-content-right">{{ListDetail.win_pnl || "--"}}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t("tableHeader.ershisi_h_net_pnl")}}:</span>
          <span class="grid-content-right">{{ListDetail.within24_pnl || "--"}}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t("tableHeader.maxSingleLoss")}}:</span>
          <span class="grid-content-right">{{ListDetail.max_loss_per || "--"}}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t("tableHeader.float_PNL")}}:</span>
          <span class="grid-content-right">{{ListDetail.float_profit || "--"}}</span>
        </div>
      </el-col>
      <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8"> </el-col>
    </el-row>

    <!-- <div style="border: 1px solid #f0f2f5">
      <div class="grid-content">
        <h3 class="grid-content-left">{{$t('others.assets')}}</h3>
      </div>
      <el-row :gutter="6" style="margin: 30px 20px; border: 1px solid #f0f2f5">
        <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8">
          <div class="grid-content">
            <span class="font">{{$t("others.walletAccountAssets")}}:</span>
          </div>
          <div class="grid-content" style="border-bottom: 1px solid #f0f2f5">
            <span>{{ ListDetail.wallettotal }}</span>
          </div>
          <el-row>
            <el-col :span="12">
              <div class="grid-content">
                <span class="font">{{$t('others.availableBalance')}}</span>
              </div>
              <div class="grid-content">
                <span>{{ ListDetail.walletbalance }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="grid-content">
                <span class="font">{{$t('others.freezeNum')}}</span>
              </div>
              <div class="grid-content">
                <span>{{ ListDetail.walletlock }}</span>
              </div>
            </el-col>
          </el-row>
        </el-col>
        <el-col
          :xs="8"
          :sm="8"
          :md="8"
          :lg="8"
          :xl="8"
          style="border-left: 1px solid #f0f2f5"
        >
          <div class="grid-content">
            <span class="font">{{$t('others.contractAccountAssets')}}:</span>
          </div>
          <div class="grid-content" style="border-bottom: 1px solid #f0f2f5">
            <span>{{ ListDetail.accbalance }}</span>
          </div>
          <el-row>
            <el-col :span="12">
              <div class="grid-content">
                <span class="font">{{$t('others.accountRights')}}</span>
              </div>
              <div class="grid-content">
                <span>{{ ListDetail.accequity }}</span>
              </div>
              <div class="grid-content">
                <span class="font">{{$t('others.frozenMargin')}}</span>
              </div>
              <div class="grid-content">
                <span>{{ ListDetail.accbond }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="grid-content">
                <span class="font">{{$t('others.availableBalance')}}</span>
              </div>
              <div class="grid-content">
                <span>{{ ListDetail.accavailable }}</span>
              </div>
              <div class="grid-content">
                <span class="font">{{$t('others.unrealizedProfitandLoss')}}</span>
              </div>
              <div class="grid-content">
                <span>{{ ListDetail.accprofit }}</span>
              </div>
            </el-col>
          </el-row>
        </el-col>
        <el-col
          :xs="8"
          :sm="8"
          :md="8"
          :lg="8"
          :xl="8"
          style="border-left: 1px solid #f0f2f5"
        >
          <div class="grid-content">
            <span class="font">{{$t('others.documentaryAccountAssets')}}:</span>
          </div>
          <div class="grid-content" style="border-bottom: 1px solid #f0f2f5">
            <span>{{ ListDetail.followbalance }}</span>
          </div>
          <el-row>
            <el-col :span="12">
              <div class="grid-content">
                <span class="font">{{$t('others.accountRights')}}</span>
              </div>
              <div class="grid-content">
                <span>{{ ListDetail.followequity }}</span>
              </div>
              <div class="grid-content">
                <span class="font">{{$t('others.frozenMargin')}}</span>
              </div>
              <div class="grid-content">
                <span>{{ ListDetail.followbond }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="grid-content">
                <span class="font">{{$t('others.availableBalance')}}</span>
              </div>
              <div class="grid-content">
                <span>{{ ListDetail.followavailable }}</span>
              </div>
              <div class="grid-content">
                <span class="font">{{$t('others.unrealizedProfitandLoss')}}</span>
              </div>
              <div class="grid-content">
                <span>{{ ListDetail.followprofit }}</span>
              </div>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </div> -->
  </div>
</template>

<script>
import { userinfo, userpayments, resetuserphone } from "@/api/userQuery";
export default {
  name: "highwinningdetails",
  data() {
    const userTypeOptions = {
      1: this.$t("filters.top_agent"),
      2: this.$t("filters.The_agent"),
      3: this.$t("filters.The_average_user"),
      4: this.$t("filters.The_proxy_directly_pushes_users"),
    };
    return {
      ListDetail: {
        user_id: "--",
      },
      payInfo: [],
      //用户类型
      userTypeOptions,
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.getDetails();
    userpayments({ user_id: JSON.parse(this.$route.query.id) }).then((data) => {
      this.payInfo = data.data;
    });
  },

  methods: {
    getDetails() {
      userinfo({ user_id: JSON.parse(this.$route.query.id) }).then((data) => {
        Object.assign(
          this.ListDetail,
          data.data,
          JSON.parse(this.$route.query.details)
        );
      });
      console.log(this.ListDetail);
    },
    reset(type) {
      let obj = {
        1: this.$t("others.phone"),
        2: this.$t("others.email"),
        3: this.$t("others.moneyPassword"),
        4: this.$t("filters.Google_Captcha"),
      };
      this.$confirm(
        this.$t("dialog.Confirm_resetting_the_current_users") + obj[type],
        this.$t("dialog.Prompt"),
        {
          confirmButtonText: this.$t("buttons.determine"),
          cancelButtonText: this.$t("buttons.cancel"),
          type: "warning",
        }
      )
        .then(() => {
          resetuserphone({
            uid: JSON.parse(this.$route.query.id) + "",
            stype: type,
          }).then((data) => {
            this.$notify({
              title: this.$t("dialog.Successful"),
              message: this.$t("dialog.Operation_is_successful"),
              type: "success",
              duration: 2000,
            });
            this.getDetails();
          });
        })
        .catch(() => {
          // this.$notify({
          //   type: 'info',
          //   message: '已取消删除'
          // });
        });
    },
  },
};
</script>
<style lang="scss" scoped>
.wc_1 {
  width: 99.7%;
  height: 100px;
  // background: firebrick;
  border: 1px solid #f0f2f5;
  margin: 20px auto;
  display: flex;
  flex-wrap: wrap;
  // flex-direction: column;
  align-items: center;
  // justify-content: space-around;

  .wc_1-one {
    width: 45%;
    // background: firebrick;
    padding-left: 10%;
    box-sizing: border-box;
  }
}
.top_info {
  .grid-content {
    border-bottom: 1px solid #f0f2f5;
    border-right: 1px solid #f0f2f5;
  }
}
.grid-content {
  min-height: 40px;
  display: flex;
  padding-left: 15px;
  align-items: center;

  .grid-content-right {
    word-wrap: break-word;
    word-break: normal;
  }
  .grid-content-left {
    width: 150px;
    font-size: 14px;
  }
}
.font {
  font-size: 14px;
  color: #999;
}
</style>