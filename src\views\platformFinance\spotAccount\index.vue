<template>
  <div class="spotAccount">
    <el-table
      v-loading="listLoading"
      :data="levelList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column prop="coin_name"   :label="$t('filters.currency')" align="center" min-width="78"></el-table-column>
      <el-table-column prop="amount"      :label="$t('tableHeader.available')" align="center" min-width="78"></el-table-column>
      <el-table-column prop="amount_lock" :label="$t('tableHeader.freeze')" align="center" min-width="78"></el-table-column>
      <el-table-column prop="asset_award" :label="$t('tableHeader.platformLiabilities')" align="center" min-width="78"></el-table-column>
    </el-table>
  </div>
</template>

<script>
import { financeasset } from "@/api/spotTrading"
export default {
  name: "spotAccount",
  data() {
    return {
      listLoading: false,
      levelList: [],
    };
  },
  components: {},

  computed: {},

  mounted() {
    this.getList();
  },

  methods: {
    // 获取数据
    getList() {
      this.listLoading = true;
      financeasset().then((res) => {
        // console.log(res)
        this.levelList = res.data;
        this.total = res.data.total;
        this.listLoading = false;
      })
    },
  },
};
</script>

<style lang="scss" scoped>
</style>