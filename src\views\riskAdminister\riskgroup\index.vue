<template>
  <div class="lebeladminister-container">
    <div class="filter-container">
      <el-button
        v-if="$store.getters.roles.indexOf('setgrouplist') > -1"
        type="primary"
        style="margin-left: 30%; margin-bottom: 10px; float: right"
        @click="AddClick()"
        >{{$t('buttons.new_group')}}</el-button
      >
    </div>

    <el-table
      v-loading="listLoading"
      :data="lebelList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column
        type="index"
        :label="$t('tableHeader.serialNumber')"
        align="center"
        min-width="50"
      />
      <el-table-column
        :label="$t('tableHeader.categoryName')"
        prop="group_name"
        align="center"
        min-width="100"
      />
      <el-table-column
        :label="$t('tableHeader.agentUID')"
        prop="uids"
        align="center"
        min-width="78"
      >
        <template slot-scope="{ row }">
          <span>{{ row.uids || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.total_golden_interval')" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.min_cash_in }} - {{ row.max_cash_in || $t('tableHeader.unlimited') }} <!--  --></span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.total_gold_interval')" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.min_cash_out }} - {{ row.max_cash_out || $t('tableHeader.unlimited') }}<!-- row.max_cash_out --></span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.net_gold_interval')" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.min_net_cash }} - {{ row.max_net_cash || $t('tableHeader.unlimited') }}<!-- row.max_net_cash --></span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('others.unwind_number_interval')" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.min_close }} - {{ row.max_close || $t('tableHeader.unlimited') }}<!-- row.max_close --></span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.transaction_number')" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.min_trade_num }} - {{ row.max_trade_num || $t('tableHeader.unlimited') }}<!-- row.max_trade_num --></span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.PNL_interval')" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.min_pnl }} - {{ row.max_pnl || $t('tableHeader.unlimited') }}<!-- row.max_pnl --></span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.poundage_interval')" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.min_commis }} - {{ row.max_commis || $t('tableHeader.unlimited') }}<!-- row.max_commis --></span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.capital_cost_interval')" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.min_catp }} - {{ row.max_catp || $t('tableHeader.unlimited') }}<!-- row.max_catp --></span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.net_PNL_interval')" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.min_net_pnl }} - {{ row.max_net_pnl || $t('tableHeader.unlimited') }}<!-- row.max_net_pnl --></span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.rate_of_return_interval')" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.min_profit }} - {{ row.max_profit || $t('tableHeader.unlimited') }}<!-- row.max_profit --></span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.odds_interval')" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.min_warn_profit }} - {{ row.max_warn_profit || $t('tableHeader.unlimited') }}<!-- row.max_warn_profit --></span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.poundage_net_gold_interval')"
        align="center"
        min-width="100"
      >
        <template slot-scope="{ row }">
          <span>{{ row.min_commis_profit }} - {{ row.max_commis_profit || $t('tableHeader.unlimited') }}<!-- row.max_commis_profit --></span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.occupancy_deposit_interval')" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.min_bond }} - {{ row.max_bond || $t('tableHeader.unlimited') }}<!-- row.max_bond --></span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.labal')" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.lable_name || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.state')" align="center" min-width="100">
        <template slot-scope="{ row }">
          <span>{{ row.status ? $t('tableHeader.effect_of') : $t('tableHeader.has_failure') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="$store.getters.roles.indexOf('setgrouplist') > -1"
        :label="$t('tableHeader.operation')"
        min-width="220"
        align="center"
      >
        <template slot-scope="{ row }">
          <el-button size="mini" @click="handleEdit(row)">{{$t('buttons.edit')}}</el-button>
          <el-button
            size="mini"
            :type="row.status ? 'info' : 'success'"
            @click="handleModifyStatus(row)"
            >{{ row.status ? $t('buttons.close') : $t('tableHeader.open') }}
          </el-button>
          <!-- <el-button type="danger" size="mini" @click="handleDel(row)"
            >删除</el-button
          > -->
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
    <el-dialog
      @close="closeConfigLabelDialog"
      :title="(this.handleDialogType == 'add' ? $t('buttons.add') : $t('buttons.edit'))+$t('tableHeader.group')"
      :visible.sync="addAndConfigDialogVisible"
      width="580px"
      v-dialogDrag
    >
      <el-form
        ref="editDialogForm"
        :rules="rules"
        :model="editDialogData"
        label-width="auto"
        label-position="left"
        size="mini"
        style="margin: 0 20px 0 10px"
      >
        <el-form-item :label="$t('tableHeader.categoryName')" prop="group_name">
          <el-input v-model="editDialogData.group_name" :placeholder="$t('filters.pleaseEnterTheContent')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('tableHeader.agentUID')" prop="uids">
          <el-input
            type="textarea"
            v-model="editDialogData.uids"
            :placeholder="$t('forms.inputContent')"
          ></el-input>
        </el-form-item>
        <el-form-item :label="$t('tableHeader.customer_total_gold')" size="mini" style="margin-bottom: 0;">
          <el-col :span="11">
            <el-form-item prop="min_cash_in"  size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.min_cash_in"
                :placeholder="$t('filters.pleaseEnterTheContent')"
                @input="()=>{editDialogData.min_cash_in=editDialogData.min_cash_in.replace(/[^\d^\.]/g,'')}"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align: center" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="max_cash_in" size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.max_cash_in"
                :placeholder="$t('filters.pleaseEnterTheContent')"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item :label="$t('tableHeader.customer_total_out')" size="mini" style="margin-bottom: 0;">
          <el-col :span="11">
            <el-form-item prop="min_close"  size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.min_cash_out"
                :placeholder="$t('filters.pleaseEnterTheContent')"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align: center" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="max_cash_out" size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.max_cash_out"
                :placeholder="$t('filters.pleaseEnterTheContent')"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item :label="$t('tableHeader.customer_net_gold')" size="mini" style="margin-bottom: 0;">
          <el-col :span="11">
            <el-form-item prop="min_net_cash"  size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.min_net_cash"
                :placeholder="$t('filters.pleaseEnterTheContent')"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align: center" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="max_net_cash" size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.max_net_cash"
                :placeholder="$t('filters.pleaseEnterTheContent')"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item :label="$t('tableHeader.unwindNum')" size="mini" style="margin-bottom: 0;">
          <el-col :span="11">
            <el-form-item prop="min_close"  size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.min_close"
                :placeholder="$t('filters.pleaseEnterTheContent')"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align: center" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="max_close" size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.max_close"
                :placeholder="$t('filters.pleaseEnterTheContent')"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item :label="$t('tableHeader.transaction_number')" size="mini" style="margin-bottom: 0;">
          <el-col :span="11">
            <el-form-item prop="min_trade_num"  size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.min_trade_num"
                :placeholder="$t('filters.pleaseEnterTheContent')"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align: center" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="max_trade_num" size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.max_trade_num"
                :placeholder="$t('filters.pleaseEnterTheContent')"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item :label="$t('tableHeader.PNL')" size="mini" style="margin-bottom: 0;">
          <el-col :span="11">
            <el-form-item prop="min_pnl"  size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.min_pnl"
                :placeholder="$t('filters.pleaseEnterTheContent')"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align: center" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="max_pnl" size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.max_pnl"
                :placeholder="$t('filters.pleaseEnterTheContent')"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item :label="$t('tableHeader.poundage')" size="mini" style="margin-bottom: 0;">
          <el-col :span="11">
            <el-form-item prop="min_commis"  size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.min_commis"
                :placeholder="$t('filters.pleaseEnterTheContent')"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align: center" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="max_commis" size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.max_commis"
                :placeholder="$t('filters.pleaseEnterTheContent')"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item :label="$t('tableHeader.moneyCost')" size="mini" style="margin-bottom: 0;">
          <el-col :span="11">
            <el-form-item prop="min_catp"  size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.min_catp"
                :placeholder="$t('filters.pleaseEnterTheContent')"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align: center" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="max_catp" size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.max_catp"
                :placeholder="$t('filters.pleaseEnterTheContent')"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item :label="$t('tableHeader.net_PNl')" size="mini" style="margin-bottom: 0;">
          <el-col :span="11">
            <el-form-item prop="min_net_pnl"  size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.min_net_pnl"
                :placeholder="$t('filters.pleaseEnterTheContent')"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align: center" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="max_net_pnl" size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.max_net_pnl"
                :placeholder="$t('filters.pleaseEnterTheContent')"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item :label="$t('tableHeader.rate_of_return')" size="mini" style="margin-bottom: 0;">
          <el-col :span="11">
            <el-form-item prop="min_profit"  size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.min_profit"
                :placeholder="$t('filters.pleaseEnterTheContent')"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align: center" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="max_profit" size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.max_profit"
                :placeholder="$t('filters.pleaseEnterTheContent')"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item :label="$t('tableHeader.odds')" size="mini" style="margin-bottom: 0;">
          <el-col :span="11">
            <el-form-item prop="min_warn_profit"  size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.min_warn_profit"
                :placeholder="$t('filters.pleaseEnterTheContent')"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align: center" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="max_warn_profit" size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.max_warn_profit"
                :placeholder="$t('filters.pleaseEnterTheContent')"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item :label="$t('tableHeader.poundage_net_gold')" size="mini" style="margin-bottom: 0;">
          <el-col :span="11">
            <el-form-item prop="min_commis_profit"  size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.min_commis_profit"
                :placeholder="$t('filters.pleaseEnterTheContent')"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align: center" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="max_commis_profit" size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.max_commis_profit"
                :placeholder="$t('filters.pleaseEnterTheContent')"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item :label="$t('tableHeader.occupancyDeposit')" size="mini" style="margin-bottom: 0;">
          <el-col :span="11">
            <el-form-item prop="min_bond"  size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.min_bond"
                :placeholder="$t('filters.pleaseEnterTheContent')"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col style="text-align: center" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="max_bond" size="mini">
              <el-input
                size="mini"
                v-model="editDialogData.max_bond"
                :placeholder="$t('filters.pleaseEnterTheContent')"
                oninput="value=value.replace(/[^\d^\.]/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item :label="$t('filters.labal')" label-position="left" prop="lable_id">
          <div style="display: flex; align-items: center">
            <el-select
              v-model="editDialogData.lable_id"
              :placeholder="$t('forms.pleaseSelectLabel')"
              style="width: 100%"
            >
              <el-option
                v-for="item in labelOptions"
                :key="item.labelid"
                :label="item.label_name"
                :value="item.labelid"
              ></el-option>
            </el-select>
            <i
              v-show="handleDialogType == 'add'"
              @click="
                () => {
                  addLableDialog = true;
                }
              "
              class="el-icon-circle-plus-outline"
              style="font-size: 24px; padding-left: 10px"
            ></i>
          </div>
        </el-form-item>
        <el-form-item :label="$t('tableHeader.priority')" label-position="left" prop="level">
          <el-select
            v-model="editDialogData.level"
            :placeholder="$t('dialog.pleaseSelect')"
            style="width: 100%"
          >
            <el-option
              v-for="item in total"
              :key="item"
              :label="item"
              :value="item"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" size="mini" @click="configLabelEntry()"
          >{{$t('buttons.determine')}}</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      @close="addLabelResetFields"
      class
      :title="$t('buttons.addLabel')"
      width="50%"
      :visible.sync="addLableDialog"
      :close-on-click-modal="false"
    >
      <el-form
        :rules="rules"
        label-position="left"
        ref="addLabelForm"
        :model="addLabelForm"
        label-width="auto"
      >
        <el-form-item :label="$t('filters.labal')" prop="addLabelVal">
          <el-input v-model="addLabelForm.addLabelVal"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="addLableDialog = false">{{$t('buttons.cancel')}}</el-button>
        <el-button size="mini" type="primary" @click="entryAddLabel()"
          >{{$t('buttons.determine')}}</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      @close="closexgmm"
      class
      :title="$t('others.googleCode')"
      width="350px"
      :visible.sync="checkvkeyDialog"
      :close-on-click-modal="false"
      @submit.native.prevent
    >
      <el-form
        :rules="rules"
        label-position="left"
        ref="ruleForm"
        :model="ruleForm"
        label-width="auto"
      >
        <el-form-item :label="$t('login.code')" prop="yzmVal">
          <el-input v-model="ruleForm.yzmVal" maxlength="6"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="checkvkeyDialog = false"
          >{{$t('buttons.cancel')}}</el-button
        >
        <el-button size="mini" type="primary" @click="entrySendyzm()"
          >{{$t('buttons.determine')}}</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getgrouplist,
  addlabel,
  addgroupinfo,
  upgroupinfo,
  getupgroupstatus,
  dellablinfo,
} from "@/api/riskAdminister";
import { bcprocontractset, getlabel, commckeckvkey } from "@/api/user";

export default {
  name: "riskgroup",
  data() {
    //单笔最小下单量
    var min_order_volume_V = (rules, value, callback) => {
      if (
        this.editDialogData.max_order_volume &&
        value &&
        Number(value) > Number(this.editDialogData.max_order_volume)
      ) {
        return callback(new Error(this.$t('dialog.Min_less_than_Max')));
      } else {
        callback();
      }
    };
    return {
      listLoading: false,
      total: 0,
      lebelList: [],    // 表格数组
      labelOptions: [], // 标签数组
      listQuery: {
        pageNo: 1,
        pagesize: 10,
      },
      addLabelForm: {
        addLabelVal: "",
      },
      editDialogData: {
        group_name: '',   // ` 组别名字,
        uids: '',   // ` 顶级代理uid, 
        min_cash_in: null,    // 最小入金,`
        max_cash_in: null,    //  ` '最大入金', `
        min_cash_out: null,   // ` 最小出金',
        max_cash_out: null,   // `'最大出金',`
        min_close: null,    // `'最小平仓', `
        max_close: null,    // ` '最大平仓', `
        min_trade_num: null,    // ` '最小交易次数',`
        max_trade_num: null,    // `'最大交易次数',`
        min_pnl: null,    // `'最小pnl',`
        max_pnl: null,    // ` '最大pnl',`
        min_commis: null,   // ` '最小手续费',`
        max_commis: null,   // `'最大手续费',`
        min_catp: null,   // ` '最小资金费用',`
        max_catp: null,   // `  '最大资金费用',`
        min_net_pnl: null,    // ` '净PNL',
        max_net_pnl: null,    // ` '净PNL',`
        min_net_cash: null,   // 净入金
        max_net_cash: null,   // 净入金
        min_profit: null,   // `  '盈利率',`
        max_profit: null,   // `  '盈利率', `
        min_warn_profit: null,    // ` '胜率', `
        max_warn_profit: null,    // `  '胜率',
        min_commis_profit: null,    // `'手续费占比',`
        max_commis_profit: null,    // ` '手续费占比',`
        min_bond: null,   // 占用保证金
        max_bond: null,   // 占用保证金
        lable_id: null,   // ` '标签ID',`
        level: null,    // `  '优先级',
      },
      ruleForm: {
        yzmVal: "", // 谷歌验证码
      },
      addLableDialog: false, // 控制google验证弹框显示
      checkvkeyDialog: false, // 控制google验证弹框显示
      addAndConfigDialogVisible: false, //控制添加标签对话框的显示和隐藏
      handleDialogType: "", //add: 添加 edit:编辑
      rules: {
        yzmVal: [
          { required: true, message: this.$t('dialog.The_input_box_cannot_be_empty'), trigger: "blur" },
        ],
        addLabelVal: [
          { required: true, message: this.$t('dialog.The_input_box_cannot_be_empty'), trigger: "blur" },
        ],
        group_name: [
          { required: true, message: this.$t('dialog.The_input_box_cannot_be_empty'), trigger: "blur" },
        ],
        // lablname: [
        //   {  message: "请选择标签", trigger: "change" },
        // ],
      },
    };
  },

  components: {},

  computed: {},

  mounted() {
    getlabel({}).then((res) => {
      this.labelOptions = res.data;
    });
    this.getList();
  },

  methods: {
    feeInput(val) {
      this.editDialogData.fee = this.clearNoNumOfAlert(val);
    },
    //只能输入数字只能有一个小数点，小数点不能在开头，不能在结尾，第一位允许添加负号
    clearNoNumOfAlert(value) {
      //得到第一个字符是否为负号
      var t = value.charAt(0);
      //先把非数字的都替换掉，除了数字和.
      value = value.replace(/[^\d.]/g, "");
      //必须保证第一个为数字而不是.
      value = value.replace(/^\./g, "");
      //保证只有出现一个.而没有多个.
      value = value.replace(/\.{2,}/g, ".");
      //保证.只出现一次，而不能出现两次以上
      value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
      //如果第一位是负号，则允许添加
      if (t == "-") {
        value = "-" + value;
      }
      return value;
    },
    //点击添加标签对话框里面确定按钮
    configLabelEntry() {
      this.$refs["editDialogForm"].validate((valid) => {
        if (valid) {
          this.checkvkeyDialog = true;
        }
      });
    },
    //点击添加标签出现弹框
    AddClick() {
      this.handleDialogType = "add";
      this.addAndConfigDialogVisible = true;
      this.$nextTick(() => {
        this.$refs["editDialogForm"].resetFields();
      });
      this.editDialogData = {
        group_name: '',   // ` 组别名字,
        uids: '',   // ` 顶级代理uid, 
        min_cash_in: null,    // 最小入金,`
        max_cash_in: null,    //  ` '最大入金', `
        min_cash_out: null,   // ` 最小出金',
        max_cash_out: null,   // `'最大出金',`
        min_close: null,    // `'最小平仓', `
        max_close: null,    // ` '最大平仓', `
        min_trade_num: null,    // ` '最小交易次数',`
        max_trade_num: null,    // `'最大交易次数',`
        min_pnl: null,    // `'最小pnl',`
        max_pnl: null,    // ` '最大pnl',`
        min_commis: null,   // ` '最小手续费',`
        max_commis: null,   // `'最大手续费',`
        min_catp: null,   // ` '最小资金费用',`
        max_catp: null,   // `  '最大资金费用',`
        min_net_pnl: null,    // ` '净PNL',
        max_net_pnl: null,    // ` '净PNL',`
        min_net_cash: null,   // 净入金
        max_net_cash: null,   // 净入金
        min_profit: null,   // `  '盈利率',`
        max_profit: null,   // `  '盈利率', `
        min_warn_profit: null,    // ` '胜率', `
        max_warn_profit: null,    // `  '胜率',
        min_commis_profit: null,    // `'手续费占比',`
        max_commis_profit: null,    // ` '手续费占比',`
        min_bond: null,   // 占用保证金
        max_bond: null,   // 占用保证金
        lable_id: null,   // ` '标签ID',`
        level: null,    // `  '优先级',
      }
    },
    // 列表编辑按钮
    handleEdit(row) {
      let data = this.transferType(JSON.stringify(row), 'string')
      Object.assign(this.editDialogData, data, {
        lable_id: Number(data.lable_id)?Number(data.lable_id):''
      });
      this.addAndConfigDialogVisible = true;
      this.handleDialogType = "edit";
    },
    //状态开启和关闭
    handleModifyStatus(row) {
      this.$confirm(`${this.$t('buttons.confirm')}${(row.status ? this.$t('buttons.close') : this.$t('tableHeader.open'))+row.group_name}？`)
        .then((_) => {
          let data = JSON.parse(JSON.stringify(row));
          // for (const key in data) {
          //   if (data.hasOwnProperty(key)) {
          //     const element = data[key];
          //     if(/^[0-9]+.?[0-9]*$/.test(element)){
          //       data[key] = Number(element)
          //       return false
          //     }
          //   }
          // }
          Object.assign(data, { status: data.status ? 0 : 1 });
          getupgroupstatus(data).then((res) => {
            this.$notify({
              title: this.$t('tableHeader.operation'),
              message: this.$t('dialog.Operation_is_successful'),
              type: "success",
              duration: 2000,
            });
            this.getList();
          });
        })
        .catch((_) => {});
    },
    //  渲染table列表
    getList() {
      this.listLoading = true;
      getgrouplist(this.listQuery).then((response) => {
        this.lebelList = response.data.list;
        this.total = response.data.total;
        this.listLoading = false;
      });
    },
    closeConfigLabelDialog() {
      this.addLabelForm.addLabelVal = "";
      this.$refs["editDialogForm"].resetFields();
    },
    addLabelResetFields() {
      this.$refs["addLabelForm"].resetFields();
    },
    entryAddLabel() {
      this.$refs["addLabelForm"].validate((valid) => {
        if (valid) {
          addlabel({
            lablname: this.addLabelForm.addLabelVal,
          }).then((res) => {
            this.addLableDialog = false;
            this.addLabelForm.addLabelVal = "";
            this.$notify({
              title: this.$t('dialog.Successful'),
              message: this.$t('dialog.Operation_is_successful'),
              type: "success",
              duration: 2000,
            });
            getlabel({}).then((res) => {
              this.labelOptions = res.data;
            });
          });
        } else {
          return false;
        }
      });
    },
    closexgmm() {
      this.$refs["ruleForm"].resetFields();
    },
    entrySendyzm() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          commckeckvkey({ code: this.ruleForm.yzmVal }).then((res) => {
            // 对象内字段value转换成Number
            let data = this.transferType(JSON.stringify(this.editDialogData), 'number')
            if (this.handleDialogType == "add") {
              addgroupinfo(data).then((res) => {
                this.ruleForm.yzmVal = "";
                this.closeConfigLabelDialog();
                this.checkvkeyDialog = false;
                this.addAndConfigDialogVisible = false;
                this.$notify({
                  title: this.$t('buttons.add'),
                  message: this.$t('dialog.Operation_is_successful'),
                  type: "success",
                  duration: 2000,
                });
                this.getList();
              });
            } else {
              upgroupinfo(data).then((res) => {
                this.ruleForm.yzmVal = "";
                this.closeConfigLabelDialog();
                this.checkvkeyDialog = false;
                this.addAndConfigDialogVisible = false;
                this.$notify({
                  title: this.$t('buttons.modify'),
                  message: this.$t('dialog.Operation_is_successful'),
                  type: "success",
                  duration: 2000,
                });
                this.getList();
              });
            }
          });
        } else {
          return false;
        }
      });
    },
    transferType(data, toType){
      let obj = {}
      Object.assign(obj,JSON.parse(data))
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          const element = obj[key];
          // 排除非转换Number的
          if (
            ["group_name","uids","manage","creat_time","lable_id","lable_name"].indexOf(key) ==
            -1
          ) {
            if(toType == 'number'){
              obj[key] = Number(element) || 0;
            }else{
              obj[key] = element+'' || '0';
            }
          } 
          else if(key == 'uids'){
            obj[key] = element+'' || ''
          } else {
            if (!element) {
              obj[key] = toType == 'number' ? 0 : '0';
            }
          }
        }
      }
      return obj
    }
  },
};
</script>
<style lang="scss" scoped>
</style>