<template>
  <div class="operating-container">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.uid"
        :placeholder="$t('filters.name')"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        size="mini"
        v-model="listQuery.manage"
        :placeholder="$t('filters.role_account')"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-right: 5px; font-size: 12px">{{
        $t("others.operationTime")
      }}</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px; margin-right: 20px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change="filterTimeTransform"
      >
      </el-date-picker>
      <el-select
        size="mini"
        v-model="listQuery.stype"
        :placeholder="$t('tableHeader.operationType')"
        clearable
        style="width: 150px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="(value, key, idx) in handleTypeOptions"
          :key="idx"
          :label="$t(value)"
          :value="key"
        />
      </el-select>
      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        @click="handleFilter"
      >
        {{ $t("buttons.search") }}
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="operatingList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column
        :label="$t('others.operationTime')"
        prop="creat_time"
        align="center"
        min-width="110px"
      ></el-table-column>
      <el-table-column
        :label="$t('filters.role_account')"
        prop="manage"
        align="center"
        min-width="90px"
      >
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.operationType')"
        align="center"
        min-width="130px"
      >
        <template slot-scope="{ row }">
          <span>{{ $t(handleTypeOptions[row.stype]) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.actionObject')"
        prop="user_id"
        align="center"
        min-width="100px"
      >
        <template slot-scope="{ row }">
          <span>{{ row.user_id || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.content')"
        align="center"
        min-width="200px"
      >
        <template slot-scope="{ row }">
          <div
            class="handle_content_wrap"
            v-html="transferText(row.stype, row.conten)"
          ></div>
        </template>
      </el-table-column>
    </el-table>

    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getmanagelogs } from "@/api/systemManagement";
import { mapGetters } from "vuex";

// 转换时间的在src/utils.index.js
import { parseTime } from "@/utils";
export default {
  name: "operatingLog",
  data() {
    return {
      listLoading: false,
      operatingList: null,
      total: 0,
      filterTime: [],
      listQuery: {
        uid: "", //用户id
        manage: "", //角色账号
        stype: "",
        star: "",
        end: "",
        pageNo: 1,
        pagesize: 10,
      },
    };
  },

  components: {},

  computed: {
    ...mapGetters(["handleTypeOptions", "handleContentTranferText"]),
  },

  mounted() {
    this.getList();
  },

  methods: {
    // 操作内容转换为文字
    transferText(type, content) {
      let text = "--";
      if ([2, 3, 7, 8, 5, 6, 9, 10, 17, 18, 12, 13, 16, 14,15].indexOf(type) > -1) {
        let object = JSON.parse(content);
        let transferObj = {};
        let a = "";
        let keyPerfix = "";
        if ([2, 3, 7].indexOf(type) > -1) {
          keyPerfix = "237_";
        } else if ([8].indexOf(type) > -1) {
          keyPerfix = "8_";
        } else if ([6, 9].indexOf(type) > -1) {
          keyPerfix = "69_";
        } else if ([5].indexOf(type) > -1) {
          keyPerfix = "5_";
        } else if ([10].indexOf(type) > -1) {
          keyPerfix = "10_";
        } else if ([17, 18].indexOf(type) > -1) {
          keyPerfix = "1718_";
        } else if ([12, 13, 16].indexOf(type) > -1) {
          keyPerfix = "121316_";
          for (const key in object) {
            if (Object.hasOwnProperty.call(object, key)) {
              const element = object[key];
              if(key.indexOf('max_')>-1){
                let k = 'min_'+key.substring(4,key.length)
                object[k] = object[k]+ '-'+(element || this.$t('tableHeader.unlimited'))
                delete object[key]
              }
            }
          }
        }  else if ([14, 15].indexOf(type) > -1) {
          keyPerfix = "1415_";
        } else {
        }
        for (const key in object) {
          if (Object.hasOwnProperty.call(object, key)) {
            const element = object[key];
            let obj = {};
            let objText = this.handleContentTranferText[keyPerfix + key] || {};
            obj["keyText"] = objText.keyText || key;
            if (objText.valueType == "0") {
              obj["value"] = (element === 0) ? element+'' : element;
              console.log('====',key,element)
            } else if (objText.valueType == "1") {
              let val =
                (objText.valueOptions && objText.valueOptions[element]) ||
                element;
              obj["value"] = this.$t(val);
            } else {
              let fix =
                objText.valueOptions && objText.valueOptions.indexOf(".") > -1
                  ? this.$t(objText.valueOptions)
                  : objText.valueOptions;
              obj["value"] = element + fix;
            }
            transferObj[key] = obj;
          }
        }
        for (const key in transferObj) {
          if (Object.hasOwnProperty.call(transferObj, key)) {
            const element = transferObj[key];
            a += `<span>${
              element.keyText.indexOf(".") > -1
                ? this.$t(element.keyText)
                : element.keyText
            }:<font class="value_text_style">${
              element.value || "--"
            }</font>;  &nbsp;&nbsp;</span>`;
          }
        }
        text = a;
      } else {
        text = content;
      }
      return text;
    },
    //渲染table数据
    getList() {
      this.listLoading = true;
      let data = {};
      getmanagelogs(
        Object.assign(data, this.listQuery, {
          stype: Number(this.listQuery.stype) || 0,
        })
      ).then((response) => {
        this.operatingList = response.data.list;
        this.total = response.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.getList();
    },
    filterTimeTransform(val) {
      this.listQuery.star = (val && val[0]) || "";
      this.listQuery.end = val ? val[1] + " 23:59:59" : "";
    },
  },
};
</script>
<style lang="scss" scoped>
.handle_content_wrap {
  ::v-deep .value_text_style {
    color: #000;
  }
}
</style>