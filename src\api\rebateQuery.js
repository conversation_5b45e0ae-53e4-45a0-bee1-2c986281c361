import request from '@/utils/request'
//用户返佣手续费列表
export function getrebotlist(data) {
  return request({
    url: '/managers/v1/period/getrebotlist',
    method: 'post',
    data: { data }
  })
}
//用户返佣发放
export function sendrebot(data) {
  return request({
    url: '/managers/v1/period/sendrebot',
    method: 'post',
    data: { data }
  })
}
//用户返佣详情
export function getuserrebot(data) {
  return request({
    url: '/managers/v1/period/getuserrebot',
    method: 'post',
    data: { data }
  })
}
//用户返佣交易记录
export function getusertrade(data) {
  return request({
    url: '/managers/v1/period/getusertrade',
    method: 'post',
    data: { data }
  })
}
//用户返佣手续费列表 - 导出
export function getrebotlistexport(data) {
  return request({
    url: '/managers/v1/period/getrebotlistexport',
    method: 'post',
    data: { data }
  })
}
//冻结发放
export function rebotswitchstatus(data) {
  return request({
    url: '/managers/v1/period/rebotswitchstatus',
    method: 'post',
    data: { data }
  })
} 