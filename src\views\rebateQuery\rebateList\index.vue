<template>
  <div class="rebate-container">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        :placeholder="$t('filters.topIDNick')"
        style="width: 150px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        :placeholder="$t('filters.agent')"
        style="width: 150px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.stype"
        :placeholder="$t('tableHeader.state')"
        clearable
        style="width: 120px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="(item, index) in stateOptions"
          :key="index"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <span style="margin-left: 20px; margin-right: 5px; font-size: 12px">{{$t('tableHeader.commissionTime')}}</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change='filterTimeTransform'
      >
      </el-date-picker>
      <el-button
        class="filter-item"
        style="margin-left: 10px"
        size="mini"
        type="primary"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
      <el-button
        class="filter-item"
        :loading="exportLoading"
        v-if="$store.getters.roles.indexOf('getrebotlistexport')>-1"
        @click="handleExport"
        size="mini"
        type="success"
      >
        {{$t('buttons.export')}}
      </el-button>
      <el-button
        class="filter-item"
        v-if="$store.getters.roles.indexOf('sendrebot')>-1"
        style="margin-left: 10px"
        size="mini"
        type="primary"
        :disabled="!multipleSelection.length"
        @click="oneClickSendrebot"
      >
        {{$t('buttons.A_key_issue')}}
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="rebateList"
      border
      fit
      highlight-current-row
      style="width: 100%; margin-top: 30px"
      size="mini"
      :header-cell-style="{ background: '#F0F8FF' }"
      @selection-change="handleSelectionChange">
    >
      <el-table-column :selectable="checkSelect" label-class-name="DisabledSelection" align="center" type="selection" width="70px"></el-table-column>
      <el-table-column :label="$t('tableHeader.uid')" prop="userid" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center" min-width="95"> </el-table-column>
      <el-table-column :label="$t('filters.topID')" prop="top_agent_id" align="center" min-width="78">
         <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('filters.topNick')" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorID')" prop="pareid" align="center" min-width="78">
          <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.userTransactionAmount')" min-width="115px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.trading_amount }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.userpoundage')" prop="trading_fee,gift_fee" min-width="105px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.gift_fee == 0 ?  row.trading_fee : row.trading_fee + '(' +(row.trading_fee-row.gift_fee) + '+EG' + row.gift_fee + ')' }}</span>
        </template>
      </el-table-column>  
      <el-table-column :label="$t('tableHeader.commissionMoney')" prop="rebate_commission" min-width="90px" align="center"/>
      <!-- <el-table-column :label="$t('tableHeader.Bonus_handling_fee')" prop="gift_fee" min-width="90px" align="center"/> -->
      <el-table-column :label="$t('tableHeader.getRidOfCommission')" prop="ignore_rebate_commission" min-width="90px" align="center"/>
      <el-table-column :label="$t('filters.backRate')" prop="agent_rebate_ratio" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.agent_rebate_ratio}}</span
          ><span>%</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.backTime')" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.period }}</span
          ><span>{{$t('forms.day')}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.state')" align="center" min-width="100px">
        <template slot-scope="{ row }">
          <span>{{ status[row.status_stype] }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.statisticalTime')" prop="end_period" align="center" width="75"/>
      <el-table-column :label="$t('tableHeader.issueTime')" prop="send_time" align="center" width="75">
        <template slot-scope="{ row }">
          {{row.status_stype !== 1?row.send_time:'--'}}
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.operation')" align="center" min-width="350px" v-if="$store.getters.roles.indexOf('sendrebot')>-1 || $store.getters.roles.indexOf('getuserrebot')>-1 || $store.getters.roles.indexOf('rebotswitchstatus')>-1">
        <template slot-scope="{ row }">
          <el-button size="mini" type="primary" :disabled="row.status_stype == 4" v-if="[1,4].indexOf(row.status_stype) > -1 && $store.getters.roles.indexOf('sendrebot')>-1" @click="sendrebotClick(row,3)">{{$t('buttons.cancelIssue')}}</el-button>
          <el-button size="mini" type="primary" :disabled="row.status_stype == 4" v-if="[1,4].indexOf(row.status_stype) > -1 && $store.getters.roles.indexOf('sendrebot')>-1" @click="sendrebotClick(row,1)">{{$t('buttons.issue')}}</el-button>
          <el-button size="mini" type="primary" v-if="[1,4].indexOf(row.status_stype) > -1 && $store.getters.roles.indexOf('rebotswitchstatus')>-1" @click="switchstatusClick(row,row.status_stype == 1?4:1)">{{row.status_stype == 1?$t('tableHeader.freeze'):$t('tableHeader.restore')}}{{$t('buttons.issue')}}</el-button>
          <el-button size="mini" type="primary" v-if="$store.getters.roles.indexOf('getuserrebot')>-1"  @click="DetailClick(row)">{{$t('buttons.toView')}}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagina-tion
      :total="total"
      :page-sizes="[10,100,500,1000]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getrebotlist, sendrebot, getrebotlistexport, rebotswitchstatus } from "@/api/rebateQuery";

export default {
  name: "USDTCommission",
  // components: { Page:Page },
  data() {
    return {
      listLoading: false,
      filterTime: [],
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        star: "", //开始
        end: "", //结束 
        stype: null, // 0：全部 1待发放 2已发放
        pageNo: 1,
        pagesize: 10,
      },
      status: {
        1: this.$t('filters.Stay_out'),
        2: this.$t('filters.Issued'),
        3: this.$t('filters.Has_been_cancelled'),
        4: this.$t('filters.Have_been_frozen'),
      },
      stateOptions: [
        { key: 1, name: this.$t('filters.Stay_out') },
        { key: 2, name: this.$t('filters.Issued') },
        { key: 3, name: this.$t('filters.Has_been_cancelled') },
        { key: 4, name: this.$t('filters.Have_been_frozen') },
      ],
      rebateList: null,
      total: 0,
      multipleSelection: [],
      exportLoading: false, //导出加载中效果
    };
  },
  components: {},

  computed: {},

  mounted() {
    this.getList();
  },

  methods: {
    // 一键发放
    oneClickSendrebot(){
      if(this.multipleSelection.length){
        this.$confirm(this.$t('dialog.Confirm_one_click_release'))
        .then(_ => {
          this.multipleSelection.forEach((v,i)=>{
            sendrebot({
              id: v.id,
              status: 1,
            }).then((res)=>{
              if(this.multipleSelection.length-1 === i){
                this.$notify({
                  title:this.$t('tableHeader.operation'),
                  message: this.$t('dialog.Operation_is_successful'),
                  type: "success",
                  duration: 2000,
                })
                this.getList()
              }
            })
          })
        })
        .catch(_ => {});
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    checkSelect (row,index) {
      let isChecked = true;
      if (row.status_stype === 1) { // 判断里面是否存在某个参数
        isChecked = true
      } else {
        isChecked = false
      }
      return isChecked
    },

    //  渲染table列表
    getList() {
      this.listLoading = true;
      let data = {}
      Object.assign(data,this.listQuery)
      data.stype = data.stype || undefined
      getrebotlist(data).then((res) => {
        this.rebateList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    sendrebotClick(row,type){
      this.$confirm(`${this.$t('buttons.confirm')}${type==3?this.$t('buttons.cancel'):''}${this.$t('buttons.issue')}?`)
      .then(_ => {
        sendrebot({
          id: row.id,
          status: type,
        }).then((res)=>{
          this.$notify({
            title:this.$t('tableHeader.operation'),
            message: this.$t('dialog.Operation_is_successful'),
            type: "success",
            duration: 2000,
          })
          this.getList()
        })
      })
      .catch(_ => {});
    },
    // 冻结功能
    switchstatusClick(row,type){
      this.$confirm(`${this.$t('dialog.Confirm_to_send_this_user')}${type==4?this.$t('tableHeader.freeze'):this.$t('tableHeader.restore')}${this.$t('buttons.issue')}?`)
      .then(_ => {
        rebotswitchstatus({
          id: row.id,
          status: type,
        }).then((res)=>{
          this.$notify({
            title:this.$t('tableHeader.operation'),
            message: this.$t('dialog.Operation_is_successful'),
            type: "success",
            duration: 2000,
          })
          this.getList()
        })
      })
      .catch(_ => {});
    },
    //跳转详情页
    DetailClick(row) {
      this.$router.push({
        path: "/rebateQuery/USDTCommissionDetail",
        query: { 
          dt: JSON.stringify(row)
        },
      });
    },
    // 搜索事件
    handleFilter() {
      this.getList();
    },
    //表格导出功能
    handleExport() {
      this.exportLoading = true;
      let data = {}
      Object.assign(data,this.listQuery)
      let newDate = parseInt(new Date().getTime()/1000);
      data.end = this.listQuery.end ? this.listQuery.end : (newDate.toDate('yyyy-MM-dd')+ ' 23:59:59')
      newDate = parseInt(newDate-518400)
      data.star = this.listQuery.star ? (this.listQuery.star + ' 00:00:00' ): (newDate.toDate('yyyy-MM-dd')+ ' 00:00:00')
      data.stype = data.stype || undefined
      getrebotlistexport(data).then((res) => {
        if(res.ret == 0){
            window.location.href=res.data.download_url;
            this.exportLoading = false
        }
      }).catch(err=>{
        this.exportLoading = false
      })
    },
    filterTimeTransform(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val && val[1]+' 23:59:59' || '';
    },
  },
};
</script>
<style lang="scss">
.rebate-container{
  /*表格表头全选*/
	.el-table .DisabledSelection .cell .el-checkbox__inner{
	  margin-left: -30px;
	  position:relative;
	}
	.el-table .DisabledSelection .cell:before{
	  content:"全选";
	  position:absolute;
	  right:11px;
	}
}

</style>
<style lang="scss" scoped>
</style>