// src/utils/course.js

export function getCourseTitle(langs) {
    const currentLang = this.$i18n.locale
    const localeToLangCode = {
        zh: 0,         // Simplified Chinese
        en: 1,         // English
        'zh-Hant': 2,  // Traditional Chinese
        ko: 3,         // Korean
        ja: 4          // Japanese
    }
    const langCode = localeToLangCode[currentLang] ?? 0  // default to Simplified Chinese
    const match = langs.find(l => l.lang_code === langCode)
    return match ? match.title : langs[0]?.title || '-'
}

export function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        this.$message.success(this.$t('common.copied'));
    }).catch(() => {
        this.$message.error(this.$t('common.copyFailed'));
    });
}
