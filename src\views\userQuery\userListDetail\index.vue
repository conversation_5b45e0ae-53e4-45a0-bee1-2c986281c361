<template>
  <div class="userlistdetail-container">
    <el-row
      :gutter="6"
      style="border: 1px solid #f0f2f5; margin: 20px auto"
    >
      <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8">
        <div class="grid-content">
          <span class="grid-content-left">{{$t('tableHeader.uid')}}:</span>
          <span class="grid-content-right">{{ ListDetail.user_id }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t('others.phone')}}:</span>
          <span class="grid-content-right">{{ ListDetail.phone || '--' }}</span>
          <el-button @click="reset(1)" v-if="ListDetail.phone && ListDetail.email && $store.getters.roles.indexOf('resetuserphone')>-1" size="mini" style="margin-left: 15px;" type="primary">{{$t('buttons.reset')}}</el-button>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t('others.googleValidation')}}:</span>
          <span v-show="Number(ListDetail.totp_secret) != 0" class="grid-content-right" style="margin-top:10px">******</span>
          <span v-show="Number(ListDetail.totp_secret) == 0" class="grid-content-right">--</span>
          <el-button @click="reset(4)" v-if="ListDetail.totp_secret && Number(ListDetail.totp_secret) != 0 && $store.getters.roles.indexOf('resetuserphone')>-1" size="mini" style="margin-left: 15px;" type="primary">{{$t('buttons.reset')}}</el-button>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t('filters.userType')}}:</span>
          <span class="grid-content-right">{{ userTypeOptions[ListDetail.user_type] }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t('filters.regTime')}}:</span>
          <span class="grid-content-right">{{ ListDetail.created_time }}</span>
        </div>
      </el-col>
      <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8">
        <div class="grid-content">
          <span class="grid-content-left">{{$t('tableHeader.userName')}}:</span>
          <span class="grid-content-right">{{ ListDetail.user_name }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t('others.email')}}:</span>
          <span class="grid-content-right">{{ ListDetail.email || '--' }}</span>
          <el-button @click="reset(2)" v-if="ListDetail.phone && ListDetail.email && $store.getters.roles.indexOf('resetuserphone')>-1" size="mini" style="margin-left: 15px;" type="primary">{{$t('buttons.reset')}}</el-button>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t('others.moneyPassword')}}:</span>
          <span v-show="Number(ListDetail.fund_passwd) != 0" class="grid-content-right" style="margin-top:10px">{{ ListDetail.fund_passwd ? '******' : '--' }}</span>
          <span v-show="Number(ListDetail.fund_passwd) == 0" class="grid-content-right">--</span>
          <el-button @click="reset(3)" v-if="ListDetail.fund_passwd && Number(ListDetail.fund_passwd) != 0 && $store.getters.roles.indexOf('resetuserphone')>-1" size="mini" style="margin-left: 15px;" type="primary">{{$t('buttons.reset')}}</el-button>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t('filters.inviteCode')}}:</span>
          <span class="grid-content-right">{{ ListDetail.invite_code || '--' }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t('tableHeader.lastLoginTime')}}:</span>
          <span class="grid-content-right">{{
            ListDetail.last_login_time
          }}</span>
        </div>
      </el-col>
      <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8">
        <div class="grid-content"></div>
        <div class="grid-content"></div>
        <div class="grid-content"></div>
        <div class="grid-content"></div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t('tableHeader.lastLoginIP')}}:</span>
          <span class="grid-content-right">{{ ListDetail.last_login_ip }}</span>
        </div>
      </el-col>
    </el-row>

    <el-row
      :gutter="6"
      style="border: 1px solid #f0f2f5; margin: 20px auto"
    >
      <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8">
        <div class="grid-content">
          <span class="grid-content-left">{{$t('filters.topID')}}:</span>
          <span class="grid-content-right">{{ ListDetail.topagent || '--' }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t('others.subordinateAgentNum')}}:</span>
          <span class="grid-content-right">{{ ListDetail.is_agent?ListDetail.childagent:'--' }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t('others.poundageCommissionPro')}}:</span>
          <span class="grid-content-right">{{
            ListDetail.is_agent?ListDetail.agent_rebate_ratio:'--'
          }}</span>
        </div>
        <!-- <div class="grid-content">
          <span class="grid-content-left">{{$t('others.modifyContent)}}:</span>
          <span class="grid-content-right">{{
            ListDetail.modify_content
          }}</span>
        </div> -->
      </el-col>
      <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8">
        <div class="grid-content">
          <span class="grid-content-left">{{$t('others.topAgent')}}:</span>
          <span class="grid-content-right">{{ ListDetail.paregant || '--' }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t('others.invitationNum')}}:</span>
          <span class="grid-content-right">{{ ListDetail.is_agent?ListDetail.childcount:'--' }}</span>
        </div>
       <!-- <div class="grid-content">
          <span class="grid-content-left">{{$t('others.lastModifyTime')}}:</span>
          <span class="grid-content-right">{{
            ListDetail.is_agent?ListDetail.up_agent_time:'--'
          }}</span>
        </div> -->
        <!-- <div class="grid-content">
          <span class="grid-content-left">{{$t('others.operator')}}:</span>
          <span class="grid-content-right">{{ ListDetail.Operator }}</span>
        </div> -->
      </el-col>
      <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8">
        <div class="grid-content"></div>
        <div class="grid-content"></div>
      </el-col>
    </el-row>

    <el-row
      :gutter="6"
      style="border: 1px solid #f0f2f5; margin: 20px auto"
    >
      <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
        <div class="grid-content" v-if="ListDetail.state == 3 || ListDetail.state == 6">
          <span class="grid-content-left">{{$t('others.identityInformation')}}:</span>
          <span class="grid-content-right">{{ ListDetail.real_name || '--' }}</span>
        </div>
      </el-col>
      <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
        <div class="grid-content" v-if="ListDetail.state == 3 || ListDetail.state == 6">
          <span class="grid-content-left">{{$t('others.throughTime')}}:</span>
          <span class="grid-content-right">{{ ListDetail.last_submit || '--' }}</span>
        </div>
      </el-col>
    </el-row>
    <el-row
     v-if="listUserDepositAddress.length"
      :gutter="6"
      style="border: 1px solid #f0f2f5; margin: 20px auto"
    >
      <el-col v-for="(item, index) in listUserDepositAddress" :key="index">
        <div class="grid-content">
          <span class="grid-content-left">{{$t('others.walletAddress', {name: item.currencyname})}}: </span>
          <span class="grid-content-right">{{ item.address || '--' }}</span>
        </div>
      </el-col>
    </el-row>

    <el-row
      :gutter="6"
      style="border: 1px solid #f0f2f5; margin: 20px auto"
    >
      <el-col :xs="3" :sm="3" :md="3" :lg="3" :xl="3">
        <div class="grid-content" style="font-size: 14px">{{$t('others.paymentMethods')}}</div>
      </el-col>
      <el-col :xs="21" :sm="21" :md="21" :lg="21" :xl="21">
        <div v-show="payInfo.length>0" v-for="(ite,index) in payInfo" :key="index" class="grid-content">
          <span style="margin-right: 30px; font-size: 14px;">{{['',$t('others.Bank_card'),$t('others.Alipay'),$t('others.WeChat')][ite.type]}}</span>
          <span v-show="ite.type == 1" style="font-size: 14px; margin-right:10px;">{{$t('others.bank')}}:</span>
          <span v-show="ite.type == 1" class="grid-content-right;" style="margin-right: 30px;">{{ ite.bank_name }}</span>
          <span v-show="ite.type == 1" style="font-size: 14px; margin-right:10px;">{{$t('others.accountOpeningBranch')}}:</span>
          <span v-show="ite.type == 1" class="grid-content-right" style="margin-right: 30px;">{{ ite.bank_branch_name || '--' }}</span>
          <span v-show="ite.type != 1" style="font-size: 14px; margin-right:10px;">{{$t('others.paymentCode')}}:</span>
          <span v-show="ite.type != 1" class="grid-content-right" style="margin-right: 30px;">
            <el-image 
              style="width: 32px; height: 32px"
              :src="ite.ocr_address" 
              :preview-src-list="[ite.ocr_address]">
            </el-image>
          </span>
          <span style="font-size: 14px; margin-right:10px;">{{['',$t('others.Bank_card_num'),$t('others.Alipay_Account'),$t('others.WeChat_Account')][ite.type]}}</span>
          <span class="grid-content-right">{{ ite.bank_numb }}</span>
        </div>
        <span v-show="payInfo.length==0" class="grid-content">--</span>
      </el-col>
    </el-row>

    <!-- <el-row
      :gutter="6"
      style="border: 1px solid #f0f2f5; margin: 20px auto"
    >
      <el-col :xs="6" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="grid-content">
          <span class="grid-content-left">{{$t('tableHeader.note')}}:</span>
          <span class="grid-content-right">{{ ListDetail.content }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t('filters.labal')}}:</span>
          <span class="grid-content-right">{{ ListDetail.label }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t('others.othersOperation')}}:</span>
          <span class="grid-content-right"></span>
        </div>
      </el-col>
      <el-col :xs="6" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="grid-content">
          <span class="grid-content-left">{{$t('others.operator')}}:</span>
          <span class="grid-content-right"></span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t('others.operator')}}:</span>
          <span class="grid-content-right"></span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t('others.operator')}}:</span>
          <span class="grid-content-right"></span>
        </div>
      </el-col>
      <el-col :xs="6" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="grid-content">
          <span class="grid-content-left">{{$t('others.operationTime')}}:</span>
          <span class="grid-content-right">{{ ListDetail.display_time }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t('others.operationTime')}}:</span>
          <span class="grid-content-right">{{ ListDetail.display_time }}</span>
        </div>
        <div class="grid-content">
          <span class="grid-content-left">{{$t('others.operationTime')}}:</span>
          <span class="grid-content-right">{{ ListDetail.display_time }}</span>
        </div>
      </el-col>
      <el-col :xs="6" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="grid-content">
        </div>
        <div class="grid-content">
          <el-button size="mini">{{$t('buttons.delete')}}</el-button>
        </div>
        <div class="grid-content">
          
        </div>
      </el-col>
    </el-row> -->
    <el-table
      v-loading="listLoading"
      :data="listMangeLogs"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.operationType')" align="center" min-width="130px">
        <template slot-scope="{ row }">
          <span>{{ $t(handleTypeOptions[row.stype]) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.content')"
        align="center"
        min-width="110px"
      >
        <template slot-scope="{row}">
          <div class="handle_content_wrap" v-html="transferText(row.stype, row.conten)"></div>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('others.operator')"
        prop="manage"
        align="center"
        min-width="110px"
      ></el-table-column>
      <el-table-column
        :label="$t('others.operationTime')"
        prop="creat_time"
        align="center"
        min-width="110px"
      ></el-table-column>
    </el-table>
    
      <div class="grid-content" style="margin-top: 50px;">
          <span class="grid-content-left">{{$t('others.assets')}}</span>
        </div>
    <el-row style="width: 100%; margin: 0 auto 50px">
      <el-col>
        <div class="grid-content">
          <span class="font">{{$t('others.walletAccountAssets')}}:</span>
        </div>
        <div class="grid-content" style="border-bottom: 1px solid #f0f2f5; align-items: flex-start;">
          <span class="fontWBold">{{ ListDetail.wallettotal }}</span>
        </div>
        <el-row v-for="(item, index) in listUserWallets" :key="index">
          <el-col :span="6">
            <div class="grid-content">
              <span>{{ item.currencyname }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="grid-content">
              <span class="font">{{$t('others.rights')}}：</span>
              <span>{{ item.rights }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="grid-content">
              <span class="font">{{$t('others.availableBalance')}}：</span>
              <span>{{ item.walletbalance }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="grid-content">
              <span class="font">{{$t('others.freezeNum')}}：</span>
              <span>{{ item.walletlock }}</span>
            </div>
          </el-col>
        </el-row>
      </el-col>
      
    </el-row>
    <el-row style="margin: 0 auto 50px">
      <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8"
      >
        <div class="grid-content">
          <span class="font">{{$t('others.contractAccountAssets')}}:</span>
        </div>
        <div class="grid-content" style="border-bottom: 1px solid #f0f2f5; align-items: flex-start;">
          <span class="fontWBold">{{ ListDetail.accbalance }}</span>
        </div>
        <el-row>
          <el-col :span="12">
            <div class="grid-content">
              <span class="font">{{$t('others.accountRights')}}</span>
            </div>
            <div class="grid-content">
              <span>{{ ListDetail.accequity }}</span>
            </div>
            <div class="grid-content">
              <span class="font">{{$t('others.frozenMargin')}}</span>
            </div>
            <div class="grid-content">
              <span>{{ ListDetail.accbond }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="grid-content">
              <span class="font">{{$t('others.availableBalance')}}</span>
            </div>
            <div class="grid-content">
              <span>{{ ListDetail.accavailable }}</span>
            </div>
            <div class="grid-content">
              <span class="font">{{$t('others.unrealizedProfitandLoss')}}</span>
            </div>
            <div class="grid-content">
              <span>{{ ListDetail.accprofit }}</span>
            </div>
          </el-col>
        </el-row>
      </el-col>
      <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8"
        style="border-left: 1px solid #f0f2f5"
      >
        <div class="grid-content">
          <span class="font">{{$t('others.documentaryAccountAssets')}}:</span>
        </div>
        <div class="grid-content" style="border-bottom: 1px solid #f0f2f5; align-items: flex-start;">
          <span class="fontWBold">{{ ListDetail.followbalance }}</span>
        </div>
        <el-row>
          <el-col :span="12">
            <div class="grid-content">
              <span class="font">{{$t('others.accountRights')}}</span>
            </div>
            <div class="grid-content">
              <span>{{ ListDetail.followequity }}</span>
            </div>
            <div class="grid-content">
              <span class="font">{{$t('others.frozenMargin')}}</span>
            </div>
            <div class="grid-content">
              <span>{{ ListDetail.followbond }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="grid-content">
              <span class="font">{{$t('others.availableBalance')}}</span>
            </div>
            <div class="grid-content">
              <span>{{ ListDetail.followavailable }}</span>
            </div>
            <div class="grid-content">
              <span class="font">{{$t('others.unrealizedProfitandLoss')}}</span>
            </div>
            <div class="grid-content">
              <span>{{ ListDetail.followprofit }}</span>
            </div>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>

<script>
//时间的转换
import { parseTime } from "@/utils";
import { userinfo, userpayments, resetuserphone } from "@/api/userQuery";
import { mapGetters } from "vuex";
export default {
  name: "userListDetail",
  data() {
    const userTypeOptions = {
      1: this.$t('filters.top_agent'),
      2: this.$t('filters.The_agent'),
      3: this.$t('filters.The_average_user'),
      4: this.$t('filters.The_proxy_directly_pushes_users')
    };
    return {
      ListDetail: {
        user_id: '--'
      },
      payInfo: [],
      //用户类型
      userTypeOptions,
      // 操作日志
      listLoading: false,
      listMangeLogs: [],
      listUserDepositAddress: [],
      // 币种资产
      listUserWallets: [],
      
    };
  },

  components: {},

  computed: {
    ...mapGetters(["handleTypeOptions", "handleContentTranferText"]),
  },

  mounted() {
    this.getDetails()
    userpayments({ user_id:JSON.parse(this.$route.query.id)}).then((data) => {
      this.payInfo = data.data;
    })
  },

  methods: {
    // 操作内容转换为文字
    transferText(type, content) {
      let text = "--";
      if ([2, 3, 7, 8, 5, 6, 9, 10, 17, 18, 12, 13, 16, 14,15].indexOf(type) > -1) {
        let object = JSON.parse(content);
        let transferObj = {};
        let a = "";
        let keyPerfix = "";
        if ([2, 3, 7].indexOf(type) > -1) {
          keyPerfix = "237_";
        } else if ([8].indexOf(type) > -1) {
          keyPerfix = "8_";
        } else if ([6, 9].indexOf(type) > -1) {
          keyPerfix = "69_";
        } else if ([5].indexOf(type) > -1) {
          keyPerfix = "5_";
        } else if ([10].indexOf(type) > -1) {
          keyPerfix = "10_";
        } else if ([17, 18].indexOf(type) > -1) {
          keyPerfix = "1718_";
        } else if ([12, 13, 16].indexOf(type) > -1) {
          keyPerfix = "121316_";
          for (const key in object) {
            if (Object.hasOwnProperty.call(object, key)) {
              const element = object[key];
              if(key.indexOf('max_')>-1){
                let k = 'min_'+key.substring(4,key.length)
                object[k] = object[k]+ '-'+(element || this.$t('tableHeader.unlimited'))
                delete object[key]
              }
            }
          }
        }  else if ([14, 15].indexOf(type) > -1) {
          keyPerfix = "1415_";
        } else {
        }
        for (const key in object) {
          if (Object.hasOwnProperty.call(object, key)) {
            const element = object[key];
            let obj = {};
            let objText = this.handleContentTranferText[keyPerfix + key] || {};
            obj["keyText"] = objText.keyText || key;
            if (objText.valueType == "0") {
              obj["value"] = (element === 0) ? element+'' : element;
              console.log('====',key,element)
            } else if (objText.valueType == "1") {
              let val =
                (objText.valueOptions && objText.valueOptions[element]) ||
                element;
              obj["value"] = this.$t(val);
            } else {
              let fix =
                objText.valueOptions && objText.valueOptions.indexOf(".") > -1
                  ? this.$t(objText.valueOptions)
                  : objText.valueOptions;
              obj["value"] = element + fix;
            }
            transferObj[key] = obj;
          }
        }
        for (const key in transferObj) {
          if (Object.hasOwnProperty.call(transferObj, key)) {
            const element = transferObj[key];
            a += `<span>${
              element.keyText.indexOf(".") > -1
                ? this.$t(element.keyText)
                : element.keyText
            }:<font class="value_text_style">${
              element.value || "--"
            }</font>;  &nbsp;&nbsp;</span>`;
          }
        }
        text = a;
      } else {
        text = content;
      }
      return text;
    },
    getDetails(){
      this.listLoading = true;
      userinfo({ user_id:JSON.parse(this.$route.query.id)}).then((data) => {
        // 字段过滤，资产保留6位小数
        let filterKey = ['wallettotal', 'accbalance', 'accequity', 'accbond', 'accavailable', 'accprofit', 'followbalance', 'followequity', 'followbond', 'followavailable', 'followprofit',]
        this.ListDetail = data.data;
        filterKey.forEach(element => {
          this.ListDetail[element] = Number(this.ListDetail[element]).cutXiaoNum(6)
        });
        this.listLoading = false;
        this.listMangeLogs = data.data.listMangeLogs || []
        this.listUserDepositAddress = data.data.listUserDepositAddress || []
        let list = data.data && data.data.listUserWallets || []
        this.listUserWallets = list.map(v=>{
          for (const key in v) {
            if (Object.hasOwnProperty.call(v, key) &&
              [
                "currencyname",
                "address",
              ].indexOf(key) == -1) {
              v[key] = Number(v[key]).cutXiaoNum(6);
            }
          }
          v.rights = Number(Number(v.walletbalance)+Number(v.walletlock)).cutXiaoNum(6)
          return v
        })
      });
    },
    reset(type){
      let obj = {
        1: this.$t('others.phone'),
        2: this.$t('others.email'),
        3: this.$t('others.moneyPassword'),
        4: this.$t('filters.Google_Captcha'),
      }
      this.$confirm(this.$t('dialog.Confirm_resetting_the_current_users')+obj[type], this.$t('dialog.Prompt'), {
          confirmButtonText: this.$t('buttons.determine'),
          cancelButtonText: this.$t('buttons.cancel'),
          type: 'warning'
        }).then(() => {
          resetuserphone({ uid :JSON.parse(this.$route.query.id)+'', stype: type}).then((data) => {
            this.$notify({
              title: this.$t('dialog.Successful'),
              message: this.$t('dialog.Operation_is_successful'),
              type: "success",
              duration: 2000,
            });
            this.getDetails()
          })
        }).catch(() => {
          // this.$notify({
          //   type: 'info',
          //   message: '已取消删除'
          // });          
        });
    }
  },
};
</script>
<style lang="scss" scoped>
.wc_1 {
  width: 99.7%;
  height: 100px;
  // background: firebrick;
  border: 1px solid #f0f2f5;
  margin: 20px auto;
  display: flex;
  flex-wrap: wrap;
  // flex-direction: column;
  align-items: center;
  // justify-content: space-around;

  .wc_1-one {
    width: 45%;
    // background: firebrick;
    padding-left: 10%;
    box-sizing: border-box;
  }
}
.grid-content {
  min-height: 40px;
  display: flex;
  padding-left: 15px;
  align-items: center;
  
  .grid-content-right {
     word-wrap: break-word;
     word-break: normal;
  }
  .grid-content-left {
    width: 150px;
    font-size: 14px;
  }
}
.font {
  font-size: 14px;
  color: #999;
}
.fontWBold{
  font-weight: bold;
}
.handle_content_wrap {
  ::v-deep .value_text_style {
    color: #000;
  }
}
</style>