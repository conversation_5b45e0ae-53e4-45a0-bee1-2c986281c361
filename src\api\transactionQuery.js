import request from '@/utils/request'

//持仓查询接口
export function positionlist(data) {
  return request({
    url: '/managers/v1/trade/positionlist',
    method: 'post',
    data: { data }
  })
}

//持仓查询导出
export function positionlistexport(data) {
  return request({
    url: '/managers/v1/trade/positionlistexport',
    method: 'post',
    data: { data }
  })
}

//平仓查询接口
export function closetradelist(data) {
  return request({
    url: '/managers/v1/trade/closetradelist',
    method: 'post',
    data: { data }
  })
} 

//平仓查询导出
export function closetradeexport(data) {
  return request({
    url: '/managers/v1/trade/closetradeexport',
    method: 'post',
    data: { data }
  })
}

//开仓查询接口
export function opentradelist(data) {
  return request({
    url: '/managers/v1/trade/opentradelist',
    method: 'post',
    data: { data }
  })
}
//开仓查询导出
export function opentradeexport(data) {
  return request({
    url: '/managers/v1/trade/opentradeexport',
    method: 'post',
    data: { data }
  })
}

//开平仓查询接口 - 旧的
export function gettradelist_old(data) {
  return request({
    url: '/managers/v1/trade/gettradelist',
    method: 'post',
    data: { data }
  })
}

//开平仓查询接口
export function gettradelist(data) {
  return request({
    url: '/managers/v1/trade/getorderlist',
    method: 'post',
    data: { data }
  })
}

//强平单查询接口
export function getstopout(data) {
  return request({
    url: '/managers/v1/trade/getstopout',
    method: 'post',
    data: { data }
  })
}
//强平单查询接口 - 导出
export function getstopoutexport(data) {
  return request({
    url: '/managers/v1/trade/getstopoutexport',
    method: 'post',
    data: { data }
  })
}

//止盈止损查询
export function plancloseorder(data) {
  return request({
    url: '/managers/v1/trade/plancloseorder',
    method: 'post',
    data: { data }
  })
}

//计划单查询
export function getplanorder(data) {
  return request({
    url: '/managers/v1/trade/getplanorder',
    method: 'post',
    data: { data }
  })
}

//用户持仓监控
export function getposstioncap(data) {
  return request({
    url: '/managers/v1/trade/getposstioncap',
    method: 'post',
    data: { data }
  })
}

//获取导出下载列表
export function getmanageexpotlist(data) {
  return request({
    url: '/managers/v1/manager/getmanageexpotlist',
    method: 'post',
    data: { data }
  })
}