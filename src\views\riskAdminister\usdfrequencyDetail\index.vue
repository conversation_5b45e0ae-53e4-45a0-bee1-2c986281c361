<template>
  <div class="frequencydetail-container">
    <el-table
      v-loading="listLoading"
      :data="openList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
   
      <el-table-column
        :label="$t('tableHeader.clinchDealSerial')"
        prop="tradeid"
        align="center"
        min-width="110px"
      >
        <template slot-scope="{ row }">
          <span>{{ row.tradeid || "--" }}</span>
        </template>
      </el-table-column>
         <el-table-column
        :label="$t('tableHeader.contract')"
        prop="contractcode"
        min-width="90px"
        align="center"
      ></el-table-column>
      <el-table-column :label="$t('filters.direction')" align="center" min-width="110px">
        <template slot-scope="{ row }">
          <span>{{ row.side == 'B' ? $t('tableHeader.buy'):$t('tableHeader.sell') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.positionType')" align="center" min-width="110px">
        <template slot-scope="{ row }">
          <span>{{ row.offset == 'O' ? $t('tableHeader.positions'):$t('tableHeader.unwind') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.leverage')"
        prop="lever"
        align="center"
        min-width="95px"
      >
        <template slot-scope="{ row }">
          <span>&times;{{ row.lever || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.pieceNum')" align="center" min-width="100px">
        <template slot-scope="{ row }">
          <span>{{ row.volume || "--" }}</span>
        </template>
         <!-- <template slot-scope="{ row }">
          <span>{{ row.volume }}张</span><span>/{{row.conversion}}{{row.contractcode.slice(0,-4)}}</span>
        </template> -->
      </el-table-column>
      <el-table-column :label="$t('tableHeader.openPositionsPrice')" min-width="80px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.open_avg_price || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.unwindPri')" align="center" min-width="80px">
        <template slot-scope="{ row }">
          <span>{{ row.price }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.poundage')"
        prop="volume"
        align="center"
        min-width="90px"
      >
        <template slot-scope="{ row }">
          <span>{{ row.commission||'--' }}</span>
        </template>
      </el-table-column>
        <el-table-column
        :label="$t('tableHeader.net_PNl')"
        prop="volume"
        align="center"
        min-width="80px"
      >
        <template slot-scope="{ row }">
          <span>{{ row.profitpnl }}</span>
        </template>
      </el-table-column>
        <el-table-column
        :label="$t('tableHeader.PNL')"
        prop="volume"
        align="center"
        min-width="80px"
      >
        <template slot-scope="{ row }">
          <!-- <span>{{ row.closeprofit||'--' }}</span>-->
           <span>{{ row.closeprofit }}</span>

        </template>
      </el-table-column>
        <el-table-column
        :label="$t('filters.transactionType')"
        prop="volume"
        align="center"
        min-width="80px"
      >
        <template slot-scope="{ row }">
          <span>{{ orderTypeObj[row.ordertype]  }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.IP_addr')"
        prop="ipaddress"
        align="center"
        min-width="120px"
      >
      </el-table-column>
      <el-table-column :label="$t('tableHeader.clinchDealTime')" align="center" min-width="160px">
        <template slot-scope="{ row }">
            <span>{{row.tradetime}}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10, 20, 30, 50]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { tradefrequencview } from "@/api/riskAdminister";
import { bcprocontractset } from "@/api/user";
export default {
  name: 'frequencyDetail',
  data() {
    return {
        listLoading: false,
      total: 0,
      openList: null,
      filterTime: [],
      listQuery: {
        contract_code: "", //合约代码
        // side: undefined, //方向 B买S卖
        pageNo: 1,
        pagesize: 10,
        star: "", //开始
        end: "", //结束
      },
      contractOptions: [],
       orderTypeObj: {
        0: this.$t('filters.Market_orders'),
        1: this.$t('tableHeader.order'), 
        2: this.$t('tableHeader.check_single'), 
        4: this.$t('tableHeader.stop_loss_orders'), 
        5: this.$t('filters.Strong_flat_sheet')
      },
    };
  },

  components: {},

  computed: {},

  mounted() {
       bcprocontractset({}).then((res) => {
      this.contractOptions = res.data.filter((v) => v.isshow == 1);
    });
      this.getList()
  },

  methods: {
        getList() {
        this.listLoading = true;
        var data = {
            userid:JSON.parse(this.$route.query.id),
            pageNo:this.listQuery.pageNo,
            pagesize:this.listQuery.pagesize,
            contract_code:this.$route.query.contract_code,
            star:this.$route.query.star,
            end:this.$route.query.end,
        }
            tradefrequencview(data).then((res) => {
            this.openList = res.data.list;
            this.total = res.data.total;
            this.listLoading = false;
        });
        },
  },
};
</script>
<style lang='scss' scoped>
</style>