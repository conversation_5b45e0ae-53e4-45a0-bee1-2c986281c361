import request from '@/utils/request'

// USD币种
export function bcusdprocontractset(data) {
    return request({
        url: '/managers/v1/comm/bcusdprocontractset',
        method: 'post',
        data: { data }
    })
}

// 合约账户
export function usdprofinaceasst(data) {
    return request({
        url: '/managers/v1/reverse/usdprofinaceasst',
        method: 'post',
        data: { data }
    })
}
// 合约账户
export function usdprofinacecapitalhistory(data) {
    return request({
        url: '/managers/v1/reverse/usdprofinacecapitalhistory',
        method: 'post',
        data: { data }
    })
}
// 开平仓查询
export function getorderlist (data) {
    return request({
        url: '/managers/v1/reverse/getorderlist',
        method: 'post',
        data: { data }
    })
}
// 开平仓查询 导出
export function getorderlistexport (data) {
    return request({
        url: '/managers/v1/reverse/getorderlistexport',
        method: 'post',
        data: { data }
    })
}
// 持仓查询
export function positionlist (data) {
    return request({
        url: '/managers/v1/reverse/positionlist',
        method: 'post',
        data: { data }
    })
}
// 持仓查询导出
// export function positionliste (data) {
//     return request({
//         url: '/managers/v1/reverse/positionliste',
//         method: 'post',
//         data: { data }
//     })
// }
// 用户强平数据
export function getstopout (data) {
    return request({
        url: '/managers/v1/reverse/getstopout',
        method: 'post',
        data: { data }
    })
}
// 用户强平数据 导出
export function getstopoutexport (data) {
    return request({
        url: '/managers/v1/reverse/getstopoutexport',
        method: 'post',
        data: { data }
    })
}
//止盈止损
export function plancloseorder (data) {
    return request({
        url: '/managers/v1/reverse/plancloseorder',
        method: 'post',
        data: { data }
    })
}
// 计划委托
export function getplanorder (data) {
    return request({
        url: '/managers/v1/reverse/getplanorder',
        method: 'post',
        data: { data }
    })
}
// 用户持仓监控
export function getposstioncap (data) {
    return request({
        url: '/managers/v1/reverse/getposstioncap',
        method: 'post',
        data: { data }
    })
}