// src/utils/format.js

export function formatNumber(value) {
  if (typeof value !== 'number') return value;
  return new Intl.NumberFormat('en-US').format(value);
}

export function getPledgeStateTagType(status) {
  switch (status) {
    case 'aboutToStart': return 'warning';
    case 'normal': return 'success';
    default: return 'info';
  }
}

export function getPledgeStatusText(status) {
  switch (status) {
    case 'aboutToStart': return this.$t('status.aboutToStart');
    case 'normal': return this.$t('status.normal');
    case 'ended': return this.$t('status.ended');
    default: return this.$t('status.unknown');
  }
}

export function getPledgeRecordStatusTag(status) {
  switch (status) {
    case 'normal': return 'success';
    case 'redemption_review': return 'warning';
    case 'redeemed': return 'info';
    default: return '';
  }
}

export function getPledgeRecordStatusText(status) {
  switch (status) {
    case 'normal': return this.$t('launchpad.placeholders.pledgeStatusNormal');
    case 'redemption_review': return this.$t('launchpad.placeholders.pledgeStatusReview');
    case 'redeemed': return this.$t('launchpad.placeholders.pledgeStatusRedeemed');
    default: return '-';
  }
}

export function getRedemptionStateText(status) {
  switch (status) {
    case 'redeemed': return this.$t('launchpad.placeholders.pledgeStatusRedeemed');
    case 'redeeming': return this.$t('launchpad.placeholders.pledgeStatusRedeeming');
    case '-' : return this.$t('launchpad.placeholders.pledgeStatusReview');
    default: return '-';
  }
}

export function getRedemptionStatusText(status) {
  switch (status) {
    case 'success': return this.$t('launchpad.forms.success');
    case 'loading': return this.$t('launchpad.forms.loading');
    case 'fail': return this.$t('launchpad.forms.fail');
    case 'noneed': return this.$t('launchpad.forms.noNeed');
    default: return '-';
  }
}

export function shortenMiddle(str, head = 4, tail = 4) {
  if (!str || typeof str !== 'string') return str
  if (str.length <= head + tail) return str
  return `${str.slice(0, head)}...${str.slice(-tail)}`
}
