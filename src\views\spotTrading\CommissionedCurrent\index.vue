<template>
  <div class="CommissionedCurrent">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.uid"
        :placeholder="$t('tableHeader.uid')"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.trading"
        :placeholder="$t('filters.Trading')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in tradingOptions"
          :key="item.key"
          :label="item.traderpairs"
          :value="item.traderpairs"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.orderType"
        :placeholder="$t('filters.orderType')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in orderTypeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.side"
        :placeholder="$t('filters.direction')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in sizeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.order_status"
        :placeholder="$t('filters.Delegate_status')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in stateOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <span style="margin-right: 20px; font-size: 12px">{{ $t('tableHeader.creationTime') }}</span>
      <el-date-picker
        style="width: 220px; margin-right: 20px; margin-top: 10px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change='filterTimeTransform'
      >
      </el-date-picker>

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-top: 10px"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
    </div>

    <!-- <div class="box_se" v-if="$store.getters.roles.indexOf('closetradelistConfluence')>-1">
      <div class="protradepnl_main protradepnlVal">
        <div class="hei">{{`买入额(${activeName})`}}</div>
        <div :class="`hei ${(protradepnl.buy_trade_volume == 0 || protradepnl.buy_trade_volume == '--')?'':protradepnl.buy_trade_volume>0?'green':'red'}`">
          <span>{{ (protradepnl.buy_trade_volume>0 && '+' || '') + protradepnl.buy_trade_volume }}</span>
        </div>
      </div>
      <div class="protradepnl_main protradepnlVal">
        <div class="hei">{{`卖出额(${activeName})`}}</div>
        <div :class="`hei ${(protradepnl.sell_trade_volume == 0 || protradepnl.sell_trade_volume == '--')?'':protradepnl.sell_trade_volume>0?'green':'red'}`">
          <span>{{ (protradepnl.sell_trade_volume>0 && '+' || '') + protradepnl.sell_trade_volume }}</span>
        </div>
      </div>
    </div> -->

    <el-tabs v-model="activeName" @tab-click="handleClick" style="margin: 15px 25px">
      <el-tab-pane v-for="(item, index) in coinnameOptions" :key="index" :label="item.coin_name + $t('tableHeader.Trading_area')" :name="item.coin_name"></el-tab-pane>
    </el-tabs>

    <el-table
      v-loading="listLoading"
      :data="levelList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')"    prop="userid" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.userid || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.user_name || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topID')" prop="top_agent_id" align="center" min-width="78"> 
        <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topNick')" prop="petname" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorID')" prop="pareid" align="center" min-width="78">
         <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column :label="$t('filters.currency')"   prop="coin_name" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.coin_name || '--' }}</span>
        </template>
      </el-table-column> -->
      <el-table-column :label="$t('filters.Trading')" prop="contractcode" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.contractcode || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.orderType')" prop="order_type" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.order_type == 0 ? $t('filters.Market_price') : row.order_type == 1 ? $t('filters.Entrust') : '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.direction')" prop="side" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.side == "B" ? $t('tableHeader.buy') : row.side == "S" ? $t('tableHeader.sell') : '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.entrustPrice')" prop="price" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.price == 0 ? $t('filters.Market_price') : row.price }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.entrustNum')" prop="volume" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.volume == "0" ? '--' : row.volume }}&nbsp;{{ row.base_coin_name }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.Commission_amount')" prop="money" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.money || '--' }}&nbsp;{{ row.coin_name }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.Lock_up_assets')" prop="asset_lock" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.asset_lock || '--' }}&nbsp;{{ row.side == "B" ? row.base_coin_name : row.coin_name }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.Deal_done')" prop="trade_volume" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.trade_volume || '--' }}&nbsp;{{ row.base_coin_name }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.Not_traded')" prop="volume,trade_volume" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ Number(row.volume) - Number(row.trade_volume) || '--' }}&nbsp;{{ row.base_coin_name }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.poundage')" prop="cost_fee" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.cost_fee || '--' }}&nbsp;{{ row.side == "B" ? row.base_coin_name : row.coin_name }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.creationTime')" prop="create_time" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.create_time || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.Delegate_status')" prop="state" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>
            {{
              row.state == 0 ? $t('filters.Temporary_order') :
              row.state == 1 ? $t('filters.Not_traded') :
              row.state == 100 ? $t('filters.Some_clinch_a_deal') :
              row.state == 200 ? $t('filters.All_clinch_a_deal') :
              row.state == 201 ? $t('filters.No_deal_has_been_withdrawn') :
              row.state == 202 ? $t('filters.Part_of_the_deal_has_been_withdrawn') : '--'
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.Estimated_transaction_price')" prop="expect_price" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.expect_price || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.Third_party_order_ID')" prop="third_order_id" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.third_order_id || '--' }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,20,30,50]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { orderlist, futuresconcode,coinarea } from "@/api/spotTrading"
export default {
  name: "CommissionedCurrent",
  data() {
    return {
      listLoading: false,
      total: 0,
      levelList: null,
      filterTime: [],
      listQuery: {
        uid: "",                // UID
        trading: "",            // 交易对
        orderType: "",          // 订单类型
        side: "",               // 方向
        order_status: "",       // 委托状态
        pageNo: 1,
        pagesize: 10,
        star: '',       // 开始
        end: '',        // 结束
      },
      tradingOptions: [],   // 交易对
      orderTypeOptions: [
        { key: 0, name: this.$t('filters.Market_price') },
        { key: 1, name: this.$t('filters.Entrust') },
      ],  // 订单类型
      sizeOptions: [
        { key: 'B', name: this.$t('tableHeader.buy') },
        { key: 'S', name: this.$t('tableHeader.sell') },
      ],  // 方向
      stateOptions: [
        { key: 0, name: this.$t('filters.Temporary_order') },
        { key: 1, name: this.$t('filters.Not_traded') },
        { key: 100, name: this.$t('filters.Some_clinch_a_deal') },
        { key: 200, name: this.$t('filters.All_clinch_a_deal') },
        { key: 201, name: this.$t('filters.No_deal_has_been_withdrawn') },
        { key: 202, name: this.$t('filters.Part_of_the_deal_has_been_withdrawn') },
      ],  // 委托状态
      activeName: '',
      coinnameOptions: [],   // 币种
      protradepnl: [],
    };
  },

  components: {},

  computed: {
    // 默认时间
    // timeDefault () {
    //   let date = new Date()
    //   // 通过时间戳计算
    //   let defalutStartTime = ((date.getTime() - 1 * 24 * 3600 * 1000)/1000).toDate('yyyy-MM-dd') // 转化为时间戳
    //   let defalutEndTime = ((date.getTime()/1000)).toDate('yyyy-MM-dd')
    //   return [defalutStartTime, defalutEndTime]
    // }
  },

  mounted() {
    // 交易区
    // coinarea({}).then((res) => {
    //   this.coinnameOptions = res.data
    //   if(this.coinnameOptions.length>0){
    //     this.activeName = this.coinnameOptions[0].coin_name
    //   }
    //   this.getList();
    // })
    // 交易对
    futuresconcode({}).then((res) => {
      this.tradingOptions = res.data
    })

  },

  methods: {
    // tab切换
    handleClick() {
      this.getList();
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    // 获取数据
    getList() {
      this.listLoading = true;
      let data = {};
      Object.assign(data, {
        coinname: this.activeName,                // 交易区
        uid: this.listQuery.uid,                  // uid
        contract_code: this.listQuery.trading,    // 交易对
        side: this.listQuery.side,                // 方向 B买 S卖
        order_type: this.listQuery.orderType + "" ? Number(this.listQuery.orderType) : -1,      // 订单类型 -1全部 0市价 1委托
        order_status: this.listQuery.order_status + "" ? Number(this.listQuery.order_status) : -1,  // 委托状态 0-临时订单 1- 未成交 100-部分成交 200-全部成交 201-未成交已撤 202-部分成交已撤
        star: this.listQuery.star,                // string 开始时间
        end: this.listQuery.end,                  // string 结束时间
        pageNo: this.listQuery.pageNo,            // int 页数
        pagesize: this.listQuery.pagesize,        // int 分页数量
      })
      orderlist(data).then((res) => {
        // console.log(res)
        this.levelList = res.data.list;
        // this.protradepnl = res.data.tradetotal;
        this.total = res.data.total;
        this.listLoading = false;
      })
    },

    filterTimeTransform(val) {
      this.listQuery.star = (val && val[0]) || "";
      this.listQuery.end = val ? val[1] + " 23:59:59" : "";
    },
  },
};
</script>

<style lang="scss" scoped>
.box_se{
  border: 1px solid #c9c9c9;
  margin: 25px 10%;
  display:flex;
  flex-wrap: wrap;
} 
.protradepnl_main {
  display: flex;
  align-items: center;
  flex-direction: column;
  flex-wrap: wrap;
  width: 33%;
  .red {
    color: #DF334E;
  }
  .green {
    color: #309F72;
  }
  &>div{
    // width: 33.3%;
    text-align: center;
  }
}
.protradepnlKey{
  margin-top: 15px;
  margin-bottom: 5px;
}
.protradepnlVal{
  font-size: 18px;
  margin: 15px auto;
  // padding: 10px auto;
}
</style>