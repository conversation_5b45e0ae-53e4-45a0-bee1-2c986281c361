<template>
  <div class="level-cantainer">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.topid"
        size="mini"
        :placeholder="$t('filters.topIDNick')"
        style="width: 130px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <!-- <el-input
        v-model="listQuery.topnick"
        size="mini"
        :placeholder="$t('filters.topNick')"
        style="width: 130px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      /> -->
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        :placeholder="$t('filters.agent')"
        style="width: 150px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.from_coin_name"
        :placeholder="$t('filters.Source_of_currency')"
        clearable
        style="width: 120px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in coinOptions"
          :key="item.currencyid"
          :label="item.currencyname"
          :value="item.currencyname"
          :disabled="item.currencyname == listQuery.to_coin_name"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.to_coin_name"
        :placeholder="$t('filters.The_target_currency')"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in coinOptions"
          :key="item.currencyid"
          :label="item.currencyname"
          :value="item.currencyname"
          :disabled="item.currencyname == listQuery.from_coin_name"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.state"
        :placeholder="$t('tableHeader.state')"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="(val, key, idx) in stateObj"
          :key="idx"
          :label="val"
          :value="key"
        />
      </el-select>
      <span style="margin-left: 20px; font-size: 12px">{{ $t('tableHeader.clinchDealTime') }}</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('tableHeader.startDate')"
        :end-placeholder="$t('filters.endTime')"
        @change='filterTimeTransform'
      >
      </el-date-picker>

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-left: 20px;"
        @click="handleFilter"
      >
        {{ $t('buttons.search') }}
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="tableList"
      border
      fit
      highlight-current-row
      style="width: 100%; margin-top: 20px"
      size="mini"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" align="center" min-width="78"> 
        <template slot-scope="{ row }">
          <span>{{ row.userid || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center" min-width="95"> </el-table-column>
      <el-table-column :label="$t('filters.topID')" prop="top_agent_id" align="center" min-width="78">
           <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('filters.topNick')" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorID')" prop="pareid" align="center" min-width="78">
           <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.Source_of_currency')" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.from_coin_name || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.The_target_currency')" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.to_coin_name || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.entrustNum')" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{ Number(row.from_amount).cutXiaoNum(6) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('dialog.accountNum')" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{ Number(row.to_amount).cutXiaoNum(6) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.The_price')" prop="price" min-width="90px" align="center"></el-table-column>
      <el-table-column :label="$t('dialog.accountNum')" prop="real_amount" min-width="90px" align="center"></el-table-column>
      <el-table-column :label="$t('tableHeader.poundage')" prop="fee" min-width="90px" align="center"></el-table-column>
      <el-table-column :label="$t('tableHeader.state')" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{stateObj[row.state]}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.creationTime')" prop="create_time" align="center" width="75"> </el-table-column>
      <el-table-column :label="$t('tableHeader.updateTime')" prop="update_time" align="center" width="75"> </el-table-column>
    </el-table>

    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,50,100,200,300]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
//封装的api
import { userexchangelist } from "@/api/fundQuery"; 
import { getprocoinList } from "@/api/user";

export default {
  name: "exchangeQuery",
  data() {
    return {
      listLoading: false,
      total: 0,
      tableList: null,
      filterTime: [],
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        topid: "", //顶级代理id
        topnick: "", // 顶级代理昵称
        sagent: "", //代理id或者名字
        from_coin_name: "", //来源币种
        to_coin_name: "", //目标币种
        state: '', //状态 0-待审核 100-失败 200-成功
        pageNo: 1,
        pagesize: 10,
        star: '', //开始
        end: '', //结束
      },
      coinOptions: [],
      stateObj:{
        0: this.$t('filters.To_audit'),
        100: this.$t('dialog.Failure'),
        200: this.$t('dialog.Successful'),
      },
    };
  },

  components: {},

  computed: {
    // 默认时间
    // timeDefault () {
    //   let date = new Date()
    //   // 通过时间戳计算
    //   let defalutStartTime = ((date.getTime() - 1 * 24 * 3600 * 1000)/1000).toDate('yyyy-MM-dd') // 转化为时间戳
    //   let defalutEndTime = ((date.getTime()/1000)).toDate('yyyy-MM-dd')
    //   return [defalutStartTime, defalutEndTime]
    // }
  },

  mounted() {
    getprocoinList({}).then((res) => {
      this.coinOptions = res.data.filter(v=>v.status)
    })
    this.filterTime = this.timeDefault;
    this.getList();
  },

  methods: {
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList();
    },
    getList() {
      this.listLoading = true;
      let data = {}
      Object.assign(data,this.listQuery,{
        star: this.filterTime ? this.filterTime[0] + " 00:00:00" : "",
        end: this.filterTime ? this.filterTime[1] + " 23:59:59" : "",
      })
      data.sname = data.sname || undefined
      data.topid = data.topid || undefined
      data.topnick = data.topnick || undefined
      data.sagent = data.sagent || undefined
      data.from_coin_name = data.from_coin_name || undefined
      data.to_coin_name = data.to_coin_name || undefined
      data.state = data.state === ''?undefined:parseInt(data.state)

      userexchangelist(data).then((res) => {
        if(res.data.list && res.data.list.length){
          this.tableList = res.data.list
          console.log(res.data.list)
        }else{
          this.tableList = []
        }
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    filterTimeTransform(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val? val[1]+' 23:59:59':'';
    },
  },
};
</script>
<style lang="scss" scoped>
.box_se{
  border: 1px solid #c9c9c9;
  margin: 25px 10%;
  display:flex;
  flex-wrap: wrap;
} 
</style>