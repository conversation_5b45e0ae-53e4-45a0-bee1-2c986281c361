import Cookies from 'js-cookie'

const state = {
  sidebar: {
    opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,
    withoutAnimation: false
  },
  device: 'desktop',
  language: localStorage.getItem("lang") || 'zh',

  handleTypeOptions: {
    1: 'buttons.updLabal', // 修改标签
    2: 'filters.Add_the_agent',  // 添加代理
    3: 'filters.Modifying_User_Information', // 修改用户信息
    4: 'filters.Add_notes',  // 添加备注
    5: 'buttons.addLabel', // 添加标签
    6: 'filters.Adding_Label_Information', // 添加标签信息
    7: 'filters.Identity_verification', // 身份审核
    8: 'filters.Review_mention_money', // 审核提币
    9: 'filters.Modifying_label_Information', // 修改标签信息
    10: "filters.Paid_commission", // 发放返佣
    11: "others.delUserLabel", // 删除用户标签
    12: "others.addUserRisk", // 添加用户风控组
    13: "others.updUserRisk", // 更新用户风控组
    15: "others.setUserRiskWhite", // 设置用户风控白名单
    14: "others.delUserRiskWhite", // 删除用户风控白名单
    16: "others.updUserRiskWhite", // 修改用户风控组信息
    17: "others.setUserTBAudit", // 设置用户提币审核限制
    18: "others.RemoveUserTBLimit", // 解除用户提币限制
  },
  // 操作内容由 key:val 转 文字:值
  handleContentTranferText: {
    "237_uid": {  // 237-类型集合；uid-
      keyText: "tableHeader.uid", // UID
      valueType: '0', // 0-直接展示值 1-值对应文字  2-值+附加文字
      valueOptions: {}, // 值对应文字的Options
    },
    "237_petname": {
      keyText: "filters.topNick", // 顶级代理昵称
      valueType: '0',
      valueOptions: {},
    },
    "237_enablelogin": {
      keyText: "forms.loginStatus", // 登录状态
      valueType: '1',
      valueOptions: {
        1: 'filters.Allowed_to_login',
        0: 'filters.Banned_logging',
      },
    },
    "237_enablewithdraw": {
      keyText: "forms.mentionMoneyStatus", // 提币状态
      valueType: '1',
      valueOptions: {
        1: 'filters.Allowed_to_mention_money',
        0: 'filters.Prohibit_mention_money',
      },
    },
    "237_enabletrade": {
      keyText: "forms.transactionStatus", // 交易状态
      valueType: '1',
      valueOptions: {
        1: 'filters.Allowed_to_trade',
        0: 'filters.Ban_on_trading',
        2: 'filters.Ban_on_open_positions',
        3: 'filters.Ban_unwind',
      },
    },
    "237_withdraw_verify": {
      keyText: "forms.mentionMoneyConfirm", // 提币二次确认
      valueType: '1',
      valueOptions: {
        0: 'tableHeader.open',
        3: 'buttons.close',
      },
    },
    "237_upagent": {
      keyText: "filters.userType", // 用户类型
      valueType: '1',
      valueOptions: {
        1: 'filters.top_agent',
        2: 'filters.The_agent',
        3: 'filters.The_average_user',
        4: 'filters.The_proxy_directly_pushes_users',
      },
    },
    "237_agent_rebate_ratio": {
      keyText: "forms.commissionPercentage", // 手续费分佣比例
      valueType: '2',
      valueOptions: '%',
    },
    "237_rake_back_time": {
      keyText: "forms.rakeBackTime", // 发放期限
      valueType: '2',
      valueOptions: 'forms.day',
    },
    "237_is_view_profit": {
      keyText: "forms.toViewProfitLoss", // 是否可查看盈亏
      valueType: '1',
      valueOptions: {
        1: 'forms.yes',
        0: 'forms.no',
      },
    },
    "237_enable_simulator": {
      keyText: "forms.catchPlate", // 是否开通模拟盘
      valueType: '1',
      valueOptions: {
        1: 'forms.yes',
        0: 'forms.no',
      },
    },
    "237_is_view_monitor": {
      keyText: "forms.poundageMonitoring", // 是否可查看手续费监控
      valueType: '1',
      valueOptions: {
        1: 'forms.yes',
        0: 'forms.no',
      },
    },
    "237_is_open_agent": {
      keyText: "forms.canProAgent", // 可提升代理
      valueType: '1',
      valueOptions: {
        1: 'forms.yes',
        0: 'forms.no',
      },
    },
    "237_show_agent_ratio": {
      keyText: "forms.webBackRate", // 前端显示返佣比例
      valueType: '1',
      valueOptions: {
        1: 'forms.yes',
        0: 'forms.no',
      },
    },
    "237_show_position_monitor": {
      keyText: "forms.positionMonitoring", // 可查看持仓监控
      valueType: '1',
      valueOptions: {
        1: 'forms.yes',
        0: 'forms.no',
      },
    },
    "237_is_child_api": {
      keyText: "forms.othersAPIpermissions", // 其用户API管理权限
      valueType: '1',
      valueOptions: {
        1: 'forms.allow',
        0: 'forms.notAllow',
      },
    },
    "237_is_open_api": {
      keyText: "forms.APIpermissions", // API管理权限
      valueType: '1',
      valueOptions: {
        1: 'tableHeader.open',
        0: 'buttons.close',
      },
    },
    "237_login_verify_enable": {
      keyText: "forms.needcode", // 是否需要验证码
      valueType: '1',
      valueOptions: {
        1: 'forms.yes',
        0: 'forms.no',
      },
    },
    "237_is_show_dealer": {
      keyText: "forms.traders", // 可申请交易员
      valueType: '1',
      valueOptions: {
        1: 'forms.yes',
        0: 'forms.no',
      },
    },
    "237_is_legal_buy": {
      keyText: "forms.buyFiat", // 可购买法币
      valueType: '1',
      valueOptions: {
        1: 'forms.yes',
        0: 'forms.no',
      },
    },
    "237_is_legal_sell": {
      keyText: "forms.sellFiat", // 可出售法币
      valueType: '1',
      valueOptions: {
        1: 'forms.yes',
        0: 'forms.no',
      },
    },
    "237_is_back_self": {
      keyText: "forms.tradingToOneself", // 自交易返自己
      valueType: '1',
      valueOptions: {
        1: 'forms.yes',
        0: 'forms.no',
      },
    },
    "237_is_show_all_account": {
      keyText: "forms.desensitization", // 非直推用户脱敏
      valueType: '1',
      valueOptions: {
        0: 'forms.yes',
        1: 'forms.no',
      },
    },
    "237_is_edit_all_account": {
      keyText: "forms.proStraightPush", // 提升非直推
      valueType: '1',
      valueOptions: {
        1: 'forms.yes',
        0: 'forms.no',
      },
    },
    "237_can_view_subordinate": {
      keyText: "forms.allUser", // 显示所有用户
      valueType: '1',
      valueOptions: {
        1: 'forms.yes',
        0: 'forms.no',
      },
    },
    "237_is_header": {
      keyText: "forms.seeHomeData", // 是否可见首页数据
      valueType: '1',
      valueOptions: {
        1: 'forms.yes',
        0: 'forms.no',
      },
    },
    "237_parcode": {
      keyText: "filters.inviteCode", // 邀请码
      valueType: '0',
      valueOptions: {},
    },
    "237_errcode": {
      keyText: "tableHeader.error_code", // 错误码
      valueType: '0',
      valueOptions: {},
    },
    "237_stats": {
      keyText: "tableHeader.KYCstate", // KYC状态
      valueType: '1',
      valueOptions: {
        0: 'others.certificationReset',
        1: 'others.inIdentityAuthentication',
        2: 'others.authenticationFailed',
        3: 'others.passIdentityAuthentication',
        4: 'others.inIdentityAuthentication',
        5: 'others.authenticationFailed',
        6: 'others.passIdentityAuthentication',
        6: 'others.passIdentityAuthentication',
        7: 'buttons.reset'
      },
    },
    "237_errmsg": {
      keyText: "tableHeader.error_content", // 错误内容
      valueType: '0',
      valueOptions: {},
    },
    "237_upid": {
      keyText: "tableHeader.ID", // ID
      valueType: '0',
      valueOptions: {},
    },
    "237_birthday": {
      keyText: "tableHeader.time", // 申请时间
      valueType: '0',
      valueOptions: {},
    },
    "8_id": {
      keyText: "tableHeader.uid", // UID
      valueType: '0',
      valueOptions: {},
    },
    "8_pass": {
      keyText: "buttons.audit", // 审核结果
      valueType: '1',
      valueOptions: {
        'false': "buttons.refuse",
        'true': "buttons.through",
      },
    },
    "8_content": {
      keyText: "tableHeader.note", // 备注
      valueType: '1',
      valueOptions: {
        'false': "buttons.refuse",
        'true': "buttons.through",
      },
    },
    "69_label_id": {
      keyText: "ID", // 标签ID
      valueType: '0',
      valueOptions: {},
    },
    "69_contract_code": {
      keyText: "tableHeader.contractName", // 合约名称
      valueType: '0',
      valueOptions: {},
    },
    "69_max_lever": {
      keyText: "tableHeader.maxLeverage", // 最大杠杆
      valueType: '0',
      valueOptions: {},
    },
    "69_max_order_volume": {
      keyText: "tableHeader.maxOrderQuantity", // 最大下单量
      valueType: '0',
      valueOptions: {},
    },
    "69_min_order_volume": {
      keyText: "tableHeader.minOrderQuantity", // 最小下单量
      valueType: '0',
      valueOptions: {},
    },
    "69_max_posi_volume": {
      keyText: "tableHeader.maxPosition", // 最大持仓量
      valueType: '0',
      valueOptions: {},
    },
    "69_fee": {
      keyText: "tableHeader.poundageReat", // 手续费率
      valueType: '0',
      valueOptions: {},
    },
    "69_label_name": {
      keyText: "filters.labal", // 标签
      valueType: '0',
      valueOptions: {},
    },
    "69_funding": {
      keyText: "tableHeader.moneyReat", // 资金费率
      valueType: '0',
      valueOptions: {},
    },
    "69_slippage": {
      keyText: "tableHeader.maxSomeBad", // 最大点差
      valueType: '0',
      valueOptions: {},
    },
    "69_min_slippage": {
      keyText: "tableHeader.minSomeBad", // 最小点差
      valueType: '0',
      valueOptions: {},
    },
    "69_creat_time": {
      keyText: "tableHeader.creationTime", // 创建时间
      valueType: '0',
      valueOptions: {},
    },
    "69_status_stype": {
      keyText: "tableHeader.state", // 状态
      valueType: '1',
      valueOptions: {
        0: 'buttons.close',
        1: 'tableHeader.open',
      },
    },
    "69_risk_rate": {
      keyText: "tableHeader.risk", // 风险率增减
      valueType: '0',
      valueOptions: {},
    },
    "69_id": {
      keyText: "ID", // ID
      valueType: '0',
      valueOptions: {},
    },
    "69_pass": {
      keyText: "buttons.audit", // 审核结果
      valueType: '1',
      valueOptions: {
        'false': "buttons.refuse",
        'true': "buttons.through",
      },
    },
    "69_content": {
      keyText: "tableHeader.note", // 备注
      valueType: '1',
      valueOptions: {
        'false': "buttons.refuse",
        'true': "buttons.through",
      },
    },
    "5_lablname": {
      keyText: "filters.labal", // 标签
      valueType: '0',
      valueOptions: {},
    },
    "10_status": {
      keyText: "tableHeader.state", // 返佣状态
      valueType: '1',
      valueOptions: {
        1: 'filters.Stay_out',
        2: 'filters.Issued',
        3: 'filters.Has_been_cancelled',
        4: 'filters.Have_been_frozen',
      },
    },
    "10_id": {
      keyText: "ID", // ID
      valueType: '0',
      valueOptions: {},
    },
    "1718_uid": {
      keyText: "UID", // UID
      valueType: '0',
      valueOptions: {},
    },
    "1718_is_child": {
      keyText: "tableHeader.content", // 是否修改整条代理下面所有用户 
      valueType: '1',
      valueOptions: {
        0: 'others.xzhOption1',
        1: 'others.xzhOption2',
      },
    },
    "1718_withdraw_limit": {
      keyText: "tableHeader.content", // 提币限制
      valueType: '1',
      valueOptions: {
        2: 'buttons.tbxzh',
        3: 'buttons.tbxzh',
        4: 'buttons.qxtbxzh',
        5: 'buttons.qxtbxzh',
      },
    },
    "121316_group_name": {
      keyText: "tableHeader.categoryName", // 组别名称
      valueType: '0',
      valueOptions: {},
    },
    "121316_uids": {
      keyText: "tableHeader.agentUID", // 代理UID
      valueType: '0',
      valueOptions: {},
    },
    "121316_lable_id": {
      keyText: "filters.labal", // 标签
      valueType: '0',
      valueOptions: {},
    },
    "121316_min_cash_in": {
      keyText: "tableHeader.customer_total_gold", // 客户总入金
      valueType: '0',
      valueOptions: {},
    },
    "121316_min_cash_out": {
      keyText: "tableHeader.customer_total_out", // 客户总出金
      valueType: '0',
      valueOptions: {},
    },
    "121316_min_close": {
      keyText: "tableHeader.unwindNum", // 平仓次数
      valueType: '0',
      valueOptions: {},
    },
    "121316_min_trade_num": {
      keyText: "tableHeader.customer_total_gold", // 总入金区间
      valueType: '0',
      valueOptions: {},
    },
    "121316_min_pnl": {
      keyText: "tableHeader.PNL", // PNL
      valueType: '0',
      valueOptions: {},
    },
    "121316_min_commis": {
      keyText: "tableHeader.poundage", // 手续费
      valueType: '0',
      valueOptions: {},
    },
    "121316_min_catp": {
      keyText: "tableHeader.moneyCost", // 资金费用
      valueType: '0',
      valueOptions: {},
    },
    "121316_min_net_pnl": {
      keyText: "tableHeader.net_PNl", // 净PNL
      valueType: '0',
      valueOptions: {},
    },
    "121316_min_net_cash": {
      keyText: "tableHeader.customer_net_gold", // 客户净入金
      valueType: '0',
      valueOptions: {},
    },
    "121316_min_profit": {
      keyText: "tableHeader.rate_of_return", // 盈利率
      valueType: '0',
      valueOptions: {},
    },
    "121316_min_warn_profit": {
      keyText: "tableHeader.odds", // 胜率
      valueType: '0',
      valueOptions: {},
    },
    "121316_min_commis_profit": {
      keyText: "tableHeader.poundage_net_gold", // 手续费占净入金比率
      valueType: '0',
      valueOptions: {},
    },
    "121316_min_bond": {
      keyText: "tableHeader.occupancyDeposit", // 占用保证金
      valueType: '0',
      valueOptions: {},
    },
    "121316_level": {
      keyText: "tableHeader.priority", // 优先级
      valueType: '0',
      valueOptions: {},
    },
    "121316_id": {
      keyText: "ID", // ID
      valueType: '0',
      valueOptions: {},
    },
    "121316_lable_name": {
      keyText: "filters.labal", // 标签
      valueType: '0',
      valueOptions: {},
    },
    "121316_manage": {
      keyText: "tableHeader.handlers", // 操纵者
      valueType: '0',
      valueOptions: {},
    },
    "121316_status": {
      keyText: "tableHeader.state", // 状态
      valueType: '1',
      valueOptions: {
        1: 'tableHeader.effect_of',
        0: 'tableHeader.has_failure',
      },
    },
    "121316_creat_time": {
      keyText: "tableHeader.time", // 时间
      valueType: '0',
      valueOptions: {},
    },
    "1415_user_id": {
      keyText: "UID", // UID
      valueType: '0',
      valueOptions: {},
    },
    "1415_remart": {
      keyText: "tableHeader.note", // 备注
      valueType: '0',
      valueOptions: {},
    },
    "1415_id": {
      keyText: "ID", // id
      valueType: '0',
      valueOptions: {},
    },
    "1415_group_id": {
      keyText: "group_id", // id
      valueType: '0',
      valueOptions: {},
    },
  }
}

const mutations = {
  TOGGLE_SIDEBAR: state => {
    state.sidebar.opened = !state.sidebar.opened
    state.sidebar.withoutAnimation = false
    if (state.sidebar.opened) {
      Cookies.set('sidebarStatus', 1)
    } else {
      Cookies.set('sidebarStatus', 0)
    }
  },
  CLOSE_SIDEBAR: (state, withoutAnimation) => {
    Cookies.set('sidebarStatus', 0)
    state.sidebar.opened = false
    state.sidebar.withoutAnimation = withoutAnimation
  },
  TOGGLE_DEVICE: (state, device) => {
    state.device = device
  },
  setLanguage(state, value) {
    state.language = value
    // Vue.set(state,'language',value)
    localStorage.setItem("lang", value);
  },
}

const actions = {
  toggleSideBar({ commit }) {
    commit('TOGGLE_SIDEBAR')
  },
  closeSideBar({ commit }, { withoutAnimation }) {
    commit('CLOSE_SIDEBAR', withoutAnimation)
  },
  toggleDevice({ commit }, device) {
    commit('TOGGLE_DEVICE', device)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
