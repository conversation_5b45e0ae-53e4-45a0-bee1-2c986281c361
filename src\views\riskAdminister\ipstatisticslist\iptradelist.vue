<template>
  <div class="level-cantainer">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.topid"
        size="mini"
        :placeholder="$t('filters.topIDNick')"
        style="width: 130px; margin-left: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        :placeholder="$t('filters.agent')"
        style="width: 150px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.contract"
        :placeholder="$t('tableHeader.contract')"
        clearable
        style="width: 120px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in contractOptions"
          :key="item.traderpairs"
          :label="item.traderpairs"
          :value="item.traderpairs"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.offset"
        :placeholder="$t('filters.positionType')"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in accountTypeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.side"
        :placeholder="$t('filters.direction')"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in sizeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <span style="margin-left: 20px; font-size: 12px">{{$t('tableHeader.clinchDealTime')}}</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change='filterTimeTransform'
      >
      </el-date-picker>
      <el-input
        v-model="listQuery.ip_address"
        size="mini"
        placeholder="IP"
        style="width: 150px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-left: 20px; margin-top: 10px"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="tableList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <!-- <el-table-column label="UID" prop="userid" align="center" min-width="78"> </el-table-column> -->
      <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center" min-width="95"> </el-table-column>
      <el-table-column :label="$t('filters.topID')" prop="top_agent_id" align="center" min-width="78">
           <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('filters.nick')" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorID')" prop="pareid" align="center" min-width="78">
           <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.contract')" prop="contract_code" min-width="90px" align="center"></el-table-column>
      <el-table-column :label="$t('filters.positionType')" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{row.offset=='O'?$t('tableHeader.positions'):$t('tableHeader.unwind')}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.direction')" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{row.side=='S'?$t('tableHeader.sell'):$t('tableHeader.buy')}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.leverage')" align="center" min-width="60px">
        <template slot-scope="{ row }">
          <span>&times;{{ row.lever }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.number')" prop="volume" align="center" min-width="110px"> 
        <template slot-scope="{ row }">
          <span>{{ row.volume }}{{$t('others.piece')}}</span><span>/{{row.conversion}}{{row.contract_code.slice(0,-4)}}</span>
        </template>
      </el-table-column>
     <el-table-column :label="$t('tableHeader.clinchDealAveragePrice')" prop="price" align="center" min-width="90px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.poundage')" prop="commission" align="center" min-width="90px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.PNL')" prop="pnl" align="center" min-width="90px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.net_PNl')" align="center" min-width="90px">
        <template slot-scope="{ row }">
          <span>{{ row.pure_pnl}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.transactionType')" align="center" min-width="90px">
        <template slot-scope="{ row }">
          <span>{{ orderTypeObj[row.order_type]  }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.orderSource')" prop="imei" align="center" min-width="130px">
        <template slot-scope="{ row }">
          <span>{{row.fromType}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.clinchDealTime')" prop="trade_time" align="center" min-width="75"> </el-table-column>
      <el-table-column :label="$t('tableHeader.transactionNumber')" prop="trade_id" align="center" min-width="110px">
        <template slot-scope="{row}">
          <span>{{ row.trade_id }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.IP_addr')" prop="ip_address" align="center" min-width="100px">>
        <template slot-scope="{ row }">
          <span>{{ row.ip_address || '--'  }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="开仓时间" prop="last_open_time" align="center" min-width="110px"></el-table-column> -->
    </el-table>

    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[200,100,50,20]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
//封装的api
import { iptradelist } from "@/api/riskAdminister";
import { bcprocontractset } from "@/api/user";


export default {
  name: "iptradelist",
  data() {
    return {
      listLoading: false,
      total: 0,
      tableList: null,
      filterTime: [],
      listQuery: {
        sname: "", //用户id,手机号，邮箱
        topid: "", //顶级代理id
        sagent: "", //代理id或者名字
        contract: "", //合约代码
        offset: undefined, //账户模式 O：开仓，C：平仓
        side: undefined, //方向 B买S卖
        star: '', //开始
        end: '', //结束
        ip_address:"",
        pageNo: 1,
        pagesize: 20,
      },
      contractOptions: [],
      accountTypeOptions: [
        { key: 'O', name: this.$t('tableHeader.positions') },
        { key: 'C', name: this.$t('tableHeader.unwind') },
      ],
      sizeOptions: [
        { key: "B", name: this.$t('tableHeader.buy') },
        { key: "S", name: this.$t('tableHeader.sell') },
      ],
      orderTypeArr:[
        {key: 0, name: this.$t('filters.Market_orders')},
        {key: 1, name: this.$t('tableHeader.order')}, 
        {key: 2, name: this.$t('tableHeader.check_single')}, 
        {key: 4, name: this.$t('tableHeader.stop_loss_orders')}, 
        {key: 5, name: this.$t('filters.Strong_flat_sheet')},
      ],
      orderTypeObj: {
        0: this.$t('filters.Market_orders'),
        1: this.$t('tableHeader.order'), 
        2: this.$t('tableHeader.check_single'), 
        4: this.$t('tableHeader.stop_loss_orders'), 
        5: this.$t('filters.Strong_flat_sheet')
      },
      os_typeObj:{
        0: 'open_api',
        1: "android",
        2: "iOS",
        3: "WEB",
        4: "H5",
        5: "open_api",
        6: this.$t('others.system_automatically'),
      },
      //总条数，默认为0
      exportLoading: false, //导出加载中效果
    };
  },

  components: {},

  computed: {
    // 默认时间
    timeDefault () {
      let date = new Date()
      // 通过时间戳计算
      let defalutStartTime = ((date.getTime() - 1 * 24 * 3600 * 1000)/1000).toDate('yyyy-MM-dd') // 转化为时间戳
      let defalutEndTime = ((date.getTime()/1000)).toDate('yyyy-MM-dd')
      return [defalutStartTime, defalutEndTime]
    }
  },

  activated(){
    this.getList();
  },

  mounted() {
    bcprocontractset({}).then((res) => {
      this.contractOptions = res.data.filter(v=>v.isshow == 1)
    })
    this.filterTime = this.timeDefault;
    this.getList();
  },

  methods: {
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList();
    },
    getList() {
      this.listLoading = true;
      let data = {}
      Object.assign(data, this.listQuery,)
      data.side = this.listQuery.side || undefined
      data.offset = this.listQuery.offset || undefined
      data.star = (this.filterTime && this.filterTime[0]) || "";
      data.end = this.filterTime ? this.filterTime[1] + " 23:59:59" : "";
      data.order_type = data.order_type === ''?-1:data.order_type
      data.ip_address = this.$route.query.ip
      
      iptradelist(data).then((res) => {
        if(res.data.list && res.data.list.length){
          this.tableList = res.data.list.map((v)=>{
            // <span>{{orderTypeObj[row.order_type]+'--'+os_typeObj[row.order_client]}}</span>
            // order_client ==> 1: android 2: iOS 3: WEB 4: H5 0: open_api 6: 系统自动
            // order_type ==> 0: 市价单 1：计划单 2：止盈单 4：止损单 5：强平单  
            // 0: 市价单 1：计划单 2：止盈单 4：止损单 5：强平单 6：条件平仓 7-带单平仓(跟单者被动平仓) 8-带单止盈 9-带单止损 101:自主平仓 102:带单平仓 103:止盈平仓 104:止损平仓 105:强制平仓 106:带单止盈 107:带单止损 108:带单开仓

            // 带单平仓：带单交易员自主平仓所产生的跟单平仓
            // 带单止盈：带单交易员设置的止盈价触发后的平仓
            // 带单止损：带单交易员设置的止损价触发后的平仓
            // api平仓：用户通过api的平仓
            // 用户平仓：用户自主平仓
            // 止盈平仓：用户自己设置的止盈价触发后的平仓
            // 止损平仓：用户自己设置的止损价触发后的平仓
            // 系统平仓：如强制平仓等系统触发的平仓
            
            let text = v.offset == 'O' ? this.$t('tableHeader.positions') : this.$t('tableHeader.unwind')

            if(v.order_client == 0 || v.order_client == 5){
            // api
              if(v.order_type == 2){
                v.fromType = this.$t('filters.Check_surplus')+ text
              }else if(v.order_type == 4){
                v.fromType = this.$t('filters.Stop_loss')+ text
              }else if(v.order_type == 5){
                v.fromType = this.$t('filters.System')+ text
              }else{
                v.fromType = 'API'+ text
              }
            }else if(v.order_client == 6){
            // 系统自动
              v.fromType = this.$t('filters.System')+ text
            }else{
            // 用户
              if(v.order_type == 2){
                v.fromType = this.$t('filters.Check_surplus')+ text
              }else if(v.order_type == 4){
                v.fromType = this.$t('filters.Stop_loss')+ text
              }else{
                v.fromType = this.$t('filters.User')+text+'/'+this.os_typeObj[v.order_client]
              }
            }
            return v
          });
        }else{
          this.tableList = []
        }
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    filterTimeTransform(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val? val[1]+' 23:59:59':'';
    },
  },
};
</script>
<style lang="scss" scoped>
.box_se{
  border: 1px solid #c9c9c9;
  margin: 25px 10%;
  display:flex;
  flex-wrap: wrap;
} 
.protradepnl_main {
  display: flex;
  align-items: center;
  flex-direction: column;
  flex-wrap: wrap;
  width: 33%;
  .red {
    color: #DF334E;
  }
  .green {
    color: #309F72;
  }
  &>div{
    // width: 33.3%;
    text-align: center;
  }
}
.protradepnlKey{
  margin-top: 15px;
  margin-bottom: 5px;
}
.protradepnlVal{
  font-size: 18px;
  margin: 15px auto;
  // padding: 10px auto;
}
</style>