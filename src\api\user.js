import request from '@/utils/request'

//登录
export function login(data) {
    return request({
        url: '/managers/v1/comm/login',
        method: 'post',
        data: { data }
    })
}
//登录
export function upmanagepass(data) {
    return request({
        url: '/managers/v1/manager/upmanagepass',
        method: 'post',
        data: { data }
    })
}
//登录谷歌验证
export function checkVkey(data) {
    return request({
        url: '/managers/v1/comm/ckeckvkey',
        method: 'post',
        data: { data }
    })
}
//谷歌验证
export function commckeckvkey(data) {
    return request({
        url: '/managers/v1/comm/commckeckvkey',
        method: 'post',
        data: { data }
    })
}
//标签列表
export function getlabel(data) {
  return request({
    url: '/managers/v1/comm/getlabllist',
    method: 'post',
    data
  })
}
//获取合约列表
export function bcprocontractset(data) {
    return request({
        url: '/managers/v1/comm/bcprocontractset',
        method: 'post',
        data: { data }
    })
}
//获取币种列表
export function getprocoinList(data) {
    return request({
        url: '/managers/v1/comm/getprocoinList',
        method: 'post',
        data: { data }
    })
}
//获取标签关联配置列表
export function getlabllistbyid(id) {
    return request({
        url: '/managers/v1/comm/getlabllistbyid',
        method: 'post',
        data: { 
            data: {
                labelid: id
            }
        }
    })
}
//根据邮箱发送登录验证码
export function sendemailcode(data) {
    return request({
        url: '/managers/v1/comm/sendemailcode',
        method: 'post',
        data: { data }
    })
}
//校验邮箱登录验证码
export function ckeckemailcode(data) {
    return request({
        url: '/managers/v1/comm/ckeckemailcode',
        method: 'post',
        data: { data }
    })
}


// 、、测试
// export function unserinfo(data){
//     return request({
//         url:'/managers/v1/manager/bcprouserlist',
//         method:'post',
//         data:{data},
//         // baseURL: process.env.VUE_APP_BASE_API
//     })
// }

export function getInfo(token) {
    return request({
        url: '/vue-admin-template/user/info',
        method: 'get',
        params: { token }
    })
}

export function logout() {
    return request({
        url: '/vue-admin-template/user/logout',
        method: 'post'
    })
}