<template>
	<div class="configuration-download">
		<div class="btns">
			<el-button type="primary" v-if="$store.getters.roles.indexOf('configurationDownloadOperation') > -1"
				@click="handleShopAddrManagement">{{ $t('buttons.Shop_address_management') }}</el-button>
			<el-button type="primary" v-if="$store.getters.roles.indexOf('configurationDownloadOperation') > -1"
				@click="handleCreateNewVersion">{{ $t('buttons.Create_new_version') }}</el-button>
		</div>

		<el-table v-loading="listLoading" :data="tableData" border fit highlight-current-row size="mini"
			style="width: 100%; margin-top: 30px" :header-cell-style="{ background: '#F0F8FF' }">
			<el-table-column prop="version" :label="$t('forms.Digital_version_number')" align="center">
				<template slot-scope="{ row }">
					<span>{{ row.version }}</span>
				</template>
			</el-table-column>
			<el-table-column prop="os_type" :label="$t('forms.client')" align="center">
				<template slot-scope="{ row }">
					<span>{{ row.os_type == 1 ? 'Android' : 'IOS' }}</span>
				</template>
			</el-table-column>
			<el-table-column prop="valid" :label="$t('tableHeader.state')" align="center">
				<template slot-scope="{ row }">
					<span>{{ row.valid == 1 ? $t('forms.To_enable') : $t('forms.Disable') }}</span>
				</template>
			</el-table-column>
			<el-table-column prop="created_time" :label="$t('tableHeader.creationTime')" align="center" min-width="100">
				<template slot-scope="{ row }">
					<span>{{ row.created_time }}</span>
				</template>
			</el-table-column>
			<el-table-column :label="$t('tableHeader.operation')" align="center" min-width="250"
				v-if="$store.getters.roles.indexOf('configurationDownloadOperation') > -1">
				<template slot-scope="scope">
					<el-button type="primary" size="mini" icon="el-icon-circle-check" plain v-if="scope.row.valid !== 1"
						@click="handleClick(scope.row, 1)">
						{{ $t('forms.To_enable') }}
					</el-button>
					<el-button type="danger" size="mini" icon="el-icon-circle-close" plain v-if="scope.row.valid == 1"
						@click="handleClick(scope.row, 2)">
						{{ $t('forms.Disable') }}
					</el-button>
					<el-button type="info" size="mini" icon="el-icon-link" plain @click="handleClick(scope.row, 3)">
						{{ $t('menus.Add_link') }}
					</el-button>
					<el-button type="warning" size="mini" icon="el-icon-edit" plain @click="handleClick(scope.row, 4)">
						{{ $t('buttons.edit') }}
					</el-button>
				</template>
			</el-table-column>
		</el-table>

		<!-- 商店地址管理 -->
		<el-dialog :title="$t('buttons.Shop_address_management')" :visible.sync="dialogVisibleShopAddrManagement"
			width="650px" :before-close="handleCloseShopAddrManagement">

			<el-form ref="formShopAddrManagement" :model="formShopAddrManagement" label-width="100px"
				:label-position="labelPosition">
				<el-form-item label="App Store">
					<el-input v-model="formShopAddrManagement.appstore"></el-input>
				</el-form-item>
				<el-form-item label="TestFlight">
					<el-input v-model="formShopAddrManagement.testflight"></el-input>
				</el-form-item>
				<el-form-item label="Google Play">
					<el-input v-model="formShopAddrManagement.googleplay"></el-input>
				</el-form-item>
			</el-form>

			<span slot="footer" class="dialog-footer">
				<el-button @click="handleShopAddrManagementCancel">{{ $t('buttons.cancel') }}</el-button>
				<el-button type="primary" @click="handleShopAddrManagementSure">{{ $t('buttons.confirm') }}</el-button>
			</span>
		</el-dialog>

		<!-- 创建新版本号 -->
		<el-dialog
			:title="dialogVisibleCreateNewVersionType == 1 ? $t('buttons.Create_new_version') : $t('buttons.Edit_new_version')"
			:visible.sync="dialogVisibleCreateNewVersion" width="650px" :before-close="handleCloseCreateNewVersion">

			<el-form ref="formCreateNewVersion" :model="formCreateNewVersion" :rules="rulesCreateNewVersion"
				label-width="100px" :label-position="labelPosition">
				<el-form-item :label="$t('forms.Digital_version_number')" prop="numVersion">
					<el-input v-model="formCreateNewVersion.numVersion"
						oninput="value= value.match(/\d+(\d{0,2})?/) ? value.match(/\d+(\d{0,2})?/)[0] : ''"></el-input>
				</el-form-item>
				<el-form-item :label="$t('forms.Select_client')" prop="client">
					<el-select v-model="formCreateNewVersion.client" :placeholder="$t('dialog.Please_select_client')"
						filterable style="width: 100%">
						<el-option label="Android" value="1"></el-option>
						<el-option label="IOS" value="2"></el-option>
					</el-select>
				</el-form-item>
			</el-form>

			<span slot="footer" class="dialog-footer">
				<el-button @click="handleCreateNewVersionCancel">{{ $t('buttons.cancel') }}</el-button>
				<el-button type="primary" @click="handleCreateNewVersionSure">{{ $t('buttons.determine') }}</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
import {
	versionlist,                  // 列表
	addversion,                   // 新增版本号
	updateversion,                // 编辑版本号
	enableordisableversion,       // 启用/停用
	getstoreurl,                  // 获取商店地址 (来自 Redis)
	updatestoreurl,               // 商店地址管理添加
} from "@/api/systemManagement";

const defaultShopAddrManagement = () => ({
	appstore: '',     // appstore
	testflight: '',   // testflight
	googleplay: '',   // googleplay
});

export default {
	name: "configurationDownload",
	data() {
		// 创建新版本
		// 数字版本号
		let numVersion_v = (rule, value, callback) => {
			if (!value) {
				return callback(new Error(this.$t('dialog.Please_enter_number_version')))
			} else {
				callback()
			}
		}
		// 客户端
		let client_v = (rule, value, callback) => {
			if (!value) {
				return callback(new Error(this.$t('dialog.Please_select_client')))
			} else {
				callback()
			}
		}

		return {
			listLoading: false,       // 懒加载
			tableData: [],            // 表格数据

			labelPosition: 'left',    // form-label靠左
			dialogVisibleShopAddrManagement: false, // 商店地址管理
			dialogVisibleCreateNewVersion: false,   // 创建新版本号
			dialogVisibleCreateNewVersionType: 1,   // 1.创建 2.编辑

			// 商店地址管理
			formShopAddrManagement: defaultShopAddrManagement(), 
			formCreateNewVersion: {
				version_id: '',   // 数字版本id
				numVersion: '',   // 数字版本号
				client: '',       // 客户端
			},                  // 创建新版本

			rulesCreateNewVersion: {
				numVersion: [{ validator: numVersion_v, trigger: 'blur' }],   // 数字版本号
				client: [{ validator: client_v, trigger: 'change' }], // 客户端
			},                                                            // 创建新版本
		};
	},

	components: {},

	computed: {},

	mounted() {
		this.getList()
	},

	methods: {
		// 商店地址管理按钮
		handleShopAddrManagement() {
			getstoreurl().then(res => {
				const data = res.data || {};
				this.formShopAddrManagement = {
					appstore: data.app_store || '',       // appstore
					testflight: data.test_flight || '',   // testflight
					googleplay: data.google_play || '',   // googleplay
				};
			}).catch(_ => {
				this.formShopAddrManagement = defaultShopAddrManagement();
			});

			this.dialogVisibleShopAddrManagement = true;
		},
		// 商店地址管理弹框icon按钮
		handleCloseShopAddrManagement() {
			this.dialogVisibleShopAddrManagement = false;
			this.formShopAddrManagement = defaultShopAddrManagement();	// 商店地址管理
			this.$refs['formShopAddrManagement'].resetFields();
		},
		// 商店地址管理弹框取消按钮
		handleShopAddrManagementCancel() {
			this.dialogVisibleShopAddrManagement = false;
			this.formShopAddrManagement = defaultShopAddrManagement();	// 商店地址管理
			this.$refs['formShopAddrManagement'].resetFields();
		},
		// 商店地址管理弹框确认按钮
		handleShopAddrManagementSure() {
			updatestoreurl({
				app_store: this.formShopAddrManagement.appstore,
				test_flight: this.formShopAddrManagement.testflight,
				google_play: this.formShopAddrManagement.googleplay,
			}).then(() => {
				this.dialogVisibleShopAddrManagement = false;
				this.$refs['formShopAddrManagement'].resetFields();
				this.$notify({
					title: this.$t('dialog.Add_a_success'),
					type: "success",
					duration: 2000,
				});
				this.getList();
			})
		},


		// 创建新版本号按钮
		handleCreateNewVersion() {
			this.dialogVisibleCreateNewVersion = true;
			this.dialogVisibleCreateNewVersionType = 1;
		},
		// 创建新版本号弹框icon按钮
		handleCloseCreateNewVersion() {
			this.dialogVisibleCreateNewVersion = false;
			this.formCreateNewVersion = {
				numVersion: '',   // 数字版本号
				client: '',       // 客户端
			};                  // 创建新版本
			this.$refs['formCreateNewVersion'].resetFields();
		},
		// 创建新版本号弹框取消按钮
		handleCreateNewVersionCancel() {
			this.dialogVisibleCreateNewVersion = false;
			this.formCreateNewVersion = {
				numVersion: '',   // 数字版本号
				client: '',       // 客户端
			};                  // 创建新版本
			this.$refs['formCreateNewVersion'].resetFields();
		},
		// 创建新版本号弹框确定按钮
		handleCreateNewVersionSure() {
			if (this.dialogVisibleCreateNewVersionType == 1) {
				this.$refs['formCreateNewVersion'].validate((valid) => {
					if (valid) {
						addversion({
							version: Number(this.formCreateNewVersion.numVersion),
							os_type: Number(this.formCreateNewVersion.client)
						}).then(() => {
							this.dialogVisibleCreateNewVersion = false,
								this.formCreateNewVersion = {
									numVersion: '',   // 数字版本号
									client: '',       // 客户端
								},                  // 创建新版本
								this.$refs['formCreateNewVersion'].resetFields();
							this.$notify({
								title: this.$t('dialog.Add_a_success'),
								type: "success",
								duration: 2000,
							});
							this.getList();
						})
					} else {
						return false
					}
				})
			} else if (this.dialogVisibleCreateNewVersionType == 2) {
				this.$refs['formCreateNewVersion'].validate((valid) => {
					if (valid) {
						updateversion({
							version_id: Number(this.formCreateNewVersion.version_id),
							version: Number(this.formCreateNewVersion.numVersion),
							os_type: Number(this.formCreateNewVersion.client)
						}).then(() => {
							this.dialogVisibleCreateNewVersion = false,
								this.formCreateNewVersion = {
									numVersion: '',   // 数字版本号
									client: '',       // 客户端
								},                  // 创建新版本
								this.$refs['formCreateNewVersion'].resetFields();
							this.$notify({
								title: this.$t('dialog.Edit_successful'),
								type: "success",
								duration: 2000,
							});
							this.getList();
						})
					} else {
						return false
					}
				})
			}
		},


		// 操作
		handleClick(v, type) {
			switch (type) {
				case 1:
					if (v.valid !== 1) {
						this.$confirm(this.$t('dialog.Whether_or_not_to_enable_this_version'), {
							distinguishCancelAndClose: true,
							confirmButtonText: this.$t('buttons.determine'),
							cancelButtonText: this.$t('buttons.cancel')
						}).then(() => {
							enableordisableversion({
								version_id: v.version_id,
								valid: 1
							}).then(() => {
								this.$notify({
									title: this.$t('dialog.Operation_is_successful'),
									type: "success",
									duration: 2000,
								});
								this.getList();
							})
						}).catch(action => { })
					} else {
						return false
					}
					break;
				case 2:
					if (v.valid !== 0) {
						this.$confirm(this.$t('dialog.Whether_or_not_disable_this_version'), {
							distinguishCancelAndClose: true,
							confirmButtonText: this.$t('buttons.determine'),
							cancelButtonText: this.$t('buttons.cancel')
						}).then(() => {
							enableordisableversion({
								version_id: v.version_id,
								valid: 0
							}).then(() => {
								this.$notify({
									title: this.$t('dialog.Operation_is_successful'),
									type: "success",
									duration: 2000,
								});
								this.getList();
							})
						}).catch(action => { })
					} else {
						return false
					}
					break;
				case 3:
					this.$router.push({
						path: '/systemManagement/addLink',
						query: { version_id: v.version_id, version: v.version, os_type: v.os_type, valid: v.valid }
					})
					break;
				case 4:
					this.dialogVisibleCreateNewVersion = true;
					this.dialogVisibleCreateNewVersionType = 2;
					this.formCreateNewVersion.version_id = v.version_id       // 版本id
					this.formCreateNewVersion.numVersion = v.version;         // 数字版本号
					this.formCreateNewVersion.client = v.os_type.toString();  // 客户端
					break;
			}
		},

		getList() {
			this.listLoading = true;
			versionlist().then((res) => {
				console.log(res)
				this.tableData = res.data.list;
				this.listLoading = false;
			}).catch((err) => {
				console.error("Error calling versionList: ", err);
				this.listLoading = false;
			})
		},
	},
};
</script>

<style lang="scss" scoped>
.configuration-download {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	padding: 20px;

	.btns {
		width: 100%;
		flex-shrink: 0;
		box-sizing: border-box;
		display: flex;
		align-items: center;
		justify-content: flex-end;

		.el-button {
			margin: 0 10px;
		}
	}

	.btn {
		margin: 0 10px;
		cursor: pointer;
	}

	// ::v-deep .el-dialog {
	//   .el-dialog__header {
	//     display: flex;
	//     justify-content: center;
	//     align-items: center;
	//     font-weight: bold;
	//   }
	// }
}
</style>
