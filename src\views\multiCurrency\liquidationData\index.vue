<template>
  <div class="liquidation-data">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.contract_code"
        :placeholder="$t('tableHeader.contract')"
        clearable
        style="width: 120px; margin-right: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in contractOptions"
          :key="item.traderpairs"
          :label="item.traderpairs"
          :value="item.traderpairs"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.marginCurrency"
        :placeholder="$t('filters.Margin_currency')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in marginCurrencyOptions"
          :key="item.currencyid"
          :label="item.currencyname"
          :value="item.currencyname"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.account_type"
        :placeholder="$t('filters.positionType')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in accountTypeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.side"
        :placeholder="$t('filters.direction')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in sizeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <span style="margin: 0 10px; font-size: 12px">{{
        $t("tableHeader.clinchDealTime")
      }}</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px; margin-right: 20px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change="filterTimeTransform"
      >
      </el-date-picker>
      <el-input
        size="mini"
        v-model="listQuery.tradeid"
        :placeholder="$t('tableHeader.transactionNumber')"
        style="width: 150px; margin-top: 10px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-right: 20px; margin-top: 10px"
        @click="handleFilter"
      >
        {{ $t("buttons.search") }}
      </el-button>
      <el-button
        class="filter-item"
        v-if="$store.getters.roles.indexOf('USDLiquidationDataexport')>-1"
        :loading="exportLoading"
        style="margin-top: 10px"
        @click="handleExport"
        size="mini"
        type="success"
      >
        {{$t('buttons.export')}}
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="levelList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')"           prop="userid" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')"      prop="user_name" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('tableHeader.contract')"      prop="contractcode" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('filters.Margin_currency')"   prop="currency_name" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('filters.positionType')"      prop="accounttype" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{row.accounttype==1?$t('tableHeader.all_warehouse'):$t('tableHeader.by_warehouse')}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.direction')"         prop="side" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{row.side=='S'?$t('tableHeader.sell'):$t('tableHeader.buy')}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.leverage')"      prop="lever" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>&times;{{ row.lever || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.number')"        prop="volume" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.volume }}</span>&nbsp;<span>{{row.currency_name}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.clinchDealAveragePrice')" prop="price" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('tableHeader.poundage')"      prop="commission" align="center" min-width="78"> </el-table-column>
      <!-- <el-table-column :label="$t('tableHeader.PNL')"           prop="closeprofit" align="center" min-width="78"> </el-table-column> -->
      <el-table-column :label="$t('tableHeader.PNL')"           prop="netpnl" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ Number(row.netpnl).add(row.commission) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.net_PNl')"       prop="netpnl" align="center" min-width="78"> </el-table-column>
      <!-- <el-table-column :label="$t('tableHeader.clear_fee')"     prop="blowing_fee" align="center" min-width="78"> </el-table-column> -->
      <el-table-column :label="$t('tableHeader.Transaction_value')"   prop="tradevalue" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('tableHeader.clinchDealTime')" prop="trade_time" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('tableHeader.transactionNumber')" prop="tradeid" align="center" min-width="78"> </el-table-column>
    </el-table>

    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,20,30,50]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getstopout, getstopoutexport, bcusdprocontractset } from "@/api/multiCurrency"
import { getprocoinList } from "@/api/user";
export default {
  name: "liquidationData",
  data() {
    return {
      listLoading: false,
      exportLoading: false,
      total: 0,
      levelList: [],
      filterTime: [],
      listQuery: {
        tradeid: "",              // 交易编号
        sname: "",                // 用户id,手机号，邮箱
        account_type: undefined,  // 仓位类型 1：全仓 2：逐仓
        contract_code: "",        // 合约
        side: undefined,          // 方向 B买S卖
        marginCurrency: "",       // 保证金币种
        pageNo: 1,
        pagesize: 10,
        star: "",                 // 开始
        end: "",                  // 结束
      },
      contractOptions: [],        // 合约
      marginCurrencyOptions: [],  // 保证金币种
      accountTypeOptions: [
        { key: 1, name: this.$t('tableHeader.all_warehouse') },
        { key: 2, name: this.$t('tableHeader.by_warehouse') },
      ], // 仓位类型
      sizeOptions: [
        { key: "B", name: this.$t('tableHeader.buy') },
        { key: "S", name: this.$t('tableHeader.sell') },
      ], // 方向
    };
  },
  components: {},

  computed: {
    // 默认时间
    // timeDefault() {
    //   let date = new Date();
    //   // 通过时间戳计算
    //   let defalutStartTime = ((date.getTime() - 1 * 24 * 3600 * 1000) / 1000 ).toDate("yyyy-MM-dd"); // 转化为时间戳
    //   let defalutEndTime = ((date.getTime() / 1000)).toDate("yyyy-MM-dd");
    //   return [defalutStartTime, defalutEndTime];
    // },
  },

  mounted() {
    getprocoinList({}).then((res) => {
      for(var i=0; i<res.data.length; i++) {
        if(res.data[i].currencyname == "USDT") {
          res.data.splice(i,1)
        }
      }
      this.marginCurrencyOptions = res.data.filter(v => v.status)
    })
    bcusdprocontractset({}).then((res) => {
      this.contractOptions = res.data.filter(v=>v.isshow == 1)
    })
    this.filterTime = this.timeDefault;
    this.getList()
  },

  methods: {
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList()
    },
    // 时间
    filterTimeTransform(val) {
      this.listQuery.star = (val && val[0]) || "";
      this.listQuery.end = val ? val[1] + " 23:59:59" : "";
    },
    getList() {
      this.listLoading = true
      let data = {};
      this.listQuery.star = (this.filterTime && this.filterTime[0]) || "";
      this.listQuery.end = this.filterTime
        ? this.filterTime[1] + " 23:59:59"
        : "";
      Object.assign(data, {
        sname: this.listQuery.sname,                        // string 账号
        account_type: Number(this.listQuery.account_type),  // int 仓位类型
        contract_code: this.listQuery.contract_code,        // string 合约
        tradeid: this.listQuery.tradeid,                    // string
        side: this.listQuery.side || null,                  // 方向
        currency_name: this.listQuery.marginCurrency,       // 保证金币种
        star: this.listQuery.star,                          // string 开始时间
        end: this.listQuery.end,                            // string 结束时间
        pageNo: this.listQuery.pageNo,                      // int 页数
        pagesize: this.listQuery.pagesize,                  // int 分页数量
      })
      getstopout(data).then((res) => {
        // console.log(res)
        this.levelList = res.data.list
        this.total = res.data.total
        this.listLoading = false
      })
    },
    handleExport() {
      this.exportLoading = true;
      let data = {};
      Object.assign(data,this.listQuery)
      let newDate = parseInt(new Date().getTime()/1000);
      data.end = this.listQuery.end ? this.listQuery.end : (newDate.toDate('yyyy-MM-dd')+ ' 23:59:59')
      newDate = parseInt(newDate-518400)
      data.star = this.listQuery.star ? (this.listQuery.star + ' 00:00:00' ): (newDate.toDate('yyyy-MM-dd')+ ' 00:00:00')
      getstopoutexport(data).then((res) => {
        if(res.ret == 0){
            window.location.href=res.data.download_url;
            this.exportLoading = false
        }
      }).catch(err=>{
        this.exportLoading = false
      })
    },
  },
};
</script>

<style>
</style>