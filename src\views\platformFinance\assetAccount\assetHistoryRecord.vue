<template>
  <div class="historyrecord-container">
    <el-table
      v-loading="listLoading"
      :data="assetHistoryList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.time')" prop="createat" align="center" min-width="90px" >
      </el-table-column>
      <el-table-column :label="$t('tableHeader.transferUID')" prop="userid" align="center" min-width="110" >
      </el-table-column>
      <el-table-column :label="$t('filters.currency')" prop="coinname" align="center" min-width="80px" />
      <el-table-column :label="$t('filters.type')" align="center" min-width="100px">
        <template slot-scope="{ row }">
          <span>{{optypeObj[row.optype]}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.changeNum')" prop="amount" min-width="90px" align="center"/>
      <el-table-column :label="$t('tableHeader.remainingAvailableBalance')" prop="amountafter" min-width="90px" align="center">
        <template slot-scope="{row}">
          <span>{{row.amountafter || '--'}}</span>
        </template>
      </el-table-column>

      <el-table-column :label="$t('others.operator')" align="center" min-width="120px">
        <template slot-scope="{ row }">
          <span>{{ row.opusername || $t('others.system_automatically')}}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { platwalletdaillist } from "@/api/platformFinance";
export default {
  name: "assetHistoryRecord",
  data() {
    return {
      //表格加载中效果
      listLoading: false,
      assetHistoryList: null,
      total: 0,
      //页数页码
      listQuery: {
        star: "", //开始
        end: "", //结束
        pageNo: 1,
        pagesize: 10,
      },
      optypeObj: {
        1: this.$t('filters.Transaction_commission'),
        4: this.$t('filters.Withdrawal_commission'),
        8: this.$t('filters.Profit_and_loss_of_income'),
        9: this.$t('tableHeader.moneyCost'),
        100: this.$t('filters.Platform_account_charging_coins'),
        200: this.$t('filters.Platform_account_withdrawal'),
        300: this.$t('filters.Shift_to'),
        301: this.$t('filters.Transfer_to_a_trading_account'),
        302: this.$t('filters.Transfer_to_the_asset_account'),
        310: this.$t('filters.RollOut'),
        311: this.$t('filters.Transfer_it_out_to_the_trading_account'),
        312: this.$t('filters.Transfer_out_to_the_asset_account'),
        320: this.$t('filters.Platform_cash_withdrawal_commission'),
        330: this.$t('filters.Invitation_commission_Award'),
        331: this.$t('filters.Agency_commission_Award'),
        332: this.$t('filters.Drop'),
        333: this.$t('filters.C2C_settlement'),
        334: this.$t('filters.C2C_Other_uses'),
        340: this.$t('filters.Analog_disk_reclaiming_assets'),
        341: this.$t('filters.Campaign_spending'),
        343: this.$t('filters.Airdrop_rewards'), // "空投奖励",
        344: this.$t('filters.Fee_activity'), // "手续费活动",
        345: this.$t('filters.system_error'), // "系统故障",
        346: this.$t('filters.Marketing_activities'), // "营销活动",
        347: this.$t('filters.Rebate_rewards'), // "返佣奖励",
        350: this.$t('filters.Liquidation_fee'), // "爆仓手续费",
        351: this.$t('filters.Full_warehouse_wear'), // "全仓穿仓",
        360: this.$t('filters.Abnormal_asset_deduction_asset'), // "异常资产扣除(用户资产账户),",
        361: this.$t('filters.Abnormal_asset_deduction_trading'), // "异常资产扣除(用户交易账户),",
        362: this.$t('filters.Abnormal_asset_deduction_documentary'), // "异常资产扣除(用户跟单账户) ",
        363: this.$t('filters.Currency_exchange_fee'), // "兑币手续费",
        364: this.$t('filters.Cash_out'), // "兑出",
        365: this.$t('filters.Cash_in'), // "兑入",
        366: this.$t('filters.Transfer_out_to_deposit_and_withdrawal_account'), // "转出到充提账户",
        367: this.$t('filters.Transfer_from_deposit_and_withdrawal_account'), // "从充提账户转入",
        368: this.$t('filters.Transfer_from_currency_exchange_account'), // "从兑币账户转入",
        369: this.$t('filters.Transfer_from_asset_account'), // "从资产账户转入",
      },
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.getList();
  },

  methods: {
    getList() {
      this.listLoading = true;
      platwalletdaillist(this.listQuery).then((response) => {
        this.assetHistoryList = response.data.list;
        this.total = response.data.total;
        this.listLoading = false;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
</style>