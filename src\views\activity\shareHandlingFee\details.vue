 <template>
  <div class="rebatelistdetail_wrap">
    <div class="parList_wrap">
      <table style="table-layout: fixed; border-collapse: collapse">
        <tr>
          <th>{{$t('filters.eventDate')}}</th>
          <th>{{$t('tableHeader.activityName')}}</th>
          <th>{{$t('tableHeader.activityDescribe')}}</th>
          <th>{{$t('tableHeader.jackpotHighestCeiling')}}</th>
          <th>{{$t('tableHeader.injectionPoundageProportion')}}</th>
          <th>{{$t('tableHeader.startDate')}}</th>
          <th>{{$t('filters.endTime')}}</th>
          <th>{{$t('tableHeader.eachIssueStartTime')}}</th>
          <th>{{$t('tableHeader.eachIssueLotteryTime')}}</th>
          <th>{{$t('tableHeader.eachIssueEndTime')}}</th>
          <th>{{$t('tableHeader.activityState')}}</th>
          <th>{{$t('tableHeader.alreadySentReward')}}</th>
        </tr>

        <tr>
          <td>{{ (rebateList && rebateList.own_day) || "--" }}</td>
          <td>{{ rebateList.activity_name || "--" }}</td>
          <td>{{ rebateList.describe || "--" }}</td>
          <td>{{ rebateList.limit_up || "--" }}</td>
          <td>{{ rebateList.ratio }}</td>
          <td>{{ rebateList.start_day }}</td>
          <td>{{ rebateList.end_day }}</td>
          <td>
            <span
              v-html="
                rebateList.start_time
                  ? rebateList.start_time.split(' ')[0] +
                    '<br/>' +
                    rebateList.start_time.split(' ')[1]
                  : '--'
              "
            ></span>
          </td>
          <td>
            <span
              v-html="
                rebateList.opening_time
                  ? rebateList.opening_time.split(' ')[0] +
                    '<br/>' +
                    rebateList.opening_time.split(' ')[1]
                  : '--'
              "
            ></span>
          </td>
          <td>
            <span
              v-html="
                rebateList.end_time
                  ? rebateList.end_time.split(' ')[0] +
                    '<br/>' +
                    rebateList.end_time.split(' ')[1]
                  : '--'
              "
            ></span>
          </td>
          <td>
            <span>{{ rebateList.ac_status ? $t('others.ongoing') : $t('others.finished') }}</span>
          </td>
          <td>{{ rebateList.lssued }}</td>
        </tr>
      </table>
    </div>

    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-select
        size="mini"
        v-model="listQuery.grade"
        :placeholder="$t('tableHeader.state')"
        clearable
        style="width: 120px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="(item, index) in gradeOptionsArr"
          :key="index"
          :label="item.name"
          :value="item.key"
        />
      </el-select>

      <el-select
        size="mini"
        v-model="listQuery.check_status"
        :placeholder="$t('tableHeader.state')"
        clearable
        style="width: 120px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="(item, index) in stateOptions"
          :key="index"
          :label="item.name"
          :value="item.key"
        />
      </el-select>

      <el-date-picker
        style="width: 220px; margin-top: 10px; margin-left: 20px"
        class="picker"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change="filterTimeTransform"
      >
      </el-date-picker>
      <el-button
        class="filter-item"
        style="margin-left: 10px"
        size="mini"
        type="primary"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px"
        size="mini"
        type="primary"
        :disabled="!multipleSelection.length"
        @click="oneClickSendrebot(1)"
      >
        {{$t('buttons.A_key_through')}}
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px"
        size="mini"
        type="warning"
        :disabled="!multipleSelection.length"
        @click="oneClickSendrebot(2)"
      >
        {{$t('buttons.A_key_refuse')}}
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="rebateDetailList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
      @selection-change="handleSelectionChange">
    >
      <el-table-column :selectable="checkSelect" label-class-name="DisabledSelection" align="center" type="selection" width="70px"></el-table-column>
      <el-table-column :label="$t('tableHeader.uid')" prop="uid" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.user_id || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.userName')"
        prop="user_name"
        align="center"
        min-width="90px"
      />
      <el-table-column
        :label="$t('tableHeader.transactionTime')"
        prop="open_time"
        align="center"
        min-width="95px"
      >
        <template slot-scope="{ row }">
          <span
            v-html="
              row.open_time
                ? row.open_time.split(' ')[0] +
                  '<br/>' +
                  row.open_time.split(' ')[1]
                : '--'
            "
          ></span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.transactionNumber')"
        prop="order_id"
        align="center"
        min-width="78"
      />
      <el-table-column
        :label="$t('tableHeader.rewardType')"
        prop="order_id"
        align="center"
        min-width="95px"
      >
        <template slot-scope="{ row }">
          <span>{{ gradeOptions[row.grade] || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.amount')"
        prop="bonus"
        align="center"
        min-width="90px"
      />
      <el-table-column
        :label="$t('tableHeader.uploadPhotos')"
        prop="share_img"
        align="center"
        min-width="100px"
      >
        <template slot-scope="{ row }">
          <div>
            <el-image
              v-if="row.share_img"
              style="width: 50px"
              :src="row.share_img"
              :preview-src-list="[row.share_img]"
            >
            </el-image>
            <span v-else>--</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.state')"
        prop="check_status"
        align="center"
        min-width="100px"
      >
        <template slot-scope="{ row }">
          <span>{{ statusOption[row.check_status] || "--" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('filters.auditTime')"
        prop="check_time"
        align="center"
        min-width="90px"
      >
        <template slot-scope="{ row }">
          <span>{{ row.check_time == "null" ? "--" : row.check_time }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.operation')" v-if="$store.getters.roles.indexOf('useractivitycheck') > -1" align="center" min-width="150px">
        <template slot-scope="{ row }">
          <el-button
            :disabled="row.check_status != 0"
            type="primary"
            size="mini"
            @click="handleBy(row, 1)"
            >{{$t('buttons.through')}}</el-button
          >
          <el-button
            :disabled="row.check_status != 0"
            type="warning"
            size="mini"
            @click="handleBy(row, 2)"
            >{{$t('buttons.rejected')}}</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[100,200,300,500]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="rebatelistDetail"
    />
  </div>
</template>

<script>
import { getuseractivitylist, useractivitycheck } from "@/api/activity";

export default {
  name: "feeDetails",
  data() {
    return {
      listLoading: false,

      rebateDetailList: null, //接受详情数据

      rebateList: {
        activity_name: "",
        current_bonus: 0,
        describe: "",
        end_day: "",
        end_time: "",
        first_prize: 0,
        hash: "",
        id: 0,
        limit_up: "",
        lssued: "",
        opening_time: "",
        own_day: "",
        ratio: 0,
        second_award: 0,
        set_bonus: 0,
        start_day: "",
        start_time: "",
        third_award: 0,
      }, //详情带进来的数据
      filterTime: [],
      gradeOptionsArr: [
        { key: 1, name: this.$t('filters.First_prize') },
        { key: 2, name: this.$t('filters.Second_prize') },
        { key: 3, name: this.$t('filters.Third_prize') },
      ],
      stateOptions: [
        { key: -1, name: this.$t('filters.Images_to_be_uploaded') },
        { key: 0, name: this.$t('filters.To_audit') },
        { key: 1, name: this.$t('buttons.audit_through') },
        { key: 2, name: this.$t('filters.Has_refused_to') },
        { key: 3, name: this.$t('filters.Expired') },
      ],
      gradeOptions: {
        1: this.$t('filters.First_prize'),
        2: this.$t('filters.Second_prize'),
        3: this.$t('filters.Third_prize'),
      },
      statusOption: {
        "-1": this.$t('filters.Images_to_be_uploaded'),
        0: this.$t('filters.To_audit'),
        1: this.$t('buttons.audit_through'),
        2: this.$t('filters.Has_refused_to'),
        3: this.$t('filters.Expired'),
      },
      listQuery: {
        sname: "", // 用户
        uid: "", // 名字或者手机号，
        grade: undefined, // 奖励类型 1一等奖 2二等奖 3 三等奖，
        check_status: undefined, // 状态  -1待领取，0待审核，1审核通过 2拒绝，3过期 ，
        start: "", // ，
        end: "", // ，
        pageNo: 1,
        pagesize: 100,
      },
      total: 0,
      multipleSelection: [],
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.rebateList = JSON.parse(this.$route.query.dt);
    this.rebatelistDetail();
  },

  methods: {
    // 一键通过
    oneClickSendrebot(type){
      if(this.multipleSelection.length){
        this.$confirm(this.$t(type==1?'dialog.Confirm_one_click_pass':'dialog.Confirm_one_click_refuse'))
        .then(_ => {
          this.multipleSelection.forEach((v,i)=>{
            useractivitycheck({ id: v.id, ctype: type }).then((res) => {
              if(this.multipleSelection.length-1 === i){
                this.$notify({
                  title: this.$t('tableHeader.operation'),
                  message: this.$t('dialog.Operation_is_successful'),
                  type: "success",
                  duration: 2000,
                })
                this.rebatelistDetail();
              }
            });
          })
        })
        .catch(_ => {});
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    checkSelect (row,index) {
      let isChecked = true;
      if (row.check_status === 0) { // 判断里面是否存在某个参数
        isChecked = true
      } else {
        isChecked = false
      }
      return isChecked
    },
    // 通过 点击事件
    handleBy(row, type) {
      this.$confirm(` ${this.$t('dialog.Confirm_audit')} ${type == 1 ? this.$t('buttons.through') : this.$t('buttons.rejected')}?`, "", {
        confirmButtonText: this.$t('buttons.determine'),
        cancelButtonText: this.$t('buttons.cancel'),
        type: type == 1 ? "success" : "warning",
      })
        .then(() => {
          useractivitycheck({ id: row.id, ctype: type }).then((res) => {
            this.$notify({
              title: this.$t('dialog.Successful'),
              message: this.$t('dialog.Operation_is_successful'),
              type: "success",
              duration: 2000,
            });
            this.rebatelistDetail();
          });
        })
        .catch(() => {});
    },
    rebatelistDetail() {
      this.listLoading = true;
      let data = {};
      Object.assign(data, this.listQuery, {
        grade: this.listQuery.grade || 0,
        check_status:
          this.listQuery.check_status !== 0
            ? this.listQuery.check_status || -2
            : 0,
        acid: this.rebateList.id
      });
      getuseractivitylist(data).then((res) => {
        this.rebateDetailList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.rebatelistDetail();
    },
    filterTimeTransform(val) {
      this.listQuery.star = (val && val[0]) || "";
      this.listQuery.end = (val && val[1] + " 23:59:59") || "";
    },
  },
};
</script>
<style lang="scss">
.rebatelistdetail_wrap{
  /*表格表头全选*/
	.el-table .DisabledSelection .cell .el-checkbox__inner{
	  margin-left: -30px;
	  position:relative;
	}
	.el-table .DisabledSelection .cell:before{
	  content:"全选";
	  position:absolute;
	  right:11px;
	}
}

</style>
<style lang="scss" scoped>
.rebatelistdetail_wrap {
  .parList_wrap {
    table {
      width: 95%;
      border: 1px solid #c0c4cc;
      border-collapse: collapse;
      margin-bottom: 15px;
      font-size: 14px;
      margin: 20px auto;
      background: #f7f9fc;
      tr {
        width: 100%;
        text-align: center;
        height: 40px;
        th {
          width: 10%;
          border: 1px solid #c0c4cc;
          font-size: 14px;
          height: 40px;
          white-space: nowrap; /* 自适应宽度*/
          word-break: keep-all; /* 避免长单词截断，保持全部 */
          white-space: pre-line;
          word-break: break-all !important;
          word-wrap: break-word !important;
          display: table-cell;
          vertical-align: middle !important;
          white-space: normal !important;
          vertical-align: text-top;
          padding: 2px 2px 0 2px;
        }
        td {
          width: 10%;
          border: 1px solid #c0c4cc;
          white-space: nowrap; /* 自适应宽度*/
          word-break: keep-all; /* 避免长单词截断，保持全部 */
          white-space: pre-line;
          word-break: break-all !important;
          word-wrap: break-word !important;
          display: table-cell;
          vertical-align: middle !important;
          white-space: normal !important;
          vertical-align: text-top;
          padding: 2px 2px 0 2px;
          display: table-cell;
        }
      }
    }
  }
}
</style>