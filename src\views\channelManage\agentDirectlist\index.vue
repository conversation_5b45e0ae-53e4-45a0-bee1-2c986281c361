<template>
  <div class="asset-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.user_id"
        size="mini"
        :placeholder="$t('tableHeader.uid')"
        style="width: 180px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.agentid"
        size="mini"
        :placeholder="$t('filters.topAgentUID')"
        style="width: 180px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-right: 20px; font-size: 12px">{{$t('filters.backRate')}}≥</span>
      <el-input
        v-model="listQuery.agentrebateratio"
        size="mini"
        :placeholder="$t('filters.backRate')"
        style="width: 180px; margin-right: 5px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <span style="margin-right: 20px; font-size: 12px">{{$t('tableHeader.time')}}</span>
        <!-- :picker-options="pickerOptions" -->
      <el-date-picker
        style="width: 220px; margin-top: 10px; margin-right: 10px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change="filterTimeTransform"
      >
      </el-date-picker>
      <el-button
        style="margin-top: 10px; margin-right: 10px"
        class="filter-item"
        size="mini"
        type="primary"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
      <!-- <el-button
        class="filter-item"
        :loading="exportLoading"
        v-if="$store.getters.roles.indexOf('') > -1"
        @click="handleExport"
        size="mini"
        type="success"
      >
        导出
      </el-button> -->
    </div>

    <el-tabs v-model="activeName" @tab-click="handleClick" style="margin: 15px 15px">
      <el-tab-pane label="USDT" name="USDT">
        <el-table
          v-loading="listLoading"
          :data="tableData"
          border
          fit
          highlight-current-row
          style="width: 100%; margin-top: 30px"
          size="mini"
          :header-cell-style="{ background: '#F0F8FF' }"
        >
          <el-table-column :label="$t('tableHeader.uid')" prop="user_id" align="center" min-width="78">
            <template slot-scope="{row}">
              <span>{{ row.user_id }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.regNum')" prop="register_amount" align="center" min-width="78"></el-table-column>
          <el-table-column :label="$t('tableHeader.net_gold')" prop="pure_deposit" align="center" min-width="78"></el-table-column>
          <el-table-column :label="$t('tableHeader.turnover')" prop="turnover" align="center" min-width="78"></el-table-column>
          <el-table-column :label="$t('tableHeader.poundage')" prop="commission" align="center" min-width="78"></el-table-column>
          <el-table-column :label="$t('filters.backRate')" prop="agent_rebate_ratio" align="center" min-width="78"></el-table-column>
          <el-table-column :label="$t('tableHeader.commissionMoney')" prop="agent_rebate_commission" align="center" min-width="78"></el-table-column>
          <el-table-column :label="$t('tableHeader.Get_the_total_amount_of_bonus')" prop="child_gift_get" align="center" min-width="78"></el-table-column>
          <el-table-column :label="$t('tableHeader.Total_use_of_bonus')" prop="child_gift_used" align="center" min-width="78"></el-table-column>
          <el-table-column :label="$t('tableHeader.poundage')" prop="child_gift_commiss" align="center" min-width="78"></el-table-column>
          <el-table-column :label="$t('tableHeader.PNL')" prop="pnl" align="center" min-width="78"></el-table-column>
          <el-table-column :label="$t('tableHeader.net_PNl')" prop="pure_pnl" align="center" min-width="78"></el-table-column>
          <el-table-column :label="$t('tableHeader.Their_own_PNL')" prop="self_pnl" align="center" min-width="78"></el-table-column>
          <el-table-column :label="$t('tableHeader.Self_handling_fee')" prop="self_commis" align="center" min-width="78"></el-table-column>
          <el-table-column :label="$t('tableHeader.PNL_directly_under')" prop="child_pnl" align="center" min-width="78"></el-table-column>
          <el-table-column :label="$t('tableHeader.Direct_commission')" prop="child_commis" align="center" min-width="78"></el-table-column>
          <el-table-column :label="$t('tableHeader.Num_of_direct_transactions')" prop="child_trade" align="center" min-width="78"></el-table-column>
          <el-table-column :label="$t('tableHeader.Number_Subordinate_Registrations')" prop="child_reg_num" align="center" min-width="78"></el-table-column>
          <el-table-column :label="$t('tableHeader.P1')" prop="user_id" align="center" min-width="78">
            <template slot-scope="{row}">
              <span>{{ row.pnl - row.agent_rebate_commission }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.P2')" prop="user_id" align="center" min-width="78">
            <template slot-scope="{row}">
              <span>{{ row.pnl && (row.commission / row.pnl)  }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.Subordinate_rebate_data')" prop="child_rebate_commiss" align="center" min-width="78"></el-table-column>
          <el-table-column :label="$t('tableHeader.profitNum')" prop="profit_person" align="center" min-width="78">
            <template slot-scope="{row}">
              <div>{{row.profit_person+"/"+((row.trade_person && (row.profit_person / row.trade_person)*100) || 0)+'%'  }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.transactionsPerCapita')" prop="trade_num" align="center" min-width="78"></el-table-column>
          <el-table-column :label="$t('tableHeader.dayLive')" prop="dau" align="center" min-width="78"></el-table-column>
          <el-table-column :label="$t('others.frozenMargin')" prop="margin" align="center" min-width="78"></el-table-column>
          <el-table-column :label="$t('tableHeader.floatProfitLoss')" prop="float_profit" align="center" min-width="78"></el-table-column>
          <el-table-column :label="$t('filters.regTime')" prop="reg_time" align="center" min-width="78"></el-table-column>
          <el-table-column :label="$t('tableHeader.operation')" align="center" width="100">
            <template slot-scope="{ row }">
              <el-button
                size="mini"
                type="primary"
                @click="detailClick(row)"
                >{{$t('buttons.toView')}}</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      
      <el-tab-pane label="USD"  name="USD">
        <el-table
          v-loading="listLoading"
          :data="tableData"
          border
          fit
          highlight-current-row
          style="width: 100%; margin-top: 30px"
          size="mini"
          :header-cell-style="{ background: '#F0F8FF' }"
        >
          <el-table-column :label="$t('tableHeader.uid')" prop="user_id" align="center" min-width="78">
            <template slot-scope="{row}">
              <span>{{ row.user_id }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.regNum')" prop="register_amount" align="center" min-width="78">
            <template slot-scope="{row}">
              <div v-for="(item,index) in row.usdagentlist" :key="index">{{ item.register_amount }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.net_gold')" prop="pure_deposit" align="center" min-width="78">
            <template slot-scope="{row}">
              <div v-for="(item,index) in row.usdagentlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.pure_deposit }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.turnover')" prop="turnover" align="center" min-width="78">
            <template slot-scope="{row}">
              <div v-for="(item,index) in row.usdagentlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.turnover }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.poundage')" prop="commission" align="center" min-width="78">
            <template slot-scope="{row}">
              <div v-for="(item,index) in row.usdagentlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.commission }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('filters.backRate')" prop="agent_rebate_ratio" align="center" min-width="78">
            <template slot-scope="{row}">
              <div v-for="(item,index) in row.usdagentlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.agent_rebate_ratio }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.commissionMoney')" prop="agent_rebate_commission" align="center" min-width="78">
            <template slot-scope="{row}">
              <div v-for="(item,index) in row.usdagentlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.agent_rebate_commission }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.Get_the_total_amount_of_bonus')" prop="child_gift_get" align="center" min-width="78">
            <template slot-scope="{row}">
              <div v-for="(item,index) in row.usdagentlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.child_gift_get }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.Total_use_of_bonus')" prop="child_gift_used" align="center" min-width="78">
            <template slot-scope="{row}">
              <div v-for="(item,index) in row.usdagentlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.child_gift_used }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.poundage')" prop="child_gift_commiss" align="center" min-width="78">
            <template slot-scope="{row}">
              <div v-for="(item,index) in row.usdagentlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.child_gift_commiss }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.PNL')" prop="pnl" align="center" min-width="78">
            <template slot-scope="{row}">
              <div v-for="(item,index) in row.usdagentlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.pnl }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.net_PNl')" prop="pure_pnl" align="center" min-width="78">
            <template slot-scope="{row}">
              <div v-for="(item,index) in row.usdagentlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.pure_pnl }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.Their_own_PNL')" align="center" min-width="78">
            <template slot-scope="{row}">
              <div v-for="(item,index) in row.usdagentlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.self_pnl }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.Self_handling_fee')" align="center" min-width="78">
            <template slot-scope="{row}">
              <div v-for="(item,index) in row.usdagentlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.self_commis }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.PNL_directly_under')" align="center" min-width="78">
            <template slot-scope="{row}">
              <div v-for="(item,index) in row.usdagentlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.child_pnl }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.Direct_commission')" align="center" min-width="78">
            <template slot-scope="{row}">
              <div v-for="(item,index) in row.usdagentlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.child_commis }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.Num_of_direct_transactions')" align="center" min-width="78">
            <template slot-scope="{row}">
              <div v-for="(item,index) in row.usdagentlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.child_trade }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.Number_Subordinate_Registrations')" align="center" min-width="78">
            <template slot-scope="{row}">
              <div v-for="(item,index) in row.usdagentlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.child_reg_num }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.P1')" prop="user_id" align="center" min-width="78">
            <template slot-scope="{row}">
              <div v-for="(item,index) in row.usdagentlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.pnl - item.agent_rebate_commission }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.P2')" prop="user_id" align="center" min-width="78">
            <template slot-scope="{row}">
              <div v-for="(item,index) in row.usdagentlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.pnl && (item.commission / item.pnl) }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.Subordinate_rebate_data')" align="center" min-width="78">
            <template slot-scope="{row}">
              <div v-for="(item,index) in row.usdagentlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.child_rebate_commiss }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.profitNum')" prop="profit_person" align="center" min-width="78">
            <template slot-scope="{row}">
              <div v-for="(item,index) in row.usdagentlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.profit_person+"/"+((item.trade_person && (item.profit_person / item.trade_person)*100) || 0)+'%' }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.transactionsPerCapita')" prop="trade_num" align="center" min-width="78">
            <template slot-scope="{ row }">
              <div v-for="(item,index) in row.usdagentlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.trade_num }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.dayLive')" prop="dau" align="center" min-width="78">
            <template slot-scope="{ row }">
              <div v-for="(item,index) in row.usdagentlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.dau }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('others.frozenMargin')" prop="margin" align="center" min-width="78">
            <template slot-scope="{ row }">
              <div v-for="(item,index) in row.usdagentlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.margin }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.floatProfitLoss')" prop="float_profit" align="center" min-width="78">
            <template slot-scope="{ row }">
              <div v-for="(item,index) in row.usdagentlist" :key="index">{{ item.currency_name }}&nbsp;{{ item.float_profit }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('filters.regTime')" prop="reg_time" align="center" min-width="78">
            <template slot-scope="{ row }">
              <div v-for="(item,index) in row.usdagentlist" :key="index">{{ item.reg_time }}</div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tableHeader.operation')" align="center" width="100">
            <template slot-scope="{ row }">
              <el-button
                size="mini"
                type="primary"
                @click="detailClick(row)"
                >{{$t('buttons.toView')}}</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>

    
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10, 20, 50, 100]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
// 封装api
import { agenttotallist,usdagenttotallist } from "@/api/channelManage";

export default {
  name: "agentDirectlist",
  data() {
    return {
    //   pickerOptions: {
    //     disabledDate: (time) => {
    //       let defalutStartTime = new Date().getTime() - 30 * 24 * 3600 * 1000; // 转化为时间戳
    //       return (
    //         time.getTime() >= Date.now() || time.getTime() <= defalutStartTime
    //       );
    //     },
    //   },
      listLoading: false,
      total: 0,
      filterTime: [],
      tableData: null,
      listQuery: {
        user_id: '',   // 用户ID
        agentid: '',   //代理ID
        agentrebateratio: '',
        pageNo: 1,
        pagesize: 10,
        star: "",
        end: "",
      },
      exportLoading: false, //导出加载中效果

      activeName: 'USDT',
    };
  },
  components: {},

  computed: {
    // 默认时间
    // timeDefault() {
    //   let date = new Date();
    //   // 通过时间戳计算
    //   let defalutStartTime = (
    //     (date.getTime() - 1 * 24 * 3600 * 1000) /
    //     1000
    //   ).toDate("yyyy-MM-dd"); // 转化为时间戳
    //   let defalutEndTime = (date.getTime() / 1000).toDate("yyyy-MM-dd");
    //   return [defalutStartTime, defalutStartTime];
    // },
  },

  mounted() {
    // this.filterTime = this.timeDefault;
    this.getList();
  },

  methods: {
    // tab切换
    handleClick() {
      this.getList()
    },
    
    //点击查看跳转详情页
    detailClick(row){
      // var user_id = JSON.parse(row.user_id)
      this.$router.push({
        path: "/riskAdminister/highwinningdetails",
        query: { 
          id:JSON.parse(row.user_id),
          details: JSON.stringify(row)
        },
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.page = 1;
      this.getList();
    },
    //  渲染table列表
    getList() {
      this.listLoading = true;
      let data = {};
      Object.assign(data, this.listQuery);
      data.agentrebateratio = parseInt(this.listQuery.agentrebateratio)
      // data.coinname = this.activeName
      data.star = (this.filterTime && this.filterTime[0]) || "";
      data.end = this.filterTime && this.filterTime[1]
        ? this.filterTime[1] + " 23:59:59"
        : "";
      if (this.activeName == 'USDT') {
        agenttotallist(data).then((res) => {
          this.tableData = res.data.list;
          this.total = res.data.total;
          this.listLoading = false;
        });
      } else if (this.activeName == 'USD') {
        usdagenttotallist(data).then((res) => {
          this.tableData = res.data.list;
          this.total = res.data.total;
          this.listLoading = false;
        });
      }
    },
    //表格导出功能
    handleExport() {
      this.exportLoading = true;
      let data = {};
      Object.assign(data, this.listQuery);
      data.star = (this.filterTime && this.filterTime[0]) || "";
      data.end = this.filterTime ? this.filterTime[1] : "";
      userpnlexport(data)
        .then((res) => {
          if (res.ret == 0) {
            this.$notify.success({
              title: this.$t('dialog.Operation_is_successful'),
              message: this.$t('dialog.Please_jiaoyi_daochu_download'),
            });
            this.exportLoading = false;
          }
        })
        .catch((err) => {
          this.exportLoading = false;
        });
    },
    // filterTimeTransform(val) {
    //   this.listQuery.star = (val && val[0] + " 00:00:00") || "";
    //   this.listQuery.end = (val && val[1] + " 23:59:59") || "";
    // },
    filterTimeTransform(val) {
      this.listQuery.star = (val && val[0]) || "";
      this.listQuery.end = val && val[1] ? val[1] + " 23:59:59" : "";
    },
  },
};
</script>
<style lang="scss" scoped>
.asset-container {
  .filter-container {
    .highSwitch_wrap {
      margin-top: 15px;
      width: 100px;
      cursor: pointer;
      font-size: 14px;
    }
  }
  .select_wrap {
    // padding: 0 30px 0 30px;
    display: flex;
    align-items: center;
    span {
      width: 100px;
      // padding-right: 20px;
    }
  }
}
</style>