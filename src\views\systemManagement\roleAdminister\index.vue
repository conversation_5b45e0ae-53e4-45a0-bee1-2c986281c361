<template>
  <div class="role-container">
    <div class="filter-container">
      <div>
        <el-input
          size="mini"
          v-model="listQuery.name"
          :placeholder="$t('tableHeader.userName')"
          style="width: 150px; margin-right: 20px"
          clearable
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
        <!-- <el-select
          size="mini"
          v-model="listQuery.modelid"
          placeholder="分组名称"
          clearable
          style="width: 120px; margin-left: 20px"
          class="filter-item"
        >
          <el-option
            v-for="item in options"
            :key="item.model_id"
            :label="item.modelname"
            :value="item.model_id">
          </el-option>
        </el-select> -->
        <!-- <span style="margin-left: 20px; font-size: 12px">创建时间</span>
        <el-date-picker
          style="width: 220px; margin-top: 10px"
          v-model="listQuery.transaction_time"
          size="mini"
          type="daterange"
          range-separator="-"
          start-placeholder="起始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker> -->
        <el-button
          class="filter-item"
          size="mini"
          type="primary"
          @click="handleFilter"
        >
          {{$t('buttons.search')}}
        </el-button>
        <el-button size="mini" type="success" v-if="$store.getters.roles.indexOf('manageadd')>-1" @click="createClick()"
          >{{$t('buttons.addAccount')}}</el-button
        >
      </div>
    </div>

    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column type="index" align="center"></el-table-column>
      <el-table-column prop="name" align="center" :label="$t('tableHeader.userName')" > </el-table-column>
      <el-table-column align="center" :label="$t('others.email')">
        <template slot-scope="{ row }">
          <span>{{row.loginemail || '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="model_name" align="center" :label="$t('tableHeader.role_of_grouping')" > </el-table-column>
      <el-table-column prop="reg_time" align="center" :label="$t('tableHeader.creationTime')"> </el-table-column>
      <el-table-column prop="is_view" align="center" :label="$t('tableHeader.whether_desensitization')">
        <template slot-scope="{ row }">
          <span>{{row.is_view?$t('forms.yes'):$t('forms.no')}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.operation')"  align="center" width="330" v-if="$store.getters.roles.indexOf('managedel')>-1 || $store.getters.roles.indexOf('managesave')>-1 || $store.getters.roles.indexOf('managesave1')>-1 || $store.getters.roles.indexOf('managesave2')>-1" >
        <template slot-scope="{ row, $index }">
          <el-button size="mini" type="primary" v-if="$store.getters.roles.indexOf('managedel')>-1"  @click="deleteClick(row, $index)"
            >{{$t('buttons.delete')}}</el-button
          >
          <el-button size="mini" type="primary" v-if="$store.getters.roles.indexOf('managesave')>-1" @click="updataClick(row)">{{$t('buttons.modify')}}</el-button>
          <el-button size="mini" type="primary" v-if="$store.getters.roles.indexOf('managesave1')>-1" @click="handlezhmm(row)">{{$t('buttons.reset_passwords')}}</el-button>
          <el-button size="mini" type="primary" v-if="$store.getters.roles.indexOf('managesave2')>-1"  @click="handleggmm(row)">{{$t('buttons.reset_google')}}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagina-tion
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
    <el-dialog
      :title="handleType === 1?$t('buttons.addAccount'):$t('buttons.change_groups')"
      width="75%"
      v-dialogDrag
      :visible.sync="dialogVisible"
      round
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="create"
        label-position="left"
        label-width="auto"
      >
        <el-form-item v-if="handleType === 1" :label="$t('forms.account')" prop="name">
          <el-input v-model="create.name"  :placeholder="$t('filters.support_English_digital')"  clearable />
        </el-form-item>
        <el-form-item  :label="$t('forms.account')" v-if="this.handleType != 1">
          <span class="spanblock nameinput"  >{{create.name}}</span>
        </el-form-item>
        <el-form-item :label="$t('others.email')" prop="loginemail">
          <el-input v-model="create.loginemail"  :placeholder="$t('forms.safety_verify_email')"  clearable />
        </el-form-item>
        <el-form-item :label="$t('forms.grouping_name')" prop="modelid">
          <el-select
            v-model="create.modelid"
            style="width: 100%"
            :placeholder="$t('filters.select_group')"
          >
            <el-option
              v-for="item in options"
              :key="item.model_id"
              :label="item.modelname"
              :value="item.model_id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('tableHeader.whether_desensitization')">
          <el-radio-group v-model="create.is_view">
            <el-radio :label="0">{{$t('forms.no')}}</el-radio>
            <el-radio :label="1">{{$t('forms.yes')}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogEntry()">
          {{$t('buttons.determine')}}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { managelist, moldelist, managesave, manageadd, managedel } from "@/api/systemManagement";
import { validEmail } from "@/utils/validate";

export default {
  name: "roleAdminister",
  data() {
    let validatename = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('dialog.The_account_cannot_be_empty')));
      }else if (!/^[0-9a-zA-Z]+$/.test(value)) {
        callback(new Error(this.$t('login.Only_English_Number')));
      }else if (!/^\w{4,16}$/.test(value)) {
        callback(new Error(this.$t('login.Length_4_16')));
      } else {
        callback();
      }
    };
    let validateloginemail = (rule, value, callback) => {
      if(value && !validEmail(value)){
        callback(new Error(this.$t('forms.input_format_error')));
      }else{
        callback();
      }
    }
    return {
      options: [],
      listLoading: false,
      listQuery: {
        pageNo: 1,
        pagesize: 10,
        name: undefined,
      },
      tableData: null,
      total: 0,
      handleType: 1, // 1添加,2修改
      dialogVisible: false, //控制添加对话框的显示和隐藏
      create: {
        name: undefined,
        modelid: undefined,
        pwd: '123abc',
        pwd2: '123abc',
        is_view: 0,
        loginemail: '',
      },
      rules: {
        name: [{ validator: validatename, trigger: "blur" }],
        loginemail: [{ validator: validateloginemail, trigger: "blur" }],
        modelid: [
          { required: true, message: this.$t('dialog.Please_select_a_group_name'), trigger: "change" },
        ],
      },
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.getList();
    moldelist({pageNo: 1, pagesize: 9999,}).then((res) => {
      this.options = res.data.list;
    });
  },

  methods: {
    //渲染table数据
    getList() {
      var that = this;
      //开始有加载中效果
      that.listLoading = true;
      // console.log(that.listQuery)
      managelist(that.listQuery).then((res) => {
        that.tableData = res.data.list;
        that.total = res.data.total;
        this.listLoading = false;
      });
    },
    //删除
    deleteClick(row, index) {
      this.$confirm(this.$t('dialog.Confirm_deletion'), this.$t('dialog.Prompt'), {
        confirmButtonText: this.$t('buttons.determine'),
        cancelButtonText: this.$t('buttons.cancel'),
        type: 'warning'
      }).then(() => {
        managedel({upid: row.id}).then((res)=>{
          this.$notify({ title: this.$t('dialog.Successful'),message:res.msg,type: 'success'});
          if(this.tableData.length-1 === 0){
            //删除后已经不再有数据 页数减1
            this.listQuery.pageNo -= 1;
            this.listQuery.pageNo = this.listQuery.pageNo<1?1:this.listQuery.pageNo;
          }
          this.getList();
        })
      })
    },
    //修改
    updataClick(row) {
      this.handleType = 2
      this.dialogVisible = true;
      this.create.name = row.name;
      this.create.modelid = row.model_id;
      this.create.upid = row.id;
      this.create.is_view = row.is_view;
      this.create.loginemail = row.loginemail;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },

    //重置账号密码
    handlezhmm(row){
      this.$confirm(this.$t('dialog.Are_you_sure_to_reset_the_account_password'), this.$t('dialog.Prompt'), {
        confirmButtonText: this.$t('dialog.determine'),
        cancelButtonText: this.$t('dialog.cancel'),
        type: 'warning'
      }).then(() => {
        managesave({
          upid: row.id,
          pwd: this.create.pwd,
          pwd2: this.create.pwd2,
          modelid: row.model_id,
          vkey: "",
        }).then(() => {
          this.$notify({
            title: this.$t('dialog.Reset_the_success'),
            type: "success",
            duration: 2000,
          });
          this.getList();
        });
      })
    },

    //重置谷歌密码
    handleggmm(row){
      this.create.upid = row.id;
      this.$confirm(this.$t('dialog.Are_you_sure_to_reset_your_Google_password'), this.$t('dialog.Prompt'), {
        confirmButtonText: this.$t('buttons.determine'),
        cancelButtonText: this.$t('buttons.cancel'),
        type: 'warning'
      }).then(() => {
        managesave({
          upid: row.id,
          vkey: "1",
          modelid: row.level,
        }).then(() => {
          this.$notify({
            title: this.$t('dialog.Reset_the_success'),
            type: "success",
            duration: 2000,
          });
          this.getList();
        });
      })
    },
    
    //添加账号
    createClick() {
      this.create.name = null;
      this.create.modelid = null;
      this.create.is_view = 0;
      this.create.loginemail = '';
      this.handleType = 1
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    //添加对话框点击确定
    dialogEntry() {
      this.$refs["dataForm"].validate((valid) =>{
        if (valid) {
          if(this.handleType == 1){
            this.$confirm(this.$t('dialog.Confirm_to_add'), this.$t('dialog.Prompt'), {
              confirmButtonText: this.$t('buttons.determine'),
              cancelButtonText: this.$t('buttons.cancel'),
              type: 'warning'
            }).then(() => {
              manageadd(this.create).then(() => {
                this.dialogVisible = false;
                this.$notify({
                  title: this.$t('dialog.Add_a_success'),
                  type: "success",
                  duration: 2000,
                });
                this.getList();
              });
            })
          }else{
            managesave({
              name: this.create.name,
              modelid: this.create.modelid,
              upid: this.create.upid,
              is_view: this.create.is_view,
              loginemail: this.create.loginemail,
            }).then(() => {
              this.dialogVisible = false;
              this.$notify({
                title: this.$t('others.Modify_the_success'),
                type: "success",
                duration: 2000,
              });
              this.getList();
            });
          }
        }
      });
    },
    // 搜索事件
    handleFilter() {
      this.getList();
    },
  },
};
</script>
<style lang="scss" scoped>
</style>