<template>
  <div class="contractMarket_wrap">
    <el-card
      class="box_card"
      ref="barparent"
      :style="{ width: width, height: height }"
    >
      <el-table
        :row-class-name="tableRowClassName"
        size="mini"
        :height="tableHeight"
        :data="tableData"
        border
        style="width: 100%"
      >
        <el-table-column prop="code"  :label="$t('tableHeader.contract')"></el-table-column>
        <el-table-column prop="buy" :label="$t('others.buy_a_price')"></el-table-column>
        <el-table-column prop="price" :label="$t('others.latest_price')"> </el-table-column>
        <el-table-column prop="sell" :label="$t('others.Sold_for_a_price')"> </el-table-column>
      </el-table>
    </el-card>
    <audio  id="audio">
        <source src="@/assets/music/ding.wav" type="audio/ogg">
    </audio>
  </div>
</template>

<script>
var myVideo = undefined;
export default {
  props: {
    contractCode: {
      type: String,
      default: "BTCUSDT",
    },
    width: {
      type: String,
      default: "400px",
    },
    height: {
      type: String,
      default: "300px",
    },
  },
  data() {
    return {
      socket: "",
      tableData: [],
      tableHeight: 0,
      lastPrice: '',
      lastSellPrice: '',
      lastBuyPrice: '',
    };
  },
  mounted() {
    // 初始化
    this.init();
    this.$nextTick(() => {
      const cardHeight = this.$refs.barparent.$el.clientHeight;
      this.tableHeight = cardHeight - 20;
    });
    this.$nextTick(()=>{
      myVideo = document.getElementById("audio");
    })
  },
  // watch: {
  //   contractCode: {
  //     deep: true,
  //     handler(val) {
  //     }
  //   }
  // },
  methods: {
    init: function () {
      if (typeof WebSocket === "undefined") {
        alert(this.$t('dialog.Your_browser_does_not_support_sockets'));
      } else {
        // 实例化socket
        this.socket = new WebSocket(process.env.VUE_APP_WSAPI + "/v1/ws/");
        // 监听socket连接
        this.socket.onopen = this.open;
        // 监听socket错误信息
        this.socket.onerror = this.error;
        // 监听socket消息
        this.socket.onmessage = this.getMessage;
        // 监听socket关闭
        this.socket.onclose = this.close;
      }
    },
    tableRowClassName({ row, rowIndex }) {
     for (const q in this.tableData) {
       if (row.price > row.sell || row.price < row.buy) {
        // if(myVideo){
        //   myVideo.play();
        // }
        return 'info-row'
       }
     }
    },
    open() {
      console.log("socket连接成功");
      this.send(
        JSON.stringify({
          action: "sub",
          topic: "market.contract.switch",
          data: {
            contract_code: this.contractCode,
          },
        })
      );
    },
    error() {
      console.log("连接错误");
    },
    getMessage(msg) {
      let ifpush = true,buyPrice=0,sellPrice=0;
      let objData = JSON.parse(msg.data)
      if(objData.topic == 'market.ticker'){
        this.lastPrice = objData.data.list[0].trade_price;
      }
      if(objData.topic == 'market.depth'){
        buyPrice = objData.data.buy[0] && objData.data.buy[0].price;
        sellPrice = objData.data.sell[0] && objData.data.sell[0].price;
        if(buyPrice == this.lastBuyPrice || sellPrice == this.lastSellPrice){
          ifpush = false 
        }else{
          this.lastBuyPrice = buyPrice
          this.lastSellPrice = sellPrice
        }
        if(ifpush){
          this.tableData.unshift({
            code: this.contractCode,
            buy: this.lastBuyPrice,
            price: this.lastPrice,
            sell: this.lastSellPrice,
          })
        }
      }
      
      
      if(this.tableData.length>100){
        this.tableData.length = 100
      }
    },
    send(params) {
      this.socket.send(params);
    },
    close(e) {
      console.log("socket已经关闭");
      if(e.code != 4999){
        setTimeout(()=>{
          this.send(
            JSON.stringify({
              action: "sub",
              topic: "market.contract.switch",
              data: {
                contract_code: this.contractCode,
              },
            })
          );
        },5000)
      }
    },
  },
  destroyed() {
    // 销毁监听
    this.socket.close(4999)
  },
};
</script>
<style lang="scss">
.contractMarket_wrap {
  .el-table .info-row {
    &>td:nth-of-type(3){
      color:red;
    }
  }
  .box_card {
    .el-card__body {
      padding: 10px;
    }
  }
}
</style>