<template>
  <div class="asset-container">
    <div class="filter-container">
      <el-select
        size="mini"
        v-model="listQuery.coinname"
        :placeholder="$t('filters.currency')"
        clearable
        style="width: 150px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      >
        <el-option
          v-for="item in currencyOptions"
          :key="item.currencyid"
          :label="item.currencyname"
          :value="item.currencyname"
        />
      </el-select>
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px; margin-left: 20px;"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        :placeholder="$t('filters.topIDtopNick')"
        style="width: 180px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        :placeholder="$t('filters.agent')"
        style="width: 180px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-button
        style="margin-top: 10px; margin-left: 10px"
        class="filter-item"
        size="mini"
        type="primary"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
    </div>
     

    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
      :default-sort = "{prop: 'netpnl', order: 'descending'}"
      @sort-change="changeTableSort"
    >
      <el-table-column :label="$t('filters.currency')" prop="currency_name" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('tableHeader.uid')" prop="user_id" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center" min-width="95"> </el-table-column>
      <el-table-column :label="$t('filters.topID')" prop="top_agent_id" align="center" min-width="78">
          <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('filters.topNick')" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorID')" prop="pareid" align="center" min-width="78">
          <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="上级用户名" align="center" min-width="100px">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column> -->
      <el-table-column sortable="custom" :label="$t('tableHeader.float_PNL')" prop="ratepnl" align="center" min-width="90px"> </el-table-column>
      <el-table-column sortable="custom" :label="$t('others.unwind_PNL')" prop="closepnl" align="center" min-width="100px"> </el-table-column>
      <el-table-column sortable="custom" :label="$t('tableHeader.net_PNl')" prop="netpnl" align="center" min-width="125"> </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,50,100,200,300]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
// 封装api
import { getuserpnl,getuserpnl2 } from "@/api/fundQuery";
import { getprocoinList } from "@/api/user";

export default {
  name: "pnlQuery",
  data() {
    return {
      listLoading: false,
      total: 0,
      tableData: null,
      listQuery: {
        coinname: "", // 币种
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        orderside: 0, //排序
        stype: 2,  // 1正序 2反序
        pageNo: 1,
        pagesize: 10,
      },

      currencyOptions: [],  // 币种
    };
  },
  components: {},

  computed: {},

  mounted() {
    getprocoinList(({reverse_enable:1})).then((res) => {
      this.currencyOptions = res.data.filter(v => v.status)
      // console.log(this.currencyOptions)
    })
    this.getList();
  },
  
  methods: {
    changeTableSort(column){
      if(column.order == 'ascending'){
        this.listQuery.stype = 1;
      }else{
        this.listQuery.stype = 2
      }
      if(column.prop == 'netpnl'){
        this.listQuery.orderside = 0;
      }else if(column.prop == 'closepnl'){
        this.listQuery.orderside = 1;
      }else if(column.prop == 'ratepnl'){
        this.listQuery.orderside = 2;
      }
      this.getList();
    },
    //  渲染table列表
    getList() {
      this.listLoading = true;
      let data = {}
      Object.assign(data,this.listQuery,{
        web_id:1,
        // coinname: this.listQuery.currency_name,
      })
      getuserpnl2(data).then((res) => {
        // console.log(res)
        this.tableData = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.page = 1;
      this.getList();
    },
  },
};
</script>
<style lang="scss" scoped>
.asset-container{
  .filter-container{
    .highSwitch_wrap{
      margin-top: 15px; 
      width: 100px; 
      cursor: pointer;
      font-size: 14px;
    }
  }
  .select_wrap{
    // padding: 0 30px 0 30px;
    display: flex;
    align-items: center;
    span{
      width:100px;
      // padding-right: 20px;
    }
  }
}
</style>