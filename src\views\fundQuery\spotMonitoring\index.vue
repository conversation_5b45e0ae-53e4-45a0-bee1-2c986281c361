<template>
  <div class="spotMonitoring">
    <div class="filter-container">
      <el-select
        size="mini"
        v-model="listQuery.trading"
        :placeholder="$t('filters.Trading')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in tradingOptions"
          :key="item.key"
          :label="item.traderpairs"
          :value="item.traderpairs"
        />
      </el-select>
      <span style="margin-right: 20px; font-size: 12px">{{ $t('tableHeader.time') }}</span>
      <el-date-picker
        style="width: 220px; margin-right: 20px; margin-top: 10px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change='filterTimeTransform'
      >
      </el-date-picker>

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-top: 10px"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="levelList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column prop="contract_code" :label="$t('filters.Trading')" align="center" min-width="78"></el-table-column>
      <el-table-column prop="buy_amount"    :label="$t('tableHeader.Buy_amount')" align="center" min-width="78"></el-table-column>
      <el-table-column prop="buy_balnce"    :label="$t('tableHeader.Buy_balnce')" align="center" min-width="78"></el-table-column>
      <el-table-column prop="buy_fee"       :label="$t('tableHeader.Buy_fee')" align="center" min-width="78"></el-table-column>
      <el-table-column prop="buy_arg"       :label="$t('tableHeader.Buy_arg')" align="center" min-width="78"></el-table-column>
      <el-table-column prop="sell_amount"   :label="$t('tableHeader.Sell_amount')" align="center" min-width="78"></el-table-column>
      <el-table-column prop="sell_balnce"   :label="$t('tableHeader.Sell_balnce')" align="center" min-width="78"></el-table-column>
      <el-table-column prop="sell_fee"      :label="$t('tableHeader.Sell_fee')" align="center" min-width="78"></el-table-column>
      <el-table-column prop="sell_arg"      :label="$t('tableHeader.Sell_arg')" align="center" min-width="78"></el-table-column>
      <el-table-column prop="buy_arg,sell_arg"      :label="$t('tableHeader.Spread')" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ Number(row.buy_arg) - Number(row.sell_arg) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="own_time"      :label="$t('tableHeader.time')" align="center" min-width="78"></el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,20,30,50]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { usermonitor, futuresconcode } from "@/api/spotTrading"
export default {
  name: "spotMonitoring",
data() {
    return {
      listLoading: false,
      total: 0,
      levelList: null,
      filterTime: [],
      listQuery: {
        trading: "",    // 交易对
        pageNo: 1,
        pagesize: 10,
        star: '',       // 开始
        end: '',        // 结束
      },
      tradingOptions: [],   // 交易对
    };
  },

  components: {},

  computed: {
    // 默认时间
    // timeDefault () {
    //   let date = new Date()
    //   // 通过时间戳计算
    //   let defalutStartTime = ((date.getTime() - 1 * 24 * 3600 * 1000)/1000).toDate('yyyy-MM-dd') // 转化为时间戳
    //   let defalutEndTime = ((date.getTime()/1000)).toDate('yyyy-MM-dd')
    //   return [defalutStartTime, defalutEndTime]
    // }
  },

  mounted() {
    futuresconcode({}).then((res) => {
      this.tradingOptions = res.data
    })
    this.filterTime = this.timeDefault;
    this.getList();
  },

  methods: {
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    // 获取数据
    getList() {
      this.listLoading = true;
      let data = {};
      this.listQuery.star = (this.filterTime && this.filterTime[0]) || "";
      this.listQuery.end = this.filterTime
        ? this.filterTime[1] + " 23:59:59"
        : "";
      Object.assign(data, {
        contract_code: this.listQuery.trading,        // 交易对
        star: this.listQuery.star,                    // string 开始时间
        end: this.listQuery.end,                      // string 结束时间
        pageNo: this.listQuery.pageNo,                // int 页数
        pagesize: this.listQuery.pagesize,            // int 分页数量
      })
      usermonitor(data).then((res) => {
        // console.log(res)
        this.levelList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
      })
    },

    filterTimeTransform(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end = val? val[1]+' 23:59:59':'';
    },
  },
};
</script>

<style lang="scss" scoped>
</style>