<template>
  <div class="welcomeHome-container">
    <!-- <p>欢迎使用平台管理系统</p> -->
    <div v-if="$store.getters.roles.indexOf('zhsjChart')>-1 || $store.getters.roles.indexOf('xzyhChart')>-1 || $store.getters.roles.indexOf('yhsbChart')>-1 || $store.getters.roles.indexOf('appxzChart')>-1" class="header">
      <h4>{{$t('menus.home_title')}}</h4>
      <el-select @change="timeChange" v-model="selectTime" :placeholder="$t('dialog.pleaseSelect')">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
    </div>
    <div class="centerWrap" v-else>
      <h4>{{$t('menus.home_title')}}</h4>
    </div>
    <el-row :gutter="24">
      <el-col v-if="$store.getters.roles.indexOf('zhsjChart')>-1" class="item_wrap first_wrap" :xs="22" :sm="22" :lg="11">
        <el-tabs v-model="firstType" @tab-click="firstTypeChange">
          <el-tab-pane :label="$t('tableHeader.on_the_user')" name="daily_life"></el-tab-pane>
          <el-tab-pane :label="$t('tableHeader.weeklyActiveUsers')" name="weekly_life"></el-tab-pane>
          <el-tab-pane :label="$t('tableHeader.monthlyActiveUsers')" name="month_life"></el-tab-pane>
          <el-tab-pane :label="$t('tableHeader.net_gold')" name="netcash"></el-tab-pane>
          <el-tab-pane :label="$t('tableHeader.trading_volume')" name="traders"></el-tab-pane>
          <el-tab-pane :label="$t('tableHeader.NumberOfTransactions')" name="traders_users"></el-tab-pane>
        </el-tabs>
        <div class="chart-wrapper">
          <line-chart ref="lineChart" :chart-data="firstChartData" :className="'chart1'" :width="'100%'" />
        </div>
      </el-col>
      <el-col v-if="$store.getters.roles.indexOf('xzyhChart')>-1" class="item_wrap second_wrap" :xs="22" :sm="22" :lg="11">
        <div class="chart-wrapper">
          <line-chart2 :chart-data="secondChartData" />
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col v-if="$store.getters.roles.indexOf('yhsbChart')>-1" class="item_wrap third_wrap" :xs="22" :sm="22" :lg="11">
        <p class="title">{{$t('tableHeader.user_equipment')}}</p>
        <div class="chart-wrapper">
          <bar-chart :chart-data="thirdChartData" />
        </div>
      </el-col>
      <el-col v-if="$store.getters.roles.indexOf('appxzChart')>-1" class="item_wrap fourth_wrap" :xs="22" :sm="22" :lg="11">
        <p class="title">{{$t('filters.app_download')}}</p>
        <div class="chart-wrapper">
          <bar-chart2 :chart-data="fourthChartData" />
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
//引入封装接口
import {
  getdaytotal,
} from "@/api/userQuery";
import { mapGetters } from 'vuex'
import LineChart from './components/LineChart'
import LineChart2 from './components/LineChart2'
import LineChart3 from './components/LineChart3'
import BarChart from './components/BarChart'
import BarChart2 from './components/BarChart2'

let firstChartData = {
  daily_life: {
    dataList: [],
    dataTime: [],
    unit: '数量',
    legend: undefined,
  },
  weekly_life: {
    dataList: [],
    dataTime: [],
    unit: '数量',
    legend: undefined,
  },
  month_life: {
    dataList: [],
    dataTime: [],
    unit: '数量',
    legend: undefined,
  },
  netcash: {
    dataList: [],
    dataTime: [],
    unit: '金额',
    legend: undefined,
  },
  traders: {
    dataList: [],
    dataTime: [],
    unit: '数量',
    legend: undefined,
  },
  traders_users: {
    dataList: [],
    dataTime: [],
    unit: '数量',
    legend: undefined,
  },
}
export default {
  name: 'welcomeHome',
  data() {
    return {
      firstType: 'daily_life',
      firstChartData: firstChartData.daily_life,
      secondChartData: {
        dataList: [],
        dataTime: [],
        dataList2: [],
      },
      thirdChartData: {
        dataList1: [],
        dataTime: [],
        dataList2: [],
        dataList3: [],
      },
      fourthChartData: {
        dataList1: [],
        dataTime: [],
        dataList2: [],
        dataList3: [],
        dataList4: [],
      },
      dailyChartData: { // 日活用户 echart data
        dataTime: [], // X轴时间
        legend: [], // 悬浮说明
        /**
         * 例如：
         * legend: ['WEB','APP']
         * WEBList: [],
         * APPList: [],
         * 
         *  */ 
      },

      options: [{
          value: '7',
          label: this.$t('filters.In_recent_7_days')
        }, {
          value: '14',
          label: this.$t('filters.In_recent_14_days')
        }, {
          value: '30',
          label: this.$t('filters.In_recent_30_days')
        },],
      selectTime: '7'
    }
  },
  methods: {
    // 切换时间
    timeChange(val){
      this.getData(Number(val))
      this.getViewsData()
    },
    firstTypeChange(type,event) {
      this.firstChartData = firstChartData[this.firstType]
      // this.$refs.lineChart.initChart()
      // this.$refs.firstChart.load(JSON.stringify(this.firstChartData))
    },
    // 获取访问量接口
    getViewsData(){
      let keys = ['daily_life','weekly_life','month_life',]
      keys.forEach((v,i) => {
        firstChartData[v]['dataList'] = []
        firstChartData[v]['dataTime'] = []
        getdaytotal({
          num: v == 'daily_life'?Number(this.selectTime):7,
          stype: i+1,  // 默认是天，2是周，3是月
        }).then(res=>{
          let data = res.data
          data.forEach(ele => {
            firstChartData[v]['dataTime'].unshift(ele.own_day)
            firstChartData[v]['dataList'].unshift(ele.daily_life)
          });
          if(this.firstType == v) this.firstChartData = firstChartData[this.firstType]
        });
      });
    },
    getData(num){
      firstChartData['netcash']['dataList'] = []
      firstChartData['traders']['dataList'] = []
      firstChartData['traders_users']['dataList'] = []
      firstChartData['netcash']['dataTime'] = []
      firstChartData['traders']['dataTime'] = []
      firstChartData['traders_users']['dataTime'] = []
      this.secondChartData = {
        dataList: [],
        dataTime: [],
        dataList2: [],
      }
      this.thirdChartData = {
        dataList1: [],
        dataTime: [],
        dataList2: [],
        dataList3: [],
      }
      this.fourthChartData = {
        dataList1: [],
        dataTime: [],
        dataList2: [],
        dataList3: [],
        dataList4: [],
      }
      getdaytotal({num}).then(res=>{
        let data = res.data
        data.forEach(ele => {
          firstChartData['netcash']['dataTime'].unshift(ele.own_day)
          firstChartData['traders']['dataTime'].unshift(ele.own_day)
          firstChartData['traders_users']['dataTime'].unshift(ele.own_day)
          this.secondChartData['dataTime'].unshift(ele.own_day)
          this.thirdChartData['dataTime'].unshift(ele.own_day)
          this.fourthChartData['dataTime'].unshift(ele.own_day)

          firstChartData['netcash']['dataList'].unshift(ele.netcash)
          firstChartData['traders']['dataList'].unshift(ele.traders)
          firstChartData['traders_users']['dataList'].unshift(ele.traders_users)
          this.secondChartData['dataList'].unshift(ele.platform_users)
          this.secondChartData['dataList2'].unshift(ele.push_users)
          this.thirdChartData['dataList1'].unshift(ele.iosuser)
          this.thirdChartData['dataList2'].unshift(ele.androiduser)
          this.thirdChartData['dataList3'].unshift(ele.webuser)
          this.fourthChartData['dataList1'].unshift(ele.iosrapidly)
          this.fourthChartData['dataList2'].unshift(ele.ioslocal)
          this.fourthChartData['dataList3'].unshift(ele.androidrapidly)
          this.fourthChartData['dataList4'].unshift(ele.androidlocal)
        });
        
        this.firstChartData = firstChartData[this.firstType]
        // this.$refs.firstChart.load(JSON.stringify(this.firstChartData))
      })
    },
  },
  computed: {
    ...mapGetters([
      'name',
      'roles'
    ])
  },
  components: {
    LineChart,
    LineChart2,
    LineChart3,
    BarChart,
    BarChart2
  },
  created(){
    this.getData(Number(this.selectTime))
    this.getViewsData()
  },
  mounted(){
  },
}
</script>

<style lang="scss" scoped>
.welcomeHome {
  &-container {
    &>.centerWrap{
      min-height: calc(100vh - 300px);
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .header{
      width: 96%;
      margin-top: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-left: 10px;
    }
    .item_wrap{
      border: 1px solid #eee;
      margin: 20px;
      height: 350px;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
    }
    .first_wrap{
      .chart-wrapper{
        margin-top: -10px;
      }
    }
    .second_wrap{
      padding-top: 30px;
    }
    .title{
      font-size: 13px;
    }
  }
}
</style>
