<template>
  <div class="open-close-query">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        :placeholder="$t('filters.name')"
        style="width: 150px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        size="mini"
        :placeholder="$t('filters.topIDNick')"
        style="width: 150px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        size="mini"
        :placeholder="$t('filters.agent')"
        style="width: 150px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.contract_code"
        :placeholder="$t('tableHeader.contract')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in contractOptions"
          :key="item.traderpairs"
          :label="item.traderpairs"
          :value="item.traderpairs"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.marginCurrency"
        :placeholder="$t('filters.Margin_currency')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in marginCurrencyOptions"
          :key="item.currencyid"
          :label="item.currencyname"
          :value="item.currencyname"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.account_type"
        :placeholder="$t('filters.positionType')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in accountTypeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.side"
        :placeholder="$t('filters.direction')"
        clearable
        style="width: 120px; margin-right: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="item in sizeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <span style="margin: 0 10px; font-size: 12px">{{
        $t("tableHeader.clinchDealTime")
      }}</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px; margin-right: 20px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change="filterTimeTransform"
      >
      </el-date-picker>
      <el-input
        size="mini"
        v-model="listQuery.tradeid"
        :placeholder="$t('tableHeader.transactionNumber')"
        style="width: 150px; margin-top: 10px; margin-right: 20px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-top: 10px; margin-right: 20px"
        @click="handleFilter"
      >
        {{ $t("buttons.search") }}
      </el-button>
      <el-button
        class="filter-item"
        v-if="$store.getters.roles.indexOf('USDOpenCloseQueryexport')>-1"
        :loading="exportLoading"
        style="margin-top: 10px"
        @click="handleExport"
        size="mini"
        type="success"
      >
        {{$t('buttons.export')}}
      </el-button>
    </div>

    <!-- <ul class="uls">
      <li class="lis" v-for="(item,index) in top" :key="index">
        <div class="currency">{{ item.currency_name }}</div>
        <div class="num">
          <div>
            <span>{{ $t("others.Net_positions_PNL") }}</span>
            <span
              v-show="item.netpnl != '--'" :class="`hei ${ Number(item.netpnl) == 0 || Number(item.netpnl) == '--' ? '' : Number(item.netpnl) > 0 ? 'green' : 'red' }`"
            >{{ ((Number(item.netpnl) > 0 && "+") || "") + Number(item.clospnl).sub( Math.abs(item.commission)) }}</span>
          </div>
          <div>
            <span>{{ $t("others.unwind_PNL") }}</span>
            <span
              :class="`hei ${ item.clospnl == 0 || item.clospnl == '--' ? '' : item.clospnl > 0 ? 'green' : 'red' }`"
            >{{ ((item.clospnl > 0 && "+") || "") + item.clospnl }}</span>
          </div>
          <div>
            <span>{{ $t("tableHeader.poundage") }}</span>
            <span
              :class="`hei ${ item.commission == 0 || item.commission == '--' ? '' : item.commission > 0 ? 'green' : 'red'}`"
            >{{ ((item.commission > 0 && "+") || "") + item.commission}}</span>
          </div>
        </div>
      </li>
    </ul> -->

    <div class="box_se" v-if="$store.getters.roles.indexOf('USDOpenCloseQuerySummary') > -1">
      <div class="box-left" v-for="(item,index) in top" :key="index">
        <div class="left-top">{{ item.currency_name }}</div>
        <div class="left-bottom">
          <div class="protradepnl_main protradepnlVal">
            <div class="hei">{{ $t("others.Net_positions_PNL") }}</div>
            <div v-show="item.netpnl != '--'" :class="`hei ${ Number(item.netpnl) == 0 || Number(item.netpnl) == '--' ? '' : Number(item.netpnl) > 0 ? 'green' : 'red' }`">
              <span>{{ ((Number(item.netpnl) > 0 && "+") || "") + Number(item.clospnl).sub( Math.abs(item.commission)) }}</span>
            </div>
            <span v-show="item.calNetpnl == '--'">--</span>
          </div>
          <div class="protradepnl_main protradepnlVal">
            <div class="hei">{{ $t("others.unwind_PNL") }}</div>
            <div :class="`hei ${ item.clospnl == 0 || item.clospnl == '--' ? '' : item.clospnl > 0 ? 'green' : 'red' }`">
              <span>{{ ((item.clospnl > 0 && "+") || "") + item.clospnl }}</span>
            </div>
          </div>
          <div class="protradepnl_main protradepnlVal">
            <div class="hei">{{ $t("tableHeader.poundage") }}</div>
            <div :class="`hei ${ item.commission == 0 || item.commission == '--' ? '' : item.commission > 0 ? 'green' : 'red'}`">
              <span>{{ ((item.commission > 0 && "+") || "") + item.commission}}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <el-table
      v-loading="listLoading"
      :data="levelList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')"               prop="userid" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.userid || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')"          prop="user_name" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.user_name || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topID')"                 prop="top_agent_id" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.topNick')"               prop="petname" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorID')"        prop="pareid" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')"  prop="parename" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.contract')"          prop="contractcode" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.contractcode || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.Margin_currency')"       prop="currency_name" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.currency_name || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.positionType')"          prop="account_type" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{row.account_type==1?$t('tableHeader.all_warehouse'):$t('tableHeader.by_warehouse')}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.direction')"             prop="side" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{row.side=='S'?$t('tableHeader.sell'):$t('tableHeader.buy')}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.leverage')"          prop="lever" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>&times;{{ row.lever || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.number')"            prop="volume" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.volume }}</span>&nbsp;<span>{{row.currency_name}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.clinchDealAveragePrice')" prop="trade_price" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.trade_price || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.poundage')"          prop="cost_fee" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.cost_fee || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.PNL')"               prop="close_profit" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{Number(row.close_profit).add(row.cost_fee)}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.net_PNl')"           prop="close_profit" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.close_profit || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.transactionType')"       prop="entrust_type" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>
            {{
              row.entrust_type == 0 ? $t('filters.Market_price') :
              row.entrust_type == 1 ? $t('filters.limited_price') :
              row.entrust_type == 2 ? $t('filters.Strong_flat') : '--'
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.orderSource')"       prop="order_client" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>
            {{
              row.order_client == 0 ? "Open_api" :
              row.order_client == 1 ? "Android" :
              row.order_client == 2 ? "IOS" :
              row.order_client == 3 ? 'WEB' :
              row.order_client == 4 ? "H5" :
              row.order_client == 6 ? $t('others.system_automatically') : '--'
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.entrustTime')"       prop="create_time" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.create_time || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.clinchDealTime')"    prop="update_time" align="center" min-width="78"> </el-table-column>
      <el-table-column :label="$t('tableHeader.transactionNumber')" prop="id" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.id || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.IP')"                prop="ip_address" align="center" min-width="78">
        <template slot-scope="{ row }">
          <span>{{ row.ip_address || '--' }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,20,30,50]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getorderlist, getorderlistexport, bcusdprocontractset } from "@/api/multiCurrency"
import { getprocoinList } from "@/api/user";
export default {
  name: "usdOpenCloseQuery",
  data() {
    return {
      listLoading: false,
      exportLoading: false,
      total: 0,
      levelList: [],
      filterTime: [],
      listQuery: {
        tradeid: "",              // 交易编号
        sname: "",                // 用户id,手机号，邮箱
        sectop: "",               // 顶级代理id或昵称
        sagent: "",               // 代理id或者名字
        account_type: undefined,  // 仓位类型 1：全仓 2：逐仓
        contract_code: "",        // 合约代码
        side: undefined,          // 方向 B买S卖
        marginCurrency: "",       // 保证金币种
        pageNo: 1,
        pagesize: 10,
        star: "",                 // 开始
        end: "",                  // 结束
      },
      contractOptions: [],        // 合约
      marginCurrencyOptions: [],  // 保证金币种
      accountTypeOptions: [
        { key: 1, name: this.$t('tableHeader.all_warehouse') },
        { key: 2, name: this.$t('tableHeader.by_warehouse') },
      ], // 仓位类型
      sizeOptions: [
        { key: "B", name: this.$t('tableHeader.buy') },
        { key: "S", name: this.$t('tableHeader.sell') },
      ], // 方向
      // protradepnl: {
      //   netpnl: "--",             //净netpnl
      //   clospnl: "--",            //平仓pnl
      //   commission: "--",         //手续费
      //   calNetpnl: "--",          //计算净平仓
      // },
      top: [],                    // 汇总数据
    };
  },

  components: {},

  computed: {
    // 默认时间
    // timeDefault() {
    //   let date = new Date();
    //   // 通过时间戳计算
    //   let defalutStartTime = ((date.getTime() - 1 * 24 * 3600 * 1000) / 1000 ).toDate("yyyy-MM-dd"); // 转化为时间戳
    //   let defalutEndTime = ((date.getTime() / 1000)).toDate("yyyy-MM-dd");
    //   return [defalutStartTime, defalutEndTime];
    // },
  },

  mounted() {
    getprocoinList({}).then((res) => {
      for(var i=0; i<res.data.length; i++) {
        if(res.data[i].currencyname == "USDT") {
          res.data.splice(i,1)
        }
      }
      this.marginCurrencyOptions = res.data.filter(v => v.status)
    })
    bcusdprocontractset({}).then((res) => {
      this.contractOptions = res.data.filter(v=>v.isshow == 1)
    })
    this.filterTime = this.timeDefault;
    this.getList()
  },

  methods: {
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList()
    },
    // 时间
    filterTimeTransform(val) {
      this.listQuery.star = (val && val[0]) || "";
      this.listQuery.end = val ? val[1] + " 23:59:59" : "";
    },
    getList() {
      this.listLoading = true
      let data = {};
      this.listQuery.star = (this.filterTime && this.filterTime[0]) || "";
      this.listQuery.end = this.filterTime
        ? this.filterTime[1] + " 23:59:59"
        : "";
      Object.assign(data, {
        sname: this.listQuery.sname,                        // string 账号
        sectop: this.listQuery.sectop,                      // string 顶级id或昵称
        sagent: this.listQuery.sagent,                      // string 代理id和昵称
        account_type: Number(this.listQuery.account_type) || undefined,          // string 仓位类型
        side: this.listQuery.side,                          // string 方向
        contract_code: this.listQuery.contract_code,        // string
        tradeid: this.listQuery.tradeid,                    // string
        currency_name: this.listQuery.marginCurrency,       // 保证金币种 name
        star: this.listQuery.star,                          // string 开始时间
        end: this.listQuery.end,                            // string 结束时间
        pageNo: this.listQuery.pageNo,                      // int 页数
        pagesize: this.listQuery.pagesize,                  // int 分页数量
      })
      getorderlist(data).then((res) => {
        // console.log(res)
        this.levelList = res.data.list
        this.top = res.data.protradepnl
        this.total = res.data.total
        this.listLoading = false
      })
    },
    handleExport() {
      this.exportLoading = true;
      let data = {};
      Object.assign(data,this.listQuery)
      let newDate = parseInt(new Date().getTime()/1000);
      data.end = this.listQuery.end ? this.listQuery.end : (newDate.toDate('yyyy-MM-dd')+ ' 23:59:59')
      newDate = parseInt(newDate-518400)
      data.star = this.listQuery.star ? (this.listQuery.star + ' 00:00:00' ): (newDate.toDate('yyyy-MM-dd')+ ' 00:00:00')
      getorderlistexport(data).then((res) => {
        if(res.ret == 0){
            window.location.href=res.data.download_url;
            this.exportLoading = false
        }
      }).catch(err=>{
        this.exportLoading = false
      })
    },
  },
};
</script>

<style lang="scss" scoped>
// .uls {
//   border: 1px solid #c9c9c9;
//   padding: 10px 10px;
//   margin: 15px 0px;
//   display: flex;
//   flex-direction: column;
//   .lis {
//     line-height: 30px;
//     display: flex;
//     flex-direction: row;
//     .currency {
//       width: 10%;
//       text-align: center;
//       font-weight: bold;
//     }
//     .num {
//       display: flex;
//       flex-direction: row;
//       justify-content: space-around;
//     }
//   }
// }
.box_se {
  border: 1px solid #c9c9c9;
  padding: 10px 0;
  margin: 15px 0px;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-around;
  .box-left {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-content: center;
    border-right: 1px solid #c9c9c9;
    .left-top {
      display: flex;
      justify-content: center;
      margin-top: 10px;
      font-weight: bold;
    }
    .left-bottom {
      display: flex;
      flex-direction: row;
      justify-content: space-around;
    }
  }
  .box-left:last-child {
    border: none;
  }
}
.protradepnl_main {
  display: flex;
  align-items: center;
  flex-direction: column;
  flex-wrap: wrap;
  width: 33%;
  .red {
    color: #df334e;
  }
  .green {
    color: #309f72;
  }
  & > div {
    // width: 33.3%;
    text-align: center;
  }
}
.protradepnlVal {
  font-size: 18px;
  margin: 15px auto;
  // padding: 10px auto;
}
</style>