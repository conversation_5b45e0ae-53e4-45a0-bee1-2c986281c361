<template>
  <div class="kfc-container">
    <el-table
      v-loading="listLoading"
      :data="tableData"
      border
      fit
      highlight-current-row
      style="width: 100%; margin-top: 20px"
      size="mini"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('filters.type')" prop="stype" align="center" min-width="105">
        <template slot-scope="{row}">
          <span>{{typeOptions[row.stype] || '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.type')" prop="stype" align="center" min-width="105">
        <template slot-scope="{row}">
          <span>{{statusOptions[row.status] || '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.creationTime')" prop="creat_at" align="center" width="160px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.startTime')" prop="star_time" align="center" width="160px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.endTime')" prop="end_time" align="center" width="160px"> </el-table-column>
      <el-table-column :label="$t('others.operator')" prop="manage" align="center" min-width="75">
        <template slot-scope="{ row }">
          <span>{{ row.manage || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('tableHeader.operation')"
        min-width="130px"
        class-name="small-padding fixed-width"
        align="center"
        v-if="$store.getters.roles.indexOf('exprotlistDownload')>-1"
      >
        <template slot-scope="{ row }">
          <el-button
            v-if="$store.getters.roles.indexOf('exprotlistDownload')>-1 && row.status == 1"
            type="primary"
            size="mini"
            @click="exportHandle(row)"
          >
            {{$t('buttons.download')}}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,50,100,200,300]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
//引入封装接口
import {
  getmanageexpotlist,
} from "@/api/transactionQuery";

export default {
  name: "exportList",
  data() {
    const typeOptions = {
      1: this.$t('filters.Trades_export'),
      2: this.$t('filters.Trade_open_positions_export'),
      3: this.$t('filters.Trading_assets_export'),
      4: this.$t('filters.User_assets_export'),
      5: this.$t('filters.Documentary_assets_export'),
      6: this.$t('filters.With_single_warehouse_export'),
      7: this.$t('filters.Documentary_open_positions_export'),
      8: this.$t('filters.Capital_cost_export'),
      9: this.$t('filters.Poundage_export'),
      10: this.$t('filters.Trading_frequency_export'),
      11: this.$t('filters.High_frequency_User_Identification_export'),
      12: this.$t('filters.High_win_rate_user_identification_export'),
      13: this.$t('filters.High_monetization_user_recognition_export'),
      14: this.$t('filters.Trade_opening_and_closing_export_export'),
      15: this.$t('filters.Follow_up_open_derivation_export'),
      16: this.$t('filters.Coin_margined_contracts_open_cloes_export'),
      17: this.$t('filters.Coin_margined_contracts_user_forced_balance_export'),
    };
    const statusOptions = {
      0: this.$t('filters.In_the_export'),
      1: this.$t('filters.Export_success'),
      2: this.$t('filters.Export_failure')
    };
    return {
      total: 0,
      tableData: [],
      listLoading: true,
      listQuery: {
        pageNo: 1,
        pagesize: 20,
      },
      updata: {
        
      },
      kycstate:{
          //备注
      content:''
      },
      updatas:[],
      ResetDialogVisible: false,//重置弹框显示控制
      typeOptions,//审核状态 Options
      statusOptions,
      stat:[],
      statIndex:[],
      rules:{
        audit_results:[
          { required: true,message:this.$t('dialog.pleaseSelect'), trigger: ['blur','change'] },
        ],
        err_info:[
          { required: true,message:this.$t('dialog.Mandatory'), trigger: ['blur'] },
        ]
      },
    
      //判断审核弹框里面是拒绝还是通过
      kycselet:false
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.getList();
  },

  methods: {
    // 回车搜索事件
    handleFilter(){
      this.getList();
    },
    //  table列表数据
    getList() {
      //开始有加载中效果
      this.listLoading = true;
      let data = {}
      Object.assign(data, this.listQuery)
      data.ttype = data.ttype === ''?-1:data.ttype
      getmanageexpotlist(data).then((res) => {
        this.tableData = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
        // console.log(this.stat)
      });
    },
    exportHandle(row){
      window.location.href = row.url;
    },  
  },
};
</script>
<style lang="scss" scoped>
.wc_1 {
  width: 100%;
  height: 30px;
  display: flex;
  align-items: center;
  margin: 0 10px;
 
  // justify-content: space-around;
  .wc_1-one {
    width: 45%;
  }
  .idPhotoImg_wrap ::v-deep .el-image__inner{
    width: auto;
   
  }
  .idPhotoImg_wrap ::v-deep .el-image__error{
    min-width: 100px;
  }
}
</style>