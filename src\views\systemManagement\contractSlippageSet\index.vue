<template>
  <div class="contractSlippageSet_wrap">
    <el-table
      v-loading="listLoading"
      :data="tableList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 30px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column
        :label="$t('tableHeader.contract')"
        prop="traderpairs"
        align="center"
        min-width="100px"
      />
      <el-table-column
        :label="$t('tableHeader.Min_point_spread_over_trading_time')"
        prop="ac_min_slippage"
        align="center"
        min-width="120"
      />
      <el-table-column
        :label="$t('tableHeader.Max_spread_over_trading_time')"
        prop="ac_max_slippage"
        align="center"
        min-width="120"
      />
      <el-table-column
        :label="$t('tableHeader.transactionTime_S')"
        prop="ac_seconds"
        align="center"
        min-width="100px"
      />
      <el-table-column
        v-if="$store.getters.roles.indexOf('bcprocontractcoinadd') > -1"
        :label="$t('tableHeader.operation')"
        min-width="100"
        align="center"
      >
        <template slot-scope="{ row }">
          <el-button size="mini" @click="handleEdit(row)">{{ $t('buttons.edit') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
    <el-dialog
      @close="setContractDialogClose"
      :title="$t('buttons.edit')"
      :visible.sync="setContractDialogShow"
      width="75%"
      v-dialogDrag
    >
      <el-form
        ref="setContractDialogRef"
        :rules="rules"
        :model="setContractDialogData"
        label-width="auto"
        label-position="left"
      >
        <el-form-item :label="$t('tableHeader.Min_point_spread_over_trading_time')" prop="ac_min_slippage">
          <el-input
            v-model="setContractDialogData.ac_min_slippage"
            oninput="value=value.replace(/[^\d^\.]/g,'')"
          ></el-input>
        </el-form-item>
        <el-form-item :label="$t('tableHeader.Max_spread_over_trading_time')" prop="ac_max_slippage">
          <el-input
            v-model="setContractDialogData.ac_max_slippage"
            oninput="value=value.replace(/[^\d^\.]/g,'')"
          ></el-input>
        </el-form-item>
        <el-form-item :label="$t('tableHeader.transactionTime_S')" prop="ac_seconds">
          <el-input
            v-model="setContractDialogData.ac_seconds"
            oninput="value=value.replace(/[^\d^\.]/g,'')"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" size="mini" @click="setContractHandle()"
          >{{ $t('buttons.determine') }}</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { contractset } from "@/api/systemManagement";
import { bcprocontractset } from "@/api/user";

export default {
  name: "contractSlippageSet",
  data() {
    //  最小点差
    var ac_min_slippage_V = (rules, value, callback) => {};
    return {
      listLoading: false,
      total: 0,
      tableList: null,
      listQuery: {
        pageNo: 1,
        pagesize: 10,
      },
      setContractDialogData: {
        ac_max_slippage: null,
        ac_min_slippage: null,
        ac_seconds: null,
      },
      setContractDialogShow: false, //控制添加标签对话框的显示和隐藏
      rules: {
        // ac_min_slippage: [{ validator: ac_min_slippage_V, trigger: "blur" }],
      },
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.getList();
  },

  methods: {
    feeInput(val) {
      this.setContractDialogData.fee = this.clearNoNumOfAlert(val);
    },
    //只能输入数字只能有一个小数点，小数点不能在开头，不能在结尾，第一位允许添加负号
    clearNoNumOfAlert(value) {
      //得到第一个字符是否为负号
      var t = value.charAt(0);
      //先把非数字的都替换掉，除了数字和.
      value = value.replace(/[^\d.]/g, "");
      //必须保证第一个为数字而不是.
      value = value.replace(/^\./g, "");
      //保证只有出现一个.而没有多个.
      value = value.replace(/\.{2,}/g, ".");
      //保证.只出现一次，而不能出现两次以上
      value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
      //如果第一位是负号，则允许添加
      if (t == "-") {
        value = "-" + value;
      }
      return value;
    },
    // 列表编辑按钮
    handleEdit(row) {
      Object.assign(this.setContractDialogData, row, {
        ac_max_slippage: Number(row.ac_max_slippage),
        ac_min_slippage: Number(row.ac_min_slippage),
        ac_seconds: Number(row.ac_seconds),
      });
      this.setContractDialogShow = true;
    },
    //  渲染table列表
    getList() {
      this.listLoading = true;
      bcprocontractset(this.listQuery).then((response) => {
        this.tableList = response.data;
        this.listLoading = false;
      });
    },
    setContractDialogClose() {
      this.setContractDialogData = {
        ac_max_slippage: null,
        ac_min_slippage: null,
        ac_seconds: null,
      };
      this.$refs["setContractDialogRef"].resetFields();
    },
    setContractHandle() {
      this.$refs["setContractDialogRef"].validate((valid) => {
        if (valid) {
          let data = this.setContractDialogData;
          contractset({
            ac_max_slippage: Number(data.ac_max_slippage),
            ac_min_slippage: Number(data.ac_min_slippage),
            ac_seconds: Number(data.ac_seconds),
            contract_id: data.traderid,
          }).then((res) => {
            this.setContractDialogShow = false;
            this.$notify({
              title: this.$t('buttons.modify'),
              message: this.$t('dialog.Operation_is_successful'),
              type: "success",
              duration: 2000,
            });
            this.getList();
          });
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
</style>