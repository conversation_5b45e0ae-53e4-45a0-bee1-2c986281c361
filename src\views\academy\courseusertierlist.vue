<!-- src/views/academy/courseusertierlist.vue -->
<template>
	<div class="academy-container">
		<!-- Filter Bar -->
		<el-form :inline="true" :model="filters" class="filter-form">
			<el-form-item>
				<el-input v-model="filters.uid" :placeholder="$t('course.placeholders.rebateId')" clearable />
			</el-form-item>
			<el-form-item>
				<el-select v-model="filters.vip_level" :placeholder="$t('course.placeholders.vipLevel')" clearable>
					<el-option v-for="item in vipLevels" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" icon="el-icon-search" @click="handleSearch">
					{{ $t('buttons.search') }}
				</el-button>
			</el-form-item>
			<el-form-item>
				<el-button @click="visible = !visible">
					{{ visible ? $t('course.forms.hiddenTab') : $t('course.forms.show') }} {{ $t('course.forms.referralGraph') }}
				</el-button>
			</el-form-item>
		</el-form>

		<!-- Course User Tier Table -->
		<el-table :data="tableData" border fit size="mini" class="course-table"
			:header-cell-style="{ background: '#F0F8FF', color: '#333', fontWeight: 'bold' }">
			<el-table-column :label="$t('course.tableHeaders.rebateId')" align="center" min-width="120">
				<template #default="{ row }">
					{{ row.user_id }}
				</template>
			</el-table-column>

			<el-table-column prop="paid_for_course" :label="$t('course.tableHeaders.purchasedCourses')"
				align="center" />

			<el-table-column prop="direct_users" :label="$t('course.tableHeaders.directReferrals')" align="center" />

			<el-table-column :label="$t('course.tableHeaders.teamSize')" align="center">
				<template #default="{ row }">
					{{ row.direct_users + row.indirect_users }}
				</template>
			</el-table-column>

			<el-table-column prop="level" :label="$t('course.tableHeaders.vipLevel')" align="center" />

			<el-table-column prop="rank_name" :label="$t('course.tableHeaders.vipTierName')" align="center" />

			<el-table-column :label="$t('course.tableHeaders.directReward')" align="center">
				<template #default="{ row }">
					{{ row.direct_reward.toFixed(2) }}%
				</template>
			</el-table-column>

			<el-table-column :label="$t('course.tableHeaders.indirectReward')" align="center">
				<template #default="{ row }">
					{{ row.indirect_reward.toFixed(2) }}%
				</template>
			</el-table-column>

			<el-table-column :label="$t('tableHeader.creationTime')" align="center">
				<template #default="{ row }">
					{{ formatUnix(row.created_at) }}
				</template>
			</el-table-column>
		</el-table>

		<!-- 引入封装分页组件 -->
		<pagina-tion v-show="total > 0" :total="total" :page-sizes="[10, 50, 100, 300]" :page.sync="filters.pageNo"
			:limit.sync="filters.pagesize" @pagination="fetchData" />

		<!-- Referral Graph -->
		<el-dialog :visible.sync="visible" :width="'80%'">
			<h3 style="margin-top: 12px; text-align: center;">{{ $t('course.forms.referralGraph') }}</h3>
			<div ref="chart" style="width: 100%; height: 600px;"></div>

			<!-- Depth Controls -->
			<div style="margin-top: 12px; text-align: center;">
				<el-button @click="decreaseDepth" icon="el-icon-minus" />
				<span style="margin: 0 10px;">{{ $t('course.forms.maxDepth') }}: {{ filters.maxDepth }}</span>
				<el-button @click="increaseDepth" icon="el-icon-plus" />
			</div>
		</el-dialog>
	</div>
</template>

<script>
import * as echarts from 'echarts'
import { formatUnix } from '@/utils/time';
import { getCourseUserTierList, getCourseUserTierGraph } from '@/api/academy'

export default {
	data() {
		return {
			filters: {
				uid: '',
				vip_level: '',
				pageNo: 1,
				pagesize: 50,
				rootUserId: 0,
				maxDepth: 3
			},
			myChart: null,
			tableData: [],
			total: 0,
			vipLevels: [
				{ value: 1, label: '企鹅蛋' },
				{ value: 2, label: '企鹅宝宝' },
				{ value: 3, label: '企鹅勇士' },
				{ value: 4, label: '企鹅领袖' },
				{ value: 5, label: '企鹅王者' },
				{ value: 6, label: '大使' }
			],
			visible: false,
		}
	},
	methods: {
		formatUnix,
		async fetchData() {
			try {
				const vipLevel = this.filters.vip_level

				const res = await getCourseUserTierList({
					pageNo: this.filters.pageNo,
					pagesize: this.filters.pagesize,
					uid: Number(this.filters.uid),
					level: vipLevel !== '' && !isNaN(Number(vipLevel)) ? Number(vipLevel) : null,
				})
				this.tableData = res.data.list || []
				this.total = res.data.total;
			} catch (err) {
				console.error('Failed to fetch course user tier data', err)
			}
		},
		handleSearch() {
			this.filters.pageNo = 1;
			this.fetchData()
		},
		async fetchGraph() {
			try {
				await getCourseUserTierGraph({
					root_user_id: Number(this.filters.rootUserId),
					max_depth: Number(this.filters.maxDepth),
				}).then(res => {
					const { nodes, links } = this.toGraph(res.data.list);
					this.renderGraph(nodes, links);
				});
			} catch (err) {
				console.error('Failed to fetch course user tier data', err)
			}
		},
		toGraph(dataList) {
			const nodesMap = new Map();
			const links = [];

			for (const row of dataList) {
				if (!row.user_id || !row.next_user_id) {
					console.warn('Invalid row in graph data:', row);
					continue;
				}

				const uid = String(row.user_id);
				const nid = String(row.next_user_id);

				if (!nodesMap.has(uid)) {
					nodesMap.set(uid, { id: uid, name: uid, symbolSize: 40 });
				}
				if (!nodesMap.has(nid)) {
					nodesMap.set(nid, { id: nid, name: nid, symbolSize: 30 });
				}

				if (uid !== nid) {
					links.push({ 
						source: uid, 
						target: nid, 
						label: { show: true, formatter: row.depth?.toString() ?? '0' }
					});
				}
			}

			return {
				nodes: Array.from(nodesMap.values()),
				links
			};
		},
		renderGraph(nodes, links) {
			if (!this.myChart) {
				this.myChart = echarts.init(this.$refs.chart);
				this.myChart.on('click', this.onNodeClick);
			}

			const option = {
				tooltip: {},
				animationDuration: 1500,
				animationEasingUpdate: 'quinticInOut',
				series: [{
					type: 'graph',
					layout: 'force',
					roam: true,
					label: {
						show: true,
						position: 'right',
						formatter: '{b}'
					},
					force: {
						repulsion: 120,
						edgeLength: [50, 150]
					},
					data: nodes,
					links: links,
				}]
			};

			if (!Array.isArray(nodes) || !Array.isArray(links)) {
				console.error('Invalid nodes or links passed to renderGraph', nodes, links);
				return;
			}

			try {
				this.myChart.setOption(option, { notMerge: true, lazyUpdate: true });
			} catch (e) {
				console.error('ECharts setOption failed:', e, { nodes, links, option });
			}
		},
		getNodes() {
			const seen = new Set();
			const nodes = [];

			this.graphData.forEach(row => {
				if (!seen.has(row.user_id)) {
					seen.add(row.user_id);
					nodes.push({ name: row.user_id.toString(), value: 1 });
				}
				if (!seen.has(row.next_user_id)) {
					seen.add(row.next_user_id);
					nodes.push({ name: row.next_user_id.toString() });
				}
			});

			return nodes;
		},
		getLinks() {
			return this.graphData
				.filter(row => row.depth > 0)
				.map(row => ({
					source: row.user_id.toString(),
					target: row.next_user_id.toString(),
					label: {
						show: true,
						formatter: `${row.depth}`
					}
				}));
		},
		onNodeClick(node) {
			this.filters.rootUserId = Number(node.id);
			this.fetchGraph();
		},
		increaseDepth() {
			this.filters.maxDepth++;
			this.fetchGraph();
		},
		decreaseDepth() {
			if (this.filters.maxDepth > 0) this.filters.maxDepth--;
			this.fetchGraph();
		},
	},
	mounted() {
		this.fetchData()
	},
	watch: {
		visible(val) {
			if (val)
				this.$nextTick(async () => {
					await this.fetchGraph()
				});
		}
	},
}
</script>

<style>
.academy-container {
	padding: 20px;
}

.filter-form {
	margin-bottom: 20px;
}
</style>
