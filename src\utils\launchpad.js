// src/utils/launchpad.js

export function getBrowserLangCode() {
    const lang = navigator.language || navigator.userLanguage
    if (lang.includes('en')) return 1
    if (lang.includes('zh-Hant')) return 2
    if (lang.includes('ko')) return 3
    if (lang.includes('ja')) return 4
    return 0
}

// Helper to get lang code from vue-i18n or browser
function getLangCode() {
    // Try global Vue i18n if available
    if (typeof window !== 'undefined' && window?.vm?.$i18n?.locale) {
        const currentLang = window.vm.$i18n.locale;
        const localeToLangCode = {
            zh: 0,
            en: 1,
            'zh-Hant': 2,
            ko: 3,
            ja: 4
        };
        return localeToLangCode[currentLang] ?? 0;
    }
    // Fallback to browser
    return getBrowserLangCode();
}

function getLocalizedField(langs, field) {
    const langCode = getLangCode();
    const match = langs.find(l => l.lang_code === langCode);
    return match ? match[field] : langs[0]?.[field] || '-';
}

export function getSubscriptionTitle(langs) {
    return getLocalizedField(langs, 'title');
}
export function getPledgeDetail(langs) {
    return getLocalizedField(langs, 'detail');
}
export function getPledgeRule(langs) {
    return getLocalizedField(langs, 'rule');
}
export function getPledgeDeposit(langs) {
    return getLocalizedField(langs, 'deposit');
}
export function getPledgeWithdraw(langs) {
    return getLocalizedField(langs, 'withdraw');
}
