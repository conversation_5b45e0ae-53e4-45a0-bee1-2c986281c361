<!-- src/views/launchpad/pledgeoverview.vue -->
<template>
	<div class="pledgeoverview-container">
		<!-- Filter Bar -->
		<el-form :inline="true" :model="filters" class="filter-form">
			<el-form-item>
				<el-input v-model="filters.title" :placeholder="$t(`launchpad.placeholders.pledgeCoin`)" clearable />
			</el-form-item>
			<el-form-item>
				<el-input v-model="filters.coin" :placeholder="$t(`launchpad.placeholders.tokenName`)" clearable />
			</el-form-item>
			<el-form-item label="Start">
				<el-date-picker v-model="filters.start" type="date" :placeholder="$t(`launchpad.placeholders.begin`)" />
			</el-form-item>
			<el-form-item label="End">
				<el-date-picker v-model="filters.end" type="date" :placeholder="$t(`launchpad.placeholders.end`)" />
			</el-form-item>
			<el-form-item>
				<el-button type="primary" icon="el-icon-search" @click="fetchData">
					{{ $t('buttons.search') }}
				</el-button>
				<el-button type="success" icon="el-icon-plus" @click="openDialog('add')">
					{{ $t('buttons.add') }}
				</el-button>
			</el-form-item>
		</el-form>

		<!-- Table -->
		<el-table :data="tableData" border fit size="mini" class="pledge-table" :row-class-name="tableRowClassName"
			:span-method="tableSpanMethod"
			:header-cell-style="{ background: '#F0F8FF', color: '#333', fontWeight: 'bold' }">
			<el-table-column :label="$t(`launchpad.tableHeaders.status`)" align="center" min-width="100">
				<template #default="{ row }">
					<el-tag :type="getPledgeStateTagType(row.status)">
						{{ getPledgeStatusText(row.status) }}
					</el-tag>
				</template>
			</el-table-column>

			<el-table-column prop="coin" :label="$t(`launchpad.tableHeaders.pledgeCoin`)" align="center" min-width="100" />

			<el-table-column :label="$t(`launchpad.tableHeaders.category`)" align="center">
				<template #default="{ row }">
					{{ row.category || '-' }}
				</template>
			</el-table-column>

			<el-table-column :label="$t(`launchpad.tableHeaders.baseCoins`)" align="center">
				<template #default="{ row }">
					{{ formatNumber(row.base_coins) }}
				</template>
			</el-table-column>
			<el-table-column :label="$t(`launchpad.tableHeaders.realCoins`)" align="center">
				<template #default="{ row }">
					{{ formatNumber(row.real_coins) }}
				</template>
			</el-table-column>
			<el-table-column :label="$t(`launchpad.tableHeaders.basePledges`)" align="center">
				<template #default="{ row }">
					{{ formatNumber(row.base_pledges) }}
				</template>
			</el-table-column>
			<el-table-column :label="$t(`launchpad.tableHeaders.realPledges`)" align="center">
				<template #default="{ row }">
					{{ formatNumber(row.real_pledges) }}
				</template>
			</el-table-column>

			<el-table-column :label="$t(`launchpad.tableHeaders.startPledge`)" align="center">
				<template #default="{ row }">
					{{ row.start ? formatUnix(row.pledge_start) : '-' }}
				</template>
			</el-table-column>

			<el-table-column :label="$t(`launchpad.tableHeaders.endPledge`)" align="center">
				<template #default="{ row }">
					{{ row.end ? formatUnix(row.pledge_end) : '-' }}
				</template>
			</el-table-column>

			<el-table-column :label="$t(`launchpad.tableHeaders.releasePledge`)" align="center">
				<template #default="{ row }">
					{{ row.release ? formatUnix(row.pledge_release) : $t(`launchpad.placeholders.redeemable`) }}
				</template>
			</el-table-column>

			<el-table-column :label="$t(`launchpad.tableHeaders.distributionMethod`)" align="center">
				<template #default="{ row }">
					{{ row.distribution_method || '-' }}
				</template>
			</el-table-column>

			<!-- <el-table-column label="Created At" align="center" min-width="150">
                <template #default="{ row }">
                    {{ formatIsoDatetime(row.created_at) }}
                </template>
            </el-table-column>
            <el-table-column label="Updated At" align="center" min-width="150">
                <template #default="{ row }">
                    {{ formatIsoDatetime(row.updated_at) }}
                </template>
            </el-table-column> -->

			<el-table-column :label="$t(`launchpad.tableHeaders.coin`)" align="center">
				<template #default="{ row }">
					{{ row.priceItem.coin }}
				</template>
			</el-table-column>
			<el-table-column :label="$t(`launchpad.tableHeaders.yield`)" align="center">
				<template #default="{ row }">
					{{ row.priceItem.yield_rate }}
				</template>
			</el-table-column>

			<el-table-column :label="$t('launchpad.tableHeaders.operation')" align="center" min-width="250">
				<template slot-scope="{ row }">
					<template v-if="row._isFirstPrice">
						<el-button type="text" size="mini" icon="el-icon-search" @click="openDialog('view', row)">
							{{ $t('buttons.toView') }}
						</el-button>
						<el-button type="text" size="mini" icon="el-icon-edit" @click="openDialog('edit', row)">
							{{ $t('buttons.edit') }}
						</el-button>
						<el-button type="text" size="mini" icon="el-icon-delete" @click="handleDelete(row)">
							{{ $t('buttons.delete') }}
						</el-button>
					</template>
				</template>
			</el-table-column>
		</el-table>

		<!-- 引入封装分页组件 -->
		<pagina-tion v-show="total > 0" :total="total" :page-sizes="[10, 50, 100, 300]" :page.sync="filters.pageNo"
			:limit.sync="filters.pagesize" @pagination="fetchData" />

		<!-- Add/Edit/View Pledge Dialog -->
		<el-dialog class="pledge-form"
			:title="dialogMode === 'add' ? $t('buttons.add') : dialogMode === 'edit' ? $t('buttons.edit') : $t('buttons.toView')"
			:visible.sync="dialogVisible" width="70%" :close-on-click-modal="false">
			<el-form :model="dialogForm" ref="pledgeForm" label-width="120px" :disabled="dialogMode === 'view'"
				:rules="rules">
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item :label="$t(`launchpad.tableHeaders.pledgeCoin`)" prop="coin"
							:class="{ 'long-label': $t('launchpad.tableHeaders.pledgeCoin').length > 12 }"
							:rules="[{ required: true, message: $t('launchpad.forms.pledgeCoinRequired'), trigger: 'blur' }]">
							<el-input v-model="dialogForm.coin" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item :label="$t('launchpad.tableHeaders.category')" prop="category"
							:class="{ 'long-label': $t('launchpad.tableHeaders.category').length > 12 }">
							<el-input v-model="dialogForm.category" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item :label="$t('launchpad.tableHeaders.baseCoins')" prop="base_coins"
							:class="{ 'long-label': $t('launchpad.tableHeaders.baseCoins').length > 12 }">
							<el-input-number v-model="dialogForm.base_coins" :min="0" style="width: 100%" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item :label="$t('launchpad.tableHeaders.realCoins')" prop="real_coins"
							:class="{ 'long-label': $t('launchpad.tableHeaders.realCoins').length > 12 }">
							<el-input-number v-model="dialogForm.real_coins" :min="0" style="width: 100%" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item :label="$t('launchpad.tableHeaders.basePledges')" prop="base_pledges"
							:class="{ 'long-label': $t('launchpad.tableHeaders.basePledges').length > 12 }">
							<el-input-number v-model="dialogForm.base_pledges" :min="0" style="width: 100%" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item :label="$t('launchpad.tableHeaders.realPledges')" prop="real_pledges"
							:class="{ 'long-label': $t('launchpad.tableHeaders.realPledges').length > 12 }">
							<el-input-number v-model="dialogForm.real_pledges" :min="0" style="width: 100%" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item :label="$t('launchpad.tableHeaders.yield')" prop="yield_rate">
							<el-input-number v-model="dialogForm.yield_rate" :min="0" :step="0.01"
								style="width: 100%" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item :label="$t('launchpad.tableHeaders.status')" prop="status">
							<el-input v-model="dialogForm.status" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item :label="$t('launchpad.tableHeaders.startPledge')"
							:class="{ 'long-label': $t('launchpad.tableHeaders.startPledge').length > 12 }">
							<el-date-picker v-model="dialogForm.pledge_start" type="datetime" style="width: 100%"
								value-format="yyyy-MM-dd'T'HH:mm:ssXXX" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item :label="$t('launchpad.tableHeaders.endPledge')"
							:class="{ 'long-label': $t('launchpad.tableHeaders.endPledge').length > 12 }">
							<el-date-picker v-model="dialogForm.pledge_end" type="datetime" style="width: 100%"
								value-format="yyyy-MM-dd'T'HH:mm:ssXXX" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item :label="$t('launchpad.tableHeaders.releasePledge')"
							:class="{ 'long-label': $t('launchpad.tableHeaders.releasePledge').length > 12 }">
							<el-date-picker v-model="dialogForm.pledge_release" type="datetime" style="width: 100%"
								value-format="yyyy-MM-dd'T'HH:mm:ssXXX" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item :label="$t('launchpad.tableHeaders.distributionMethod')"  prop="distribution_method"
							:class="{ 'long-label': $t('launchpad.tableHeaders.distributionMethod').length > 12 }">
							<el-input v-model="dialogForm.distribution_method" />
						</el-form-item>
					</el-col>
				</el-row>

				<!-- Dynamic Langs -->
				<el-divider content-position="left" />
				<div v-for="(lang, idx) in dialogForm.langs" :key="`lang-${idx}`"
					style="padding: 16px; margin-bottom: 16px;">
					<el-form-item :label="$t('launchpad.tableHeaders.language')" :prop="'langs.' + idx + '.lang_code'"
						:rules="[{ required: true, message: $t('launchpad.forms.langRequired'), trigger: 'change' }]">
						<el-select v-model="lang.lang_code" :placeholder="$t('launchpad.placeholders.selectLang')"
							style="width: 100%;">
							<el-option v-for="option in availableLangOptions(idx)" :key="option.value"
								:label="option.label" :value="option.value" />
						</el-select>
					</el-form-item>
					<el-form-item :label="$t('launchpad.tableHeaders.details')" :prop="'langs.' + idx + '.detail'"
						:rules="[{ required: true, message: $t('launchpad.forms.detailsRequired'), trigger: 'blur' }]">
						<el-input type="textarea" v-model="lang.detail" :rows="3" />
					</el-form-item>
					<el-form-item label="Rule" :prop="'langs.' + idx + '.rule'">
						<el-input type="textarea" v-model="lang.rule" :rows="3" />
					</el-form-item>
					<el-form-item label="Deposit" :prop="'langs.' + idx + '.deposit'">
						<el-input type="textarea" v-model="lang.deposit" :rows="3" />
					</el-form-item>
					<el-form-item label="Withdraw" :prop="'langs.' + idx + '.withdraw'">
						<el-input type="textarea" v-model="lang.withdraw" :rows="3" />
					</el-form-item>
					<el-form-item label="Yield" :prop="'langs.' + idx + '.yield'">
						<el-input v-model="lang.yield" />
					</el-form-item>

					<el-button v-if="dialogMode !== 'view' && dialogForm.langs.length > 1" type="danger"
						icon="el-icon-delete" plain size="mini" @click="deleteLang(idx)" style="margin-top: 8px;">
						{{ $t('common.delete') }}
					</el-button>
				</div>

				<el-form-item>
					<el-button v-if="dialogMode !== 'view'" type="primary" icon="el-icon-plus" plain size="mini"
						@click="addLang" :disabled="dialogForm.langs.length >= langOptions.length">
						{{ $t('common.add') }}
					</el-button>
				</el-form-item>

				<!-- Dynamic Coins -->
				<el-divider content-position="left" />
				<div v-for="(coin, idx) in dialogForm.coins" :key="`coin-${idx}`"
					style="padding: 16px; margin-bottom: 16px;">
					<el-form-item :label="$t('launchpad.tableHeaders.coin')" :prop="'coins.' + idx + '.coin'"
						:rules="[{ required: true, message: $t('launchpad.forms.coinRequired'), trigger: 'blur' }]">
						<el-input v-model="coin.coin" :placeholder="$t('launchpad.placeholders.coin')"
							:disabled="dialogMode === 'view'" />
					</el-form-item>
					<el-form-item :label="$t('launchpad.tableHeaders.yield')" :prop="'coins.' + idx + '.yield_rate'"
						:rules="[{ required: true, message: $t('launchpad.forms.yieldRequired'), trigger: 'blur' }]">
						<el-input-number v-model="coin.yield_rate" :min="0" :step="0.01"
							:disabled="dialogMode === 'view'" style="width: 100%;" />
					</el-form-item>

					<el-button v-if="dialogMode !== 'view' && dialogForm.coins.length > 1" type="danger"
						icon="el-icon-delete" plain size="mini" @click="deleteCoin(idx)" style="margin-top: 8px;">
						{{ $t('common.delete') }}
					</el-button>
				</div>
				<el-form-item>
					<el-button v-if="dialogMode !== 'view' && dialogForm.coins.length < 2" type="primary"
						icon="el-icon-plus" plain size="mini" @click="addCoin">
						{{ $t('common.add') }}
					</el-button>
				</el-form-item>

				<!-- Dynamic Networks -->
				<el-divider content-position="left" />
				<div v-for="(network, idx) in dialogForm.networks" :key="`network-${idx}`"
					style="padding: 16px; margin-bottom: 16px;">
					<el-form-item :label="$t('launchpad.tableHeaders.network')"  :prop="'networks.' + idx + '.name'">
						<el-input v-model="network.name" :placeholder="$t('launchpad.placeholders.networkName')"  />
					</el-form-item>
					<el-form-item :label="$t('launchpad.tableHeaders.iconUrl')"  :prop="'networks.' + idx + '.icon'">
						<el-input v-model="network.icon" :placeholder="$t('launchpad.placeholders.iconUrl')" />
					</el-form-item>

					<el-button v-if="dialogMode !== 'view' && dialogForm.networks.length > 1" type="danger"
						icon="el-icon-delete" plain size="mini" @click="deleteNetwork(idx)" style="margin-top: 8px;">
						{{ $t('common.delete') }}
					</el-button>
				</div>
				<el-form-item>
					<el-button v-if="dialogMode !== 'view'" type="primary" icon="el-icon-plus" plain size="mini"
						@click="addNetwork">
						{{ $t('common.add') }}
					</el-button>
				</el-form-item>
			</el-form>

			<!-- Footer -->
			 <div slot="footer" class="dialog-footer">
				<template v-if="dialogMode === 'view'">
					<el-button @click="dialogVisible = false">{{ $t('common.close') }}</el-button>
				</template>
				<template v-else>
					<el-button @click="dialogVisible = false">{{ $t('common.cancel') }}</el-button>
					<el-button type="primary" @click="handleSubmit">
						{{ dialogMode === 'edit' ? $t('common.confirm') : $t('common.add') }}
					</el-button>
				</template>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import { getPledgeList, addPledge, updatePledge, deletePledge } from '@/api/launchpad';
import { getBrowserLangCode } from '@/utils/launchpad';
import { formatIsoDatetime, formatUnix } from '@/utils/time';
import { formatNumber, getPledgeStateTagType, getPledgeStatusText } from '@/utils/format';

const defaultDialogForm = () => ({
	coin: '',
	category: '',
	base_coins: 0,
	real_coins: 0,
	base_pledges: 0,
	real_pledges: 0,
	yield_rate: 0,
	status: '',
	pledge_start: '',
	pledge_end: '',
	pledge_release: '',
	distribution_method: '',
	langs: [{ lang_code: 0, detail: '', rule: '', deposit: '', withdraw: '', yield: '' }],
	coins: [{ coin: '', yield_rate: 0 }],
	networks: [{ list_id: '', name: '', icon: '' }],
});

export default {
	name: 'PledgeOverview',
	data() {
		return {
			filters: {
				title: '',
				coin: '',
				start: null,
				end: null,
				pageNo: 1,
				pagesize: 10,
				langCode: 0, // default lang
			},
			tableData: [],
			total: 0,
			dialogVisible: false,
			dialogMode: 'add',
			dialogForm: defaultDialogForm(),
			langOptions: [
				{ value: 0, label: this.$t('lang.zhCN') },
				{ value: 1, label: this.$t('lang.en') },
				{ value: 2, label: this.$t('lang.zhTW') },
				{ value: 3, label: this.$t('lang.ko') },
				{ value: 4, label: this.$t('lang.ja') }
			],
			rules: {
				coin: [
					{ required: true, message: 'Coin is required', trigger: 'blur' }
				],
				coins: [
					{
						validator: (rule, value, callback) => {
							if (!value || value.length < 1 || value.length > 2) {
								callback(new Error('Must have 1-2 coins'));
							} else {
								callback();
							}
						},
						trigger: 'change'
					}
				],
				langs: [
					{
						validator: (rule, value, callback) => {
							if (!value || value.length < 1) {
								callback(new Error('At least one language required'));
							} else {
								callback();
							}
						},
						trigger: 'change'
					}
				],
				networks: [
					{
						validator: (rule, value, callback) => {
							if (!value || value.length < 1) {
								callback(new Error('At least one network required'));
							} else {
								callback();
							}
						},
						trigger: 'change'
					}
				]
			},
		}
	},
	methods: {
		formatIsoDatetime,
		formatNumber,
		formatUnix,
		getBrowserLangCode,
		getPledgeStateTagType,
		getPledgeStatusText,
		async fetchData() {
			try {
				// Reset pageNo if any filter is applied
				const hasFilter = Object.entries(this.filters).some(
					([key, value]) =>
						!['pageNo', 'pagesize'].includes(key) &&
						value !== '' &&
						value !== null &&
						value !== undefined
				);
				if (hasFilter) {
					this.filters.pageNo = 1;
				}
				await getPledgeList(this.filters).then(({ data }) => {
					let groupIndex = 0;
					const flattened = [];
					const list = data.list || [];
					list.forEach((pledge) => {
						// if (!Array.isArray(pledge.coins)) return;

						// If coins is a non-empty array, flatten as usual
						if (Array.isArray(pledge.coins) && pledge.coins.length > 0) {
							pledge._groupColorIndex = groupIndex;
							pledge.coins.forEach((priceItem, index) => {
								flattened.push({
									...pledge,
									_isFirstPrice: index === 0,
									_priceRowSpan: pledge.coins.length,
									priceItem,
								});
							});
							groupIndex++;
						} else {
							// Note: To remove these
							// If coins is empty or not an array, push a dummy row
							flattened.push({
								...pledge,
								_isFirstPrice: true,
								_priceRowSpan: 1,
								priceItem: {
									coin: '-',
									yield_rate: '-',
								},
							});
							groupIndex++;
						}
					});

					// console.log(JSON.stringify(flattened))
					this.tableData = flattened;
					this.total = data.total;
				});
			} catch (err) {
				console.error('Failed to fetch pledges:', err);
			}
		},
		tableSpanMethod({ row, column, rowIndex, columnIndex }) {
			// Define column indices that require rowspan
			const mergeCols = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 13]; // Adjust as per actual column order

			if (!mergeCols.includes(columnIndex)) {
				return [1, 1]; // Normal cell
			}

			if (row._isFirstPrice) {
				return [row._priceRowSpan, 1]; // rowspan, colspan
			} else {
				return [0, 0]; // merged cell, don't render
			}
		},
		tableRowClassName({ row, rowIndex }) {
			if (row._isFirstPrice) {
				this._currentStripeIndex = (this._currentStripeIndex || 0) + 1;
			}
			const groupIndex = this._currentStripeIndex || 1;
			return groupIndex % 2 === 1 ? 'group-row-odd' : 'group-row-even';
		},
		openDialog(mode, row = null) {
			this.dialogMode = mode;
			if (mode === 'add') {
				this.dialogForm = defaultDialogForm();
			} else {
				// Find the full pledge object by id (row may be a flattened row)
				const pledge = this.tableData.find(item => item.id === row.id) || row;
				this.dialogForm = {
					...pledge,
					langs: Array.isArray(pledge.langs) ? JSON.parse(JSON.stringify(pledge.langs)) : [{ lang_code: 0, detail: '' }],
					coins: Array.isArray(pledge.coins) ? JSON.parse(JSON.stringify(pledge.coins)) : [{ coin: '', yield_rate: 0 }],
					networks: Array.isArray(pledge.networks) ? JSON.parse(JSON.stringify(pledge.networks)) : [],
				};
			}
			this.dialogVisible = true;
		},
		handleSubmit() {
			if (this.dialogMode === 'view') {
				this.dialogVisible = false;
				return;
			}
			this.$refs.pledgeForm.validate((valid) => {
				if (!valid) return;
				const payload = {
					...this.dialogForm,
					pledge_start: Math.floor(new Date(this.dialogForm.pledge_start).getTime() / 1000), // stored as unix timestamp
					pledge_end: Math.floor(new Date(this.dialogForm.pledge_end).getTime() / 1000), // stored as unix timestamp
					pledge_release: Math.floor(new Date(this.dialogForm.pledge_release).getTime() / 1000), // stored as unix timestamp
					coins: this.dialogForm.coins.map(c => ({
						coin: c.coin,
						yield_rate: Number(c.yield_rate),
					})),
					langs: this.dialogForm.langs.map(l => ({
						lang_code: l.lang_code,
						detail: l.detail,
					})),
					networks: this.dialogForm.networks.map(n => ({
						name: n.name,
						icon: n.icon,
					})),
				};
				const action = this.dialogMode === 'edit'
					? updatePledge(payload)
					: addPledge(payload);

				action.then(() => {
					this.dialogVisible = false;
					this.fetchData();
				});
			});
		},
		availableLangOptions(index) {
			const used = this.dialogForm.langs
				.map((lang, idx) => idx === index ? null : lang.lang_code)
				.filter(code => code !== null && code !== undefined);

			return this.langOptions.filter(opt =>
				!used.includes(opt.value) || opt.value === this.dialogForm.langs[index].lang_code
			);
		},
		addLang() {
			const used = this.dialogForm.langs.map(l => l.lang_code);
			const remaining = this.langOptions.find(opt => !used.includes(opt.value));
			if (remaining) {
				this.dialogForm.langs.push({
					lang_code: remaining.value,
					detail: ''
				});
			}
		},
		deleteLang(idx) {
			if (this.dialogForm.langs.length > 1) {
				this.dialogForm.langs.splice(idx, 1);
			}
		},
		addCoin() {
			if (this.dialogForm.coins.length < 2) {
				this.dialogForm.coins.push({ coin: '', yield_rate: 0 });
			}
		},
		deleteCoin(idx) {
			this.dialogForm.coins.splice(idx, 1);
		},
		addNetwork() {
			this.dialogForm.networks.push({ name: '', icon: '' });
		},
		deleteNetwork(idx) {
			this.dialogForm.networks.splice(idx, 1);
		},
		handleDelete(row) {
			this.$confirm('Delete this pledge?', this.$t('dialog.Prompt'), {
				confirmButtonText: this.$t('buttons.determine'),
				cancelButtonText: this.$t('buttons.cancel'),
				type: "warning",
			}).then(() => {
				var data = {
					id: JSON.parse(row.id)
				};
				deletePledge(data).then(async (res) => {
					// console.log(res)
					if (this.tableData.length - 1 == 0) {
						this.currentPage -= 1;
						this.currentPage = this.currentPage < 1 ? 1 : this.currentPage;
					}
					await this.fetchData();
					this.$message({
						type: "error",
						message: this.$t('dialog.Delete_the_success'),
						title: this.$t('dialog.Prompt'),
					});
				});
			})
		},
	},
	mounted() {
		this.filters.langCode = this.getBrowserLangCode()
		this.fetchData();
	},
}
</script>

<style>
.pledgeoverview-container {
	padding: 20px;
}

.filter-form {
	margin-bottom: 20px;
}

.pledge-table .group-row-even {
	background-color: #f5f6f7 !important;
}

.pledge-table .group-row-odd {
	background-color: #ffffff !important;
}

.pledge-table .el-table__row:hover>td {
	background-color: inherit !important;
	transition: none;
}

.pledge-table th .cell {
	white-space: normal !important;
	word-wrap: break-word !important;
	word-break: break-word !important;
	line-height: 1.2;
}

.pledge-form .el-form-item.long-label .el-form-item__label {
	white-space: normal;
	word-wrap: break-word;
	word-break: break-word;
	line-height: 1.2;
}
</style>
