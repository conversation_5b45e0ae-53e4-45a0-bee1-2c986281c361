<template>
  <div class="open-cantainer">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="listQuery.sname"
        clearable
        :placeholder="$t('filters.name')"
        style="width: 150px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sectop"
        clearable
        size="mini"
        :placeholder="$t('filters.topIDNick')"
        style="width: 130px; margin-left: 20px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.sagent"
        clearable
        size="mini"
        :placeholder="$t('filters.agent')"
        style="width: 150px; margin-left: 20px; margin-top: 5px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="listQuery.contract_code"
        :placeholder="$t('tableHeader.contract')"
        clearable
        style="width: 120px; margin-left: 20px"
        class="filter-item"
      >
        <el-option
          v-for="item in contractOptions"
          :key="item.traderpairs"
          :label="item.traderpairs"
          :value="item.traderpairs"
        />
      </el-select>
      <!-- <el-select
        size="mini"
        v-model="listQuery.account_type"
        :placeholder="$t('filters.positionType')"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 5px"
        class="filter-item"
      >
        <el-option
          v-for="item in accountTypeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select> -->
      <el-select
        size="mini"
        v-model="listQuery.side"
        :placeholder="$t('filters.direction')"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 5px"
        class="filter-item"
      >
        <el-option
          v-for="item in sizeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.entrust_type"
        :placeholder="$t('filters.delegateType')"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="(val, key, idx) in entrustTypeObj"
          :key="idx"
          :label="val"
          :value="key"
        />
      </el-select>
      <el-select
        size="mini"
        v-model="listQuery.order_status"
        :placeholder="$t('tableHeader.clinchDealState')"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 10px"
        class="filter-item"
      >
        <el-option
          v-for="(val, key, idx) in stateObj"
          :key="idx"
          :label="val"
          :value="key"
        />
      </el-select>
      <!-- <el-select
        size="mini"
        v-model="listQuery.order_type"
        :placeholder="$t('filters.transactionType')"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 5px"
        class="filter-item"
      >
        <el-option
          v-for="item in orderTypeArr"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select> -->
      <span style="margin-left: 20px; font-size: 12px">{{$t('tableHeader.clinchDealTime')}}</span>
      <el-date-picker
        style="width: 220px; margin-top: 5px"
        v-model="filterTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change='filterTimeTransform'
      >
      </el-date-picker>
      <el-input
        v-model="listQuery.ip"
        size="mini"
        placeholder="IP"
        style="width: 150px; margin-left: 20px; margin-top: 5px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        size="mini"
        v-model="listQuery.tradeid"
        :placeholder="$t('tableHeader.transactionNumber')"
        style="width: 150px; margin-left: 20px; margin-top: 5px"
        class="filter-item"
        clearable
        @keyup.enter.native="handleFilter"
      />

      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-left: 20px; margin-top: 5px"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
      <!-- <el-button
        class="filter-item"
        :loading="exportLoading"
        v-if="$store.getters.roles.indexOf('followopenexport')>-1"
        @click="handleExport"
        size="mini"
        type="success"
      >
        {{$t('buttons.export')}}
      </el-button> -->
    </div>
    <div class="box_se" v-if="$store.getters.roles.indexOf('doctradelistConfluence')>-1">
      <div class="protradepnl_main protradepnlVal">
        <div class="hei">{{$t('others.Net_positions_PNL')}}</div>
        <div :class="`hei ${(Number(protradepnl.netpnl) == 0 || protradepnl.netpnl == '--')?'':Number(protradepnl.calNetpnl)>0?'green':'red'}`">
          <span> {{ (protradepnl.netpnl>0 && '+' || '') + protradepnl.netpnl }}</span>
        </div>
      </div>
      <div class="protradepnl_main protradepnlVal">
        <div class="hei">{{$t('others.unwind_PNL')}}</div>
        <div :class="`hei ${(protradepnl.clospnl == 0 || protradepnl.clospnl == '--')?'':protradepnl.clospnl>0?'green':'red'}`">
          <span>{{ (protradepnl.clospnl>0 && '+' || '') + protradepnl.clospnl }}</span>
        </div>
      </div>
      <div class="protradepnl_main protradepnlVal">
        <div class="hei">{{$t('tableHeader.poundage')}}</div>
        <div :class="`hei ${(protradepnl.commission == 0 || protradepnl.commission == '--')?'':protradepnl.commission>0?'green':'red'}`">
          <span>{{ (protradepnl.commission>0 && '+' || '') + protradepnl.commission }}</span>
        </div>
      </div>
    </div>


    <el-table
      v-loading="listLoading"
      :data="openList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.uid')" prop="userid" align="center" min-width="78">
         <template slot-scope="{ row }">
          <span>{{ row.userid || '--' }}</span>
        </template> 
        </el-table-column>
      <el-table-column :label="$t('tableHeader.userName')" prop="user_name" align="center" min-width="95"> </el-table-column>
      <el-table-column :label="$t('filters.topID')" prop="top_agent_id" align="center" min-width="78">
          <template slot-scope="{ row }">
          <span>{{ row.top_agent_id || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('filters.topNick')" align="center" min-width="90">
        <template slot-scope="{ row }">
          <span>{{ row.petname || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorID')" prop="pareid" align="center" min-width="78">
           <template slot-scope="{ row }">
          <span>{{ row.pareid || '--' }}</span>
        </template>
       </el-table-column>
      <el-table-column :label="$t('tableHeader.superiorUsername')" align="center" min-width="95">
        <template slot-scope="{ row }">
          <span>{{ row.parename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.contract')" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.contractcode }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.positionType')" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{row.offset=='O'?$t('tableHeader.positions'):$t('tableHeader.unwind')}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.direction')" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{row.side=='S'?$t('tableHeader.sell'):$t('tableHeader.buy')}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.accountType')" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{row.follow_order_id ? $t('tableHeader.documentary'): $t('tableHeader.withSin')}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.leverage')" align="center" min-width="60px">
        <template slot-scope="{ row }">
          <span>x{{ row.lever }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.entrustPrice')" align="center" min-width="90px">
        <template slot-scope="{ row }">
          <span>{{ row.entrust_type == 0?$t('tableHeader.fivefile'):row.price }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.entrustNum')" align="center" min-width="95px">
        <template slot-scope="{ row }">
          <span>{{ row.volume }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.clinchDealNum')" align="center" min-width="95px">
        <template slot-scope="{ row }">
          <span>{{ row.trade_volume }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.clinchDealAveragePrice')" align="center" min-width="95px">
        <template slot-scope="{ row }">
          <span>{{ row.trade_price }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.poundage')" align="center" min-width="90px">
        <template slot-scope="{ row }">
          <span>{{ row.cost_fee }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.entrustTime')" align="center" min-width="75px">
        <template slot-scope="{ row }">
          <span>{{ row.create_time }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.updateTime')" align="center" min-width="75px">
        <template slot-scope="{ row }">
          <span>{{ row.update_time }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.PNL')" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{Number(row.close_profit).add(row.cost_fee)}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.net_PNl')" min-width="90px" align="center">
        <template slot-scope="{ row }">
          <span>{{row.close_profit}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('filters.transactionType')" align="center" min-width="90px">
        <template slot-scope="{ row }">
          <span>{{ entrustTypeObj[row.entrust_type] }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.orderSource')" align="center" min-width="100px">
        <template slot-scope="{ row }">
          <span>{{ row.fromType }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.clinchDealState')" align="center" min-width="100px">
        <template slot-scope="{ row }">
          <span>{{ stateObj[row.state] }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.transactionNumber')" align="center" min-width="80">
        <template slot-scope="{ row }">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.IP')" align="center" min-width="100px">
        <template slot-scope="{ row }">
          <span>{{ row.ip_address || '--' }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page-sizes="[10,50,100,200,300]"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pagesize"
      @pagination="getList"
    />
  </div>
</template>

<script>
//封装的api
// import { gettradelist, followopenexport } from "@/api/documentaryQuery"; //opentradeexport
import { gettradelist, closetradeexport } from "@/api/transactionQuery";
import { bcprocontractset } from "@/api/user";
export default {
  name: "followtradelist",
  data() {
    return {
      listLoading: false,
      total: 0,
      openList: null,
      filterTime: [],
      listQuery: {
        tradeid: "", //
        sname: "", //用户id,手机号，邮箱
        sectop: "", //顶级代理id或昵称
        sagent: "", //代理id或者名字
        account_type: 3, //账户模式 1：全仓 2：逐仓 3:跟单  
        contract_code: "", //合约代码
        trade_id: "", //交易id
        side: undefined, //方向 B买S卖
        order_type: "",  // 交易类型
        entrust_type: '', // 委托类型
        order_status: '', // 委托类型
        pageNo: 1,
        pagesize: 10,
        star: '', //开始
        end: '', //结束
        ip:""
      },
      protradepnl: {
        netpnl: '--', //净netpnl
        clospnl: '--', //平仓pnl
        commission: '--', //手续费
      },
      contractOptions: [],
      accountTypeOptions: [
        { key: 3, name: this.$t('tableHeader.documentary') },
        { key: 4, name: this.$t('tableHeader.withSin') },
      ],
      sizeOptions: [
        { key: "O_B", name: this.$t('tableHeader.open_to_buy_more') },
        { key: "O_S", name: this.$t('tableHeader.sell_empty') },
        { key: "C_B", name: this.$t('tableHeader.buy_sides_jersey') },
        { key: "C_S", name: this.$t('tableHeader.sell_more_flat') },
      ],
      orderTypeArr:[
        {key: 0, name: this.$t('filters.Market_orders')},
        {key: 1, name: this.$t('tableHeader.order')}, 
        {key: 2, name: this.$t('tableHeader.check_single')}, 
        {key: 4, name: this.$t('tableHeader.stop_loss_orders')}, 
        {key: 5, name: this.$t('filters.Strong_flat_sheet')},
      ],
      entrustTypeObj: {
        0: this.$t('filters.Market_price'),
        1: this.$t('filters.limited_price'),
        2: this.$t('filters.Strong_flat'),
      },
      stateObj: {
        0: this.$t('filters.To_clinch_a_deal'),
        101: this.$t('filters.No_deal_has_been_withdrawn'),
        200: this.$t('filters.All_clinch_a_deal'),
        201: this.$t('filters.Part_of_the_deal_has_been_withdrawn'),
        202: this.$t('filters.Some_clinch_a_deal'),
      },
      orderTypeObj: {
        0: this.$t('filters.Market_orders'),
        1: this.$t('tableHeader.order'), 
        2: this.$t('tableHeader.check_single'), 
        4: this.$t('tableHeader.stop_loss_orders'), 
        5: this.$t('filters.Strong_flat_sheet'),
        6: this.$t('filters.Conditions_unwind'),
        7: this.$t('filters.With_single_warehouse'),
        8: this.$t('filters.With_a_single_check'),
        9: this.$t('filters.With_a_single_stop'),
      },
      os_typeObj:{
        0: 'open_api',
        1: "android",
        2: "iOS",
        3: "WEB",
        4: "H5",
        5: "open_api",
        6: this.$t('others.system_automatically'),
      },
      exportLoading: false, //导出加载中效果
    };
  },

  components: {},

  computed: {
    // 默认时间
    timeDefault () {
      let date = new Date()
      // 通过时间戳计算
      let defalutStartTime = ((date.getTime() - 1 * 24 * 3600 * 1000)/1000).toDate('yyyy-MM-dd') // 转化为时间戳
      let defalutEndTime = ((date.getTime()/1000)).toDate('yyyy-MM-dd')
      return [defalutStartTime, defalutEndTime]
    }
  },

  mounted() {
    bcprocontractset({}).then((res) => {
      this.contractOptions = res.data.filter(v=>v.isshow == 1)
    })
    this.filterTime = this.timeDefault;
    this.getList();
  },

  methods: {
    getList() {
      this.listLoading = true;
      let data = {}
      Object.assign(data,this.listQuery)
      data.side = this.listQuery.side && this.listQuery.side.split('_')[1] || undefined
      data.offset = this.listQuery.side && this.listQuery.side.split('_')[0] || undefined
      data.account_type = this.listQuery.account_type || undefined
      data.star = (this.filterTime && this.filterTime[0]) || "";
      data.end = this.filterTime ? this.filterTime[1] + " 23:59:59" : "";
      data.order_type = data.order_type === ''?-1:data.order_type
      data.entrust_type = data.entrust_type === ''?-1:Number(data.entrust_type)
      data.order_status = data.order_status === ''?-1:Number(data.order_status)
      gettradelist(data).then((res) => {
        if(res.data.list && res.data.list.length){
          this.openList = res.data.list.map((v)=>{
            // <span>{{orderTypeObj[row.order_type]+'--'+os_typeObj[row.order_client]}}</span>
            // order_client ==> 1: android 2: iOS 3: WEB 4: H5 0: open_api 6: 系统自动
            // order_type ==> 0: 市价单 1：计划单 2：止盈单 4：止损单 5：强平单  6: "条件平仓" 7: "带单平仓" 8: "带单止盈" 9: "带单止损",
                
            // api平仓：用户通过api的平仓
            // 用户平仓：用户自主平仓
            // 止盈平仓：用户自己设置的止盈价触发后的平仓
            // 止损平仓：用户自己设置的止损价触发后的平仓
            // 系统平仓：如强制平仓等系统触发的平仓
            // 带单平仓：带单交易员自主平仓所产生的跟单平仓
            // 带单止盈：带单交易员设置的止盈价触发后的平仓
            // 带单止损：带单交易员设置的止损价触发后的平仓
            let text = v.offset == 'O' ? this.$t('tableHeader.positions') : this.$t('tableHeader.unwind')
            if(v.order_client == 0){
              if(v.order_type == 0){
                v.fromType = this.$t('tableHeader.withSin')+text
              }else if(v.order_type == 2){
                v.fromType = this.$t('filters.Check_surplus')
              }else if(v.order_type == 4){
                v.fromType = this.$t('filters.Stop_positions')
              }else if(v.order_type == 5 || v.order_type == 6 ){
                v.fromType = this.$t('tableHeader.withSin')+text
              }else if(v.order_type == 7){
                v.fromType = this.$t('filters.With_single_warehouse')
              }else if(v.order_type == 8){
                v.fromType = this.$t('filters.With_a_single_check')
              }else if(v.order_type == 9){
                v.fromType = this.$t('filters.With_a_single_stop')
              }
            }else{
              // 用户操作
              v.fromType = this.$t('filters.User')+text+'/'+this.os_typeObj[v.order_client]
            }
            return v
          });
        }else{
          this.openList = []
        }
        this.total = res.data.total;
        let protradepnl = res.data.protradepnl
        Object.assign(this.protradepnl, protradepnl)
        this.listLoading = false;
      });
    },
    // 搜索事件
    handleFilter() {
      this.listQuery.pageNo = 1;
      this.getList();
    },
    //表格导出功能
    handleExport() {
      this.exportLoading = true;
      let data = {}
      Object.assign(data,this.listQuery)
      let newDate = parseInt(new Date().getTime()/1000);
      data.end = this.listQuery.end ? this.listQuery.end : (newDate.toDate('yyyy-MM-dd')+ ' 23:59:59')
      newDate = parseInt(newDate-518400)
      data.star = this.listQuery.star ? (this.listQuery.star + ' 00:00:00' ): (newDate.toDate('yyyy-MM-dd')+ ' 00:00:00')
      data.side = this.listQuery.side || undefined
      data.account_type = this.listQuery.account_type || undefined
      data.order_type = data.order_type === ''?-1:data.order_type
      followopenexport(data).then((res) => {
        if(res.ret == 0){
          this.$notify.success({title:this.$t('dialog.Operation_is_successful'),message:this.$t('dialog.Please_jiaoyi_daochu_download')})
          this.exportLoading = false
        }
      }).catch(err=>{
        this.exportLoading = false
      })
    },
    filterTimeTransform(val) {
      this.listQuery.star = val && val[0] || ''
      this.listQuery.end= val? val[1]+' 23:59:59':'';
    },
  },
};
</script>
<style lang="scss" scoped>
.box_se{
  border: 1px solid #c9c9c9;
  margin: 25px 10%;
  display:flex;
  flex-wrap: wrap;
} 
.protradepnl_main {
  display: flex;
  align-items: center;
  flex-direction: column;
  flex-wrap: wrap;
  width: 33%;
  .red {
    color: #DF334E;
  }
  .green {
    color: #309F72;
  }
  &>div{
    // width: 33.3%;
    text-align: center;
  }
}
.protradepnlKey{
  margin-top: 15px;
  margin-bottom: 5px;
}
.protradepnlVal{
  font-size: 18px;
  margin: 15px auto;
  // padding: 10px auto;
}
</style>