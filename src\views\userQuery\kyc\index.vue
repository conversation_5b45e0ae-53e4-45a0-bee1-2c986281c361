<template>
  <div class="kfc-container">
    <div class="filter-container">
      <el-input
        size="mini"
        v-model="kfcQuery.uid"
        :placeholder="$t('filters.name')"
        style="width: 150px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-select
        size="mini"
        v-model="kfcQuery.ttype"
        :placeholder="$t('tableHeader.state')"
        clearable
        style="width: 120px; margin-left: 20px; margin-top: 5px"
        class="filter-item"
      >
        <el-option
          v-for="item in ttypeOptions"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        />
      </el-select>
      <span style="margin-left: 20px; font-size: 12px">{{$t('tableHeader.toApplyForTime')}}</span>
      <el-date-picker
        style="width: 220px; margin-top: 10px"
        v-model="dataTime"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="-"
        :start-placeholder="$t('filters.startTime')"
        :end-placeholder="$t('filters.endTime')"
        @change="dataTimeChange"
      >
      </el-date-picker>

      <!-- <el-button v-waves class="filter-item" size="mini" type="primary" icon="el-icon-search" @click="handleFilter">
            {{$t('buttons.search')}}
        </el-button> -->
      <el-button
        class="filter-item"
        size="mini"
        type="primary"
        style="margin-left: 20px; margin-top: 10px"
        icon="el-icon-search"
        @click="handleFilter"
      >
        {{$t('buttons.search')}}
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="kfcList"
      border
      fit
      highlight-current-row
      size="mini"
      style="width: 100%; margin-top: 20px"
      :header-cell-style="{ background: '#F0F8FF' }"
    >
      <el-table-column :label="$t('tableHeader.toApplyForTime')" prop="create_at" align="center" width="160px"> </el-table-column>
      <el-table-column :label="$t('tableHeader.orderSerialNumber')" prop="id" align="center" min-width="70" ></el-table-column>
      <el-table-column :label="$t('tableHeader.uid')" prop="user_id" align="center" min-width="105">
        <template slot-scope="{row}">
          <span>{{row.user_id || '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.countriesRegions')" prop="country_cn" align="center" min-width="70">
      </el-table-column>
      <el-table-column :label="$t('tableHeader.state')" align="center" min-width="95px">
        <template slot-scope="{ row }">
          <div v-if="row.country_code == 'CN'">
            <span>{{ (row.state || row.state == 0) && ttypeOptions.find((v)=>(v.key == row.state)).name || '--' }}</span>
          </div>
           <div v-else>
            <span v-if="row.state == 1 || row.state == 4">{{$t('others.inIdentityAuthentication')}}</span>
            <span v-else-if="row.state == 2 || row.state == 5">{{$t('others.authenticationFailed')}}</span>
            <span v-else-if="row.state == 3 || row.state == 6">{{$t('others.passIdentityAuthentication')}}</span>
            <span v-else-if="row.state == 0">{{$t('others.certificationReset')}}</span>
          </div>
         
        </template>
      </el-table-column>
      <el-table-column :label="$t('tableHeader.processingTime')" prop="face_update_at" align="center" width="160px"> </el-table-column>
      <el-table-column
        :label="$t('tableHeader.operation')"
        min-width="130px"
        class-name="small-padding fixed-width"
        align="center"
        v-if="$store.getters.roles.indexOf('verifyhistorylist')>-1 || $store.getters.roles.indexOf('bcupverifyhistory')>-1 || $store.getters.roles.indexOf('bcupverifyreset')>-1"
      >
        <template slot-scope="{ row }">
          <el-button
            v-if="$store.getters.roles.indexOf('verifyhistorylist')>-1"
            type="primary"
            size="mini"
            @click="handle(row, 'look')"
          >
            {{$t('buttons.toView')}}
          </el-button>
          <el-button type="primary" size="mini" @click="handleReset(row)" v-if="(row.state == 5 || row.state == 6 || row.state == 2|| row.state == 3 ) && $store.getters.roles.indexOf('bcupverifyreset')>-1">{{$t('buttons.reset')}}</el-button>
          <!-- (row.state == 4 || row.manual  == 1) && -->
          <span style="margin-left:10px;" v-if="row.country_code !='CN'">
            <el-button type="primary" size="mini" @click="handle(row,'check')" v-if=" (row.state == 1 || row.state == 4) && $store.getters.roles.indexOf('bcupverifyhistory')>-1 ">{{$t('buttons.audit')}}</el-button>
          </span>
           <span style="margin-left:10px;" v-else-if="row.country_code == 'CN'">
            <el-button type="primary" size="mini" @click="handle(row,'check')" v-if=" row.manual  == 1 && row.state == 4  ">{{$t('buttons.audit')}}</el-button>
          </span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('others.operator')" prop="country_map" align="center" min-width="75">
        <template slot-scope="{ row }">
          <span>{{ row.operator || $t('others.system_automatically') }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagina-tion
      v-show="total > 0"
      :total="total"
      :page.sync="kfcQuery.pageNo"
      :limit.sync="kfcQuery.pagesize"
      @pagination="getList"
    />

    <!-- 点击查看 -->
    <el-dialog
      :title="dialogType == 'check'?$t('buttons.audit'):$t('buttons.toView')"
      :visible.sync="UpdataDialogVisible"
      width="75%"
      v-dialogDrag
    >
      <div style="width:100%; text-align: center;">
        <div class="wc_1">
          <div class="wc_1-one">{{$t('tableHeader.countriesRegions')}}:</div>
          <div>{{ updata.country_cn +'+'+updata.area_code }}</div>
        </div>
        <div class="wc_1">
          <div class="wc_1-one">{{$t('tableHeader.uid')}}:</div>
          <div>{{ updata.user_id }}</div>
        </div>
        <div class="wc_1">
          <div class="wc_1-one">{{$t('tableHeader.userName')}}:</div>
          <div>{{ updata.real_name }}</div>
        </div>
        <div class="wc_1">
          <div class="wc_1-one">{{$t('dialog.name')}}:</div>
          <div>{{ updata.forename }}</div>
        </div>
        <div class="wc_1">
          <div class="wc_1-one">{{$t('dialog.surname')}}:</div>
          <div>{{ updata.surname }}</div>
        </div>
        <div class="wc_1">
          <div class="wc_1-one">{{$t('dialog.IDnumber')}}:</div>
          <div>{{ updata.id_number }}</div>
        </div>
        <div class="wc_1" style="height: 100px">
          <div class="wc_1-one">{{$t('dialog.submitIDPhoto')}}:</div>
          <div class="idPhotoImg_wrap">
            <el-image 
              style="width: auto; height: 100px;object-fit: cover;"
              :src="updata.id_photo" 
              :preview-src-list="updata.srcList">
            </el-image>
            <el-image 
              v-if="updata.emblem_photo != ''"
              style="width: auto; height: 100px;object-fit: cover;"
              :src="updata.emblem_photo" 
              :preview-src-list="updata.emblem">
            </el-image>
            <el-image 
              v-if="updata.emblem_photo != ''"
              style="width: auto; height: 100px;object-fit: cover;"
              :src="updata.hold_photo" 
              :preview-src-list="updata.hold">
            </el-image>
          </div>
        </div>
        <!-- <div class="wc_1">
          <div class="wc_1-one">{{$t('dialog.dateOfBirth')}}:</div>
          <div>{{ updata.birthday }}</div>
        </div>
        <div class="wc_1">
          <div class="wc_1-one">{{$t('dialog.currentAge')}}:</div>
          <div>
            {{ updata.age }}
          </div>
        </div> -->
        <div class="wc_1">
          <div class="wc_1-one">{{$t('dialog.certificationStatus')}}:</div>
         
          <div v-if="updata.country_code == 'CN'">
            <span>{{ (updata.state || updata.state == 0) && ttypeOptions.find((v)=>(v.key == updata.state)).name || '--' }}</span>
          </div>
           <div v-else>
            <span v-if="updata.state == 1 || updata.state == 4">{{$t('others.inIdentityAuthentication')}}</span>
            <span v-else-if="updata.state == 2 || updata.state == 5">{{$t('others.authenticationFailed')}}</span>
            <span v-else-if="updata.state == 3 || updata.state == 6">{{$t('others.passIdentityAuthentication')}}</span>
            <span v-else-if="updata.state == 0">{{$t('others.certificationReset')}}</span>

          </div>
         
        </div>
        <div v-if="dialogType == 'look'" class="wc_1">
          <div class="wc_1-one">{{$t('dialog.reviewResults')}}:</div>
          <div>
            <span>{{[3,6].indexOf(updata.state) > -1 ?$t('buttons.audit_through'): [2,5].indexOf(updata.state) > -1?$t('buttons.audit_refuse'):updata.state == 0?$t('buttons.audit_reset'):$t('buttons.await_audit')}}</span> 
          </div>
        </div>
        <div v-if="dialogType == 'look'" class="wc_1">
          <div class="wc_1-one">{{$t('dialog.reviewer')}}:</div>
          <div>
            {{ updata.operator || '--' }}
          </div>
        </div>
        <div class="wc_1" style="margin-top:20px;" v-if="dialogType == 'check'"> <!-- v-if="dialogType == 'check'" -->
          <el-form :model="updata" :rules="rules" ref="ruleFormaudit" style=" margin-top:20px;margin-left:40%;">
            <el-form-item prop="audit_results" >
                <el-select
                  v-model="updata.audit_results"
                  :placeholder="$t('dialog.pleaseSelect')"
                  required
                  @change="select()">
                  <el-option
                    v-for="item in auditResultsOptions"
                    :key="item.key"
                    :label="item.name"
                    :value="item.key"
                  >
                  </el-option>
                </el-select> 
             </el-form-item>
          </el-form>
        </div>
          <div  class="wc_1" style="margin-top:20px;"  v-if="kycselet == true && dialogType == 'check'"> 
            <!-- v-if="kycselet==true && dialogType == 'check'" -->
          <el-form :model="updata"  :rules="rules" ref="ruleFormaudit"  style=" margin-top:40px;margin-left:40%;">
            <el-form-item prop="err_info" >
                 <el-input type="textarea" v-model="updata.err_info" :placeholder="$t('forms.refuseReason')"></el-input>
             </el-form-item>
          </el-form>
        </div>
         <div class="dis" style="margin-top:20px;display:flex;width:100%"  v-if=" updata.state == 5 || updata.state == 2"> 
             <div style="margin-left:20%;">{{$t('forms.refuseReason')}}:</div>
              <!-- <el-input type="textarea" :disabled="true" style="width:170px;margin-left:120px" v-model="updata.err_info" :placeholder="$t('forms.refuseReason')"></el-input> -->
              <div style="width:50%;">{{updata.err_info}}</div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" size="mini" @click="UpdataDialogVisible = false">{{dialogType == 'check'?$t('buttons.cancel'):$t('buttons.close')}}</el-button>
        <el-button type="primary" v-if="dialogType == 'check'" size="mini" @click="ReviewDialogClick('ruleFormaudit')">{{$t('buttons.determine')}}</el-button>
      </span>
    </el-dialog>

    <!-- 重置对话框 -->
    <el-dialog
      :title="$t('dialog.confirmInformation')"
      :visible.sync="ResetDialogVisible"
      width="50%"
      v-dialogDrag
    >
      <span>{{$t('dialog.confirmWhetherResetCertification')}}？</span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" size="mini" @click="resetEntry">{{$t('buttons.determine')}}</el-button>
      </span>
    </el-dialog>
   <!-- <el-date-picker
              v-model="birthday"
              @change="birthdayChange"
              type="date"
              v-if="dialogData.state === 1"
              placeholder="请选择出生日期"
            ></el-date-picker> -->
  </div>
  
</template>

<script>
//引入封装接口
import {
  verifyhistorylist,
  bcupverifyhistory,
  bcupverifyreset,
} from "@/api/userQuery";
import { parseTime } from "@/utils";
//引入的自定义指令
// import waves from '@/directive/waves'
export default {
  name: "Kyc",
  data() {
    const auditResultsOptions = [
      { key: 1, name: this.$t('buttons.through') },
      { key: 0, name: this.$t('buttons.refuse') },
    ];
    return {
      total: 0,
      kfcList: [],
      listLoading: true,
      dataTime: [],
      kfcQuery: {
        pageNo: 1,
        pagesize: 20,
        uid: undefined,
        ttype: '',
        star: "", 
        end: "",
      },
      flag:true,
      flags:true,
      ttypeOptions: [
        // { key: -1, name: '全部'},
        // { key: 0, name: '未认证'},
        { key: 1, name: this.$t('others.inIdentityAuthentication') },
        { key: 2, name: this.$t('others.authenticationFailed') },
        { key: 3, name: this.$t('others.passIdentityAuthentication') },
        { key: 4, name: this.$t('filters.Face_authentication') },
        { key: 5, name: this.$t('filters.Face_authentication_failure') },
        { key: 6, name: this.$t('filters.Face_authentication_passed') },
        { key: 0, name: this.$t('others.certificationReset') },
      ],
      UpdataDialogVisible: false,//查看、审核弹框显示控制
      dialogType: 'look',
      updata: {
        
      },
      kycstate:{
          //备注
      content:''
      },
      updatas:[],
      ResetDialogVisible: false,//重置弹框显示控制
      auditResultsOptions,//审核状态 Options
      stat:[],
      statIndex:[],
      rules:{
        audit_results:[
          { required: true,message:this.$t('dialog.pleaseSelect'), trigger: ['blur','change'] },
        ],
        err_info:[
          { required: true,message:this.$t('dialog.Mandatory'), trigger: ['blur'] },
        ]
      },
    
      //判断审核弹框里面是拒绝还是通过
      kycselet:false
    };
  },

  components: {},

  computed: {},

  mounted() {
    this.getList();
    // this.updata()
  },

  methods: {
    //点击搜索
    handleFilter() {
      // console.log("搜索");
    },
    // 回车搜索事件
    handleFilter(){
      this.getList();
    },
    //  table列表数据
    getList() {
      //开始有加载中效果
      this.listLoading = true;
      let data = {}
      Object.assign(data, this.kfcQuery)
      data.ttype = data.ttype === ''?-1:data.ttype
      verifyhistorylist(data).then((res) => {
        this.kfcList = res.data.list;
        this.total = res.data.total;
        this.listLoading = false;
        // console.log(this.stat)
      });
    },
    handle(row,type){
      this.UpdataDialogVisible = true;
      this.updata = Object.assign({audit_results:null}, row);
      // console.log(this.updata)
      if(this.updata.country_code == 'CN'){
        // console.log('中国')
      }else{
        // console.log('别的')
        // this.updata.state == 1,2,3
      }
      this.updata.srcList = [row.id_photo]
      // console.log(this.updata.srcList)
      this.updata.emblem = [row.emblem_photo]
      this.updata.hold = [row.hold_photo]
      this.dialogType = type
    },
    //审核确定按钮
    ReviewDialogClick(ruleFormaudit) {
      // console.log(this.updata)
      this.$refs[ruleFormaudit].validate((valid)=>{
        if(valid){
            var stats = 0
            if(this.updata.country_code == 'CN'){
              if(this.updata.audit_results==1){
                stats =6
              }else if(this.updata.audit_results==0){
                stats = 5
              }
            }else{
              if(this.updata.audit_results==1){
                stats =3
              }else if(this.updata.audit_results==0){
                stats = 2
              }
            }
        }else{
          return
        }
        var errcode = 0
        if(stats == 3 || stats == 6){
          errcode = 0
        }else if(stats == 5 || stats == 2){
          errcode = 1002
        }
     
      let data = {
        // "uid": JSON.stringify(this.updata.user_id), //用户uid
        "uid": this.updata.user_id, //用户uid
        stats, //3通过，5拒绝，7 重置 国内5拒绝6通过 国外2拒绝 3通过
        // "errcode": this.updata.err_code, //错误码
        errcode,
        "errmsg": this.updata.err_info, //错误信息
        "birthday": this.updata.birthday, //开始
        "age": this.updata.age,//生日
        "upid": this.updata.id //更新数据id
      }
      bcupverifyhistory(data).then((response) => {
        this.UpdataDialogVisible = false;
        this.$notify({
          title: this.$t('dialog.Successful'),
          message: this.$t('dialog.Operation_is_successful'),
          type: "success",
          duration: 2000,
        });
        this.getList()
      });
      })
    
    },
    //重置按钮
    handleReset(row) {
      this.updata = Object.assign({}, row);
      this.ResetDialogVisible = true;
    },
    //重置确认按钮
    resetEntry() {
      bcupverifyreset({
        "uid": this.updata.user_id, //用户uid
        "stats": 7, //3通过，5拒绝，7 重置
        "upid": this.updata.id //更新数据id
      }).then((res)=>{
        this.ResetDialogVisible = false;
        this.getList()
        this.$notify({
          title: this.$t('dialog.Successful'),
          message: this.$t('dialog.Operation_is_successful'),
          type: "success",
          duration: 2000,
        });
      })
    },
    //select框点击拒绝需要显示填写拒绝原因textarea
    select(){
      if(this.updata.audit_results ==1){
        this.kycselet = false
      }else if(this.updata.audit_results == 0){
        this.kycselet = true
      }
      // console.log(this.updata.audit_results)
    },
    dataTimeChange(val) {
      if (val) {
        this.kfcQuery.star = val[0];
        this.kfcQuery.end = val[1] + " 23:59:59";
      } else {
        this.kfcQuery.star = "";
        this.kfcQuery.end = "";
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.wc_1 {
  width: 100%;
  height: 30px;
  display: flex;
  align-items: center;
  margin: 0 10px;
 
  // justify-content: space-around;
  .wc_1-one {
    width: 45%;
  }
  .idPhotoImg_wrap ::v-deep .el-image__inner{
    width: auto;
   
  }
  .idPhotoImg_wrap ::v-deep .el-image__error{
    min-width: 100px;
  }
}
</style>