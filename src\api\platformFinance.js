import request from '@/utils/request'
//概览数据
export function getfinacewallet(data) {
  return request({
    url: '/managers/v1/platform/getfinacewallet',
    method: 'post',
    data: { data }
  })
}
//资产用户数据
export function platwalletlist(data) {
  return request({
    url: '/managers/v1/platform/platwalletlist',
    method: 'post',
    data: { data }
  })
}
//资产账户历史记录
export function platwalletdaillist(data) {
  return request({
    url: '/managers/v1/platform/platwalletdaillist',
    method: 'post',
    data: { data }
  })
}

//交易账户数据
export function getplataccount(data) {
  return request({
    url: '/managers/v1/platform/getplataccount',
    method: 'post',
    data: { data }
  })
}
//交易账户历史记录
export function plataccountdail(data) {
  return request({
    url: '/managers/v1/platform/plataccountdail',
    method: 'post',
    data: { data }
  })
}

//强平注入资金
export function getbursthous(data) {
  return request({
    url: '/managers/v1/platform/bursthous',
    method: 'post',
    data: { data }
  })
}
//强平注入资金 - 导出
export function bursthousexport(data) {
  return request({
    url: '/managers/v1/platform/bursthousexport',
    method: 'post',
    data: { data }
  })
}

//穿仓支出资金
export function getthroughhous(data) {
  return request({
    url: '/managers/v1/platform/throughhous',
    method: 'post',
    data: { data }
  })
}
//穿仓支出资金 - 导出
export function throughhousexport(data) {
  return request({
    url: '/managers/v1/platform/throughhousexport',
    method: 'post',
    data: { data }
  })
}

// 每日活动支出
export function plationtaltolout(data) {
  return request({
    url: '/managers/v1/platform/plationtaltolout',
    method: 'post',
    data: { data }
  })
}
// 赠金支出
export function getusergiftout(data) {
  return request({
    url: '/managers/v1/platform/getusergiftout',
    method: 'post',
    data: { data }
  })
}
// 活动支出
export function getusecointout(data) {
  return request({
    url: '/managers/v1/platform/getusecointout',
    method: 'post',
    data: { data }
  })
}