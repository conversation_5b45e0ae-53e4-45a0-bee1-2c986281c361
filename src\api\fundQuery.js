import request from '@/utils/request'

//用户总资产查询接口
export function reversetotalassets(data) {
  return request({
    url: '/managers/v1/reverse/reversetotalassets',
    method: 'post',
    data: { data }
  })
}
// USD资产查询
export function reverseusdassetquery(data) {
  return request({
    url: '/managers/v1/reverse/reverseusdassetquery',
    method: 'post',
    data: { data }
  })
}
//用户资产查询接口
export function getwellinfo(data) {
  return request({
    url: '/managers/v1/account/getwellinfo',
    method: 'post',
    data: { data }
  })
}
//交易资产查询接口
export function getaccinfo(data) {
  return request({
    url: '/managers/v1/account/getaccinfo',
    method: 'post',
    data: { data }
  })
}
//交易资产查询-设置标签
export function setuserlabl(data) {
  return request({
    url: '/managers/v1/account/setuserlabl',
    method: 'post',
    data: { data }
  })
}
//交易资产查询-删除标签
export function setuserlabldel(data) {
  return request({
    url: '/managers/v1/account/deluserlabl',
    method: 'post',
    data: { data }
  })
}
//用户出入金查询接口
export function getwalletbill(data) {
  return request({
    url: '/managers/v1/account/getwalletbill',
    method: 'post',
    data: { data }
  })
}
//用户财务记录 钱包
export function getwallerhistory(data) {
  return request({
    url: '/managers/v1/account/getwallerhistory',
    method: 'post',
    data: { data }
  })
}
//用户财务记录 USDT合约
export function getacchistory(data) {
  return request({
    url: '/managers/v1/account/getacchistory',
    method: 'post',
    data: { data }
  })
}
//用户财务记录 USD合约
export function getusdacchistory(data) {
  return request({
    url: '/managers/v1/account/getusdacchistory',
    method: 'post',
    data: { data }
  })
}
//用户提币管理接口
export function getwithdrawlist(data) {
  return request({
    url: '/managers/v1/account/getwithdrawlist',
    method: 'post',
    data: { data }
  })
}
//用户提币复审
export function withdrawcheck(data) {
  return request({
    url: '/managers/v1/account/withdrawcheck',
    method: 'post',
    data: { data }
  })
}
//用户提币初审
export function firstwithdrawcheck(data) {
  return request({
    url: '/managers/v1/account/firstwithdrawcheck',
    method: 'post',
    data: { data }
  })
}
//提币审核对账列表
export function getcheckwithdrawlist(data) {
  return request({
    url: '/managers/v1/account/getcheckwithdrawlist',
    method: 'post',
    data: { data }
  })
}
//提币用户log日志
export function getuserloglis(data) {
  return request({
    url: '/managers/v1/user/getuserloglis',
    method: 'post',
    data: { data }
  })
}
//法币卖出管理
export function legalorderlist(data) {
  return request({
    url: '/managers/v1/account/legalorderlist',
    method: 'post',
    data: { data }
  })
}
//法币订单审核
export function legalordercheck(data) {
  return request({
    url: '/managers/v1/account/legalordercheck',
    method: 'post',
    data: { data }
  })
}
//获取顶级代理统计
export function getagentcapital(data) {
  return request({
    url: '/managers/v1/period/getagentcapital',
    method: 'post',
    data: { data }
  })
}
//跟单流水财务记录
export function followhistory(data) {
  return request({
    url: '/managers/v1/follow/followhistory',
    method: 'post',
    data: { data }
  })
}
// 用户pnl
export function getuserpnl(data) {
  return request({
    url: 'managers/v1/risk/userpnl',
    method: 'post',
    data: { data }
  })
}
// 用户pnl
export function getuserpnl2(data) {
  return request({
    url: 'managers/v1/reverse/userpnl',
    method: 'post',
    data: { data }
  })
}
// 用户pnl - 导出
export function userpnlexport(data) {
  return request({
    url: 'managers/v1/risk/userpnlexport',
    method: 'post',
    data: { data }
  })
}
// 用户收益数据
export function tradepnl(data) {
  return request({
    url: 'managers/v1/trade/tradepnl',
    method: 'post',
    data: { data }
  })
}
// 收益曲线图
export function usertradepnl(data) {
  return request({
    url: 'managers/v1/trade/usertradepnl',
    method: 'post',
    data: { data }
  })
}
// 交易资产查询 导出 
export function getaccinfoexport(data) {
  return request({
    url: 'managers/v1/account/getaccinfoexport',
    method: 'post',
    data: { data }
  })
}
// 用户资产查询 导出 
export function getwellinfoexport(data) {
  return request({
    url: 'managers/v1/account/getwellinfoexport',
    method: 'post',
    data: { data }
  })
}
// 获取资金费用列表 reverse account
export function getusercaption(data) {
  return request({
    url: 'managers/v1/reverse/getusercaption',
    method: 'post',
    data: { data }
  })
}
// 获取资金费用列表-导出
export function usercaptionexport(data) {
  return request({
    url: 'managers/v1/account/usercaptionexport',
    method: 'post',
    data: { data }
  })
}
// 获取手续费列表 reverse trade
export function getcommiss(data) {
  return request({
    url: 'managers/v1/reverse/getcommiss',
    method: 'post',
    data: { data }
  })
}
// 获取手续费列表-导出
export function getcommissexport(data) {
  return request({
    url: 'managers/v1/trade/commissexport',
    method: 'post',
    data: { data }
  })
}
// 获取手续费列表-导出
export function getpnlperday(data) {
  return request({
    url: 'managers/v1/trade/pnlperday',
    method: 'post',
    data: { data }
  })
}

//币币兑换管理
export function userexchangelist(data) {
  return request({
    url: '/managers/v1/exchange/userexchangelist',
    method: 'post',
    data: { data }
  })
}

//查询提币地址相关uid
export function getchecktoaddrlist(data) {
  return request({
    url: '/managers/v1/account/getchecktoaddrlist',
    method: 'post',
    data: { data }
  })
}



