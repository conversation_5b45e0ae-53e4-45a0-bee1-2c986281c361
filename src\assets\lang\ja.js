import jaLocale from 'element-ui/lib/locale/lang/ja'
export default {
    filters: {
        Simplified: '簡体字中国語',
        Traditional: '繁体字中国語',
        English: '英語',
        Korean: '韓国語',
        Japanese: '日本語',
        Vietnamese: 'ベトナム語',
        Indonesian: 'インドネシア語',
        Russian: 'ロシア語',
        German: 'ドイツ語',
    },

    menus: {
        system_dictionary_management: 'システム辞書管理',
        system_dictionary_data_management: '辞書データ管理',
        name: 'プラットフォーム管理システム',
        notice_management: '通知管理',

        // feature/daniel-academycoursemanagement-0507
        course_management: 'コース管理',
        course_list: 'コース一覧',
        course_purchases: '購入履歴',
        customer_tier: '顧客ランク',
        reward_list: 'リベート一覧',

        // feature/daniel-launchpadmanagement-0529
        launchpad: 'ランチパッド管理',
        subscriptions: '申込み総覧',
        subscription_records: '定期購読購入履歴',
        pledges: 'ステーキング概要',
        pledge_records: 'ステーキング記録検索',
        redemption_records: '救済の記録',
    },

    // 页面所有的 button 文字
    buttons: {
        search: '検索',
        cancel: 'キャンセル',
        confirm: '確認',
        confim_through: '承認',
        confim_refuse: '却下',
        determine: 'OK',
        setLabal: 'ラベルを設定',
        updLabal: 'ラベルを修正',
        delLabal: 'ラベルを削除',
        delete: '削除',
        modify: '修正',
        canTop: 'トップを解除',
        placedTop: 'トップ',
        release: '公開',
        addHomeFloatingLayer: 'ホームページフローティングレイヤーを追加',
        frame: '棚',
        edit: '編集',
        remove: '削除',
        startUsing: '有効化',
        remove_startUsing: '削除/有効化',
        proTopAgent: 'トップエージェントに昇格',
        proAgent: 'エージェントに昇格',
        toView: '表示',
        reset: 'リセット',
        audit: '審査',
        export: 'エクスポート',
        download: 'ダウンロード',
        rejected: '却下',
        through: '承認',
        refuse: '拒否',
        save: '保存',
        summary: '概要',
        trial: '一次審査',
        recheck: '再審査',
        trial_through: '一次審査通過',
        trial_refuse: '一次審査拒否',
        recheck_through: '再審査通過',
        recheck_refuse: '再審査拒否',
        A_key_issue: 'ワンクリック公開',
        A_key_through: 'ワンクリック承認',
        A_key_refuse: 'ワンクリック拒否',
        cancelIssue: '発行をキャンセル',
        issue: '発行',
        close: '閉じる',
        chargeMoney: 'チャージ',
        copy: 'コピー',
        modifyLeaderboard: 'ランキングを修正',
        activitiesSet: 'アクティビティ設定',
        HASH: 'HASH',
        setSmount: '金額を設定',
        determineInsert: '挿入を確認',
        addLabel: 'ラベルを追加',
        viewAll: 'すべて表示',
        seeYesterday: '昨日を見る',
        view_IP_user_overview: 'IPユーザー概要を見る',
        view_IP_transaction_detailsv: 'IP取引詳細を見る',
        add_suspected_user: '疑わしいユーザーを追加',
        cancel_watch: '観察をキャンセル',
        new_group: '新規グループ',
        risk_control_whitelist: 'リスク管理ホワイトリスト',
        add: '追加',
        addBANNER: 'BANNERを追加',
        all: 'すべて',
        public: '公開',
        android: 'Android',
        IOS: 'IOS',
        WEB: 'WEB',
        clickOnUpload: 'クリックしてアップロード',
        addAccount: 'アカウントを追加',
        reset_passwords: 'パスワードをリセット',
        reset_google: 'Googleをリセット',
        add_grouping: 'グループを追加',
        add_delete: '追加/削除',
        edit_label: 'ラベルを編集',
        stand_up_down: '棚に上げる/下げる',
        determine_modify: '修正を確認',
        down: '下げる',
        up: '上げる',
        withdraw_remove_rebate: 'コミッションの引き出し削除',
        eliminate: '削除',
        change_groups: 'グループを修正',
        has_been_to: 'トップに到達',
        have_what: '最後まで到達',
        move_up: '上に移動',
        move_down: '下に移動',
        on_the_cross: 'アップロード中...',
        audit_through: '審査通過',
        audit_refuse: '審査拒否',
        audit_reset: '審査リセット',
        await_audit: '審査待ち',

        // 2022/02/25 新增
        Shop_address_management: '店舗住所管理',
        Create_new_version: '新バージョン作成',
        Edit_new_version: 'バージョン編集',
        // 2022/03/13 新增
        Language_description: '言語説明',
        // 2021/12/22 新增
        query: '照会',
        tbxzh: '出金制限',
        qxtbxzh: '出金制限を解除',
        bind: '管理者をバインド',

        // 2025/05/03
        approve: '承認',
    },

    others: {
        articleTitle: '記事のタイトル',
        min_max: '最大100文字、最小4文字を入力してください;',
        languageType: '言語タイプ',
        straightMatter: 'テキスト',
        picture: '画像',
        pictureSize: '画像サイズの要件：640 x 700 ピクセル',
        uploadPictures: '画像をアップロード',
        // 2025/05/06
        categoryType: 'Category',
        // 2025/05/12
        noticeTabTitle: 'お知らせタブのタイトル',
    },

    sysdict: {
        dictId: '辞書ID',
        dictName: '辞書名',
        dictType: '辞書タイプ',
        status: 'ステータス',
        remark: '備考',
        createTime: '作成時間',
        pleaseEnterDictName: '辞書名を入力してください',
        pleaseEnterDictType: '辞書タイプを入力してください',
        pleaseSelectStatus: 'ステータスを選択してください',
        pleaseEnterDictLabel: 'データラベルを入力してください',
        pleaseEnterDictSort: '辞書の並び順を入力してください',
        pleaseEnterDictValue: 'データの値を入力してください',
        normal: '正常',
        disabled: '無効',
        addDictType: '辞書タイプを追加',
        editDictType: '辞書タイプを編集',
        deleteDictType: '辞書タイプを削除',
        confirmDeleteDictType: '辞書タイプを削除してもよろしいですか？',
        pleaseEnterRemark: '備考を入力してください',
        dictTypeRequired: '辞書タイプは必須です',
        dictNameRequired: '辞書名は必須です',
        dictLabelRequired: '辞書ラベルは必須です',
        dictSortRequired: '辞書の並び順は必須です',
        dictValueRequired: '辞書の値は必須です',
        dictLabel: 'データラベル',
        dictValue: 'データ値',
        dictSort: '表示順',
        dictStatus: '表示ステータス',
        dictCode: '辞書コード',
        pleaseSelectDictName: '辞書名を選択してください',
        cssClass: 'スタイル属性',
        pleaseEnterCssClass: 'スタイル属性を入力してください',
        listClass: 'リスト表示スタイル',
        pleaseEnterListClass: 'リストのクラス名を入力してください',
        isDefault: 'デフォルトですか',
        addDictData: '辞書データを追加',
        editDictData: '辞書データを編集',
        deleteDictData: '辞書データを削除',
        confirmDeleteDictData: '辞書データを削除してもよろしいですか？',
        pleaseEnterDictData: '辞書データを入力してください',
        pleaseEnterDictDataSort: '辞書データの並び順を入力してください',
        pleaseSelectListClass: 'リスト表示スタイルを選択してください',
    },
    sysdictdata: {
        dictName: '辞書名',
        dictType: '辞書タイプ',
        dictValue: '辞書値',
        dictLabel: '辞書ラベル',
        dictSort: '表示順',
        dictStatus: 'ステータス',
        pleaseEnterDictName: '辞書名を入力してください',
        pleaseEnterDictType: '辞書タイプを入力してください',
        pleaseEnterDictLabel: '辞書ラベルを入力してください',
        pleaseEnterDictSort: '辞書の並び順を入力してください',
        pleaseEnterDictValue: '辞書の値を入力してください',
        pleaseSelectStatus: 'ステータスを選択してください',
        normal: '正常',
        disabled: '無効',
    },
    common: {
        pleaseEnterDictName: '辞書名を入力してください',
        pleaseEnterDictType: '辞書タイプを入力してください',
        pleaseEnterDictLabel: '辞書ラベルを入力してください',
        pleaseEnterDictSort: '辞書の並び順を入力してください',
        pleaseEnterDictValue: '辞書の値を入力してください',
        search: '検索',
        add: '追加',
        edit: '編集',
        delete: '削除',
        purgeRedis: 'Redisを消去する',
        confirmDelete: '削除してもよろしいですか？',
        cancel: 'キャンセル',
        reset: 'リセット',
        confirm: '確認',
        operation: '操作',
        index: '番号',
        addSuccess: '追加に成功しました',
        updateSuccess: '更新に成功しました',
        tip: 'ヒント',
        deleteSuccess: '削除に成功しました',
        deleteFailed: '削除に失敗しました',
        yes: 'はい',
        no: 'いいえ',
        copied: 'クリップボードにコピーしました',
        copyFailed: 'コピーに失敗しました',
        close: '閉じる',
    },

    // feature/daniel-academycoursemanagement-0507
    course: {
        commissionTypes: {
            direct: '直接',
            indirect: '間接',
        },
        forms: {
            add: 'コースを追加',
            edit: 'コースを編集',
            detailsLinkRequired: 'コースリンクが必要です',
            portalLinkRequired: 'ポータル画像のURLが必要です',
            validLink: '有効なURLを入力してください',
            tokenRequired: 'トークン名は必須です',
            langRequired: '少なくとも1つの言語エントリが必要です',
            titleRequired: 'コースのタイトルが必要です',
            detailsRequired: 'コースの詳細が必要です',
            showHidden: '非表示のタブを表示',
            addTab: '通知タブを追加する',
            addNotice: '通知を追加する',
            hiddenTab: '隠す',
            show: '表示',
            referralGraph: '紹介グラフ',
            maxDepth: '最大深度',
        },
        
        placeholders: {
            title: 'コース名を入力してください',
            tokenName: 'トークン名を入力してください',
            start: '開始日',
            end: '終了日',
            selectLang: '言語を選択',
            rebateId: 'リベート UID',
            sourceId: 'ソース UID',
            commissionType: 'コミッションタイプ',
            vipLevel: 'VIPレベルを選択',
        },
        tableHeaders: {
            status: 'ステータス',
            title: 'コース名',
            totalSeats: '総席数',
            totalSold: '販売数',
            amount: '単価',
            coin: 'トークン',
            begin: '開始日時',
            end: '終了日時',
            operation: '操作',
            details: '詳細',
            detailsLink: 'コースリンク',
            portalImage: 'ポータル画像',
            langContent: '多言語コンテンツ',
            language: '言語',
            purchaseTime: '購入時間',
            txId: '講座購入トランザクション ID',
            rebateId: 'リベート UID',
            sourceId: 'ソース UID',
            commissionType: 'コミッションタイプ',
            payoutTime: '支払時間',
            purchasedCourses: '購入済みコース数',
            directReferrals: '直接招待数',
            teamSize: 'チーム人数',
            vipLevel: 'VIPレベル',
            vipTierName: 'VIPランク名',
            directReward: '直接報酬',
            indirectReward: '間接報酬',
        },
    },

    // feature/daniel-launchpadmanagement-0529
    lang: {
        zhCN: '簡体字中国語',
        en: '英語',
        zhTW: '繁体字中国語',
        ko: '韓国語',
        ja: '日本語',
    },
    status: {
        all: 'すべて',
        aboutToStart: '開始前',
        inProgress: '進行中',
        normal: '正常',
        registrationClosed: '登録締切',
        ended: '終了',
        unknown: '不明',
    },
    launchpad: {
        dialogs: {
            redemptionApprove: '償還審査は通りましたか？',
        },
        forms: {
            detailsLinkRequired: '詳細リンクは必須項目です。',
            portalLinkRequired: 'ポータル画像URLは必須項目です。',
            validLink: '有効なURLを入力してください。',
            tokenRequired: 'トークン名は必須項目です。',
            langRequired: '少なくとも1つの言語項目が必要です。',
            titleRequired: 'プロジェクトタイトルは必須項目です。',
            detailsRequired: 'プロジェクトの詳細は必須項目です。',
            view: 'サブスクリプションの詳細を見る',
            pledgeCoinRequired: '担保資産/通貨を入力してください',
            success: '発放成功',
            loading: '配布中',
            fail: '配布に失敗しました',
            noNeed: 'いらない',
        },
        placeholders: {
            projectTitle: 'プロジェクト名を入力してください',
            tokenName: 'トークン名を入力してください',
            begin: '開始日',
            end: '終了日',
            selectLang: '言語を選択',
            pricePerUnit: '価格（単位ごと）',
            priceNotEntered: '価格を入力してください',
            selectToken: 'トークンを選択',
            pledgeStatusNormal: '正常',
	        pledgeStatusReview: '償還審査中',
            pledgeStatusRedeeming: '償還中',
	        pledgeStatusRedeemed: 'すでに引き換えられました',
            pledgeCoin: '担保資産/通貨を入力してください',
            redeemable: 'いつでも引き出せます',
        },
        tableHeaders: {
            begin: '開始日',
            end: '終了日',
            status: 'ステータス',
            title: 'プロジェクト名/回',
            details: '詳細',
            coin: 'トークン名',
            totalVolume: '総申込数量',
            offerPrice: '米ドルで申し込み（偽物）',
            unitPrice: '価格単位',
            totalFundsRaised: '総調達資金',
            fundsRaised: '調達済資金',
            usersFundRaised: 'ユーザー参加',
            operation: '操作',
            portal: 'プロジェクトバナー画像',
            language: '言語',
            icon: 'プロジェクトアイコン',
            price: '単価',
            offered: '獲得したコインを購入する',
            paymentAmount: '支払い金額',
            paymentCoin: '支払いコイン',
            purchasePrice: '토큰 가격',
            quantitySubscribed: '申込数量',
            category: '担保カテゴリ',
            pledgeCoin: '担保資産/通貨',
            baseCoins: '総担保額',
            realCoins: '総利用可能流動性',
            basePledges: '混合担保',
            realPledges: '実際の担保額',
            startPledge: '開始時間',
            endPledge: '終了時間',
            releasePledge: 'リリース時間',
            distributionMethod: '分配方式',
            yield: '利回り (%)',
            network: 'ブロックチェーン',
            iconUrl: 'アイコン',      
            pledgeStatus: 'ステーキング状態',
            pledgeTime: 'プレッジ時間',
            income: '収益',
            actualIncome: '実際の収益',
            redemptionInitiated: '償還開始時間',
            redemptionRedeemed: '贖いがされた時間',
            redemptionState: '償還状態',
            automaticReviewStatus: '自動審査状況',
        }
    },

    ...jaLocale
}